<script>
  import BaseGrid from './BaseGrid.svelte';
  
  /**
   * ServiceTableGrid - A specialized grid component for displaying service ID data in a table format
   * Extends the BaseGrid component with specific styling for service ID listings
   */
  export let items = [];
  export let onDelete = () => {};
</script>

<BaseGrid>
  <div class="service-table-grid">
    <div class="table-header">
      <div class="col service-code">Service Code</div>
      <div class="col service-name">Name</div>
      <div class="col service-type">Type</div>
      <div class="col validity-group">Product Validity Group</div>
      <div class="col description">Description</div>
      <div class="col internal-hours">Hours</div>
      <div class="col internal-months">Months</div>
      <div class="col actions">Actions</div>
    </div>
    <div class="table-body">
      {#if items.length === 0}
        <div class="empty-state">
          <div class="empty-icon">
            <span class="material-icons">inventory</span>
          </div>
          <h3>No Service IDs Found</h3>
          <p>Get started by adding your first service ID</p>
          <a href="/service-id/create" class="btn primary">
            <span class="material-icons">add</span>
            Add New Service
          </a>
        </div>
      {:else}
        {#each items as item}
          <div class="table-row">
            <div class="col service-code">{item.serviceCode || 'N/A'}</div>
            <div class="col service-name">{item.serviceName || 'Unnamed'}</div>
            <div class="col service-type">
              <span class="badge {item.serviceType?.toLowerCase() || 'standard'}">{item.serviceType || 'Standard'}</span>
            </div>
            <div class="col validity-group">{item.ProductValidityGroup || 'N/A'}</div>
            <div class="col description">{item.description || 'No description'}</div>
            <div class="col internal-hours">{item.internalHours || '0'}</div>
            <div class="col internal-months">{item.internalMonths || '0'}</div>
            <div class="col actions">
              <button class="action-button view" title="View Details">
                <span class="material-icons">visibility</span>
              </button>
              <a href="/service-id/edit/{item._id}" class="action-button edit" title="Edit">
                <span class="material-icons">edit</span>
              </a>
              <button on:click={() => onDelete(item)} class="action-button delete" title="Delete">
                <span class="material-icons">delete</span>
              </button>
            </div>
          </div>
        {/each}
      {/if}
    </div>
  </div>
</BaseGrid>

<style>
  .service-table-grid {
    width: 100%;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }
  
  .table-header {
    display: grid;
    grid-template-columns: 1fr 1.5fr 1fr 1fr 1.5fr 0.5fr 0.5fr 0.8fr;
    padding: 0.8rem 1rem;
    font-weight: 500;
    color: #495057;
    width: 100%;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
  }
  
  .table-body {
    width: 100%;
  }
  
  .table-row {
    display: grid;
    grid-template-columns: 1fr 1.5fr 1fr 1fr 1.5fr 0.5fr 0.5fr 0.8fr;
    padding: 0.8rem 1rem;
    border-bottom: 1px solid #f1f1f1;
    align-items: center;
    width: 100%;
  }
  
  .table-row:hover {
    background-color: #f8f9fa;
  }
  
  .table-row:last-child {
    border-bottom: none;
  }
  
  .col {
    padding: 0.5rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .service-code {
    font-weight: 500;
    color: #0a2463;
  }
  
  .badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: capitalize;
  }
  
  .badge.standard {
    background-color: #e9ecef;
    color: #495057;
  }
  
  .badge.maintenance {
    background-color: #cce5ff;
    color: #0d6efd;
  }
  
  .badge.repair {
    background-color: #f8d7da;
    color: #dc3545;
  }
  
  .badge.installation {
    background-color: #d1e7dd;
    color: #198754;
  }
  
  .badge.consultation {
    background-color: #fff3cd;
    color: #ffc107;
  }
  
  .badge.other {
    background-color: #e2e3e5;
    color: #6c757d;
  }
  
  .actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
  }
  
  .action-button {
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    transition: all 0.2s ease;
    color: #6c757d;
  }
  
  .action-button:hover {
    background-color: #f1f3f5;
  }
  
  .action-button.view:hover {
    color: #0a2463;
  }
  
  .action-button.edit:hover {
    color: #0d6efd;
  }
  
  .action-button.delete:hover {
    color: #dc3545;
  }
  
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    text-align: center;
    grid-column: 1 / -1;
  }
  
  .empty-icon {
    font-size: 4rem;
    color: #e0e6ed;
    margin-bottom: 1rem;
  }
  
  .empty-icon .material-icons {
    font-size: 4rem;
  }
  
  .empty-state h3 {
    margin: 0 0 0.5rem;
    color: #34495e;
    font-size: 1.5rem;
  }
  
  .empty-state p {
    margin: 0 0 1.5rem;
    color: #6c757d;
  }
  
  .btn {
    padding: 0.6rem 1.2rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .primary {
    background-color: #0a2463;
    color: white;
  }
  
  .primary:hover {
    background-color: #1e3a8a;
  }
  
  @media (max-width: 1200px) {
    .table-header, .table-row {
      grid-template-columns: 1fr 1.5fr 1fr 1fr 1.5fr 0.8fr;
    }
    
    .col.internal-hours,
    .col.internal-months {
      display: none;
    }
  }
  
  @media (max-width: 768px) {
    .table-header, .table-row {
      grid-template-columns: 1fr 1.5fr 1fr;
    }
    
    .col.validity-group,
    .col.description,
    .col.service-type,
    .col.internal-hours,
    .col.internal-months {
      display: none;
    }
  }
</style>
