{"name": "svelte-mongo3", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@sveltejs/adapter-auto": "^4.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@types/node": "^22.13.10", "clsx": "^2.1.1", "lucide-svelte": "^0.483.0", "shadcn-svelte": "^0.14.0", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwind-merge": "^3.0.2", "typescript": "^5.0.0", "vite": "^6.0.0"}, "dependencies": {"@types/lodash-es": "^4.17.12", "lodash-es": "^4.17.21", "mongodb": "^6.14.2", "xlsx": "^0.18.5"}}