import { error } from '@sveltejs/kit';
import { getCollection, isValidObjectId, toObjectId, convertDocumentsForClient } from '$lib/db/mongo.js';

export async function load({ params }) {
    try {
        const customerId = params.id;
        
        // Validate customerId format
        if (!isValidObjectId(customerId)) {
            throw error(400, { message: 'Invalid customer ID format' });
        }
        
        // Get customer details
        const customersCollection = await getCollection('Customers');
        const customer = await customersCollection.findOne({ _id: toObjectId(customerId) });
        
        if (!customer) {
            throw error(404, { message: 'Customer not found' });
        }
        
        // Get service contracts for this customer
        const contractsCollection = await getCollection('ServiceContracts');
        const contracts = await contractsCollection.aggregate([
            {
                $match: { customerId: toObjectId(customerId) }
            },
            {
                $lookup: {
                    from: 'Services',
                    let: { serviceId: '$serviceId' },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ['$Service ID', '$$serviceId'] }
                            }
                        }
                    ],
                    as: 'serviceDetails'
                }
            },
            {
                $unwind: {
                    path: '$serviceDetails',
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $sort: { startDate: -1 }
            }
        ]).toArray();
        
        // Convert ObjectIds to strings for client-side use as per MEMORIES
        return {
            customer: convertDocumentsForClient(customer),
            contracts: convertDocumentsForClient(contracts)
        };
    } catch (err) {
        console.error('Error loading service contracts:', err);
        if (err.status) {
            throw err;
        }
        throw error(500, { message: 'Failed to load service contracts. Please try again.' });
    }
}
