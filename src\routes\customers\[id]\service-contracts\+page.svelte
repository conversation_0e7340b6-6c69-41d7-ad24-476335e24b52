<script>
    import { goto } from '$app/navigation';
    import { connectionStatus } from '$lib/db/connectionStore';
    
    export let data;
    
    let { customer, contracts } = data;
    let showAddForm = false;
    let newContract = {
        serviceId: '',
        startDate: new Date().toISOString().split('T')[0],
        endDate: '',
        status: 'Active',
        notes: ''
    };
    
    // Format date for display
    function formatDate(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'short', 
            day: 'numeric' 
        });
    }
    
    // Get status badge color
    function getStatusColor(status) {
        if (!status) return 'bg-gray-100 text-gray-800';
        
        const statusLower = status.toLowerCase();
        
        if (statusLower.includes('active') || statusLower.includes('approved')) {
            return 'bg-green-100 text-green-800';
        } else if (statusLower.includes('pending') || statusLower.includes('wait')) {
            return 'bg-yellow-100 text-yellow-800';
        } else if (statusLower.includes('expired') || statusLower.includes('cancel') || statusLower.includes('reject')) {
            return 'bg-red-100 text-red-800';
        } else {
            return 'bg-gray-100 text-gray-800';
        }
    }
    
    // Get cost based on customer type
    function getCostForCustomerType(contract) {
        if (!contract || !contract.serviceDetails) return 'N/A';
        
        const service = contract.serviceDetails;
        
        if (customer.type === 'OEM Importer' && service['Service Cost OEM/Importer']) {
            return `$${service['Service Cost OEM/Importer']}`;
        } else if (customer.type === 'Fleet Owner' && service['Service Cost Fleet Owner']) {
            return `$${service['Service Cost Fleet Owner']}`;
        } else if (service['Retail or Estimated "Value"']) {
            return `$${service['Retail or Estimated "Value"']}`;
        }
        
        return 'N/A';
    }
    
    // Navigate back to customer details
    function goBack() {
        goto(`/customers/${customer._id}`);
    }
</script>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <div class="flex items-center gap-3">
            <button class="text-gray-600 hover:text-gray-800" on:click={goBack}>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                </svg>
            </button>
            <h1 class="text-2xl font-bold text-gray-800">Service Contracts</h1>
        </div>
        
        <!-- Connection status indicator -->
        <div class="flex items-center gap-2">
            <div class="w-3 h-3 rounded-full" 
                class:bg-green-500={$connectionStatus === 'connected'} 
                class:bg-red-500={$connectionStatus === 'disconnected'} 
                class:bg-yellow-500={$connectionStatus === 'unknown'}>
            </div>
            <span class="text-sm text-gray-600">Database Status</span>
        </div>
    </div>
    
    <!-- Customer Info -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
        <div class="bg-gray-800 px-6 py-4 text-white">
            <h2 class="text-xl font-semibold">{customer.name || customer.companyName || 'Unnamed Customer'}</h2>
            {#if customer.type}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white text-gray-800 mt-2">
                    {customer.type}
                </span>
            {/if}
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Customer Details -->
                <div>
                    <h3 class="text-sm font-semibold text-gray-700 mb-3 border-b pb-1">Contact Information</h3>
                    <div class="space-y-2 text-sm">
                        {#if customer.email}
                            <p><span class="font-medium">Email:</span> {customer.email}</p>
                        {/if}
                        {#if customer.phone}
                            <p><span class="font-medium">Phone:</span> {customer.phone}</p>
                        {/if}
                        {#if customer.contactPerson}
                            <p><span class="font-medium">Contact:</span> {customer.contactPerson}</p>
                        {/if}
                    </div>
                </div>
                
                <!-- Contract Summary -->
                <div>
                    <h3 class="text-sm font-semibold text-gray-700 mb-3 border-b pb-1">Contract Summary</h3>
                    <div class="space-y-2 text-sm">
                        <p><span class="font-medium">Total Contracts:</span> {contracts.length}</p>
                        <p><span class="font-medium">Active Contracts:</span> 
                            {contracts.filter(c => c.status?.toLowerCase().includes('active')).length} of {contracts.length}
                        </p>
                        {#if customer.type}
                            <p><span class="font-medium">{customer.type} Pricing Applied</span></p>
                        {/if}
                    </div>
                </div>
                
                <!-- Actions -->
                <div>
                    <h3 class="text-sm font-semibold text-gray-700 mb-3 border-b pb-1">Actions</h3>
                    <div class="space-y-2">
                        <button 
                            class="w-full px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
                            on:click={() => showAddForm = !showAddForm}
                        >
                            {showAddForm ? 'Cancel' : 'Add New Contract'}
                        </button>
                          </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Add Contract Form -->
    {#if showAddForm}
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Add New Service Contract</h2>
            
            <form method="POST" action="?/create" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <input type="hidden" name="customerId" value={customer._id} />
                
                <div class="form-group">
                    <label for="serviceId" class="block text-sm font-medium text-gray-700 mb-1">Service</label>
                    <select 
                        id="serviceId" 
                        name="serviceId" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        bind:value={newContract.serviceId}
                        required
                    >
                        <option value="" disabled>Select a service</option>
                        <!-- Service options would be populated here -->
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select 
                        id="status" 
                        name="status" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        bind:value={newContract.status}
                    >
                        <option value="Active">Active</option>
                        <option value="Pending">Pending</option>
                        <option value="Expired">Expired</option>
                        <option value="Cancelled">Cancelled</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="startDate" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    <input 
                        type="date" 
                        id="startDate" 
                        name="startDate" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        bind:value={newContract.startDate}
                        required
                    />
                </div>
                
                <div class="form-group">
                    <label for="endDate" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                    <input 
                        type="date" 
                        id="endDate" 
                        name="endDate" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        bind:value={newContract.endDate}
                    />
                </div>
                
                <div class="form-group md:col-span-2">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                    <textarea 
                        id="notes" 
                        name="notes" 
                        rows="3" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        bind:value={newContract.notes}
                    ></textarea>
                </div>
                
                <div class="md:col-span-2 flex justify-end gap-3 mt-2">
                    <button 
                        type="button" 
                        class="px-4 py-2 bg-gray-100 text-gray-800 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors"
                        on:click={() => showAddForm = false}
                    >
                        Cancel
                    </button>
                    <button 
                        type="submit" 
                        class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
                    >
                        Save Contract
                    </button>
                </div>
            </form>
        </div>
    {/if}
    
    <!-- Service Contracts List -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        {#if contracts.length > 0}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Service
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Date Range
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Cost
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Notes
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {#each contracts as contract (contract._id)}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">
                                        {contract.serviceDetails?.Offering || 'Unknown Service'}
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        {contract.serviceDetails?.['Service Description'] || ''}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {getStatusColor(contract.status)}">
                                        {contract.status || 'Unknown'}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {formatDate(contract.startDate)} - {contract.endDate ? formatDate(contract.endDate) : 'Ongoing'}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {getCostForCustomerType(contract)}
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                                    {contract.notes || '-'}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="/customers/{customer._id}/service-contracts/{contract._id}" class="text-blue-600 hover:text-blue-900 mr-3">
                                        Edit
                                    </a>
                                    <button class="text-red-600 hover:text-red-900">
                                        Delete
                                    </button>
                                </td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
            </div>
        {:else}
            <div class="p-8 text-center">
                <p class="text-gray-500 mb-4">No service contracts found for this customer.</p>
                <button 
                    class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
                    on:click={() => showAddForm = true}
                >
                    Add First Contract
                </button>
            </div>
        {/if}
    </div>
</div>

<style>
    .container {
        max-width: 1400px;
    }
</style>
