import { json } from '@sveltejs/kit';
import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/** @type {import('./$types').RequestHandler} */
export async function GET({ url }) {
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('ProductValidityGroupPartNumber');
    
    // Parse query parameters
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const skip = (page - 1) * limit;
    
    // Build filter based on query parameters
    const filter = {};
    
    if (url.searchParams.has('productName')) {
      filter.ProductName = { $regex: url.searchParams.get('productName'), $options: 'i' };
    }
    
    if (url.searchParams.has('productPartNumber')) {
      // Handle part number search - could be numeric or string
      const partNumber = url.searchParams.get('productPartNumber');
      if (!isNaN(parseInt(partNumber))) {
        filter.ProductPartNumber = parseInt(partNumber);
      } else {
        filter.ProductPartNumber = { $regex: partNumber, $options: 'i' };
      }
    }
    
    if (url.searchParams.has('productDesignation')) {
      filter.ProductDesignation = { $regex: url.searchParams.get('productDesignation'), $options: 'i' };
    }
    
    if (url.searchParams.has('productValidityGroup')) {
      // Use the correct field name without the typo for this collection
      filter.ProductValidityGroup = { $regex: url.searchParams.get('productValidityGroup'), $options: 'i' };
    }
    
    // Get total count for pagination
    const totalItems = await collection.countDocuments(filter);
    
    // Get items with pagination and sorting
    const items = await collection.find(filter)
      .sort({
        ProductGroupId: 1,    // Primary sort
        Id: 1,                // Secondary sort
        ProductPartNumber: 1  // Final sort - unique per product
      })
      .skip(skip)
      .limit(limit)
      .toArray();
    
    console.log(`Found ${items.length} items out of ${totalItems} total`);
    
    return json({
      items,
      totalItems,
      page,
      limit,
      totalPages: Math.ceil(totalItems / limit)
    });
  } catch (error) {
    console.error('Error fetching data:', error);
    return json({ error: 'Failed to fetch data' }, { status: 500 });
  } finally {
    // Note: In a production app, you might want to manage connection pooling differently
    // For this example, we'll close the connection after each request
    // await client.close();
  }
}

/** @type {import('./$types').RequestHandler} */
export async function POST({ request }) {
  try {
    const data = await request.json();
    
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('ProductValidityGroupPartNumber');
    
    // Ensure required fields are present
    if (!data.ProductName || !data.ProductDesignation) {
      return json({ error: 'ProductName and ProductDesignation are required' }, { status: 400 });
    }
    
    // Convert numeric fields if provided as strings
    if (data.ProductPartNumber && typeof data.ProductPartNumber === 'string') {
      data.ProductPartNumber = parseInt(data.ProductPartNumber);
    }
    
    if (data.ProductGroupId && typeof data.ProductGroupId === 'string') {
      data.ProductGroupId = parseInt(data.ProductGroupId);
    }
    
    if (data.Id && typeof data.Id === 'string') {
      data.Id = parseInt(data.Id);
    }
    
    // No need to rename the field in this collection
    
    // Add timestamps
    data.createdAt = new Date();
    data.updatedAt = new Date();
    
    const result = await collection.insertOne(data);
    
    return json({ 
      success: true, 
      id: result.insertedId,
      message: 'Item created successfully' 
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating item:', error);
    return json({ error: 'Failed to create item' }, { status: 500 });
  }
}
