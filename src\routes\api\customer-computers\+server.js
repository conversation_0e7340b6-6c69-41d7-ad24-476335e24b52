import { json } from '@sveltejs/kit';
import { getCollection } from '$lib/db/mongo';
import { ObjectId } from 'mongodb';

export async function GET() {
    try {
        const collection = await getCollection('CustomerComputers');

        const pipeline = [
            {
                $lookup: {
                    from: 'Customers',
                    localField: 'customerId',
                    foreignField: '_id',
                    as: 'customer'
                }
            },
            {
                $unwind: {
                    path: '$customer',
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $lookup: {
                    from: 'LabourTime',
                    localField: 'type',
                    foreignField: 'ComputerCategory',
                    as: 'labourTime'
                }
            },
            {
                $project: {
                    _id: 1,
                    customerId: 1,
                    name: 1,
                    type: 1,
                    serialNumber: 1,
                    model: 1,
                    manufacturer: 1,
                    purchaseDate: 1,
                    warrantyEndDate: 1,
                    operatingSystem: 1,
                    notes: 1,
                    createdAt: 1,
                    designation: 1,
                    productName: 1,
                    updatedAt: 1,
                    Category: 1,
                    ProductDesignation: 1,
                    ProductPartNumber: 1,
                    customerName: { $ifNull: ['$customer.companyName', 'Unknown Customer'] },
                    customerEmail: '$customer.email',
                    customerPhone: '$customer.phone',
                    customerCity: '$customer.city',
                    customerCountry: '$customer.country',
                    labourHours: {
                        $map: {
                            input: '$labourTime',
                            as: 'lt',
                            in: {
                                serviceCode: '$$lt.Service Code',
                                description: '$$lt.Service Description',
                                vstCode: '$$lt.VST Code',
                                vstHours: '$$lt.VST Hours',
                                servicePhase: '$$lt.ServicePhase'
                            }
                        }
                    },
                    contractNumber: 1,
                    desiredContractLengthYrs: 1,
                    desiredContractLengthHrs: 1,
                    desiredContractStartDate: 1,
                    contractAgeLimit: 1,
                    contractHoursLimit: 1,
                    contractLengthYrs: 1,
                    engineAgeYrs: 1,
                    engineAgeHrs: 1,
                    estimatedUtilizationHrsYr: 1
                }
            },
            {
                $sort: {
                    customerName: 1,
                    name: 1
                }
            }
        ];

        const computers = await collection.aggregate(pipeline).toArray();
        console.log(`Found ${computers.length} computers`);

        return json(computers);
    } catch (err) {
        console.error('Error details:', err);
        return new Response(
            JSON.stringify({
                error: 'Internal Server Error',
                details: err instanceof Error ? err.message : 'Unknown error'
            }),
            {
                status: 500,
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );
    }
}

/**
 * POST handler for creating a new customer computer
 * @param {import('@sveltejs/kit').RequestEvent} event
 */
export async function POST({ request }) {
    try {
        // Get computer data from request
        const computerData = await request.json();

        // Validate required fields
        if (!computerData.customerId || !computerData.name || !computerData.serialNumber) {
            return new Response(
                JSON.stringify({
                    error: 'Missing required fields',
                    details: 'Customer ID, name, and serial number are required'
                }),
                {
                    status: 400,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );
        }

        // Validate ObjectId format
        if (!ObjectId.isValid(computerData.customerId)) {
            return new Response(
                JSON.stringify({
                    error: 'Invalid customer ID format'
                }),
                {
                    status: 400,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );
        }

        // Convert string ID to ObjectId
        computerData.customerId = new ObjectId(computerData.customerId);

        // Add timestamps
        const now = new Date();
        computerData.createdAt = now;
        computerData.updatedAt = now;

        // Get collection and insert computer
        const collection = await getCollection('CustomerComputers');
        const result = await collection.insertOne(computerData);

        if (!result.acknowledged) {
            return new Response(
                JSON.stringify({
                    error: 'Failed to create computer'
                }),
                {
                    status: 500,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );
        }

        return json({
            success: true,
            _id: result.insertedId.toString(),
            message: 'Computer created successfully'
        }, { status: 201 });
    } catch (err) {
        console.error('Error creating computer:', err);
        return new Response(
            JSON.stringify({
                error: 'Internal Server Error',
                details: err instanceof Error ? err.message : 'Unknown error'
            }),
            {
                status: 500,
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );
    }
}
