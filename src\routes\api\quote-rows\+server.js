import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET({ url }) {
  try {
    const db = client.db('ServiceContracts');
    
    // Get quotationId from query parameter
    const quotationId = url.searchParams.get('quotationId');
    
    if (!quotationId) {
      return json({ success: false, message: 'Missing quotationId parameter' }, { status: 400 });
    }
    
    // Validate ObjectId format
    if (!ObjectId.isValid(quotationId)) {
      return json({ success: false, message: 'Invalid quotationId format' }, { status: 400 });
    }
    
    console.log(`Fetching quote rows for quotationId: ${quotationId}`);
    
    // Query the QuotationRows collection
    const quoteRows = await db.collection('QuotationRows')
      .find({ quotationId: new ObjectId(quotationId) })
      .sort({ rowType: 1, rowOrder: 1 })
      .toArray();
    
    console.log(`Found ${quoteRows.length} quote rows`);
    
    // Transform ObjectIds to strings for JSON serialization
    const transformedRows = quoteRows.map(row => ({
      ...row,
      _id: row._id.toString(),
      quotationId: row.quotationId.toString()
    }));
    
    return json({ 
      success: true, 
      data: transformedRows,
      collection: 'QuotationRows'
    });
  } catch (error) {
    console.error('Error fetching quote rows:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return json({ 
      success: false, 
      message: 'Error fetching quote rows', 
      error: errorMessage 
    }, { status: 500 });
  }
}
