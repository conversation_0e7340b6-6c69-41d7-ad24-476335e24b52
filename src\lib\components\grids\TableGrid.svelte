<!-- @component
A grid component for displaying tabular data with header and content sections.
Extends BaseGrid with specific styling for tables.
-->
<script>
  import BaseGrid from './BaseGrid.svelte';
</script>

<BaseGrid>
  <div class="table-grid">
    <slot name="header" />
    <slot name="content" />
  </div>
</BaseGrid>

<style>
  .table-grid {
    display: grid;
    grid-template-rows: auto 1fr;
    gap: 1rem;
    height: 100%;
    width: 100%;
  }
</style>
