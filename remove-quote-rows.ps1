# Script to remove the entire quote-rows directory

Write-Host "Removing quote-rows directory from the application..."

# Define path to quote-rows directory
$quoteRowsPath = "C:\SvelteProjects\Svelte-Mongo3\src\routes\api\quote-rows"

# Check if directory exists
$directoryExists = Test-Path $quoteRowsPath

if ($directoryExists) {
    Write-Host "Found quote-rows directory at: $quoteRowsPath"
    
    # List contents before removal
    Write-Host "Directory contains the following items:"
    Get-ChildItem -Path $quoteRowsPath -Recurse | ForEach-Object {
        Write-Host "  - $($_.FullName.Replace($quoteRowsPath, ''))"
    }
    
    # Remove the directory
    Write-Host "Removing directory..."
    Remove-Item -Path $quoteRowsPath -Recurse -Force
    
    # Verify removal
    if (Test-Path $quoteRowsPath) {
        Write-Host "Failed to remove quote-rows directory" -ForegroundColor Red
    } else {
        Write-Host "Successfully removed quote-rows directory" -ForegroundColor Green
    }
} else {
    Write-Host "quote-rows directory not found at: $quoteRowsPath" -ForegroundColor Yellow
}

Write-Host "Operation complete. Please restart your development server."
