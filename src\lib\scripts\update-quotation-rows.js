import { MongoClient } from 'mongodb';

// MongoDB connection string
const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function listQuotationRows() {
  try {
    // Connect to MongoDB
    await client.connect();
    console.log('Connected to MongoDB');
    
    // Get the database and collection
    const db = client.db('ServiceContracts');
    const collection = db.collection('QuotationRows');
    
    // Find all documents and sort by _id
    const rows = await collection.find({}).sort({ _id: 1 }).toArray();
    
    // Display all fields for each row
    rows.forEach((row, index) => {
      console.log(`\nRow ${index + 1}:`);
      Object.entries(row).forEach(([key, value]) => {
        console.log(`${key}: ${value}`);
      });
    });
    
    console.log(`\nTotal rows found: ${rows.length}`);
    
  } catch (error) {
    console.error('Error listing documents:', error);
  } finally {
    // Close the connection
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the list function
listQuotationRows();
