// Script to verify that QuoteRow collection exists in ServiceContracts database
const { MongoClient } = require('mongodb');

// MongoDB connection URI
const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function verifyCollection() {
  try {
    // Connect to MongoDB
    await client.connect();
    console.log('Connected to MongoDB');
    
    // List all databases
    const adminDb = client.db('admin');
    const dbs = await adminDb.admin().listDatabases();
    console.log('Available databases:');
    dbs.databases.forEach(db => {
      console.log(`- ${db.name}`);
    });
    
    // Check if ServiceContracts database exists
    const hasServiceContracts = dbs.databases.some(db => db.name === 'ServiceContracts');
    if (!hasServiceContracts) {
      console.error('ERROR: ServiceContracts database does not exist!');
      return;
    }
    
    // Get the ServiceContracts database
    const db = client.db('ServiceContracts');
    
    // List all collections in ServiceContracts
    const collections = await db.listCollections().toArray();
    console.log('\nCollections in ServiceContracts database:');
    collections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });
    
    // Check if QuoteRow collection exists
    const hasQuoteRow = collections.some(collection => collection.name === 'QuoteRow');
    if (hasQuoteRow) {
      console.log('\n✅ VERIFIED: QuoteRow collection exists in ServiceContracts database');
      
      // Count documents in QuoteRow
      const count = await db.collection('QuoteRow').countDocuments();
      console.log(`Total documents in QuoteRow: ${count}`);
      
      // Show sample document
      if (count > 0) {
        const sample = await db.collection('QuoteRow').findOne();
        console.log('\nSample document from QuoteRow:');
        console.log(JSON.stringify(sample, null, 2));
      }
    } else {
      console.error('ERROR: QuoteRow collection does not exist in ServiceContracts database!');
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the connection
    await client.close();
    console.log('\nMongoDB connection closed');
  }
}

// Run the function
verifyCollection().catch(console.error);
