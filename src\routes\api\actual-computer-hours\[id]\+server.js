import { json } from '@sveltejs/kit';
import { getCollection, isValidObjectId, toObjectId } from '$lib/db/mongo.js';

function logServerActivity(activity, details) {
  // eslint-disable-next-line no-console
  console.log(`[API LOG] ${activity}`, details || '');
}

export async function GET({ params }) {
  const id = params.id;
  logServerActivity('GET called', { id });
  try {
    const collection = await getCollection('ActualComputerHours');
    const filter = isValidObjectId(id) ? { _id: toObjectId(id) } : { _id: id };
    const doc = await collection.findOne(filter);
    if (!doc) {
      logServerActivity('GET not found', { id });
      return new Response('Not found', { status: 404 });
    }
    doc._id = doc._id?.toString?.() ?? doc._id;
    if (doc.computerId && typeof doc.computerId === 'object' && doc.computerId.toString) {
      doc.computerId = doc.computerId.toString();
    }
    logServerActivity('GET success', doc);
    return json(doc);
  } catch (err) {
    logServerActivity('GET error', err);
    return new Response('Internal Server Error', { status: 500 });
  }
}

export async function PUT({ params, request }) {
  const id = params.id;
  logServerActivity('PUT called', { id });
  
  try {
    // Parse the request body
    let data;
    try {
      data = await request.json();
      logServerActivity('PUT request data', data);
    } catch (err) {
      logServerActivity('PUT parse error', err);
      return new Response(JSON.stringify({ 
        success: false,
        error: 'Invalid JSON in request body'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Validate data
    if (!data) {
      return new Response(JSON.stringify({ 
        success: false,
        error: 'No data provided'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Ensure hours is an integer
    if (data.hours !== undefined) {
      data.hours = parseInt(data.hours);
      if (isNaN(data.hours)) data.hours = 0;
    }
    
    // Convert computerId to ObjectId if present
    if (data.computerId && isValidObjectId(data.computerId)) {
      data.computerId = toObjectId(data.computerId);
    }
    
    // Ensure ReportDateTime is present (combine ReportDate and ReportTime if needed)
    if (!data.ReportDateTime && data.ReportDate && data.ReportTime) {
      // Combine date and time fields into ISO string
      const dateStr = data.ReportDate;
      const timeStr = data.ReportTime.length === 5 ? data.ReportTime + ':00' : data.ReportTime; // HH:MM or HH:MM:SS
      data.ReportDateTime = new Date(`${dateStr}T${timeStr}`);
    }

    // Remove _id from update data (can't modify _id)
    const { _id, ...updateData } = data;
    
    // Calculate HoursPerMonth and CalculatedHoursPerMonth using ReportDate (not ReportDateTime)
    let hoursPerMonth = null;
    let calculatedHoursPerMonth = null;
    if (updateData.computerId && updateData.ReportDate) {
      const collection = await getCollection('ActualComputerHours');
      const prev = await collection.find({
        computerId: updateData.computerId,
        _id: { $ne: toObjectId(id) },
        ReportDate: { $lt: updateData.ReportDate }
      }).sort({ ReportDate: -1 }).limit(1).toArray();
      if (prev.length > 0) {
        const prevDoc = prev[0];
        const prevHours = parseFloat(prevDoc.hours) || 0;
        const prevDate = new Date(prevDoc.ReportDate);
        const currHours = parseFloat(updateData.hours) || 0;
        const currDate = new Date(updateData.ReportDate);
        const diffHours = currHours - prevHours;
        const monthsDiff = (currDate.getFullYear() - prevDate.getFullYear()) * 12 + (currDate.getMonth() - prevDate.getMonth()) || 1;
        hoursPerMonth = Math.trunc(monthsDiff > 0 ? diffHours / monthsDiff : diffHours);
        calculatedHoursPerMonth = Math.trunc(monthsDiff > 0 ? diffHours / monthsDiff : diffHours);
      }
    }
    if (hoursPerMonth !== null && !isNaN(hoursPerMonth)) {
      updateData.HoursPerMonth = hoursPerMonth;
    }
    if (calculatedHoursPerMonth !== null && !isNaN(calculatedHoursPerMonth)) {
      updateData.CalculatedHoursPerMonth = calculatedHoursPerMonth;
    }
    
    // Update the document
    const collection = await getCollection('ActualComputerHours');
    const result = await collection.updateOne(
      { _id: toObjectId(id) },
      { $set: {
        ...updateData,
        updatedAt: new Date()
      }}
    );
    
    if (result.matchedCount === 0) {
      logServerActivity('PUT not found', { id });
      return new Response(JSON.stringify({ 
        success: false,
        error: 'Document not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    logServerActivity('PUT success', { id, result });
    return json({ 
      success: true,
      id: id
    });
    
  } catch (err) {
    logServerActivity('PUT error', err);
    return new Response(JSON.stringify({ 
      success: false,
      error: 'Server error: ' + (err.message || 'Unknown error')
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Recalculate HoursPerMonth for all records of a computer
async function recalculateComputerHours(computerId) {
  if (!computerId) return false;
  
  const MAX_HOURS_PER_MONTH = 744; // Maximum hours in a month
  
  try {
    const collection = await getCollection('ActualComputerHours');
    
    // Get all records for this computer
    const allRecords = await collection.find({
      computerId: computerId
    }).toArray();
    
    // Sort by ReportDateTime if available, otherwise fallback to year/month
    allRecords.sort((a, b) => {
      // Try to use ReportDateTime first (might be stored in different case variations)
      const aReportDate = a.ReportDateTime || a.reportDateTime || a.reportdatetime;
      const bReportDate = b.ReportDateTime || b.reportDateTime || b.reportdatetime;
      
      if (aReportDate && bReportDate) {
        return new Date(aReportDate) - new Date(bReportDate);
      }
      
      // Fallback to year/month if ReportDateTime not available
      if (a.year !== b.year) return a.year - b.year;
      return a.month - b.month;
    });
    
    // Process each record in chronological order
    let previousDoc = null;
    for (const doc of allRecords) {
      // Get hours as integer
      const hours = parseInt(doc.hours || 0);
      
      // Calculate months since previous entry
      let calculatedHours;
      if (!previousDoc) {
        // First entry - just use hours (capped at max)
        calculatedHours = Math.min(hours, MAX_HOURS_PER_MONTH);
      } else {
        // Get report dates for calculation
        const currentReportDate = doc.ReportDateTime || doc.reportDateTime || doc.reportdatetime;
        const previousReportDate = previousDoc.ReportDateTime || previousDoc.reportDateTime || previousDoc.reportdatetime;
        
        let monthsDiff = 1; // Default to 1 month if dates not available
        
        if (currentReportDate && previousReportDate) {
          const currentDate = new Date(currentReportDate);
          const previousDate = new Date(previousReportDate);
          
          // Calculate months difference
          monthsDiff = (currentDate.getFullYear() - previousDate.getFullYear()) * 12 + 
                       (currentDate.getMonth() - previousDate.getMonth());
          
          // Ensure at least 1 month difference
          monthsDiff = Math.max(1, monthsDiff);
        }
        
        // Divide hours by number of months
        calculatedHours = Math.floor(hours / monthsDiff);
      }
      
      // Cap at maximum hours per month
      calculatedHours = Math.min(calculatedHours, MAX_HOURS_PER_MONTH);
      
      // Update the document
      await collection.updateOne(
        { _id: doc._id },
        { $set: { 
            hours: hours, // Ensure hours is integer
            HoursPerMonth: Math.trunc(calculatedHours), 
            CalculatedHoursPerMonth: Math.trunc(calculatedHours) 
          } 
        }
      );
      
      // Store current document as previous for next iteration
      previousDoc = doc;
    }
    
    logServerActivity('Recalculated hours using ReportDateTime', { 
      computerId: computerId.toString(), 
      recordCount: allRecords.length,
      maxHoursPerMonth: MAX_HOURS_PER_MONTH
    });
    return true;
  } catch (error) {
    logServerActivity('Error recalculating hours', error);
    return false;
  }
}
