import { error } from '@sveltejs/kit';

/** @type {import('./$types').PageServerLoad} */
export async function load({ params, url, fetch }) {
  const { computerId } = params;
  const productDesignation = url.searchParams.get('productDesignation') || 'TAD1640-42GE-B';

  try {
    // Fetch the service plan data from our API
    const response = await fetch(`/api/service-plan-generator/${computerId}?productDesignation=${productDesignation}`);

    if (!response.ok) {
      throw new Error(`Failed to fetch service plan: ${response.status}`);
    }

    const servicePlanData = await response.json();

    if (!servicePlanData.success) {
      throw new Error(servicePlanData.error || 'Failed to generate service plan');
    }

    // Try to fetch existing disabled services from ComputerServicePlan
    let disabledServices = [];
    try {
      const disabledResponse = await fetch(`/api/computer-service-plan?computerId=${computerId}&productDesignation=${productDesignation}`);
      if (disabledResponse.ok) {
        const disabledData = await disabledResponse.json();
        if (disabledData.success && disabledData.data.disabledServices) {
          disabledServices = disabledData.data.disabledServices;
        }
      }
    } catch (err) {
      console.log('No existing disabled services found, starting fresh');
    }

    return {
      servicePlan: servicePlanData,
      computerId,
      productDesignation,
      disabledServices
    };

  } catch (err) {
    console.error('Error loading service plan:', err);
    throw error(500, `Failed to load service plan: ${err.message}`);
  }
}
