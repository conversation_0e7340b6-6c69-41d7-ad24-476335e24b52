import { ObjectId } from 'mongodb';
import serviceContractDB, { COLLECTIONS } from '$lib/server/serviceContractDB';

/** @type {import('./$types').PageServerLoad} */
export async function load({ params }) {
    try {
        const db = serviceContractDB.db;
        const computerId = params.id;
        
        console.log(`Loading service quotation data for computer ID: ${computerId}`);
        
        // Get computer details
        const computer = await db.collection('CustomerComputers')
            .findOne({ _id: new ObjectId(computerId) });
            
        if (!computer) {
            throw new Error('Computer not found');
        }
        
        // Get service packages
        const servicePackages = await db.collection('ServicePackages')
            .find({ 
                active: true,
                $or: [
                    { universalOffer: true },
                    { computerCategories: { $in: [computer.Category || ''] } }
                ]
            })
            .sort({ packageLevel: 1 })
            .toArray();
            
        // Get base service offerings
        const baseServices = await db.collection('BaseServices')
            .find({
                $or: [
                    { universalOffer: true },
                    { ProductValidityGroup: computer.ProductValidityGroup }
                ]
            })
            .toArray();
            
        // Get extended support offerings
        const supportServices = await db.collection('SupportServices')
            .find({
                $or: [
                    { universalOffer: true },
                    { applicableCategories: { $in: [computer.Category || ''] } }
                ]
            })
            .toArray();
            
        // Get service contracts for this computer
        const serviceContracts = await db.collection('ServiceContracts')
            .find({ computerId: computerId.toString() })
            .sort({ createdAt: -1 })
            .limit(1)
            .toArray();
            
        // Get existing quotation header
        const quotationHeader = await db.collection(COLLECTIONS.QUOTATION_HEADERS)
            .findOne({ computerId: computerId.toString() });
            
        // Get existing quotation lines
        const quotationLines = await db.collection(COLLECTIONS.QUOTATION_LINES)
            .find({ quotationId: quotationHeader?._id?.toString() || 'new' })
            .toArray();
            
        // Transform data for client
        const transformedComputer = {
            _id: computer._id.toString(),
            name: computer.name || '',
            serialNumber: computer.serialNumber || '',
            productDesignation: computer.ProductDesignation || '',
            productPartNumber: computer.ProductPartNumber || '',
            category: computer.Category || '',
            productValidityGroup: computer.ProductValidityGroup || '',
            customer: computer.Customer || '',
            customerType: computer.CustomerType || '',
            hoursAtContractStart: computer.HoursAtContractStart || 0,
            contractExpiry: computer.ContractExpiry || null,
            desiredContractLength: computer.DesiredContractLength || 12,
            contractSignDate: computer.ContractSignDate || null,
            contractNumber: computer.ContractNumber || '',
            engineHp: computer.EngineHp || 0,
            tracHp: computer.TracHp || 0
        };
        
        // Build quotation packages
        const quotationPackages = buildQuotationPackages(
            transformedComputer,
            servicePackages,
            baseServices,
            supportServices,
            serviceContracts[0] || null,
            quotationLines
        );
        
        return {
            computer: transformedComputer,
            quotationPackages,
            quotationHeader: quotationHeader ? serviceContractDB.serializeDocument(quotationHeader) : null,
            success: true
        };
        
    } catch (error) {
        console.error('Error loading service quotation data:', error);
        return {
            computer: null,
            quotationPackages: [],
            quotationHeader: null,
            success: false,
            error: error.message || 'Failed to load service quotation data'
        };
    }
}

/**
 * Build the quotation packages from various data sources
 */
function buildQuotationPackages(computer, servicePackages, baseServices, supportServices, existingContract, quotationLines) {
    // Initialize the quotation package structure based on the displayed image
    const quotationStructure = {
        basicInfo: {
            desiredContractLength: computer.desiredContractLength || 12,
            desiredContractStartDate: existingContract?.startDate || new Date(),
            contractNumber: existingContract?.contractNumber || computer.contractNumber || '',
            serialNumber: computer.serialNumber || '',
            productDesignation: computer.productDesignation || '',
            productValidityGroup: computer.productValidityGroup || '',
            deliveryDate: existingContract?.deliveryDate || null,
            engineHp: computer.engineHp || 0,
            tracHp: computer.tracHp || 0,
            contractSignLevel: existingContract?.contractLevel || 0,
            firstRunDate: existingContract?.firstRunDate || null,
            engineRunHours: computer.hoursAtContractStart || 0,
            primarySpare: existingContract?.primarySpare || false,
            labourRate: existingContract?.labourRate || 0,
            totalVisible: true
        },
        baseContract: mapBaseServices(baseServices, quotationLines, 'BaseContract'),
        repairPackages: mapRepairPackages(servicePackages, quotationLines, 'DealerAddOns'),
        supportServices: mapSupportServices(supportServices, quotationLines, 'SupportServices'),
        replacementServices: mapReplacementServices(servicePackages, quotationLines, 'ReplacementServices')
    };
    
    return quotationStructure;
}

/**
 * Map base services to the format shown in the image
 */
function mapBaseServices(baseServices, quotationLines, lineType) {
    const baseMappings = [
        { id: 1, name: 'Registration', packLevel: 0, includeInOffer: true, required: true, oem: true },
        { id: 2, name: 'Parts Supply on Request', packLevel: 0, includeInOffer: true, required: true, oem: true },
        { id: 3, name: 'Software Updates', packLevel: 0, includeInOffer: true, required: false, oem: false }
    ];
    
    return baseMappings.map(mapping => {
        const matchingService = baseServices.find(s => {
            const serviceLabel = s["Service activity Label"];
            return serviceLabel && typeof serviceLabel === 'string' && 
                   serviceLabel.toLowerCase().includes(mapping.name.toLowerCase());
        });
        
        const savedLine = quotationLines?.find(line => 
            line.lineType === lineType && 
            line.lineNumber === mapping.id
        );
        
        return {
            ...mapping,
            cost: savedLine?.cost || matchingService?.Cost || 0,
            customerSpecific: savedLine?.customerSpecific || matchingService?.CustomerSpecific || false,
            oemImporter: savedLine?.oemImporter || matchingService?.OEMImporter || mapping.oem,
            fleetOwner: savedLine?.fleetOwner || matchingService?.FleetOwner || false,
            includeInOffer: savedLine?.includeInOffer === undefined ? mapping.includeInOffer : savedLine.includeInOffer,
            required: savedLine?.required === undefined ? mapping.required : savedLine.required,
            serviceCode: matchingService?.ServiceCode || '',
            serviceActivity: matchingService?.["Service activity Label"] || mapping.name
        };
    });
}

/**
 * Map repair packages to the format shown in the image
 */
function mapRepairPackages(servicePackages, quotationLines, lineType) {
    const repairMappings = [
        { id: 1, name: 'Dealer Add-Ons', packLevel: 1, activity: 'Delivery Inspection' },
        { id: 2, name: 'Dealer Add-Ons', packLevel: 2, activity: 'Repair Parts Only' },
        { id: 3, name: 'Dealer Add-Ons', packLevel: 3, activity: 'Preventative Repairs Parts Only' },
        { id: 4, name: 'Dealer Add-Ons', packLevel: 4, activity: 'Preventative Inspections' },
        { id: 5, name: 'Dealer Add-Ons', packLevel: 5, activity: 'Value Adjustments' },
        { id: 6, name: 'Dealer Add-Ons', packLevel: 6, activity: 'PARTS KIT' },
        { id: 7, name: 'Dealer Add-Ons', packLevel: 7, activity: 'Parts Supply Surcharge' },
        { id: 8, name: 'Dealer Add-Ons', packLevel: 8, activity: 'Contract Services' }
    ];
    
    return repairMappings.map(mapping => {
        const matchingPackage = servicePackages.find(p => {
            const packageName = p.packageName;
            return p.packageLevel === mapping.packLevel &&
                   packageName && typeof packageName === 'string' && 
                   packageName.includes(mapping.name);
        });
        
        const savedLine = quotationLines?.find(line => 
            line.lineType === lineType && 
            line.lineNumber === mapping.id
        );
        
        return {
            ...mapping,
            cost: savedLine?.cost || matchingPackage?.Cost || 0,
            customerSpecific: savedLine?.customerSpecific || matchingPackage?.CustomerSpecific || false,
            oemImporter: savedLine?.oemImporter || matchingPackage?.OEMImporter || false,
            fleetOwner: savedLine?.fleetOwner || matchingPackage?.FleetOwner || false,
            includeInOffer: savedLine?.includeInOffer === undefined ? false : savedLine.includeInOffer,
            required: savedLine?.required === undefined ? false : savedLine.required,
            serviceCode: matchingPackage?.ServiceCode || '',
            serviceActivity: matchingPackage?.Activity || mapping.activity
        };
    });
}

/**
 * Map support services to the format shown in the image
 */
function mapSupportServices(supportServices, quotationLines, lineType) {
    const supportMappings = [
        { id: 1, name: 'Support Services', packLevel: 1, activity: 'Extended Warranty' },
        { id: 2, name: 'Support Services', packLevel: 2, activity: 'Tech Support' },
        { id: 3, name: 'Support Services', packLevel: 3, activity: 'Operator Training' },
        { id: 4, name: 'Support Services', packLevel: 4, activity: 'Field Support' }
    ];
    
    return supportMappings.map(mapping => {
        const matchingService = supportServices.find(s => {
            const serviceName = s.serviceName;
            return s.serviceLevel === mapping.packLevel &&
                   serviceName && typeof serviceName === 'string' && 
                   serviceName.includes(mapping.name);
        });
        
        const savedLine = quotationLines?.find(line => 
            line.lineType === lineType && 
            line.lineNumber === mapping.id
        );
        
        return {
            ...mapping,
            cost: savedLine?.cost || matchingService?.Cost || 0,
            customerSpecific: savedLine?.customerSpecific || matchingService?.CustomerSpecific || false,
            oemImporter: savedLine?.oemImporter || matchingService?.OEMImporter || false,
            fleetOwner: savedLine?.fleetOwner || matchingService?.FleetOwner || false,
            includeInOffer: savedLine?.includeInOffer === undefined ? false : savedLine.includeInOffer,
            required: savedLine?.required === undefined ? false : savedLine.required,
            serviceCode: matchingService?.ServiceCode || '',
            serviceActivity: matchingService?.ServiceDescription || mapping.activity
        };
    });
}

/**
 * Map replacement services to the format shown in the image
 */
function mapReplacementServices(servicePackages, quotationLines, lineType) {
    const replacementMappings = [
        { id: 1, name: 'Replacement Services', packLevel: 1, activity: 'Full Machine Replacement' },
        { id: 2, name: 'Replacement Services', packLevel: 2, activity: 'Component Replacement' },
        { id: 3, name: 'Replacement Services', packLevel: 3, activity: 'Loaner Equipment' },
        { id: 4, name: 'Replacement Services', packLevel: 4, activity: 'Wear Parts Replacement' }
    ];
    
    return replacementMappings.map(mapping => {
        const matchingPackage = servicePackages.find(p => {
            const packageType = p.packageType;
            return packageType && typeof packageType === 'string' && 
                   packageType.includes('Replacement') && 
                   p.packageLevel === mapping.packLevel;
        });
        
        const savedLine = quotationLines?.find(line => 
            line.lineType === lineType && 
            line.lineNumber === mapping.id
        );
        
        return {
            ...mapping,
            cost: savedLine?.cost || matchingPackage?.Cost || 0,
            customerSpecific: savedLine?.customerSpecific || matchingPackage?.CustomerSpecific || false,
            oemImporter: savedLine?.oemImporter || matchingPackage?.OEMImporter || false,
            fleetOwner: savedLine?.fleetOwner || matchingPackage?.FleetOwner || false,
            includeInOffer: savedLine?.includeInOffer === undefined ? false : savedLine.includeInOffer,
            required: savedLine?.required === undefined ? false : savedLine.required,
            serviceCode: matchingPackage?.ServiceCode || '',
            serviceActivity: matchingPackage?.Activity || mapping.activity
        };
    });
}

/** @type {import('./$types').Actions} */
export const actions = {
    updateQuotation: async ({ request }) => {
        try {
            const formData = await request.formData();
            const quotationData = JSON.parse(formData.get('quotationData') || '{}');
            const computerId = quotationData.computerId;
            
            if (!computerId) {
                throw new Error('Computer ID is required');
            }
            
            const db = serviceContractDB.db;
            
            // First handle the quotation header
            let quotationHeaderId = quotationData.quotationId || null;
            
            // Create or update the quotation header
            if (!quotationHeaderId) {
                // Create new quotation header
                const newHeader = {
                    computerId: computerId,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    status: 'Draft',
                    validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
                    amount: calculateTotalAmount(quotationData),
                    contractLength: quotationData.basicInfo?.desiredContractLength || 12,
                    contractStartDate: quotationData.basicInfo?.desiredContractStartDate || new Date(),
                    notes: quotationData.notes || ''
                };
                
                const result = await db.collection(COLLECTIONS.QUOTATION_HEADERS).insertOne(newHeader);
                quotationHeaderId = result.insertedId.toString();
            } else {
                // Update existing quotation header
                await db.collection(COLLECTIONS.QUOTATION_HEADERS).updateOne(
                    { _id: new ObjectId(quotationHeaderId) },
                    {
                        $set: {
                            updatedAt: new Date(),
                            amount: calculateTotalAmount(quotationData),
                            contractLength: quotationData.basicInfo?.desiredContractLength || 12,
                            contractStartDate: quotationData.basicInfo?.desiredContractStartDate || new Date(),
                            notes: quotationData.notes || ''
                        }
                    }
                );
            }
            
            // Delete existing quotation lines
            await db.collection(COLLECTIONS.QUOTATION_LINES).deleteMany({
                quotationId: quotationHeaderId
            });
            
            // Prepare all quotation lines
            const quotationLines = [];
            
            // Process base contract lines
            if (quotationData.baseContract) {
                quotationData.baseContract.forEach((line, index) => {
                    if (line.includeInOffer) {
                        quotationLines.push({
                            quotationId: quotationHeaderId,
                            lineType: 'BaseContract',
                            lineNumber: line.id,
                            serviceCode: line.serviceCode || '',
                            serviceActivity: line.serviceActivity || line.name,
                            cost: line.cost || 0,
                            customerSpecific: line.customerSpecific || false,
                            oemImporter: line.oemImporter || false,
                            fleetOwner: line.fleetOwner || false,
                            includeInOffer: true,
                            required: line.required || false,
                            notes: line.notes || '',
                            createdAt: new Date(),
                            updatedAt: new Date()
                        });
                    }
                });
            }
            
            // Process repair packages
            if (quotationData.repairPackages) {
                quotationData.repairPackages.forEach((line, index) => {
                    if (line.includeInOffer) {
                        quotationLines.push({
                            quotationId: quotationHeaderId,
                            lineType: 'DealerAddOns',
                            lineNumber: line.id,
                            serviceCode: line.serviceCode || '',
                            serviceActivity: line.serviceActivity || line.activity,
                            cost: line.cost || 0,
                            customerSpecific: line.customerSpecific || false,
                            oemImporter: line.oemImporter || false,
                            fleetOwner: line.fleetOwner || false,
                            includeInOffer: true,
                            required: line.required || false,
                            notes: line.notes || '',
                            createdAt: new Date(),
                            updatedAt: new Date()
                        });
                    }
                });
            }
            
            // Process support services
            if (quotationData.supportServices) {
                quotationData.supportServices.forEach((line, index) => {
                    if (line.includeInOffer) {
                        quotationLines.push({
                            quotationId: quotationHeaderId,
                            lineType: 'SupportServices',
                            lineNumber: line.id,
                            serviceCode: line.serviceCode || '',
                            serviceActivity: line.serviceActivity || line.activity,
                            cost: line.cost || 0,
                            customerSpecific: line.customerSpecific || false,
                            oemImporter: line.oemImporter || false,
                            fleetOwner: line.fleetOwner || false,
                            includeInOffer: true,
                            required: line.required || false,
                            notes: line.notes || '',
                            createdAt: new Date(),
                            updatedAt: new Date()
                        });
                    }
                });
            }
            
            // Process replacement services
            if (quotationData.replacementServices) {
                quotationData.replacementServices.forEach((line, index) => {
                    if (line.includeInOffer) {
                        quotationLines.push({
                            quotationId: quotationHeaderId,
                            lineType: 'ReplacementServices',
                            lineNumber: line.id,
                            serviceCode: line.serviceCode || '',
                            serviceActivity: line.serviceActivity || line.activity,
                            cost: line.cost || 0,
                            customerSpecific: line.customerSpecific || false,
                            oemImporter: line.oemImporter || false,
                            fleetOwner: line.fleetOwner || false,
                            includeInOffer: true,
                            required: line.required || false,
                            notes: line.notes || '',
                            createdAt: new Date(),
                            updatedAt: new Date()
                        });
                    }
                });
            }
            
            // Insert all quotation lines
            if (quotationLines.length > 0) {
                await db.collection(COLLECTIONS.QUOTATION_LINES).insertMany(quotationLines);
            }
            
            return {
                success: true,
                quotationId: quotationHeaderId,
                message: 'Quotation saved successfully'
            };
            
        } catch (error) {
            console.error('Error updating quotation:', error);
            return {
                success: false,
                error: error.message || 'Failed to update quotation'
            };
        }
    }
};

/**
 * Calculate the total amount of the quotation
 */
function calculateTotalAmount(quotationData) {
    let totalAmount = 0;
    
    // Add base contract costs
    if (quotationData.baseContract) {
        quotationData.baseContract.forEach(line => {
            if (line.includeInOffer) {
                totalAmount += parseFloat(line.cost || 0);
            }
        });
    }
    
    // Add repair packages costs
    if (quotationData.repairPackages) {
        quotationData.repairPackages.forEach(line => {
            if (line.includeInOffer) {
                totalAmount += parseFloat(line.cost || 0);
            }
        });
    }
    
    // Add support services costs
    if (quotationData.supportServices) {
        quotationData.supportServices.forEach(line => {
            if (line.includeInOffer) {
                totalAmount += parseFloat(line.cost || 0);
            }
        });
    }
    
    // Add replacement services costs
    if (quotationData.replacementServices) {
        quotationData.replacementServices.forEach(line => {
            if (line.includeInOffer) {
                totalAmount += parseFloat(line.cost || 0);
            }
        });
    }
    
    return totalAmount;
}
