// Script to update ProductDesignationShort for specific ProductDesignation values
// This script sets ProductDesignationShort to "D13B-N MH" for documents with specific ProductDesignation values

import { MongoClient } from 'mongodb';

// Connection URL and Database name
const url = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';
const collectionName = 'ProductValidityGroup';

async function updateSpecificDesignations() {
  let client;

  try {
    // Connect to MongoDB
    client = new MongoClient(url);
    await client.connect();
    console.log('Connected to MongoDB server');

    // Get database and collection
    const db = client.db(dbName);
    const collection = db.collection(collectionName);

    // Find documents with the specific ProductDesignation values before update
    const matchingDocsBefore = await collection.find({
      ProductDesignation: { $in: ["D13B-N MH R1", "D13B-N MH R2"] }
    }).toArray();
    
    console.log(`Found ${matchingDocsBefore.length} documents with the specified ProductDesignation values before update`);
    
    if (matchingDocsBefore.length > 0) {
      console.log('Sample documents before update:');
      matchingDocsBefore.slice(0, 5).forEach(doc => {
        console.log(`ID: ${doc._id}, ProductDesignation: ${doc.ProductDesignation}, ProductDesignationShort: ${doc.ProductDesignationShort}`);
      });
    }

    // Update documents with the specific ProductDesignation values
    const updateResult = await collection.updateMany(
      { ProductDesignation: { $in: ["D13B-N MH R1", "D13B-N MH R2"] } },
      { $set: { ProductDesignationShort: "D13B-N MH" } }
    );

    console.log(`Updated ${updateResult.modifiedCount} documents with ProductDesignationShort set to "D13B-N MH"`);

    // Verify update by checking the updated documents
    const matchingDocsAfter = await collection.find({
      ProductDesignation: { $in: ["D13B-N MH R1", "D13B-N MH R2"] }
    }).toArray();
    
    if (matchingDocsAfter.length > 0) {
      console.log('Sample documents after update:');
      matchingDocsAfter.slice(0, 5).forEach(doc => {
        console.log(`ID: ${doc._id}, ProductDesignation: ${doc.ProductDesignation}, ProductDesignationShort: ${doc.ProductDesignationShort}`);
      });
    }

  } catch (err) {
    console.error('Error updating documents:', err);
  } finally {
    // Close the connection
    if (client) {
      await client.close();
      console.log('MongoDB connection closed');
    }
  }
}

// Run the update function
updateSpecificDesignations().catch(console.error);
