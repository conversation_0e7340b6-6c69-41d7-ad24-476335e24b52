// Script to convert CalculatedHoursPerMonth field to integer in ActualComputerHours collection

import { MongoClient, ObjectId } from 'mongodb';

async function fixCalculatedHoursField() {
  // Connection URI
  const uri = 'mongodb://localhost:27017';
  const client = new MongoClient(uri);

  try {
    // Connect to MongoDB
    await client.connect();
    console.log('Connected to MongoDB');

    // Access the ServiceContracts database and ActualComputerHours collection
    const db = client.db('ServiceContracts');
    const collection = db.collection('ActualComputerHours');

    // Find all documents
    const docs = await collection.find({}).toArray();
    console.log(`Found ${docs.length} documents in ActualComputerHours collection`);

    // Group documents by computerId for correct calculation
    const computerGroups = {};
    
    // Group documents by computerId
    docs.forEach(doc => {
      const computerIdStr = doc.computerId.toString();
      if (!computerGroups[computerIdStr]) {
        computerGroups[computerIdStr] = [];
      }
      computerGroups[computerIdStr].push(doc);
    });

    // Process each computer group
    for (const [computerIdStr, computerDocs] of Object.entries(computerGroups)) {
      console.log(`Processing ${computerDocs.length} documents for computer ${computerIdStr}`);
      
      // Sort documents by year and month
      computerDocs.sort((a, b) => {
        if (a.year !== b.year) return a.year - b.year;
        return a.month - b.month;
      });
      
      // Calculate running total
      let runningTotal = 0;
      for (const doc of computerDocs) {
        // Ensure hours is an integer
        const hours = parseInt(doc.hours || 0);
        runningTotal += hours;
        
        // Update document with integer values
        await collection.updateOne(
          { _id: doc._id },
          { $set: { 
              hours: hours,
              CalculatedHoursPerMonth: runningTotal 
            }
          }
        );
        console.log(`Updated document ${doc._id}: set hours=${hours}, CalculatedHoursPerMonth=${runningTotal}`);
      }
    }

    // Verify some updated documents
    const updatedSamples = await collection.find({}).limit(5).toArray();
    console.log('\nSample documents after update:');
    updatedSamples.forEach(doc => {
      console.log(
        `ID: ${doc._id}, ` +
        `Hours: ${doc.hours} (${typeof doc.hours}), ` +
        `CalculatedHoursPerMonth: ${doc.CalculatedHoursPerMonth} (${typeof doc.CalculatedHoursPerMonth})`
      );
    });

  } catch (error) {
    console.error('Error updating documents:', error);
  } finally {
    // Close the connection
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the function
fixCalculatedHoursField().catch(console.error);
