import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function exploreData() {
    try {
        await client.connect();
        const db = client.db('ServiceContracts');

        // First show some sample documents and their normalized groups
        console.log('Sample BaseServices documents with normalized groups:');
        const baseServices = await db.collection('BaseServices').aggregate([
            {
                $addFields: {
                    normalizedGroup: {
                        $replaceAll: {
                            input: {
                                $replaceAll: {
                                    input: '$ProductValidityGroup',
                                    find: '1-',
                                    replacement: ''
                                }
                            },
                            find: '-',
                            replacement: ''
                        }
                    }
                }
            },
            { $limit: 2 }
        ]).toArray();
        console.log(JSON.stringify(baseServices, null, 2));

        console.log('\nSample PartNumbersServiceCodeAction documents with normalized groups:');
        const serviceActions = await db.collection('PartNumbersServiceCodeAction').aggregate([
            {
                $addFields: {
                    normalizedGroup: {
                        $replaceAll: {
                            input: {
                                $replaceAll: {
                                    input: '$ProductValidityGroup',
                                    find: '1-',
                                    replacement: ''
                                }
                            },
                            find: '-',
                            replacement: ''
                        }
                    }
                }
            },
            { $limit: 2 }
        ]).toArray();
        console.log(JSON.stringify(serviceActions, null, 2));

        // Perform the inner join on normalized ProductValidityGroup
        const result = await db.collection('BaseServices').aggregate([
            {
                $addFields: {
                    normalizedGroup: {
                        $replaceAll: {
                            input: {
                                $replaceAll: {
                                    input: '$ProductValidityGroup',
                                    find: '1-',
                                    replacement: ''
                                }
                            },
                            find: '-',
                            replacement: ''
                        }
                    }
                }
            },
            {
                $lookup: {
                    from: 'PartNumbersServiceCodeAction',
                    let: { pvg: '$normalizedGroup' },
                    pipeline: [
                        {
                            $addFields: {
                                normalizedGroup: {
                                    $replaceAll: {
                                        input: {
                                            $replaceAll: {
                                                input: '$ProductValidityGroup',
                                                find: '1-',
                                                replacement: ''
                                            }
                                        },
                                        find: '-',
                                        replacement: ''
                                    }
                                }
                            }
                        },
                        {
                            $match: {
                                $expr: {
                                    $eq: ['$normalizedGroup', '$$pvg']
                                }
                            }
                        }
                    ],
                    as: 'serviceActions'
                }
            },
            {
                $match: {
                    'serviceActions': { $ne: [] }
                }
            },
            {
                $project: {
                    _id: 1,
                    ServiceCode: 1,
                    'Service activity Label': 1,
                    'Activity purpose': 1,
                    ProductValidityGroup: 1,
                    normalizedGroup: 1,
                    'Internal No of Hours': 1,
                    'Internal No of Months': 1,
                    serviceActions: {
                        $map: {
                            input: '$serviceActions',
                            as: 'action',
                            in: {
                                PartNumber: '$$action.PartNumber',
                                ActionType: '$$action.ActionType',
                                ProductValidityGroup: '$$action.ProductValidityGroup',
                                normalizedGroup: '$$action.normalizedGroup',
                                ServiceCode: '$$action.ServiceCode',
                                Quantity: '$$action.Quantity',
                                'Unit of Measure': '$$action.Unit of Measure'
                            }
                        }
                    }
                }
            },
            { $limit: 100 }
        ]).toArray();

        console.log('\nResults from inner join:');
        console.log(JSON.stringify(result, null, 2));

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await client.close();
    }
}

exploreData();
