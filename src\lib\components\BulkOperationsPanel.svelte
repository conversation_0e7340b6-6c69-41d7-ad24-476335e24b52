<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import BaseGrid from './grids/BaseGrid.svelte';
  
  // Props
  export let months: string[] = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  export let years: number[] = [];
  export let isLoading: boolean = false;
  
  // Default to current year for start/end ranges
  const currentYear = new Date().getFullYear();
  let startYear = currentYear;
  let startMonth = 1;
  let endYear = currentYear;
  let endMonth = 12;
  let hours = 0;
  let activity = '';
  let hasFixed = false;
  
  // Event dispatcher
  const dispatch = createEventDispatcher<{
    apply: {
      startYear: number;
      startMonth: number;
      endYear: number;
      endMonth: number;
      hours: number;
      activity: string;
      hasFixed: boolean;
    };
  }>();
  
  // Validate input
  $: isValid = hours >= 0 && startYear && endYear && 
               startMonth >= 1 && startMonth <= 12 && 
               endMonth >= 1 && endMonth <= 12 &&
               !(startYear > endYear || (startYear === endYear && startMonth > endMonth));
  
  // Apply bulk operations
  function handleApply() {
    if (!isValid) return;
    
    dispatch('apply', {
      startYear,
      startMonth,
      endYear,
      endMonth,
      hours,
      activity,
      hasFixed
    });
  }
</script>

<BaseGrid>
  <div class="bulk-operations-panel">
    <header class="panel-header">
      <h2>Bulk Operations</h2>
    </header>
    
    <div class="panel-content">
      <div class="form-grid">
        <div class="date-range">
          <div class="date-inputs">
            <div class="form-group">
              <label for="startYear">Start Year</label>
              <select id="startYear" bind:value={startYear} class="form-control">
                {#each years as year}
                  <option value={year}>{year}</option>
                {/each}
              </select>
            </div>
            
            <div class="form-group">
              <label for="startMonth">Start Month</label>
              <select id="startMonth" bind:value={startMonth} class="form-control">
                {#each months as month, i}
                  <option value={i + 1}>{month}</option>
                {/each}
              </select>
            </div>
          </div>
          
          <div class="range-separator">to</div>
          
          <div class="date-inputs">
            <div class="form-group">
              <label for="endYear">End Year</label>
              <select id="endYear" bind:value={endYear} class="form-control">
                {#each years as year}
                  <option value={year}>{year}</option>
                {/each}
              </select>
            </div>
            
            <div class="form-group">
              <label for="endMonth">End Month</label>
              <select id="endMonth" bind:value={endMonth} class="form-control">
                {#each months as month, i}
                  <option value={i + 1}>{month}</option>
                {/each}
              </select>
            </div>
          </div>
        </div>
        
        <div class="activity-info">
          <div class="form-group">
            <label for="hours">Hours</label>
            <input 
              type="number" 
              id="hours" 
              bind:value={hours} 
              min="0"
              step="0.1"
              class="form-control"
            />
          </div>
          
          <div class="form-group">
            <label for="activity">Activity (Optional)</label>
            <input 
              type="text" 
              id="activity" 
              bind:value={activity}
              placeholder="Activity description"
              class="form-control" 
            />
          </div>
          
          <div class="form-group checkbox-group">
            <label class="checkbox-label">
              <input type="checkbox" bind:checked={hasFixed} />
              <span>Fixed Schedule</span>
            </label>
          </div>
        </div>
        
        {#if !isValid}
          <div class="validation-error">
            Please ensure that your date range is valid (start date must be before or equal to end date)
          </div>
        {/if}
      </div>
    </div>
    
    <footer class="panel-footer">
      <button 
        type="button" 
        class="btn-primary" 
        on:click={handleApply}
        disabled={!isValid || isLoading}
      >
        {isLoading ? 'Applying...' : 'Apply to Range'}
      </button>
    </footer>
  </div>
</BaseGrid>

<style>
  .bulk-operations-panel {
    display: grid;
    grid-template-rows: auto 1fr auto;
    height: 100%;
    gap: 1rem;
  }
  
  .panel-header {
    padding: 1rem;
    border-bottom: 1px solid #334155;
  }
  
  .panel-header h2 {
    margin: 0;
    color: #60a5fa;
    font-size: 1.25rem;
  }
  
  .panel-content {
    padding: 1rem;
    overflow-y: auto;
  }
  
  .form-grid {
    display: grid;
    gap: 1.5rem;
  }
  
  .date-range {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 1rem;
    align-items: center;
  }
  
  .date-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
  
  .range-separator {
    color: #94a3b8;
    font-weight: 500;
    text-align: center;
  }
  
  .activity-info {
    display: grid;
    gap: 1rem;
  }
  
  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.4rem;
  }
  
  label {
    color: #e2e8f0;
    font-weight: 500;
    font-size: 0.875rem;
  }
  
  .form-control {
    background-color: #0f172a;
    border: 1px solid #334155;
    border-radius: 6px;
    color: #e2e8f0;
    padding: 0.6rem 0.8rem;
    font-size: 0.95rem;
    width: 100%;
  }
  
  .form-control:focus {
    border-color: #60a5fa;
    outline: none;
  }
  
  .checkbox-group {
    margin-top: 0.5rem;
  }
  
  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
  }
  
  .checkbox-label input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    accent-color: #3b82f6;
  }
  
  .checkbox-label span {
    color: #e2e8f0;
  }
  
  .validation-error {
    color: #ef4444;
    font-size: 0.875rem;
    padding: 0.5rem;
    border-left: 3px solid #ef4444;
    background: rgba(239, 68, 68, 0.1);
    border-radius: 4px;
  }
  
  .panel-footer {
    padding: 1rem;
    border-top: 1px solid #334155;
    display: flex;
    justify-content: flex-end;
  }
  
  .btn-primary {
    background-color: #3b82f6;
    color: #ffffff;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    font-size: 0.95rem;
    cursor: pointer;
    transition: background-color 0.2s;
    border: none;
  }
  
  .btn-primary:hover:not(:disabled) {
    background-color: #2563eb;
  }
  
  .btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
</style>
