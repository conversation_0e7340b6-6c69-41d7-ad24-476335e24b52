import { error, redirect } from '@sveltejs/kit';
import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

/**
 * @type {import('./$types').PageServerLoad}
 */
export async function load({ url }) {
  let client;
  
  try {
    // Get customerId from query params
    const customerId = url.searchParams.get('customerId');
    
    // If no customerId is provided, redirect to the list page
    if (!customerId) {
      return {
        customers: [],
        selectedCustomer: null
      };
    }
    
    // Validate customerId format
    if (!ObjectId.isValid(customerId)) {
      throw error(400, 'Invalid customer ID format');
    }
    
    // Connect to MongoDB
    client = new MongoClient(uri);
    await client.connect();
    const db = client.db(dbName);
    
    // Get all customers for the dropdown
    const customers = await db.collection('Customers')
      .find({})
      .sort({ companyName: 1 })
      .project({ 
        _id: 1, 
        companyName: 1, 
        name: 1, 
        type: 1, 
        city: 1, 
        country: 1 
      })
      .toArray();
    
    // Get the selected customer
    const selectedCustomer = await db.collection('Customers')
      .findOne({ _id: new ObjectId(customerId) });
    
    if (!selectedCustomer) {
      throw error(404, 'Customer not found');
    }
    
    // Transform data for client-side use
    const serializedCustomers = customers.map(customer => ({
      ...customer,
      _id: customer._id.toString()
    }));
    
    const serializedSelectedCustomer = {
      ...selectedCustomer,
      _id: selectedCustomer._id.toString()
    };
    
    return {
      customers: serializedCustomers,
      selectedCustomer: serializedSelectedCustomer
    };
  } catch (err) {
    console.error('Error loading customer data:', err);
    if (err.status) {
      throw err; // Re-throw SvelteKit errors
    }
    throw error(500, 'Failed to load customer data');
  } finally {
    if (client) {
      await client.close();
    }
  }
}

/**
 * @type {import('./$types').Actions}
 */
export const actions = {
  addComputer: async ({ request }) => {
    let client;
    
    try {
      const formData = await request.formData();
      
      // Get required fields
      const customerId = formData.get('customerId')?.toString();
      const serialNumber = formData.get('serialNumber')?.toString();
      const model = formData.get('model')?.toString();
      const productType = formData.get('productType')?.toString();
      
      // Validate required fields
      if (!customerId || !serialNumber || !model || !productType) {
        return { success: false, error: 'Missing required fields' };
      }
      
      // Validate ObjectId format
      if (!ObjectId.isValid(customerId)) {
        return { success: false, error: 'Invalid customer ID format' };
      }
      
      // Generate a name for the computer if not provided
      const name = formData.get('name')?.toString() || `${model} - ${serialNumber}`;
      
      // Parse numeric values
      const operatingHours = parseInt(formData.get('operatingHours')?.toString() || '0', 10);
      
      // Create computer data object
      const computerData = {
        customerId: new ObjectId(customerId),
        name,
        serialNumber,
        model,
        productType,
        productDesignation: formData.get('productDesignation')?.toString() || '',
        engineType: formData.get('engineType')?.toString() || '',
        operatingHours: isNaN(operatingHours) ? 0 : operatingHours,
        installationDate: formData.get('installationDate')?.toString() || null,
        manufactureDate: formData.get('manufactureDate')?.toString() || null,
        isActive: formData.get('isActive') === 'on',
        notes: formData.get('notes')?.toString() || '',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      client = new MongoClient(uri);
      await client.connect();
      const db = client.db(dbName);
      const computersCollection = db.collection('CustomerComputers');
      
      // Insert the computer
      const result = await computersCollection.insertOne(computerData);
      
      if (!result.acknowledged) {
        return { success: false, error: 'Failed to add computer' };
      }
      
      // Redirect to the new computer's detail page
      throw redirect(303, `/customer-computers/${result.insertedId.toString()}`);
    } catch (err) {
      if (err.status === 303) {
        throw err; // Re-throw redirect
      }
      
      console.error('Error adding computer:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Unknown error' 
      };
    } finally {
      if (client) {
        await client.close();
      }
    }
  }
};
