/* ======================================================
   COMPREHENSIVE GRID STYLES FROM SERVICE PLANNING PAGE
   ====================================================== */

/* ----------------
   BASE GRID STYLES
   ---------------- */
.grid-container {
  display: grid;
  grid-template-columns: var(--columns, repeat(12, 1fr));
  grid-template-rows: var(--rows, auto);
  gap: var(--gap, 1rem);
  padding: var(--padding, 1rem);
  min-height: var(--min-height, auto);
  max-width: var(--max-width, 100%);
  background-color: var(--background-color, transparent);
  width: 100%;
  box-sizing: border-box;
}

/* Base Grid Responsive Styles */
@media (max-width: 768px) {
  .grid-container {
    grid-template-columns: repeat(6, 1fr);
    gap: calc(var(--gap, 1rem) * 0.75);
  }
}

@media (max-width: 480px) {
  .grid-container {
    grid-template-columns: repeat(4, 1fr);
    gap: calc(var(--gap, 1rem) * 0.5);
  }
}

/* --------------------
   PLANNING GRID STYLES
   -------------------- */
.planning-grid {
  display: grid;
  grid-template-areas:
    "header header"
    "sidebar content"
    "footer footer";
  grid-template-columns: minmax(250px, 1fr) 3fr;
  grid-template-rows: auto 1fr auto;
  gap: 1rem;
  height: 100%;
  width: 100%;
  max-width: 1800px;
  margin: 0 auto;
  padding: 1rem;
}

.header {
  grid-area: header;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.sidebar {
  grid-area: sidebar;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.main-content {
  grid-area: content;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.footer {
  grid-area: footer;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-top: 1rem;
}

/* Planning Grid Responsive Styles */
@media (max-width: 768px) {
  .planning-grid {
    grid-template-areas:
      "header"
      "sidebar"
      "content"
      "footer";
    grid-template-columns: 1fr;
    grid-template-rows: auto auto 1fr auto;
  }
}

/* -------------------------
   SERVICE ACTIVITY GRID STYLES
   ------------------------- */
.service-matrix {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.year-cell {
  width: 60px;
  padding: 0.5rem;
  text-align: center;
  background: #f0f2f5;
  border: 1px solid #ddd;
}

.month-header {
  padding: 0.5rem;
  text-align: center;
  background: #e2e8f0;
  border: 1px solid #ddd;
  font-weight: 500;
}

.activity-cell {
  padding: 0.5rem;
  text-align: center;
  border: 1px solid #ddd;
  min-width: 40px;
  position: relative;
}

.activity-cell:hover {
  background: #f8fafc;
}

.empty-cell {
  background: #f8f9fa;
}

/* Editing Styles */
.editing-cell {
  background: #e5f6fd !important;
  box-shadow: inset 0 0 0 2px #38b2e0;
  position: relative;
}

.edit-controls {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 6px;
}

.btn-save, .btn-cancel {
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-save {
  background-color: #4caf50;
  color: white;
}

.btn-save:hover {
  background-color: #43a047;
}

.btn-cancel {
  background-color: #f44336;
  color: white;
}

.btn-cancel:hover {
  background-color: #e53935;
}

.icon {
  margin-right: 4px;
  font-size: 14px;
}

.edit-form {
  padding: 6px;
}

.edit-form input, 
.edit-form select {
  width: 100%;
  padding: 4px;
  margin-bottom: 4px;
  border: 1px solid #ccc;
  border-radius: 3px;
}

/* ----------------
   CONTENT GRID STYLES
   ---------------- */
.content-grid {
  display: grid;
  grid-template-columns: 1fr minmax(0, var(--max-width, 100%)) 1fr;
  gap: var(--padding, 1rem);
  width: 100%;
}

.content-area {
  grid-column: 2;
  width: 100%;
  padding: var(--padding, 1rem);
}

@media (max-width: 768px) {
  .content-grid {
    grid-template-columns: var(--padding, 1rem) 1fr var(--padding, 1rem);
  }
  
  .content-area {
    padding: calc(var(--padding, 1rem) / 2);
  }
}

/* ------------------
   MATRIX GRID STYLES
   ------------------ */
.matrix-grid {
  display: grid;
  grid-template-columns: auto repeat(12, 1fr);
  width: 100%;
  overflow-x: auto;
}

.matrix-header {
  background-color: #e2e8f0;
  padding: 0.5rem;
  text-align: center;
  font-weight: 500;
  border: 1px solid #ddd;
}

.matrix-year {
  background-color: #f0f2f5;
  padding: 0.5rem;
  text-align: center;
  font-weight: 500;
  border: 1px solid #ddd;
}

.matrix-cell {
  border: 1px solid #ddd;
  padding: 0.5rem;
  text-align: center;
  position: relative;
  min-width: 40px;
  cursor: pointer;
}

.matrix-cell.editing {
  background-color: #e5f6fd;
  padding: 0.25rem;
}

.matrix-cell.has-activity {
  background-color: #e6f7ff;
  font-weight: bold;
}

.matrix-cell.event-cell {
  color: white;
  font-weight: bold;
}

.matrix-cell.empty {
  background-color: #f8f9fa;
}

/* Cell input styling */
.cell-input {
  width: 100%;
  padding: 4px;
  margin-bottom: 4px;
  border: 1px solid #ccc;
  border-radius: 3px;
}

.cell-select {
  width: 100%;
  padding: 4px;
  margin-bottom: 4px;
  border: 1px solid #ccc;
  border-radius: 3px;
}

.cell-checkbox {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.cell-btn {
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 0 2px;
}

.cell-btn.save {
  background-color: #4caf50;
  color: white;
}

.cell-btn.cancel {
  background-color: #f44336;
  color: white;
}

/* -----------------
   RESPONSIVE STYLES
   ----------------- */
@media (max-width: 1024px) {
  .matrix-grid {
    grid-template-columns: auto repeat(6, 1fr);
    overflow-x: scroll;
  }
}

@media (max-width: 640px) {
  .matrix-grid {
    grid-template-columns: auto repeat(3, 1fr);
  }
  
  .matrix-cell {
    padding: 0.25rem;
    font-size: 0.75rem;
  }
}

/* ------------------
   ADDITIONAL STYLES
   ------------------ */
.workload-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  width: 100%;
}

.year-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.event-dates-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
  padding: 1rem;
  background-color: #f5f7fa;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.edit-panel {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  padding: 1rem;
  background-color: #f5f7fa;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  margin-bottom: 1.5rem;
}

.edit-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.faultcodes-section {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-top: 2rem;
}

.bulk-hours-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.5rem;
  align-items: end;
  margin-bottom: 1rem;
}

/* Ensure all grids use CSS Grid properly */
.json-view-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.json-section {
  display: grid;
  grid-template-rows: auto 1fr;
}
