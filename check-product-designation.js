import { MongoClient } from 'mongodb';

// From memory: all collections are in database "ServiceContracts"
const url = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function checkProductDesignation() {
    const client = await MongoClient.connect(url);
    try {
        const db = client.db(dbName);
        // From memory: use ProductValidityGroup collection in PascalCase
        const collection = db.collection('ProductValidityGroup');

        // Check specific product designation
        const items = await collection.find({
            ProductDesignation: 'D1-13B'
        }).sort({
            // From memory: Products with the same ProductGroupId and Id appear to be related
            ProductGroupId: 1,
            Id: 1,
            // From memory: ProductPartNumber appears to be unique for each product
            ProductPartNumber: 1
        }).toArray();

        console.log(`Found ${items.length} items with ProductDesignation "D1-13B":`);
        items.forEach(item => {
            console.log({
                _id: item._id.toString(),
                ProductName: item.ProductName,
                ProductPartNumber: item.ProductPartNumber,
                ProductDesignation: item.ProductDesignation,
                ProductGroupId: item.ProductGroupId,
                Id: item.Id,
                "Product Validity GRoup": item["Product Validity GRoup"]
            });
        });

    } catch (err) {
        console.error('Error:', err);
    } finally {
        await client.close();
    }
}

// Run the script
checkProductDesignation().catch(console.error);
