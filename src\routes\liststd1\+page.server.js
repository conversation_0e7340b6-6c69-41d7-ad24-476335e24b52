import { redirect, error } from '@sveltejs/kit';
import { getCollection, ObjectId } from '$lib/db/mongo';

/** @type {import('./$types').Actions} */
export const actions = {
  createItem: async ({ request, url }) => {
    const collectionName = url.searchParams.get('collection') || 'Customers';
    const data = await request.formData();
    const formData = Object.fromEntries(data);
    
    try {
      // Get MongoDB collection
      const collection = await getCollection(collectionName);
      
      // Process form data - handle arrays, booleans, etc.
      /** @type {Record<string, any>} */
      const itemData = { ...formData };
      
      // Prepare versatile as an array if present
      if (typeof itemData.versatile === 'string') {
        itemData.versatile = itemData.versatile ? itemData.versatile.split(',') : [];
      }
      
      // Remove _id if present in new item creation
      delete itemData._id;
      
      // Insert document
      const result = await collection.insertOne({
        ...itemData,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      if (!result.acknowledged) {
        throw error(500, 'Failed to create item');
      }
      
      return { success: true, id: result.insertedId };
    } catch (err) {
      console.error('Error creating item:', err);
      throw error(500, err instanceof Error ? err.message : 'Unknown error');
    }
  },
  
  updateItem: async ({ request, url }) => {
    const collectionName = url.searchParams.get('collection') || 'Customers';
    const data = await request.formData();
    const formData = Object.fromEntries(data);
    
    try {
      const id = formData._id;
      if (!id || typeof id !== 'string') {
        throw error(400, 'Item ID is required for update');
      }
      
      // Get MongoDB collection
      const collection = await getCollection(collectionName);
      
      // Process form data
      /** @type {Record<string, any>} */
      const itemData = { ...formData };
      delete itemData._id; // Remove _id from update data
      
      // Prepare versatile as an array if present
      if (typeof itemData.versatile === 'string') {
        itemData.versatile = itemData.versatile ? itemData.versatile.split(',') : [];
      }
      
      // Update document
      const result = await collection.updateOne(
        { _id: new ObjectId(id) },
        { 
          $set: {
            ...itemData,
            updatedAt: new Date()
          } 
        }
      );
      
      if (!result.matchedCount) {
        throw error(404, 'Item not found');
      }
      
      return { success: true };
    } catch (err) {
      console.error('Error updating item:', err);
      throw error(500, err instanceof Error ? err.message : 'Unknown error');
    }
  },
  
  deleteItem: async ({ request, url }) => {
    const collectionName = url.searchParams.get('collection') || 'Customers';
    const data = await request.formData();
    const id = data.get('_id');
    
    if (!id || typeof id !== 'string') {
      throw error(400, 'Item ID is required for deletion');
    }
    
    try {
      // Get MongoDB collection
      const collection = await getCollection(collectionName);
      
      // Delete document
      const result = await collection.deleteOne({ _id: new ObjectId(id) });
      
      if (!result.deletedCount) {
        throw error(404, 'Item not found');
      }
      
      return { success: true };
    } catch (err) {
      console.error('Error deleting item:', err);
      throw error(500, err instanceof Error ? err.message : 'Unknown error');
    }
  }
};
