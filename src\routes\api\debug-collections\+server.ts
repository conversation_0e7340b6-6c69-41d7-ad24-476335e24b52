import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';
import type { RequestEvent } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/** @type {import('./$types').RequestHandler} */
export async function GET({ url }: RequestEvent) {
    try {
        await client.connect();
        const db = client.db('ServiceContracts');

        const computerId = url.searchParams.get('computerId');
        if (!computerId) {
            throw new Error('Computer ID is required');
        }

        // 1. Get Computer Details
        const computersCollection = db.collection('CustomerComputers');
        const computer = await computersCollection.findOne({ _id: new ObjectId(computerId) });
        if (!computer) {
            throw new Error('Computer not found');
        }

        // 2. Get Product Validity Group
        const pvgCollection = db.collection('ProductValidityGroupPartNumber');
        const pvgDoc = await pvgCollection.findOne({
            ProductPartNumber: parseInt(computer.ProductPartNumber)
        });

        // 3. Get Service Elements
        const serviceCodeCollection = db.collection('ServiceCodeAndActionType');
        const serviceElements = await serviceCodeCollection
            .find({})
            .limit(10)
            .toArray();

        // Log raw data
        console.log('Computer:', JSON.stringify(computer, null, 2));
        console.log('PVG Doc:', JSON.stringify(pvgDoc, null, 2));
        console.log('Service Elements Sample:', JSON.stringify(serviceElements, null, 2));

        // Get collection stats
        const collections = {
            ServiceCodeAndActionType: await serviceCodeCollection.countDocuments(),
            ProductValidityGroupPartNumber: await pvgCollection.countDocuments(),
            CustomerComputers: await computersCollection.countDocuments()
        };

        // Get sample documents
        const samples = {
            computer: {
                _id: computer._id,
                ProductPartNumber: computer.ProductPartNumber,
                ProductDesignation: computer.ProductDesignation,
                "Product Validity Group": computer["Product Validity Group"]
            },
            pvg: pvgDoc ? {
                _id: pvgDoc._id,
                ProductPartNumber: pvgDoc.ProductPartNumber,
                "Product Validity Group": pvgDoc["Product Validity Group"]
            } : null,
            serviceElements: serviceElements.map(e => ({
                _id: e._id,
                ServiceCode: e.ServiceCode,
                "Product Validity Group": e["Product Validity Group"]
            }))
        };

        // Get field names from service elements
        const fieldNames = new Set();
        serviceElements.forEach(doc => {
            Object.keys(doc).forEach(key => fieldNames.add(key));
        });

        return json({
            success: true,
            collectionCounts: collections,
            samples: samples,
            serviceElementFields: Array.from(fieldNames),
            query: {
                partNumber: computer.ProductPartNumber,
                productValidityGroup: pvgDoc?.["Product Validity Group"]
            }
        });

    } catch (error: unknown) {
        console.error('Error:', error);
        return json({
            success: false,
            message: error instanceof Error ? error.message : 'An unknown error occurred'
        }, { status: 500 });
    } finally {
        await client.close();
    }
}
