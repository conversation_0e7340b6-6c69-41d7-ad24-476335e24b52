import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function updateCustomerComputersCategory() {
    try {
        await client.connect();
        console.log('Connected to MongoDB');

        const db = client.db('ServiceContracts');
        const collection = db.collection('CustomerComputers');

        // Update all documents to add Category field with value 'D11'
        const result = await collection.updateMany(
            {}, // Match all documents
            { $set: { Category: 'D11' } }
        );

        console.log(`Updated ${result.modifiedCount} documents`);
    } catch (error) {
        console.error('Error updating documents:', error);
    } finally {
        await client.close();
        console.log('Disconnected from MongoDB');
    }
}

updateCustomerComputersCategory();
