<script>
  import { enhance } from '$app/forms';
  import { slide } from 'svelte/transition';
  import { goto } from '$app/navigation';
  export let data;
  let { customer, customerComputers, customerTypes, labourTimes } = data;
  let showAddForm = false;
  let formError = '';
  let isSubmitting = false;
  let newComputer = {
    name: '',
    type: '',
    model: '',
    manufacturer: '',
    serialNumber: '',
    operatingSystem: '',
    purchaseDate: '',
    warrantyEndDate: '',
    notes: ''
  };

  async function handleDelete(computerId) {
    if (!confirm('Are you sure you want to delete this computer?')) {
      return;
    }

    try {
      const form = new FormData();
      form.append('computerId', computerId);

      const response = await fetch(`?/delete`, {
        method: 'POST',
        body: form
      });

      const result = await response.json();
      if (result.success) {
        // Remove the computer from the list and refresh to show current state
        window.location.reload();
      } else {
        throw new Error(result.error || 'Failed to delete computer');
      }
    } catch (error) {
      console.error('Error deleting computer:', error);
      alert(error.message || 'Failed to delete computer. Please try again.');
    }
  }

  async function handleAddComputer(event) {
    event.preventDefault();
    formError = '';
    isSubmitting = true;

    try {
      const form = event.target;
      const formData = new FormData(form);
      formData.append('customerId', customer._id);

      const response = await fetch('?/add', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();
      if (result.success) {
        // Add the new computer to the list
        customerComputers = [...customerComputers, result.computer];
        showAddForm = false;
        formError = '';
        // Reset form
        newComputer = {
          name: '',
          type: '',
          model: '',
          manufacturer: '',
          serialNumber: '',
          operatingSystem: '',
          purchaseDate: '',
          warrantyEndDate: '',
          notes: ''
        };
        // Refresh the page to show updated list
        window.location.reload();
      } else {
        formError = result.error || 'Failed to add computer';
      }
    } catch (error) {
      console.error('Error adding computer:', error);
      formError = error.message || 'Failed to add computer. Please try again.';
    } finally {
      isSubmitting = false;
    }
  }

  async function toggleMCP(computerId) {
    const form = new FormData();
    form.append('computerId', computerId);
    form.append('customerId', customer._id);

    const response = await fetch(`?/toggleMCP`, {
      method: 'POST',
      body: form
    });

    const result = await response.json();
    if (result.success) {
      // Update the computer's MCP status in the list
      customerComputers = customerComputers.map(c => {
        if (c._id === computerId) {
          return { ...c, mcpEnabled: !c.mcpEnabled };
        }
        return c;
      });
    } else {
      alert(result.error || 'Failed to toggle MCP');
    }
  }

  function formatDate(date) {
    if (!date) return '-';
    return new Date(date).toLocaleDateString();
  }
</script>

<div class="container">
  <!-- Left Column -->
  <div class="content-grid">
    <div class="left-column">
      <div class="section-card">
        <div class="header-content">
          <div class="header-title">
            <h1>Customer Computers</h1>
            <div class="customer-info">
              <span class="customer-name">{customer.companyName}</span>
              <span class="customer-type {customer.type.toLowerCase().replace(' ', '-')}">
                {customer.type || 'Retail'}
              </span>
            </div>
          </div>
          <div class="header-actions">
            <button class="btn btn-primary" on:click={() => showAddForm = !showAddForm} disabled={isSubmitting}>
              {showAddForm ? 'Cancel' : 'Add Computer'}
            </button>
            <a href="/customers/{customer._id}" class="btn btn-secondary">
              Back to Customer
            </a>
          </div>
        </div>

        {#if showAddForm}
          <div class="add-form-container" transition:slide>
            <h2>Add New Computer</h2>
            {#if formError}
              <div class="error-message" transition:slide>
                {formError}
              </div>
            {/if}
            <form on:submit={handleAddComputer} class="add-form">
              <div class="form-grid">
                <div class="form-group">
                  <label for="name">Computer Name *</label>
                  <input 
                    type="text" 
                    id="name" 
                    name="name" 
                    bind:value={newComputer.name}
                    required
                    class="form-control"
                    placeholder="Enter computer name"
                    disabled={isSubmitting}
                  />
                </div>

                <div class="form-group">
                  <label for="type">Type *</label>
                  <input 
                    type="text" 
                    id="type" 
                    name="type" 
                    bind:value={newComputer.type}
                    required
                    class="form-control"
                    placeholder="e.g. Desktop, Laptop, Server"
                    disabled={isSubmitting}
                  />
                </div>

                <div class="form-group">
                  <label for="model">Model</label>
                  <input 
                    type="text" 
                    id="model" 
                    name="model" 
                    bind:value={newComputer.model}
                    class="form-control"
                    placeholder="Enter model number"
                    disabled={isSubmitting}
                  />
                </div>

                <div class="form-group">
                  <label for="manufacturer">Manufacturer</label>
                  <input 
                    type="text" 
                    id="manufacturer" 
                    name="manufacturer" 
                    bind:value={newComputer.manufacturer}
                    class="form-control"
                    placeholder="Enter manufacturer name"
                    disabled={isSubmitting}
                  />
                </div>

                <div class="form-group">
                  <label for="serialNumber">Serial Number</label>
                  <input 
                    type="text" 
                    id="serialNumber" 
                    name="serialNumber" 
                    bind:value={newComputer.serialNumber}
                    class="form-control monospace"
                    placeholder="Enter serial number"
                    disabled={isSubmitting}
                  />
                </div>

                <div class="form-group">
                  <label for="operatingSystem">Operating System</label>
                  <input 
                    type="text" 
                    id="operatingSystem" 
                    name="operatingSystem" 
                    bind:value={newComputer.operatingSystem}
                    class="form-control"
                    placeholder="e.g. Windows 11, Ubuntu 22.04"
                    disabled={isSubmitting}
                  />
                </div>

                <div class="form-group">
                  <label for="purchaseDate">Purchase Date</label>
                  <input 
                    type="date" 
                    id="purchaseDate" 
                    name="purchaseDate" 
                    bind:value={newComputer.purchaseDate}
                    class="form-control"
                    disabled={isSubmitting}
                  />
                </div>

                <div class="form-group">
                  <label for="warrantyEndDate">Warranty End Date</label>
                  <input 
                    type="date" 
                    id="warrantyEndDate" 
                    name="warrantyEndDate" 
                    bind:value={newComputer.warrantyEndDate}
                    class="form-control"
                    disabled={isSubmitting}
                  />
                </div>
              </div>

              <div class="form-group full-width">
                <label for="notes">Notes</label>
                <textarea 
                  id="notes" 
                  name="notes" 
                  bind:value={newComputer.notes}
                  class="form-control"
                  rows="3"
                  placeholder="Enter any additional notes about this computer"
                  disabled={isSubmitting}
                ></textarea>
              </div>

              <div class="form-actions">
                <button type="submit" class="btn btn-primary" disabled={isSubmitting}>
                  {isSubmitting ? 'Adding...' : 'Add Computer'}
                </button>
                <button type="button" class="btn btn-secondary" on:click={() => showAddForm = false} disabled={isSubmitting}>
                  Cancel
                </button>
              </div>
            </form>
          </div>
        {/if}

        <div class="table-container">
          <table>
            <thead>
              <tr>
                <th>Computer</th>
                <th>Details</th>
                <th>Warranty</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {#each customerComputers as computer (computer._id)}
                <tr>
                  <td>
                    <div class="computer-info">
                      <span class="computer-name">{computer.name}</span>
                      <span class="computer-type">{computer.type}</span>
                    </div>
                  </td>
                  <td>
                    <div class="details-info">
                      <div class="model-info">
                        <span class="model">{computer.model}</span>
                        {#if computer.manufacturer}
                          <span class="manufacturer">by {computer.manufacturer}</span>
                        {/if}
                      </div>
                      {#if computer.serialNumber}
                        <span class="serial-number monospace">{computer.serialNumber}</span>
                      {/if}
                      {#if computer.operatingSystem}
                        <span class="os-info">{computer.operatingSystem}</span>
                      {/if}
                    </div>
                  </td>
                  <td>
                    <div class="warranty-info">
                      {#if computer.purchaseDate}
                        <div class="date-item">
                          <span class="date-label">Purchased:</span>
                          <span class="date-value">{formatDate(computer.purchaseDate)}</span>
                        </div>
                      {/if}
                      {#if computer.warrantyEndDate}
                        <div class="date-item">
                          <span class="date-label">Warranty until:</span>
                          <span class="date-value">{formatDate(computer.warrantyEndDate)}</span>
                        </div>
                      {/if}
                    </div>
                  </td>
                  <td class="actions">
                    <button class="btn btn-edit">
                      Edit
                    </button>
                    <a href="/plan?computerId={computer._id}" class="btn btn-primary">
                      Service Plan
                    </a>
                    <button 
                      class="btn btn-delete"
                      on:click={() => handleDelete(computer._id)}
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              {/each}
              {#if customerComputers.length === 0}
                <tr>
                  <td colspan="4" class="empty-state">
                    No computers found for this customer
                  </td>
                </tr>
              {/if}
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Right Column -->
    <div class="right-column">
      <!-- Labour Times Card -->
      <div class="section-card">
        <div class="header-content">
          <div class="header-title">
            <h2>Labour Times</h2>
          </div>
        </div>
        {#if labourTimes && labourTimes.length > 0}
          <div class="labour-times">
            {#each labourTimes as labourTime}
              <div class="labour-time-item">
                <div class="category">
                  <strong>Category:</strong> {labourTime.ComputerCategory}
                </div>
                <div class="time">
                  <strong>Standard Hours:</strong> {labourTime.StandardHours}
                </div>
                <div class="description">
                  <strong>Description:</strong> {labourTime.Description || 'N/A'}
                </div>
              </div>
            {/each}
          </div>
        {:else}
          <p class="no-data">No labour times found for the current computer categories.</p>
        {/if}
      </div>

      <!-- Customer Information Card -->
      <div class="section-card">
        <h2>Customer Information</h2>
        <div class="info-list">
          <div class="info-item">
            <span class="label">Contact Person</span>
            <span class="value">{customer.contactPerson || '-'}</span>
          </div>
          <div class="info-item">
            <span class="label">Email</span>
            <span class="value">{customer.email || '-'}</span>
          </div>
          <div class="info-item">
            <span class="label">Phone</span>
            <span class="value">{customer.phone || '-'}</span>
          </div>
          <div class="info-item">
            <span class="label">Location</span>
            <span class="value">{customer.city ? `${customer.city}, ${customer.country}` : (customer.country || '-')}</span>
          </div>
          {#if customer.notes}
            <div class="info-item full-width">
              <span class="label">Notes</span>
              <span class="value notes">{customer.notes}</span>
            </div>
          {/if}
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
  }

  .content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
  }

  .section-card {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
  }

  .header-title h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1a365d;
    margin: 0 0 0.5rem 0;
  }

  .customer-info {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .customer-name {
    font-size: 1.125rem;
    color: #4a5568;
  }

  .customer-type {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .customer-type.oem-importer {
    background: #dbeafe;
    color: #1e40af;
  }

  .customer-type.fleet-owner {
    background: #fae8ff;
    color: #86198f;
  }

  .customer-type.retail {
    background: #ecfdf5;
    color: #065f46;
  }

  .header-actions {
    display: flex;
    gap: 1rem;
  }

  .btn {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }

  .btn-primary {
    background: #1a365d;
    color: white;
    border: none;
  }

  .btn-primary:hover {
    background: #2a4365;
  }

  .btn-secondary {
    background: #f3f4f6;
    color: #4b5563;
    border: 1px solid #d1d5db;
    text-decoration: none;
  }

  .btn-secondary:hover {
    background: #e5e7eb;
  }

  .add-form-container {
    background: #f8fafc;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid #e2e8f0;
  }

  .add-form-container h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1a365d;
    margin: 0 0 1.5rem 0;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-group.full-width {
    grid-column: 1 / -1;
  }

  label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #4b5563;
  }

  .form-control {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    width: 100%;
  }

  .form-control:focus {
    outline: none;
    border-color: #60a5fa;
    box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
  }

  textarea.form-control {
    resize: vertical;
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
  }

  .table-container {
    overflow-x: auto;
  }

  table {
    width: 100%;
    border-collapse: collapse;
  }

  th, td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
  }

  th {
    font-weight: 600;
    color: #4b5563;
    background: #f8fafc;
  }

  .computer-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .computer-name {
    font-weight: 500;
    color: #1f2937;
  }

  .computer-type {
    font-size: 0.75rem;
    color: #6b7280;
  }

  .details-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .model-info {
    display: flex;
    gap: 0.5rem;
    align-items: baseline;
  }

  .model {
    font-weight: 500;
    color: #1f2937;
  }

  .manufacturer {
    font-size: 0.75rem;
    color: #6b7280;
  }

  .serial-number {
    font-size: 0.75rem;
    color: #4b5563;
    font-family: monospace;
  }

  .os-info {
    font-size: 0.75rem;
    color: #6b7280;
  }

  .warranty-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .date-item {
    display: flex;
    gap: 0.5rem;
    font-size: 0.75rem;
  }

  .date-label {
    color: #6b7280;
  }

  .date-value {
    color: #1f2937;
  }

  .actions {
    display: flex;
    gap: 0.5rem;
  }

  .btn-edit {
    background: #dbeafe;
    color: #1e40af;
    border: none;
  }

  .btn-edit:hover {
    background: #bfdbfe;
  }

  .btn-delete {
    background: #fee2e2;
    color: #991b1b;
    border: none;
  }

  .btn-delete:hover {
    background: #fecaca;
  }

  .empty-state {
    text-align: center;
    color: #6b7280;
    font-style: italic;
  }

  .info-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .info-item.full-width {
    grid-column: 1 / -1;
  }

  .label {
    font-size: 0.75rem;
    font-weight: 500;
    color: #6b7280;
  }

  .value {
    font-size: 0.875rem;
    color: #1f2937;
  }

  .value.notes {
    white-space: pre-wrap;
  }

  .monospace {
    font-family: monospace;
  }

  .error-message {
    background-color: #fee2e2;
    color: #991b1b;
    padding: 1rem;
    border-radius: 0.375rem;
    margin-bottom: 1.5rem;
    border: 1px solid #fecaca;
  }

  .form-control:disabled {
    background-color: #f3f4f6;
    cursor: not-allowed;
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .labour-times {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .labour-time-item {
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 1rem;
  }

  .labour-time-item > div {
    margin-bottom: 0.5rem;
  }

  .labour-time-item > div:last-child {
    margin-bottom: 0;
  }

  .no-data {
    padding: 1rem;
    color: #64748b;
    font-style: italic;
  }

  @media (max-width: 768px) {
    .container {
      padding: 1rem;
    }

    .content-grid {
      grid-template-columns: 1fr;
    }

    .header-content {
      flex-direction: column;
      gap: 1rem;
    }

    .header-actions {
      width: 100%;
    }

    .btn {
      flex: 1;
    }

    .form-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
