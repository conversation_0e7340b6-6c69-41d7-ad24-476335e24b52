import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function listServiceActions() {
    const client = new MongoClient(uri);
    
    try {
        await client.connect();
        console.log('Connected to MongoDB');
        
        const db = client.db(dbName);
        const collection = db.collection('PartNumbersServiceCodeAction');
        
        const items = await collection.find({}).toArray();
        console.log(`\nFound ${items.length} documents in PartNumbersServiceCodeAction:\n`);
        
        items.forEach((item, index) => {
            console.log(`Document ${index + 1}:`);
            console.log(JSON.stringify(item, null, 2));
            console.log('-------------------');
        });

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await client.close();
        console.log('\nConnection closed');
    }
}

listServiceActions().catch(console.error);
