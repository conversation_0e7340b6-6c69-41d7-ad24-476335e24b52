const { MongoClient } = require('mongodb');

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);
const dbName = 'ServiceContracts';

const collectionsToUpdate = [
    {
        name: 'BaseServices',
        oldField: 'Product Validity Group',
        newField: 'ProductValidityGroup'
    },
    {
        name: 'PartNumbersServiceCodeAction',
        oldField: 'Product Validity Group',
        newField: 'ProductValidityGroup'
    },
    {
        name: 'ProductValidityGroup',
        oldField: 'Product Validity GRoup',
        newField: 'ProductValidityGroup'
    },
    {
        name: 'ServiceCodeHeader',
        oldField: 'Product Validity Group',
        newField: 'ProductValidityGroup'
    }
];

async function main() {
    await client.connect();
    const db = client.db(dbName);
    
    for (const info of collectionsToUpdate) {
        await db.collection(info.name).updateMany(
            { [info.oldField]: { $exists: true } },
            [
                { $set: { [info.newField]: `$${info.oldField}` } },
                { $unset: [info.oldField] }
            ]
        );
    }
    
    await client.close();
}

main();
