import { MongoClient, ObjectId } from 'mongodb';
import { error } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/** @type {import('./$types').PageServerLoad} */
export async function load({ params }) {
  // Handle "new" route
  if (params.id === 'new') {
    return {
      item: null,
      isNew: true
    };
  }

  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('PartNumbersServiceCodeAction');
    
    // Validate ObjectId format
    if (!ObjectId.isValid(params.id)) {
      throw error(400, 'Invalid ID format');
    }
    
    const item = await collection.findOne({ _id: new ObjectId(params.id) });
    
    if (!item) {
      throw error(404, 'Item not found');
    }
    
    // Convert ObjectId to string for serialization
    const serializedItem = {
      ...item,
      _id: item._id.toString()
    };
    
    return {
      item: serializedItem,
      isNew: false
    };
  } catch (err) {
    if (err.status) {
      throw err; // Re-throw SvelteKit errors
    }
    console.error('Error loading item:', err);
    throw error(500, 'Failed to load item');
  } finally {
    await client.close();
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  save: async ({ request, params }) => {
    try {
      const formData = await request.formData();
      
      const data = {
        ServiceCode: formData.get('ServiceCode')?.toString() || '',
        PartNumber: formData.get('PartNumber')?.toString() || '',
        ActionType: formData.get('ActionType')?.toString() || '',
        'Product Validity Group': formData.get('ProductValidityGroup')?.toString() || '',
        Quantity: parseInt(formData.get('Quantity')?.toString() || '0'),
        'Unit of Measure': formData.get('UnitOfMeasure')?.toString() || ''
      };

      await client.connect();
      const db = client.db('ServiceContracts');
      const collection = db.collection('PartNumbersServiceCodeAction');

      if (params.id === 'new') {
        // Create new item
        data.createdAt = new Date();
        data.updatedAt = new Date();
        
        const result = await collection.insertOne(data);
        
        return {
          success: true,
          message: 'Item created successfully',
          id: result.insertedId.toString()
        };
      } else {
        // Update existing item
        if (!ObjectId.isValid(params.id)) {
          return {
            success: false,
            error: 'Invalid ID format'
          };
        }

        data.updatedAt = new Date();
        
        const result = await collection.updateOne(
          { _id: new ObjectId(params.id) },
          { $set: data }
        );

        if (result.matchedCount === 0) {
          return {
            success: false,
            error: 'Item not found'
          };
        }

        return {
          success: true,
          message: 'Item updated successfully'
        };
      }
    } catch (err) {
      console.error('Error saving item:', err);
      return {
        success: false,
        error: 'Failed to save item'
      };
    } finally {
      await client.close();
    }
  },

  delete: async ({ params }) => {
    try {
      if (params.id === 'new') {
        return {
          success: false,
          error: 'Cannot delete new item'
        };
      }

      await client.connect();
      const db = client.db('ServiceContracts');
      const collection = db.collection('PartNumbersServiceCodeAction');

      if (!ObjectId.isValid(params.id)) {
        return {
          success: false,
          error: 'Invalid ID format'
        };
      }

      const result = await collection.deleteOne({ _id: new ObjectId(params.id) });

      if (result.deletedCount === 0) {
        return {
          success: false,
          error: 'Item not found'
        };
      }

      return {
        success: true,
        message: 'Item deleted successfully'
      };
    } catch (err) {
      console.error('Error deleting item:', err);
      return {
        success: false,
        error: 'Failed to delete item'
      };
    } finally {
      await client.close();
    }
  }
};
