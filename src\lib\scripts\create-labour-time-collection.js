import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function createLabourTimeCollection() {
    const client = new MongoClient(uri);
    
    try {
        await client.connect();
        console.log('Connected to MongoDB');
        
        const db = client.db(dbName);
        
        // Check if collection already exists
        const collections = await db.listCollections().toArray();
        const collectionExists = collections.some(col => col.name === 'LabourTime');
        
        if (collectionExists) {
            console.log('Collection "LabourTime" already exists');
            return;
        }

        // Create the collection with schema validation
        await db.createCollection('LabourTime', {
            validator: {
                $jsonSchema: {
                    bsonType: 'object',
                    required: ['productDesignation', 'laborHours', 'createdAt', 'updatedAt'],
                    properties: {
                        productDesignation: {
                            bsonType: 'string',
                            description: 'Product designation code - required'
                        },
                        laborHours: {
                            bsonType: 'number',
                            description: 'Labor hours required - required'
                        },
                        description: {
                            bsonType: 'string',
                            description: 'Description of the labor task'
                        },
                        category: {
                            bsonType: 'string',
                            description: 'Category of labor'
                        },
                        createdAt: {
                            bsonType: 'date',
                            description: 'Creation timestamp - required'
                        },
                        updatedAt: {
                            bsonType: 'date',
                            description: 'Last update timestamp - required'
                        }
                    }
                }
            }
        });

        console.log('Successfully created "LabourTime" collection with schema validation');
        
        // Create indexes
        await db.collection('LabourTime').createIndex({ productDesignation: 1 });
        await db.collection('LabourTime').createIndex({ category: 1 });
        
        console.log('Created indexes on productDesignation and category');
        
    } catch (error) {
        console.error('Error creating collection:', error);
    } finally {
        await client.close();
        console.log('Disconnected from MongoDB');
    }
}

// Run the creation operation
createLabourTimeCollection().catch(console.error);
