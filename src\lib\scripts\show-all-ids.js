import { MongoClient } from 'mongodb';

// MongoDB connection URI
const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function showAllIds() {
  try {
    // Connect to MongoDB
    await client.connect();
    console.log('Connected to MongoDB');

    // Get the ServiceContracts database
    const db = client.db('ServiceContracts');
    
    // Get all collections in the database
    const collections = await db.listCollections().toArray();
    
    console.log(`Found ${collections.length} collections in ServiceContracts database\n`);
    
    // Iterate through each collection and get sample IDs
    for (const collectionInfo of collections) {
      const collectionName = collectionInfo.name;
      const collection = db.collection(collectionName);
      
      // Get document count
      const count = await collection.countDocuments();
      
      console.log(`\n==== Collection: ${collectionName} (${count} documents) ====`);
      
      if (count > 0) {
        // Get a sample of documents (limit to 10 for readability)
        const sampleDocs = await collection.find({}).limit(10).toArray();
        
        // Display the _id for each document
        sampleDocs.forEach((doc, index) => {
          console.log(`[${index + 1}] _id: ${doc._id}`);
        });
        
        if (count > 10) {
          console.log(`... and ${count - 10} more documents`);
        }
      } else {
        console.log('No documents found in this collection');
      }
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the connection
    await client.close();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the function
showAllIds();
