// Script to create a new ProductDesignation collection from ProductDesignation_Partnumber_ValidityGroup
// This script extracts unique ProductDesignation values and creates a new collection

import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function createProductDesignationCollection() {
  try {
    // Connect to MongoDB
    await client.connect();
    console.log('Connected to MongoDB');
    
    // Access the ServiceContracts database
    const db = client.db('ServiceContracts');
    
    // Check if ProductDesignation collection already exists
    const collections = await db.listCollections({ name: 'ProductDesignation' }).toArray();
    if (collections.length > 0) {
      console.log('ProductDesignation collection already exists. Dropping it before recreation.');
      await db.collection('ProductDesignation').drop();
    }
    
    // Create the new ProductDesignation collection
    await db.createCollection('ProductDesignation');
    console.log('Created new ProductDesignation collection');
    
    // Get distinct ProductDesignation values from the source collection
    const sourceCollection = db.collection('ProductDesignation_Partnumber_ValidityGroup');
    const distinctDesignations = await sourceCollection.distinct('ProductDesignation');
    console.log(`Found ${distinctDesignations.length} unique product designations`);
    
    // Create documents with ProductDesignation as both the value and caption
    const designationDocuments = distinctDesignations.map(designation => ({
      ProductDesignation: designation,
      Caption: designation // Using the designation itself as the caption
    }));
    
    // Insert the documents into the new collection
    const result = await db.collection('ProductDesignation').insertMany(designationDocuments);
    console.log(`Inserted ${result.insertedCount} documents into ProductDesignation collection`);
    
    // Create an index on ProductDesignation field for faster queries
    await db.collection('ProductDesignation').createIndex({ ProductDesignation: 1 }, { unique: true });
    console.log('Created index on ProductDesignation field');
    
    // Display a sample of the new collection
    const sampleDocs = await db.collection('ProductDesignation').find().limit(5).toArray();
    console.log('Sample documents from the new collection:');
    console.log(sampleDocs);
    
    console.log('Script completed successfully');
  } catch (error) {
    console.error('Error occurred:', error);
  } finally {
    // Close the connection
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the function
createProductDesignationCollection();
