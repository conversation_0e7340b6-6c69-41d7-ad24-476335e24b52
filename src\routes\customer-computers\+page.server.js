import { error } from '@sveltejs/kit';
import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

/**
 * @type {import('./$types').PageServerLoad}
 */
export async function load({ url }) {
  let client;
  
  try {
    // Get customerId from query params if provided
    const customerId = url.searchParams.get('customerId');
    
    // Connect to MongoDB
    client = new MongoClient(uri);
    await client.connect();
    const db = client.db(dbName);
    
    // Build query for computers
    const query = {};
    if (customerId && ObjectId.isValid(customerId)) {
      query.customerId = new ObjectId(customerId);
    }
    
    // Get all customers for the dropdown
    const customers = await db.collection('Customers')
      .find({})
      .sort({ companyName: 1 })
      .project({ 
        _id: 1, 
        companyName: 1, 
        name: 1, 
        type: 1, 
        city: 1, 
        country: 1 
      })
      .toArray();
    
    // Get computers based on query
    const pipeline = [
      { $match: query },
      {
        $lookup: {
          from: 'Customers',
          localField: 'customerId',
          foreignField: '_id',
          as: 'customer'
        }
      },
      {
        $unwind: {
          path: '$customer',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $project: {
          _id: 1,
          customerId: 1,
          name: 1,
          serialNumber: 1,
          model: 1,
          manufacturer: 1,
          type: 1,
          productType: 1,
          productDesignation: 1,
          engineType: 1,
          operatingHours: 1,
          installationDate: 1,
          manufactureDate: 1,
          isActive: 1,
          notes: 1,
          purchaseDate: 1,
          warrantyEndDate: 1,
          createdAt: 1,
          updatedAt: 1,
          'customer._id': 1,
          'customer.companyName': 1,
          'customer.name': 1,
          'customer.type': 1
        }
      },
      {
        $sort: {
          'customer.companyName': 1,
          name: 1
        }
      }
    ];
    
    const computers = await db.collection('CustomerComputers')
      .aggregate(pipeline)
      .toArray();
    
    // Transform data for client-side use
    const serializedComputers = computers.map(computer => ({
      ...computer,
      _id: computer._id.toString(),
      customerId: computer.customerId.toString(),
      customer: computer.customer ? {
        ...computer.customer,
        _id: computer.customer._id.toString()
      } : null,
      purchaseDate: computer.purchaseDate ? new Date(computer.purchaseDate) : null,
      warrantyEndDate: computer.warrantyEndDate ? new Date(computer.warrantyEndDate) : null,
      installationDate: computer.installationDate ? new Date(computer.installationDate) : null,
      manufactureDate: computer.manufactureDate ? new Date(computer.manufactureDate) : null,
      createdAt: computer.createdAt ? new Date(computer.createdAt) : null,
      updatedAt: computer.updatedAt ? new Date(computer.updatedAt) : null
    }));
    
    const serializedCustomers = customers.map(customer => ({
      ...customer,
      _id: customer._id.toString()
    }));
    
    // Get selected customer if customerId is provided
    let selectedCustomer = null;
    if (customerId && ObjectId.isValid(customerId)) {
      const customer = serializedCustomers.find(c => c._id === customerId);
      if (customer) {
        selectedCustomer = customer;
      }
    }
    
    return {
      computers: serializedComputers,
      customers: serializedCustomers,
      selectedCustomer
    };
  } catch (err) {
    console.error('Error loading customer computers:', err);
    throw error(500, {
      message: 'Failed to load customer computers'
    });
  } finally {
    if (client) {
      await client.close();
    }
  }
}

/**
 * @type {import('./$types').Actions}
 */
export const actions = {
  addComputer: async ({ request }) => {
    let client;
    
    try {
      const formData = await request.formData();
      
      // Get required fields
      const customerId = formData.get('customerId')?.toString();
      const serialNumber = formData.get('serialNumber')?.toString();
      const model = formData.get('model')?.toString();
      const productType = formData.get('productType')?.toString();
      
      // Validate required fields
      if (!customerId || !serialNumber || !model || !productType) {
        return { success: false, error: 'Missing required fields' };
      }
      
      // Validate ObjectId format
      if (!ObjectId.isValid(customerId)) {
        return { success: false, error: 'Invalid customer ID format' };
      }
      
      // Generate a name for the computer if not provided
      const name = formData.get('name')?.toString() || `${model} - ${serialNumber}`;
      
      // Parse numeric values
      const operatingHours = parseInt(formData.get('operatingHours')?.toString() || '0', 10);
      
      // Create computer data object
      const computerData = {
        customerId: new ObjectId(customerId),
        name,
        serialNumber,
        model,
        productType,
        productDesignation: formData.get('productDesignation')?.toString() || '',
        engineType: formData.get('engineType')?.toString() || '',
        operatingHours: isNaN(operatingHours) ? 0 : operatingHours,
        installationDate: formData.get('installationDate')?.toString() || null,
        manufactureDate: formData.get('manufactureDate')?.toString() || null,
        isActive: formData.get('isActive') === 'on',
        notes: formData.get('notes')?.toString() || '',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      client = new MongoClient(uri);
      await client.connect();
      const db = client.db(dbName);
      const computersCollection = db.collection('CustomerComputers');
      
      // Insert the computer
      const result = await computersCollection.insertOne(computerData);
      
      if (!result.acknowledged) {
        return { success: false, error: 'Failed to add computer' };
      }
      
      return { 
        success: true, 
        type: 'success',
        message: 'Computer added successfully',
        computerId: result.insertedId.toString()
      };
    } catch (err) {
      console.error('Error adding computer:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Unknown error' 
      };
    } finally {
      if (client) {
        await client.close();
      }
    }
  },
  
  deleteComputer: async ({ request }) => {
    let client;
    
    try {
      const formData = await request.formData();
      const computerId = formData.get('computerId')?.toString();
      
      // Validate ObjectId
      if (!computerId || !ObjectId.isValid(computerId)) {
        return {
          success: false,
          error: 'Invalid computer ID format'
        };
      }
      
      client = new MongoClient(uri);
      await client.connect();
      const db = client.db(dbName);
      
      // Delete computer
      const result = await db.collection('CustomerComputers').deleteOne({
        _id: new ObjectId(computerId)
      });
      
      if (!result.deletedCount) {
        return {
          success: false,
          error: 'Computer not found or already deleted'
        };
      }
      
      return {
        success: true,
        message: 'Computer deleted successfully'
      };
    } catch (err) {
      console.error('Error deleting computer:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Unknown error' 
      };
    } finally {
      if (client) {
        await client.close();
      }
    }
  }
};
