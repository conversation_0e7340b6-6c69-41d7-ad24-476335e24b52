import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/** @type {import('./$types').PageServerLoad} */
export async function load({ params }) {
  try {
    const computerId = params.id;
    const quoteId = params.quoteId;
    
    console.log(`Loading quote data for computer ID: ${computerId}, quote ID: ${quoteId}`);
    
    // Get MongoDB connection
    const db = client.db('ServiceContracts');
    
    // Get computer details
    let computer = null;
    try {
      computer = await db.collection('CustomerComputers')
        .findOne({ _id: new ObjectId(computerId) });
          
      if (!computer) {
        console.error('Computer not found:', computerId);
        return { error: 'Computer not found' };
      }
      
      // Convert ObjectId to string for client-side use
      computer._id = computer._id.toString();
      if (computer.customerId) {
        computer.customerId = computer.customerId.toString();
      }
    } catch (computerError) {
      console.error('Error fetching computer:', computerError);
      return { error: 'Error fetching computer details' };
    }
    
    // Get customer details
    let customer = null;
    try {
      if (computer.customerId && ObjectId.isValid(computer.customerId)) {
        customer = await db.collection('Customers')
          .findOne({ _id: new ObjectId(computer.customerId) });
          
        if (customer) {
          // Convert ObjectId to string for client-side use
          customer._id = customer._id.toString();
        }
      }
    } catch (customerError) {
      console.error('Error fetching customer:', customerError);
      // Continue without customer data
    }
    
    // Get quotation header
    let quotationHeader = null;
    try {
      if (quoteId !== 'new' && ObjectId.isValid(quoteId)) {
        quotationHeader = await db.collection('QuotationHeader')
          .findOne({ _id: new ObjectId(quoteId) });
        
        if (quotationHeader) {
          // Convert ObjectIds to strings for client-side use
          quotationHeader._id = quotationHeader._id.toString();
          if (quotationHeader.computerId) {
            quotationHeader.computerId = quotationHeader.computerId.toString();
          }
        } else {
          console.error('Quotation header not found:', quoteId);
          return { error: 'Quotation not found' };
        }
      } else if (quoteId === 'new') {
        // Create a new empty quotation header for a new quote
        quotationHeader = {
          _id: 'new',
          computerId: computerId,
          status: 'Draft',
          contractLength: 12,
          contractStartDate: new Date(),
          totalAmount: 0,
          notes: ''
        };
      } else {
        return { error: 'Invalid quote ID' };
      }
    } catch (headerError) {
      console.error('Error fetching quotation header:', headerError);
      return { error: 'Error fetching quotation details' };
    }
    
    // Get quotation rows
    let quotationRows = [];
    try {
      if (quoteId !== 'new' && ObjectId.isValid(quoteId)) {
        quotationRows = await db.collection('QuotationRows')
          .find({ quotationId: new ObjectId(quoteId) })
          .sort({ rowOrder: 1 })
          .toArray();
        
        console.log(`Found ${quotationRows.length} quotation rows`);
        
        // Convert ObjectIds to strings for client-side use
        quotationRows = quotationRows.map(row => {
          try {
            return {
              ...row,
              _id: row._id ? row._id.toString() : '',
              quotationId: row.quotationId ? row.quotationId.toString() : '',
              rowId: row._id ? row._id.toString() : '' // Add rowId for easier reference
            };
          } catch (rowError) {
            console.error('Error processing row:', rowError);
            return {
              _id: '',
              quotationId: '',
              rowType: 'Error',
              rowOrder: 0
            };
          }
        });
      }
    } catch (rowsError) {
      console.error('Error fetching quotation rows:', rowsError);
      quotationRows = []; // Use empty array if there's an error
    }
    
    // Create the data structure expected by the Svelte component
    // Organize rows by type
    const baseContractRows = quotationRows.filter(row => row.rowType === 'BaseContract');
    const dealerAddOnRows = quotationRows.filter(row => row.rowType === 'DealerAddOn');
    const supportServiceRows = quotationRows.filter(row => row.rowType === 'SupportService');
    const retirementPlanRows = quotationRows.filter(row => row.rowType === 'RetirementPlan');
    
    // Create service offerings structure
    const serviceOfferings = {
      baseContract: baseContractRows.map(row => ({
        id: row._id,
        rowId: row._id,
        serviceId: row.packageId || 0,
        level: row.rowOrder || 0,
        name: 'Base Contract',
        service: row.packageName || 'Base Service',
        ServiceActivity: row.ServiceActivity || row.activity || '',
        cost: row.cost || 0,
        fixed: row.fixed !== undefined ? row.fixed : true,
        variableType: row.variableType || 'hr',
        oemImporter: row.oemImporter || false,
        fleetOwner: row.fleetOwner || false,
        customerSpecific: row.customerSpecific || false,
        required: row.required || false
      })),
      dealerAddOns: dealerAddOnRows.map(row => ({
        id: row._id,
        rowId: row._id,
        serviceId: row.packageId || 0,
        level: row.rowOrder || 0,
        name: 'Dealer Add-On',
        service: row.packageName || 'Add-On Service',
        ServiceActivity: row.ServiceActivity || row.activity || '', 
        cost: row.cost || 0,
        fixed: row.fixed !== undefined ? row.fixed : true,
        variableType: row.variableType || 'hr',
        oemImporter: row.oemImporter || false,
        fleetOwner: row.fleetOwner || false,
        customerSpecific: row.customerSpecific || false,
        uniqueId: row.uniqueId || `addon_${Date.now()}_${Math.floor(Math.random() * 1000)}`
      })),
      supportServices: supportServiceRows.map(row => ({
        id: row._id,
        rowId: row._id,
        serviceId: row.packageId || 0,
        level: row.rowOrder || 0,
        name: 'Support Service',
        service: row.packageName || 'Support Service',
        ServiceActivity: row.ServiceActivity || row.activity || '', 
        cost: row.cost || 0,
        fixed: row.fixed !== undefined ? row.fixed : true,
        variableType: row.variableType || 'hr',
        oemImporter: row.oemImporter || false,
        fleetOwner: row.fleetOwner || false,
        customerSpecific: row.customerSpecific || false
      })),
      retirementPlans: retirementPlanRows.map(row => ({
        id: row._id,
        rowId: row._id,
        serviceId: row.packageId || 0,
        level: row.rowOrder || 0,
        name: 'Retirement Plan',
        service: row.packageName || 'Retirement Plan',
        ServiceActivity: row.ServiceActivity || row.activity || '', // Update the field name from activity to ServiceActivity
        cost: row.cost || 0,
        fixed: row.fixed !== undefined ? row.fixed : true,
        variableType: row.variableType || 'hr',
        oemImporter: row.oemImporter || false,
        fleetOwner: row.fleetOwner || false,
        customerSpecific: row.customerSpecific || false
      }))
    };
    
    // Create selected IDs structure
    const selectedIds = {
      baseContract: serviceOfferings.baseContract.map(service => service.id),
      dealerAddOns: serviceOfferings.dealerAddOns.map(service => service.id),
      supportServices: serviceOfferings.supportServices.map(service => service.id),
      retirementPlans: serviceOfferings.retirementPlans.map(service => service.id)
    };
    
    // Calculate section totals
    const sectionTotals = {
      baseContract: serviceOfferings.baseContract.reduce((total, service) => total + (parseFloat(service.cost) || 0), 0),
      dealerAddOns: serviceOfferings.dealerAddOns.reduce((total, service) => total + (parseFloat(service.cost) || 0), 0),
      supportServices: serviceOfferings.supportServices.reduce((total, service) => total + (parseFloat(service.cost) || 0), 0),
      retirementPlans: serviceOfferings.retirementPlans.reduce((total, service) => total + (parseFloat(service.cost) || 0), 0),
      total: 0
    };
    
    // Calculate total
    sectionTotals.total = 
      sectionTotals.baseContract + 
      sectionTotals.dealerAddOns + 
      sectionTotals.supportServices + 
      sectionTotals.retirementPlans;
    
    return {
      computer,
      customer,
      quotationHeader,
      quotationRows,
      serviceOfferings,
      selectedIds,
      sectionTotals
    };
  } catch (err) {
    console.error('Error loading quote data:', err);
    return { error: err instanceof Error ? err.message : 'Failed to load quote data' };
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  updateQuotationRow: async ({ request }) => {
    try {
      const data = await request.formData();
      const rowDataStr = data.get('rowData');
      
      if (!rowDataStr) {
        return { success: false, error: 'No row data provided' };
      }
      
      const rowData = JSON.parse(rowDataStr);
      console.log('Received row data:', rowData);
      
      // Get the MongoDB client
      const db = client.db('ServiceContracts');
      
      // Prepare the update data
      const updateData = {
        quotationId: rowData.quotationId ? new ObjectId(rowData.quotationId) : null,
        rowType: getRowTypeFromSection(rowData.section || 'baseContract'),
        rowOrder: rowData.level || 0,
        packageId: rowData.serviceId || 0,
        packageName: rowData.service || '',
        ServiceActivity: rowData.ServiceActivity || rowData.activity || '', 
        cost: parseFloat(rowData.cost) || 0,
        fixed: rowData.fixed !== undefined ? rowData.fixed : true,
        variableType: rowData.variableType || 'hr',
        oemImporter: rowData.oemImporter || false,
        fleetOwner: rowData.fleetOwner || false,
        customerSpecific: rowData.customerSpecific || false,
        required: rowData.required || false,
        includeInOffer: true,
        updatedAt: new Date()
      };
      
      console.log('Storing ServiceActivity:', rowData.ServiceActivity);
      
      // For dealer add-ons, include the unique identifier
      if (rowData.section === 'dealerAddOns' && rowData.uniqueId) {
        updateData.uniqueId = rowData.uniqueId;
      }
      
      let result;
      
      if (rowData.rowId && ObjectId.isValid(rowData.rowId)) {
        // Update existing row
        result = await db.collection('QuotationRows').updateOne(
          { _id: new ObjectId(rowData.rowId) },
          { $set: updateData }
        );
        
        console.log(`Updated row ${rowData.rowId} with ServiceActivity: ${updateData.ServiceActivity}`);
        
        return { 
          success: true, 
          message: 'Row updated successfully',
          rowId: rowData.rowId
        };
      } else {
        // Create new row
        updateData.createdAt = new Date();
        result = await db.collection('QuotationRows').insertOne(updateData);
        
        console.log(`Created new row with ServiceActivity: ${updateData.ServiceActivity}`);
        
        return { 
          success: true, 
          message: 'Row created successfully',
          rowId: result.insertedId.toString()
        };
      }
    } catch (error) {
      console.error('Error updating quotation row:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to update quotation row' 
      };
    }
  },
  
  removeQuotationRow: async ({ request }) => {
    try {
      const data = await request.formData();
      const rowId = data.get('rowId');
      
      if (!rowId || !ObjectId.isValid(rowId)) {
        return { success: false, error: 'Invalid row ID' };
      }
      
      // Get the MongoDB client
      const db = client.db('ServiceContracts');
      
      // Delete the row
      const result = await db.collection('QuotationRows').deleteOne({
        _id: new ObjectId(rowId)
      });
      
      if (result.deletedCount === 1) {
        return { success: true, message: 'Row deleted successfully' };
      } else {
        return { success: false, error: 'Row not found' };
      }
    } catch (error) {
      console.error('Error removing quotation row:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to remove quotation row' 
      };
    }
  },
  
  saveQuote: async ({ request }) => {
    try {
      const formData = await request.formData();
      const quoteDataStr = formData.get('quoteData');
      
      if (!quoteDataStr) {
        return { success: false, error: 'No quote data provided' };
      }
      
      const quoteData = JSON.parse(quoteDataStr);
      const computerId = quoteData.computerId;
      
      if (!computerId) {
        return { success: false, error: 'Computer ID is required' };
      }
      
      // Get the MongoDB client
      const db = client.db('ServiceContracts');
      
      // Create or update quotation header
      const quotationHeader = {
        computerId: new ObjectId(computerId),
        updatedAt: new Date(),
        status: quoteData.status || 'Draft',
        contractLength: quoteData.contractLength || 12,
        contractStartDate: quoteData.contractStartDate ? new Date(quoteData.contractStartDate) : new Date(),
        totalAmount: quoteData.totalAmount || 0,
        notes: quoteData.notes || ''
      };
      
      let quotationId;
      if (quoteData.quotationId && quoteData.quotationId !== 'new' && ObjectId.isValid(quoteData.quotationId)) {
        // Update existing quotation header
        await db.collection('QuotationHeader').updateOne(
          { _id: new ObjectId(quoteData.quotationId) },
          { $set: quotationHeader }
        );
        quotationId = quoteData.quotationId;
      } else {
        // Create new quotation header
        quotationHeader.createdAt = new Date();
        quotationHeader.quotationNumber = await generateQuotationNumber(db);
        const insertResult = await db.collection('QuotationHeader').insertOne(quotationHeader);
        quotationId = insertResult.insertedId.toString();
      }
      
      // Delete existing quotation rows for this quotation
      await db.collection('QuotationRows').deleteMany({ 
        quotationId: new ObjectId(quotationId) 
      });
      
      // Create new quotation rows
      if (quoteData.selectedServices && quoteData.selectedServices.length > 0) {
        const quotationRows = quoteData.selectedServices.map(service => {
          console.log(`Service activity for ${service.service}: ${service.ServiceActivity}`);
          
          return {
            quotationId: new ObjectId(quotationId),
            rowType: getRowTypeFromSection(service.section),
            rowOrder: service.level || 0,
            packageId: service.serviceId || 0,
            packageName: service.service || '',
            ServiceActivity: service.ServiceActivity || '', 
            cost: service.cost || 0,
            fixed: service.fixed !== undefined ? service.fixed : true,
            variableType: service.variableType || 'hr',
            oemImporter: service.oemImporter || false,
            fleetOwner: service.fleetOwner || false,
            customerSpecific: service.customerSpecific || false,
            uniqueId: service.section === 'dealerAddOns' ? (service.uniqueId || null) : null,
            createdAt: new Date(),
            updatedAt: new Date()
          };
        });
        
        await db.collection('QuotationRows').insertMany(quotationRows);
      }
      
      return {
        success: true,
        message: 'Quote saved successfully',
        quotationId: quotationId
      };
    } catch (error) {
      console.error('Error saving quote:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to save quote' 
      };
    }
  }
};

// Generate a unique quotation number
async function generateQuotationNumber(db) {
  const currentYear = new Date().getFullYear();
  const latestQuotation = await db.collection('QuotationHeader')
    .find({ quotationNumber: { $regex: `^Q${currentYear}-` } })
    .sort({ quotationNumber: -1 })
    .limit(1)
    .toArray();
  
  let nextNumber = 1;
  if (latestQuotation.length > 0) {
    const latestNumber = latestQuotation[0].quotationNumber;
    const numberPart = latestNumber.split('-')[1];
    nextNumber = parseInt(numberPart, 10) + 1;
  }
  
  return `Q${currentYear}-${nextNumber.toString().padStart(4, '0')}`;
}

// Get the row type based on the section
function getRowTypeFromSection(section) {
  switch (section) {
    case 'baseContract':
      return 'BaseContract';
    case 'dealerAddOns':
      return 'DealerAddOn';
    case 'supportServices':
      return 'SupportService';
    case 'retirementPlans':
      return 'RetirementPlan';
    default:
      return 'Unknown';
  }
}
