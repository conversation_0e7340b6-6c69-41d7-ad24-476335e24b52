import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

/** @type {import('./$types').RequestHandler} */
export async function GET({ url }) {
  const computerId = url.searchParams.get('computerId');
  const productDesignation = url.searchParams.get('productDesignation');

  if (!computerId || !ObjectId.isValid(computerId)) {
    return json({ error: 'Valid Computer ID is required' }, { status: 400 });
  }

  const client = new MongoClient(uri);

  try {
    await client.connect();
    const db = client.db(dbName);

    // Build query
    const query = { computerId: new ObjectId(computerId) };
    if (productDesignation) {
      query.productDesignation = productDesignation;
    }

    // Get the most recent service plan for this computer
    const servicePlan = await db.collection('ComputerServicePlan')
      .findOne(query, { sort: { generatedAt: -1 } });

    if (!servicePlan) {
      return json({
        success: false,
        error: 'No service plan found for this computer',
        computerId,
        productDesignation
      }, { status: 404 });
    }

    return json({
      success: true,
      data: {
        ...servicePlan,
        _id: servicePlan._id.toString(),
        computerId: servicePlan.computerId.toString(),
        disabledServices: servicePlan.disabledServices || []
      }
    });

  } catch (err) {
    console.error('❌ Error retrieving service plan:', err);
    return json({
      error: 'Failed to retrieve service plan',
      details: err.message
    }, { status: 500 });
  } finally {
    await client.close();
  }
}

/** @type {import('./$types').RequestHandler} */
export async function POST({ request }) {
  const client = new MongoClient(uri);

  try {
    await client.connect();
    const db = client.db(dbName);

    const data = await request.json();
    const { computerId, productDesignation, servicePlanData } = data;

    if (!computerId || !ObjectId.isValid(computerId)) {
      return json({ error: 'Valid Computer ID is required' }, { status: 400 });
    }

    if (!productDesignation) {
      return json({ error: 'Product designation is required' }, { status: 400 });
    }

    if (!servicePlanData) {
      return json({ error: 'Service plan data is required' }, { status: 400 });
    }

    console.log(`💾 Saving service plan for Computer ID: ${computerId}`);

    // Prepare the service plan document
    const servicePlanDocument = {
      computerId: new ObjectId(computerId),
      productDesignation: productDesignation,
      computer: servicePlanData.computer,
      workloadSummary: servicePlanData.workloadSummary,
      servicePlan: servicePlanData.servicePlan,
      statistics: servicePlanData.servicePlan.statistics,
      totalServices: servicePlanData.servicePlan.totalServices,
      dataSource: servicePlanData.servicePlan.dataSource || 'ServiceCodeAndActionType',
      generatedAt: new Date(servicePlanData.generatedAt),
      savedAt: new Date(),
      version: '1.0',
      disabledServices: [] // Initialize empty disabled services array
    };

    // Check if a service plan already exists for this computer and product
    const existingPlan = await db.collection('ComputerServicePlan')
      .findOne({
        computerId: new ObjectId(computerId),
        productDesignation: productDesignation
      });

    let result;

    if (existingPlan) {
      // Update existing plan
      result = await db.collection('ComputerServicePlan')
        .updateOne(
          { _id: existingPlan._id },
          {
            $set: {
              ...servicePlanDocument,
              disabledServices: existingPlan.disabledServices || [], // Preserve existing disabled services
              updatedAt: new Date(),
              previousVersions: existingPlan.previousVersions || []
            },
            $push: {
              'previousVersions': {
                generatedAt: existingPlan.generatedAt,
                savedAt: existingPlan.savedAt,
                totalServices: existingPlan.totalServices
              }
            }
          }
        );

      console.log(`✅ Updated existing service plan (ID: ${existingPlan._id})`);

      return json({
        success: true,
        action: 'updated',
        id: existingPlan._id.toString(),
        totalServices: servicePlanDocument.totalServices,
        generatedAt: servicePlanDocument.generatedAt
      });

    } else {
      // Create new plan
      result = await db.collection('ComputerServicePlan')
        .insertOne(servicePlanDocument);

      console.log(`✅ Created new service plan (ID: ${result.insertedId})`);

      return json({
        success: true,
        action: 'created',
        id: result.insertedId.toString(),
        totalServices: servicePlanDocument.totalServices,
        generatedAt: servicePlanDocument.generatedAt
      });
    }

  } catch (err) {
    console.error('❌ Error saving service plan:', err);
    return json({
      error: 'Failed to save service plan',
      details: err.message
    }, { status: 500 });
  } finally {
    await client.close();
  }
}

/** @type {import('./$types').RequestHandler} */
export async function DELETE({ url }) {
  const computerId = url.searchParams.get('computerId');
  const planId = url.searchParams.get('planId');

  if (!computerId || !ObjectId.isValid(computerId)) {
    return json({ error: 'Valid Computer ID is required' }, { status: 400 });
  }

  const client = new MongoClient(uri);

  try {
    await client.connect();
    const db = client.db(dbName);

    let query;
    if (planId && ObjectId.isValid(planId)) {
      // Delete specific plan by ID
      query = { _id: new ObjectId(planId), computerId: new ObjectId(computerId) };
    } else {
      // Delete all plans for this computer
      query = { computerId: new ObjectId(computerId) };
    }

    const result = await db.collection('ComputerServicePlan').deleteMany(query);

    console.log(`🗑️ Deleted ${result.deletedCount} service plan(s) for computer ${computerId}`);

    return json({
      success: true,
      deletedCount: result.deletedCount,
      computerId
    });

  } catch (err) {
    console.error('❌ Error deleting service plan:', err);
    return json({
      error: 'Failed to delete service plan',
      details: err.message
    }, { status: 500 });
  } finally {
    await client.close();
  }
}

/** @type {import('./$types').RequestHandler} */
export async function PATCH({ request, url }) {
  const computerId = url.searchParams.get('computerId');
  const productDesignation = url.searchParams.get('productDesignation');

  if (!computerId || !ObjectId.isValid(computerId)) {
    return json({ error: 'Valid Computer ID is required' }, { status: 400 });
  }

  const client = new MongoClient(uri);

  try {
    const { disabledServices } = await request.json();

    if (!Array.isArray(disabledServices)) {
      return json({ error: 'disabledServices must be an array' }, { status: 400 });
    }

    await client.connect();
    const db = client.db(dbName);

    // Build query
    const query = { computerId: new ObjectId(computerId) };
    if (productDesignation) {
      query.productDesignation = productDesignation;
    }

    // Update the service plan with disabled services
    const result = await db.collection('ComputerServicePlan')
      .updateOne(
        query,
        {
          $set: {
            disabledServices: disabledServices,
            updatedAt: new Date()
          }
        }
      );

    if (result.matchedCount === 0) {
      return json({
        error: 'No service plan found for this computer',
        computerId,
        productDesignation
      }, { status: 404 });
    }

    console.log(`💾 Updated disabled services for Computer ID: ${computerId}`);

    return json({
      success: true,
      message: 'Disabled services updated successfully',
      disabledServices: disabledServices,
      updatedCount: result.modifiedCount
    });

  } catch (err) {
    console.error('❌ Error updating disabled services:', err);
    return json({
      error: 'Failed to update disabled services',
      details: err.message
    }, { status: 500 });
  } finally {
    await client.close();
  }
}