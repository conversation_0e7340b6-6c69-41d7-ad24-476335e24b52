/** @type {import('./$types').PageServerLoad} */
export async function load({ url, fetch }) {
  const computerId = url.searchParams.get('computerId') || '682ae75f05ee3d76deed9776';
  const productDesignation = url.searchParams.get('productDesignation') || 'TAD1640-42GE-B';
  
  try {
    // Fetch the diagnostic data from our API
    const response = await fetch(`/api/data-diagnostic?computerId=${computerId}&productDesignation=${productDesignation}`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch diagnostic: ${response.status}`);
    }
    
    const diagnosticData = await response.json();
    
    return {
      diagnostic: diagnosticData,
      computerId,
      productDesignation
    };
    
  } catch (err) {
    console.error('Error loading diagnostic:', err);
    return {
      error: err.message,
      computerId,
      productDesignation
    };
  }
}
