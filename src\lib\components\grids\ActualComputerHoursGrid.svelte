<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  import { goto } from '$app/navigation';
  import { createEventDispatcher } from 'svelte';
  const dispatch = createEventDispatcher();
  export let actualComputerHours: any[] = [];

  function logData(label: string, data: any) {
    console.log(`📊 GRID: ${label} 📊`);
    console.group(label);
    if (Array.isArray(data)) {
      console.log(`Array with ${data.length} items`);
      if (data.length > 0) {
        console.log('First item sample:', data[0]);
        if (data.length > 1) {
          console.log('Last item sample:', data[data.length - 1]);
        }
      }
    } else if (data && typeof data === 'object') {
      console.log(data);
    } else {
      console.log(data);
    }
    console.groupEnd();
    console.log(`📊 END GRID: ${label} 📊`);
  }

  function pretty(val: any, field?: string) {
    if ((field === 'createdAt' || field === 'updatedAt') && val) {
      return new Date(val).toLocaleString();
    }
    if (val === null || val === undefined) return '';
    if (typeof val === 'object') return JSON.stringify(val);
    return val.toString();
  }

  // Accept a prop to filter by computerId
  export let filterComputerId: string | null = null;

  // Grid columns with all fields from the database
  // Move 'actions' to first column for leftmost Edit button
  const displayFields = [
    'actions',
    'index', 
    '_id',
    'computerId',
    'activity', 
    'hasFixed',
    'isIdle',
    'hours', 
    'ReportDate', 
    'ReportTime',
    'CalculatedHoursPerMonth',
    'HoursPerMonth',
    'createdAt',
    'updatedAt'
  ];
  
  // Adjust gridStyle to match new column order
  const gridStyle = `
    grid-template-columns: 
      1fr /* actions */
      0.3fr /* # */
      2fr /* _id */
      2fr /* computerId */
      1fr /* activity */
      0.7fr /* hasFixed */
      0.7fr /* isIdle */
      0.7fr /* hours */
      1fr /* ReportDate */
      1fr /* ReportTime */
      1.3fr /* CalculatedHoursPerMonth */
      1.3fr /* HoursPerMonth */
      1.3fr /* createdAt */
      1.3fr /* updatedAt */
  `;

  // Only display rows matching the filterComputerId (if provided), and sort by ReportDate and ReportTime
  $: filteredRows = filterComputerId
    ? actualComputerHours.filter(row => row.computerId === filterComputerId)
    : actualComputerHours;
  
  // Sort by ReportDate and ReportTime (ascending, oldest first)
  $: sortedRows = [...filteredRows].sort((a, b) => {
    const aDate = new Date(`${a.ReportDate || ''}T${a.ReportTime || '00:00'}`);
    const bDate = new Date(`${b.ReportDate || ''}T${b.ReportTime || '00:00'}`);
    return aDate - bDate;
  });

  $: {
    // Log when input data changes
    console.log(`[${new Date().toISOString()}] ActualComputerHoursGrid received ${actualComputerHours.length} rows`);
    logData('Input Data', actualComputerHours);
  }

  $: processedHours = sortedRows.map((row, index) => {
    // Deep clone to avoid mutation issues
    const newRow = {...row, index: index + 1};
    // Always ensure date and time are present
    if (!newRow.ReportDate || newRow.ReportDate === '') {
      const now = new Date('2025-04-22T12:37:07+02:00');
      newRow.ReportDate = now.toISOString().split('T')[0];
    }
    if (!newRow.ReportTime || newRow.ReportTime === '') {
      const now = new Date('2025-04-22T12:37:07+02:00');
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      newRow.ReportTime = `${hours}:${minutes}`;
    }
    if (newRow.ReportDateTime) {
      delete newRow.ReportDateTime;
    }
    // Convert _id and computerId to string if needed
    if (newRow._id && typeof newRow._id === 'object' && newRow._id.$oid) newRow._id = newRow._id.$oid;
    if (newRow.computerId && typeof newRow.computerId === 'object' && newRow.computerId.$oid) newRow.computerId = newRow.computerId.$oid;
    return newRow;
  });

  $: allKeys = processedHours.length > 0
    ? Array.from(new Set(processedHours.flatMap(row => Object.keys(row))))
    : [];

  // Deduplicate processedHours by _id (skip duplicates, keep first occurrence)
  $: dedupedProcessedHours = [];
  $: seenIds = new Set();
  $: {
    dedupedProcessedHours = [];
    seenIds = new Set();
    for (const row of processedHours) {
      if (!seenIds.has(row._id)) {
        dedupedProcessedHours.push(row);
        seenIds.add(row._id);
      }
    }
  }
</script>

<BaseGrid>
  <div slot="header" class="ach-header ach-row" style={gridStyle}>
    {#each displayFields as field}
      <div class="ach-cell {field === 'ReportDate' || field === 'ReportTime' ? 'date-time-cell' : ''}">
        {field === 'actions' ? 'Select' :
         field === 'index' ? '#' :
         field === '_id' ? 'DocumentId' :
         field === 'computerId' ? 'ComputerId' :
         field === 'ReportDate' ? 'Report Date' :
         field === 'ReportTime' ? 'Report Time' :
         field === 'HoursPerMonth' ? 'Hours Per Month' :
         field.charAt(0).toUpperCase() + field.slice(1)}
      </div>
    {/each}
  </div>
  <div slot="content">
    {#if processedHours.length === 0}
      <div class="ach-row"><div class="ach-cell" style="grid-column: span {displayFields.length}; text-align:center;">No actual computer hours found.</div></div>
    {:else}
      {#each processedHours as row, i}
        <div 
          class="ach-row selectable" 
          style={gridStyle}
          role="button"
          aria-label={`Select Actual Computer Hour for DocumentId ${row._id}`}
          on:click={() => {
            goto(`/actual-computer-hours/${row._id}`);
          }}
          on:keydown={(e) => { if (e.key === 'Enter' || e.key === ' ') { 
            goto(`/actual-computer-hours/${row._id}`); 
          } }}
        >
          {#each displayFields as field}
            <div class="ach-cell {field === 'ReportDate' || field === 'ReportTime' ? 'date-time-cell' : ''}">
              {#if field === 'actions'}
                <button class="action-button edit" type="button" tabindex="-1" on:click|stopPropagation={() => goto(`/actual-computer-hours/${row._id}`)}>Select</button>
              {:else if field === 'index'}
                {row.index}
              {:else if field === '_id'}
                {row._id}
              {:else if field === 'computerId'}
                {row.computerId}
              {:else if field === 'ReportDate'}
                {row.ReportDate || '-'}
              {:else if field === 'ReportTime'}
                {row.ReportTime || '-'}
              {:else if field === 'HoursPerMonth'}
                {row.HoursPerMonth !== undefined && row.HoursPerMonth !== null ? row.HoursPerMonth.toFixed(2) : '-'}
              {:else if field === 'createdAt' || field === 'updatedAt'}
                {row[field] ? new Date(row[field]).toLocaleString() : '-'}
              {:else}
                {pretty(row[field], field)}
              {/if}
            </div>
          {/each}
        </div>
      {/each}
    {/if}
  </div>
</BaseGrid>

<!-- Debug Panel: Shows all data as a grid with headlines from document keys and a Select button at the start of each row -->
<div class="debug-panel" style="margin-top:1rem; background:#fff; border:1px solid #102a54; padding:1em; border-radius:6px; font-size:0.95em; overflow-x:auto;">
  <div style="margin-top:0.7em;">
    {#if dedupedProcessedHours.length > 0}
      <table class="debug-table" style="border-collapse:collapse; min-width:900px;">
        <thead>
          <tr>
            <th style="border:1px solid #e0e7ef; padding:0.3em 0.6em; background:#f8fafc; color:#102a54; font-weight:600;">Select</th>
            {#each allKeys as key}
              <th style="border:1px solid #e0e7ef; padding:0.3em 0.6em; background:#f8fafc; color:#102a54; font-weight:600;">{key}</th>
            {/each}
          </tr>
        </thead>
        <tbody>
          {#each dedupedProcessedHours as row}
            <tr>
              <td style="border:1px solid #e0e7ef; padding:0.3em 0.6em; background:#fff;">
                <button class="action-button" type="button" on:click={() => goto(`/actual-computer-hours/${row._id}`)}>Select</button>
              </td>
              {#each allKeys as key}
                <td style="border:1px solid #e0e7ef; padding:0.3em 0.6em; color:#102a54; background:#fff;">
                  {typeof row[key] === 'object' && row[key] !== null ? JSON.stringify(row[key]) : row[key]}
                </td>
              {/each}
            </tr>
          {/each}
        </tbody>
      </table>
    {:else}
      <div>No data to display.</div>
    {/if}
  </div>
</div>

<style>
.ach-row {
  display: grid;
  gap: 0.5rem;
  align-items: center;
  color: #102a54; /* dark blue text */
  background: #fff !important;
}
.ach-cell {
  padding: 0.6em 0.8em;
  color: #102a54; /* dark blue text */
  background: #fff !important;
  font-size: 1em;
}
.ach-header {
  font-weight: bold;
  color: #102a54;
  background: #fff !important;
}
.date-time-cell {
  /* additional styling for date/time cells if needed */
}
.action-button {
  background: #fff;
  color: #102a54;
  border: 1px solid #102a54;
  border-radius: 5px;
  font-weight: 600;
  padding: 0.4em 1em;
  font-size: 1em;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.action-button:hover {
  background: #102a54;
  color: #fff;
}
.debug-panel {
  color: #102a54;
  background: #fff !important;
  border: 1px solid #102a54;
}
</style>
