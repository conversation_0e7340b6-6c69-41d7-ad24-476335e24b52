<script>
  import { enhance } from '$app/forms';
  import { goto } from '$app/navigation';
  import { CUSTOMER_TYPES, CUSTOMER_DIVISIONS, DEFAULT_CUSTOMER_DIVISION, VERSATILE_OPTIONS } from '$lib/constants';
  import { onMount } from 'svelte';

  /** @type {import('./$types').PageData} */
  export let data;

  // Get item data from page props
  const itemId = data.itemId;
  const collection = data.collection || 'Customers';
  let item = data.item;
  let relatedItems = data.relatedItems || {};

  // Get related items data
  const computers = relatedItems.computers || [];
  const computerCount = relatedItems.computerCount || 0;
  const serviceContracts = relatedItems.serviceContracts || [];
  const serviceContractCount = relatedItems.serviceContractCount || 0;

  // Initialize form data with item data
  let formData = {
    _id: item?._id || '',
    companyName: item?.companyName || '',
    email: item?.email || '',
    phone: item?.phone || '',
    address: item?.address || '',
    city: item?.city || '',
    country: item?.country || '',
    type: item?.type || 'Retail',
    division: item?.division || DEFAULT_CUSTOMER_DIVISION,
    versatile: item?.versatile || [],
    notes: item?.notes || ''
  };

  // State variables for editing and sublists
  let showForm = false;
  let showDeleteConfirm = false;
  /** @type {string|null} */
  let activeSublist = null; // 'computers', 'serviceContracts', or 'future'
  let showSublistDetail = false;
  /** @type {Object|null} */
  let selectedSubItem = null;

  // Modal state for adding new items
  let showAddComputerForm = false;
  let showAddContractForm = false;
  let isSubmitting = false;
  let formError = '';

  // New computer form data
  let newComputer = {
    name: '',
    type: 'Desktop',
    serialNumber: '',
    model: '',
    manufacturer: '',
    operatingSystem: '',
    purchaseDate: '',
    warrantyEndDate: '',
    notes: ''
  };

  // New service contract form data
  let newContract = {
    name: '',
    type: 'Standard',
    startDate: '',
    endDate: '',
    value: '',
    status: 'Active',
    notes: ''
  };

  // Computer types for dropdown
  const computerTypes = [
    'Desktop',
    'Laptop',
    'Server',
    'Workstation',
    'Tablet',
    'Other'
  ];

  // Contract types for dropdown
  const contractTypes = [
    'Standard',
    'Premium',
    'Basic',
    'Custom'
  ];

  // Contract statuses for dropdown
  const contractStatuses = [
    'Active',
    'Pending',
    'Expired',
    'Cancelled'
  ];

  // Form handling
  function editItem() {
    formData = { ...item };
    showForm = true;
  }

  function cancelEdit() {
    showForm = false;
  }

  // Sublist handling
  /**
   * Toggle the active sublist
   * @param {string} sublistName - Name of the sublist to toggle
   */
  function toggleSublist(sublistName) {
    if (activeSublist === sublistName) {
      activeSublist = null;
    } else {
      activeSublist = sublistName;
      showSublistDetail = false;
      selectedSubItem = null;
    }
  }

  /**
   * View a sublist item's details
   * @param {Object} subItem - The sublist item to view
   */
  function viewSubItem(subItem) {
    // Navigate to the SubListStd1OnePage with the appropriate parameters
    goto(`/subliststd1-one-page?collection=${subItem.collection || 'CustomerComputers'}&itemId=${subItem._id}&parentCollection=${collection}&parentId=${itemId}`);
  }

  function closeSubItemDetail() {
    showSublistDetail = false;
    selectedSubItem = null;
  }

  // Show modal for adding a new computer
  function showAddComputerModal() {
    showAddComputerForm = true;
    newComputer = {
      name: '',
      type: 'Desktop',
      serialNumber: '',
      model: '',
      manufacturer: '',
      operatingSystem: '',
      purchaseDate: '',
      warrantyEndDate: '',
      notes: ''
    };
    formError = '';
  }

  // Close computer modal
  function closeAddComputerModal() {
    showAddComputerForm = false;
  }

  // Show modal for adding a new service contract
  function showAddContractModal() {
    showAddContractForm = true;
    newContract = {
      name: '',
      type: 'Standard',
      startDate: '',
      endDate: '',
      value: '',
      status: 'Active',
      notes: ''
    };
    formError = '';
  }

  // Close contract modal
  function closeAddContractModal() {
    showAddContractForm = false;
  }

  // Add a new computer
  async function addComputer() {
    try {
      isSubmitting = true;
      formError = '';

      // Validate required fields
      if (!newComputer.name || !newComputer.serialNumber) {
        formError = 'Name and Serial Number are required';
        isSubmitting = false;
        return;
      }

      // Create computer object with customer ID
      const computerData = {
        ...newComputer,
        customerId: itemId
      };

      // Send to API
      const response = await fetch('/api/customer-computers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(computerData)
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // Refresh the page to show the new computer
        window.location.reload();
      } else {
        formError = result.error || 'Failed to add computer';
      }
    } catch (err) {
      console.error('Error adding computer:', err);
      formError = err instanceof Error ? err.message : 'An error occurred';
    } finally {
      isSubmitting = false;
    }
  }

  // Add a new service contract
  async function addServiceContract() {
    try {
      isSubmitting = true;
      formError = '';

      // Validate required fields
      if (!newContract.name || !newContract.startDate) {
        formError = 'Name and Start Date are required';
        isSubmitting = false;
        return;
      }

      // Create contract object with customer ID
      const contractData = {
        ...newContract,
        customerId: itemId
      };

      // Send to API
      const response = await fetch('/api/service-contracts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(contractData)
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // Refresh the page to show the new contract
        window.location.reload();
      } else {
        formError = result.error || 'Failed to add service contract';
      }
    } catch (err) {
      console.error('Error adding service contract:', err);
      formError = err instanceof Error ? err.message : 'An error occurred';
    } finally {
      isSubmitting = false;
    }
  }

  // Format date for input fields
  function formatDateForInput(dateStr) {
    if (!dateStr) return '';
    try {
      const date = new Date(dateStr);
      return date.toISOString().split('T')[0];
    } catch (e) {
      return '';
    }
  }
</script>

<div class="one-page-container">
  <!-- Header with navigation and actions -->
  <div class="header-bar">
    <button
      class="btn-secondary"
      on:click={() => goto(`/liststd1?collection=${collection}`)}
      aria-label="Back to list"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
      </svg>
      Back to List
    </button>

    <h1 class="page-title">{showForm ? 'Edit Item' : item?.companyName}</h1>

    <div class="header-actions">
      {#if !showForm}
        <button
          class="btn-primary"
          on:click={editItem}
        >
          Edit
        </button>
        <button
          class="btn-danger"
          on:click={() => showDeleteConfirm = true}
        >
          Delete
        </button>
      {:else}
        <div class="edit-actions">
          <button
            class="btn-secondary"
            on:click={cancelEdit}
          >
            Cancel
          </button>
          <button
            type="submit"
            class="btn-primary"
            form="edit-form"
          >
            Save Changes
          </button>
        </div>
      {/if}
    </div>
  </div>

  <!-- Main content area -->
  {#if item}
    <div class="info-card">
      <div class="card-header">
        <h2>Customer Information</h2>
      </div>
      <div class="card-content">
        <!-- Single unified grid layout -->
        <div class="unified-grid">
          <!-- Left column -->
          <div class="grid-column">
            <!-- Company Name -->
            {#if !showForm}
              <div class="detail-item">
                <span class="label">Company</span>
                <span class="value">{item.companyName}</span>
              </div>
            {:else}
              <div class="form-group">
                <label for="companyName">Company</label>
                <input
                  type="text"
                  id="companyName"
                  name="companyName"
                  bind:value={formData.companyName}
                  required
                />
              </div>
            {/if}

            <!-- Email -->
            {#if !showForm}
              <div class="detail-item">
                <span class="label">Email</span>
                <span class="value">{item.email || 'Not provided'}</span>
              </div>
            {:else}
              <div class="form-group">
                <label for="email">Email</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  bind:value={formData.email}
                />
              </div>
            {/if}

            <!-- Phone -->
            {#if !showForm}
              <div class="detail-item">
                <span class="label">Phone</span>
                <span class="value">{item.phone || 'Not provided'}</span>
              </div>
            {:else}
              <div class="form-group">
                <label for="phone">Phone</label>
                <input
                  type="text"
                  id="phone"
                  name="phone"
                  bind:value={formData.phone}
                />
              </div>
            {/if}
          </div>

          <!-- Middle column -->
          <div class="grid-column">
            <!-- Address -->
            {#if !showForm}
              <div class="detail-item">
                <span class="label">Address</span>
                <span class="value">{item.address || 'Not provided'}</span>
              </div>
            {:else}
              <div class="form-group">
                <label for="address">Address</label>
                <input
                  type="text"
                  id="address"
                  name="address"
                  bind:value={formData.address}
                />
              </div>
            {/if}

            <!-- City -->
            {#if !showForm}
              <div class="detail-item">
                <span class="label">City</span>
                <span class="value">{item.city || 'Not provided'}</span>
              </div>
            {:else}
              <div class="form-group">
                <label for="city">City</label>
                <input
                  type="text"
                  id="city"
                  name="city"
                  bind:value={formData.city}
                />
              </div>
            {/if}

            <!-- Country -->
            {#if !showForm}
              <div class="detail-item">
                <span class="label">Country</span>
                <span class="value">{item.country || 'Not provided'}</span>
              </div>
            {:else}
              <div class="form-group">
                <label for="country">Country</label>
                <input
                  type="text"
                  id="country"
                  name="country"
                  bind:value={formData.country}
                />
              </div>
            {/if}
          </div>

          <!-- Right column -->
          <div class="grid-column">
            <!-- Type -->
            {#if !showForm}
              <div class="detail-item">
                <span class="label">Type</span>
                <span class="value">{item.type}</span>
              </div>
            {:else}
              <div class="form-group">
                <label for="type">Type</label>
                <select
                  id="type"
                  name="type"
                  bind:value={formData.type}
                >
                  {#each CUSTOMER_TYPES as type}
                    <option value={type}>{type}</option>
                  {/each}
                </select>
              </div>
            {/if}

            <!-- Division -->
            {#if !showForm}
              <div class="detail-item">
                <span class="label">Division</span>
                <span class="value">{item.division}</span>
              </div>
            {:else}
              <div class="form-group">
                <label for="division">Division</label>
                <select
                  id="division"
                  name="division"
                  bind:value={formData.division}
                >
                  {#each CUSTOMER_DIVISIONS as division}
                    <option value={division}>{division}</option>
                  {/each}
                </select>
              </div>
            {/if}

            <!-- Versatile Options -->
            {#if !showForm}
              {#if item.versatile && item.versatile.length > 0}
                <div class="detail-item">
                  <span class="label">Versatile</span>
                  <span class="value">{item.versatile.join(', ')}</span>
                </div>
              {/if}
            {:else}
              <div class="form-group">
                <label for="versatile">Versatile</label>
                <select
                  id="versatile"
                  name="versatile"
                  multiple
                  bind:value={formData.versatile}
                  class="compact-select"
                >
                  {#each VERSATILE_OPTIONS as option}
                    <option value={option}>{option}</option>
                  {/each}
                </select>
              </div>
            {/if}
          </div>
        </div>

        <!-- Notes section (full width) -->
        <div class="notes-section">
          {#if !showForm}
            <div class="detail-item notes-item">
              <span class="label">Notes</span>
              <span class="value notes-value">{item.notes || 'No notes available'}</span>
            </div>
          {:else}
            <div class="form-group notes-group">
              <label for="notes">Notes</label>
              <textarea
                id="notes"
                name="notes"
                rows="3"
                bind:value={formData.notes}
                class="compact-textarea"
              ></textarea>
            </div>
          {/if}
        </div>

        <!-- Related Items Section -->
        <div class="related-items-section">
          <h3 class="section-title">Related Items</h3>

          <div class="sublist-buttons">
            <!-- Computers Button -->
            <button
              class="sublist-btn {activeSublist === 'computers' ? 'active' : ''}"
              on:click={() => toggleSublist('computers')}
            >
              <span class="btn-label">Computers</span>
              <span class="btn-count">{computerCount}</span>
            </button>

            <!-- ServiceContracts Button -->
            <button
              class="sublist-btn {activeSublist === 'serviceContracts' ? 'active' : ''}"
              on:click={() => toggleSublist('serviceContracts')}
            >
              <span class="btn-label">Service Contracts</span>
              <span class="btn-count">{serviceContractCount}</span>
            </button>

            <!-- Future Use Button (Placeholder) -->
            <button
              class="sublist-btn {activeSublist === 'future' ? 'active' : ''}"
              on:click={() => toggleSublist('future')}
            >
              <span class="btn-label">Future Use</span>
              <span class="btn-count">0</span>
            </button>
          </div>

          <!-- Sublist Content Area -->
          {#if activeSublist}
            <div class="sublist-container">
              <div class="sublist-header">
                <h4 class="sublist-title">
                  {#if activeSublist === 'computers'}
                    Customer Computers
                  {:else if activeSublist === 'serviceContracts'}
                    Service Contracts
                  {:else}
                    Future Items
                  {/if}
                </h4>

                <div class="sublist-actions">
                  {#if !showSublistDetail}
                    {#if activeSublist === 'computers'}
                      <button class="btn-primary btn-sm" on:click={showAddComputerModal}>
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <line x1="12" y1="5" x2="12" y2="19"></line>
                          <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                        Add Computer
                      </button>
                    {:else if activeSublist === 'serviceContracts'}
                      <button class="btn-primary btn-sm" on:click={showAddContractModal}>
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <line x1="12" y1="5" x2="12" y2="19"></line>
                          <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                        Add Contract
                      </button>
                    {/if}
                    <button class="btn-secondary btn-sm" on:click={() => activeSublist = null}>
                      Close
                    </button>
                  {/if}
                </div>
              </div>

              {#if !showSublistDetail}
                <!-- Sublist Items -->
                <div class="sublist-items">
                  {#if activeSublist === 'computers'}
                    {#if computers.length > 0}
                      {#each computers as computer}
                        <button
                          class="sublist-item"
                          on:click={() => viewSubItem(computer)}
                          aria-label="View {computer.name || 'Computer'} details"
                        >
                          <span class="item-name">{computer.name || 'Computer'}</span>
                          <span class="item-detail">{computer.type || 'Unknown'} | SN: {computer.serialNumber || 'N/A'}</span>
                        </button>
                      {/each}
                    {:else}
                      <div class="empty-state">No computers found for this customer</div>
                    {/if}
                  {:else if activeSublist === 'serviceContracts'}
                    {#if serviceContracts.length > 0}
                      {#each serviceContracts as contract}
                        <button
                          class="sublist-item"
                          on:click={() => viewSubItem(contract)}
                          aria-label="View {contract.name || 'Contract'} details"
                        >
                          <span class="item-name">{contract.name || 'Service Contract'}</span>
                          <span class="item-detail">{contract.type || 'Standard'} | Starts: {contract.startDate || 'N/A'}</span>
                        </button>
                      {/each}
                    {:else}
                      <div class="empty-state">No service contracts found for this customer</div>
                    {/if}
                  {:else}
                    <div class="empty-state">No items available yet</div>
                  {/if}
                </div>
              {:else}
                <!-- Sublist Item Detail View -->
                <div class="subitem-detail">
                  <div class="subitem-header">
                    <button class="btn-secondary btn-sm" on:click={closeSubItemDetail}>
                      Back to List
                    </button>
                    <h5 class="subitem-title">{selectedSubItem?.name}</h5>
                    <button class="btn-primary btn-sm">
                      Edit
                    </button>
                  </div>

                  <div class="subitem-content">
                    {#if activeSublist === 'computers'}
                      <div class="detail-row">
                        <span class="detail-label">Type:</span>
                        <span class="detail-value">{selectedSubItem?.type}</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">Serial Number:</span>
                        <span class="detail-value">{selectedSubItem?.serialNumber}</span>
                      </div>
                    {:else if activeSublist === 'serviceContracts'}
                      <div class="detail-row">
                        <span class="detail-label">Type:</span>
                        <span class="detail-value">{selectedSubItem?.type}</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">Start Date:</span>
                        <span class="detail-value">{selectedSubItem?.startDate}</span>
                      </div>
                    {/if}
                  </div>
                </div>
              {/if}
            </div>
          {/if}
        </div>

        <!-- Hidden form for submitting data -->
        {#if showForm}
          <form
            id="edit-form"
            class="hidden-form"
            action="?/updateItem"
            method="POST"
            use:enhance={() => {
              return async ({ result }) => {
                if (result.type === 'success') {
                  showForm = false;
                }
              };
            }}
          >
            <input type="hidden" name="_id" value={item._id} />

            <!-- Hidden inputs to submit all form data -->
            <input type="hidden" name="companyName" bind:value={formData.companyName} />
            <input type="hidden" name="email" bind:value={formData.email} />
            <input type="hidden" name="phone" bind:value={formData.phone} />
            <input type="hidden" name="address" bind:value={formData.address} />
            <input type="hidden" name="city" bind:value={formData.city} />
            <input type="hidden" name="country" bind:value={formData.country} />
            <input type="hidden" name="type" bind:value={formData.type} />
            <input type="hidden" name="division" bind:value={formData.division} />
            <input type="hidden" name="notes" bind:value={formData.notes} />

            <!-- Handle versatile options array -->
            {#each formData.versatile as option}
              <input type="hidden" name="versatile" value={option} />
            {/each}
          </form>
        {/if}
      </div>
    </div>
  {/if}

  <!-- Delete confirmation modal -->
  {#if showDeleteConfirm}
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h2 class="modal-title">Confirm Delete</h2>
          <button
            type="button"
            class="modal-close"
            on:click={() => showDeleteConfirm = false}
            aria-label="Close delete confirmation"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="modal-body">
          <p>Are you sure you want to delete this item? This action cannot be undone.</p>
          <p class="warning">This will permanently delete the item from the database.</p>
        </div>

        <div class="modal-actions">
          <form
            action="?/deleteItem"
            method="POST"
            use:enhance={() => {
              return async ({ result }) => {
                if (result.type === 'success') {
                  // Redirect back to list view
                  goto(`/liststd1?collection=${collection}`);
                }
              };
            }}
          >
            <input type="hidden" name="_id" value={item._id} />
            <button type="button" class="btn-secondary" on:click={() => showDeleteConfirm = false}>Cancel</button>
            <button type="submit" class="btn-danger">Delete</button>
          </form>
        </div>
      </div>
    </div>
  {/if}

  <!-- Add Computer Modal -->
  {#if showAddComputerForm}
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h2 class="modal-title">Add Computer</h2>
          <button
            type="button"
            class="modal-close"
            on:click={closeAddComputerModal}
            aria-label="Close add computer form"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="modal-body">
          {#if formError}
            <div class="error-message">{formError}</div>
          {/if}

          <div class="form-group">
            <label for="computerName">Name</label>
            <input
              type="text"
              id="computerName"
              bind:value={newComputer.name}
              required
            />
          </div>

          <div class="form-group">
            <label for="computerType">Type</label>
            <select
              id="computerType"
              bind:value={newComputer.type}
            >
              {#each computerTypes as type}
                <option value={type}>{type}</option>
              {/each}
            </select>
          </div>

          <div class="form-group">
            <label for="computerSerialNumber">Serial Number</label>
            <input
              type="text"
              id="computerSerialNumber"
              bind:value={newComputer.serialNumber}
              required
            />
          </div>

          <div class="form-group">
            <label for="computerModel">Model</label>
            <input
              type="text"
              id="computerModel"
              bind:value={newComputer.model}
            />
          </div>

          <div class="form-group">
            <label for="computerManufacturer">Manufacturer</label>
            <input
              type="text"
              id="computerManufacturer"
              bind:value={newComputer.manufacturer}
            />
          </div>

          <div class="form-group">
            <label for="computerOS">Operating System</label>
            <input
              type="text"
              id="computerOS"
              bind:value={newComputer.operatingSystem}
            />
          </div>

          <div class="form-group">
            <label for="computerPurchaseDate">Purchase Date</label>
            <input
              type="date"
              id="computerPurchaseDate"
              bind:value={newComputer.purchaseDate}
            />
          </div>

          <div class="form-group">
            <label for="computerWarrantyEndDate">Warranty End Date</label>
            <input
              type="date"
              id="computerWarrantyEndDate"
              bind:value={newComputer.warrantyEndDate}
            />
          </div>

          <div class="form-group">
            <label for="computerNotes">Notes</label>
            <textarea
              id="computerNotes"
              bind:value={newComputer.notes}
              rows="3"
            ></textarea>
          </div>
        </div>

        <div class="modal-actions">
          <button type="button" class="btn-secondary" on:click={closeAddComputerModal}>Cancel</button>
          <button type="button" class="btn-primary" on:click={addComputer} disabled={isSubmitting}>
            {isSubmitting ? 'Adding...' : 'Add Computer'}
          </button>
        </div>
      </div>
    </div>
  {/if}

  <!-- Add Service Contract Modal -->
  {#if showAddContractForm}
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h2 class="modal-title">Add Service Contract</h2>
          <button
            type="button"
            class="modal-close"
            on:click={closeAddContractModal}
            aria-label="Close add contract form"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="modal-body">
          {#if formError}
            <div class="error-message">{formError}</div>
          {/if}

          <div class="form-group">
            <label for="contractName">Name</label>
            <input
              type="text"
              id="contractName"
              bind:value={newContract.name}
              required
            />
          </div>

          <div class="form-group">
            <label for="contractType">Type</label>
            <select
              id="contractType"
              bind:value={newContract.type}
            >
              {#each contractTypes as type}
                <option value={type}>{type}</option>
              {/each}
            </select>
          </div>

          <div class="form-group">
            <label for="contractStartDate">Start Date</label>
            <input
              type="date"
              id="contractStartDate"
              bind:value={newContract.startDate}
              required
            />
          </div>

          <div class="form-group">
            <label for="contractEndDate">End Date</label>
            <input
              type="date"
              id="contractEndDate"
              bind:value={newContract.endDate}
            />
          </div>

          <div class="form-group">
            <label for="contractValue">Value</label>
            <input
              type="number"
              id="contractValue"
              bind:value={newContract.value}
              step="0.01"
            />
          </div>

          <div class="form-group">
            <label for="contractStatus">Status</label>
            <select
              id="contractStatus"
              bind:value={newContract.status}
            >
              {#each contractStatuses as status}
                <option value={status}>{status}</option>
              {/each}
            </select>
          </div>

          <div class="form-group">
            <label for="contractNotes">Notes</label>
            <textarea
              id="contractNotes"
              bind:value={newContract.notes}
              rows="3"
            ></textarea>
          </div>
        </div>

        <div class="modal-actions">
          <button type="button" class="btn-secondary" on:click={closeAddContractModal}>Cancel</button>
          <button type="button" class="btn-primary" on:click={addServiceContract} disabled={isSubmitting}>
            {isSubmitting ? 'Adding...' : 'Add Service Contract'}
          </button>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  /* Base styles */
  .one-page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: #f8fafc;
    min-height: calc(100vh - 100px);
  }

  .header-bar {
    display: grid;
    grid-template-columns: auto 1fr auto;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.75rem 1rem;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .page-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
    text-align: center;
  }

  .header-actions {
    display: flex;
    gap: 0.5rem;
  }

  .edit-actions {
    display: flex;
    gap: 0.5rem;
  }

  /* Unified grid layout */
  .unified-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .grid-column {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .notes-section {
    margin-top: 1rem;
    grid-column: 1 / -1;
  }

  .info-card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin-bottom: 1rem;
  }

  .card-header {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f1f5f9;
    background-color: #f8fafc;
  }

  .card-header h2 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #334155;
  }

  .card-content {
    padding: 1rem;
  }

  /* Detail items */
  .detail-item {
    display: grid;
    grid-template-columns: 80px 1fr;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    align-items: start;
  }

  .detail-item:last-child {
    margin-bottom: 0;
  }

  .notes-item {
    grid-template-columns: 80px 1fr;
  }

  .label {
    font-weight: 500;
    color: #64748b;
    font-size: 0.875rem;
  }

  .value {
    color: #1e293b;
    word-break: break-word;
    font-size: 0.95rem;
  }

  .notes-value {
    white-space: pre-line;
  }

  /* Form styles */
  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    margin-bottom: 0.5rem;
  }

  .form-group:last-child {
    margin-bottom: 0;
  }

  .notes-group {
    margin-top: 0.5rem;
  }

  label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #64748b;
  }

  input, select, textarea {
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #1e293b;
    background-color: #ffffff;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
  }

  .compact-select {
    height: 80px;
  }

  .compact-textarea {
    height: 80px;
    resize: vertical;
  }

  input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }

  .hidden-form {
    display: none;
  }

  /* Modal styles */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 1rem;
  }

  .modal-content {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
  }

  .modal-title {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
  }

  .modal-close {
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .modal-body {
    padding: 1.5rem;
  }

  .warning {
    color: #dc2626;
    margin-top: 0.5rem;
  }

  .modal-actions {
    display: flex;
    justify-content: flex-end;
    padding: 1rem;
    gap: 0.75rem;
    border-top: 1px solid #e2e8f0;
  }

  /* Button styles */
  .btn-primary, .btn-secondary, .btn-danger {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    font-size: 0.875rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
  }

  .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  .btn-primary {
    background-color: #3b82f6;
    color: white;
  }

  .btn-primary:hover {
    background-color: #2563eb;
  }

  .btn-secondary {
    background-color: #f1f5f9;
    color: #475569;
    border: 1px solid #e2e8f0;
  }

  .btn-secondary:hover {
    background-color: #e2e8f0;
  }

  .btn-danger {
    background-color: #ef4444;
    color: white;
  }

  .btn-danger:hover {
    background-color: #dc2626;
  }

  /* Related Items Section */
  .related-items-section {
    margin-top: 1.5rem;
    border-top: 1px solid #e2e8f0;
    padding-top: 1rem;
  }

  .section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #334155;
    margin: 0 0 0.75rem 0;
  }

  .sublist-buttons {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
  }

  .sublist-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #475569;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .sublist-btn:hover {
    background-color: #f1f5f9;
    border-color: #cbd5e1;
  }

  .sublist-btn.active {
    background-color: #eff6ff;
    border-color: #93c5fd;
    color: #2563eb;
  }

  .btn-count {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #e2e8f0;
    color: #475569;
    border-radius: 9999px;
    min-width: 1.5rem;
    height: 1.5rem;
    padding: 0 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
  }

  .sublist-btn.active .btn-count {
    background-color: #bfdbfe;
    color: #1d4ed8;
  }

  /* Sublist Container */
  .sublist-container {
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
    overflow: hidden;
  }

  .sublist-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
  }

  .sublist-title {
    margin: 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: #334155;
  }

  .sublist-items {
    padding: 0.5rem;
  }

  .sublist-item {
    display: flex;
    flex-direction: column;
    padding: 0.75rem;
    border-radius: 0.375rem;
    background-color: #f8fafc;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.15s ease;
    border: 1px solid transparent;
    width: 100%;
    text-align: left;
  }

  .sublist-item:last-child {
    margin-bottom: 0;
  }

  .sublist-item:hover {
    background-color: #f1f5f9;
    border-color: #e2e8f0;
  }

  .item-name {
    font-weight: 500;
    color: #1e293b;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
  }

  .item-detail {
    color: #64748b;
    font-size: 0.75rem;
  }

  .empty-state {
    padding: 1.5rem;
    text-align: center;
    color: #64748b;
    font-size: 0.875rem;
  }

  /* Subitem Detail */
  .subitem-detail {
    padding: 0.75rem;
  }

  .subitem-header {
    display: grid;
    grid-template-columns: auto 1fr auto;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .subitem-title {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    text-align: center;
  }

  .subitem-content {
    display: grid;
    gap: 0.5rem;
  }

  .detail-row {
    display: grid;
    grid-template-columns: 120px 1fr;
    gap: 0.5rem;
    align-items: start;
  }

  .detail-label {
    font-weight: 500;
    color: #64748b;
    font-size: 0.75rem;
  }

  .detail-value {
    color: #1e293b;
    font-size: 0.875rem;
  }

  /* SVG icon styles */
  .h-5 {
    height: 1.25rem;
  }

  .w-5 {
    width: 1.25rem;
  }

  .h-6 {
    height: 1.5rem;
  }

  .w-6 {
    width: 1.5rem;
  }

  /* Error message */
  .error-message {
    background-color: #fee2e2;
    color: #b91c1c;
    padding: 0.75rem;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
  }
</style>
