import { json } from '@sveltejs/kit';
import type { RequestEvent } from '@sveltejs/kit';
import { MongoClient, ObjectId } from 'mongodb';

export async function POST({ request }: RequestEvent) {
    const uri = "mongodb://localhost:27017";
    const client = new MongoClient(uri);

    try {
        const { computerId } = await request.json();
        if (!computerId) {
            throw new Error('Computer ID is required');
        }

        await client.connect();
        const db = client.db("ServiceContracts");
        const CustomerComputers = db.collection("CustomerComputers");
        const Workload = db.collection("Workload");

        // Get the specific computer
        const computer = await CustomerComputers.findOne({ _id: new ObjectId(computerId) });
        if (!computer) {
            throw new Error('Computer not found');
        }

        const activities = {};

        // Validate required dates
        if (!computer.deliveryDate || !computer.warrantyEndDate) {
            throw new Error('Delivery date and warranty end date are required');
        }

        // Set order date activity if exists
        if (computer.orderDate) {
            const orderDate = new Date(computer.orderDate);
            const year = orderDate.getFullYear();
            const month = orderDate.getMonth() + 1;
            activities[`${year}-${month}`] = "order";

            // Update Workload collection for order month
            await Workload.updateOne(
                { 
                    computerId: new ObjectId(computerId),
                    year: year,
                    month: month
                },
                {
                    $set: {
                        activity: "order",
                        hours: 200,
                        hasFixed: false,
                        updatedAt: new Date()
                    }
                },
                { upsert: true }
            );
        }

        // Set purchase date activity if exists
        if (computer.purchaseDate) {
            const purchaseDate = new Date(computer.purchaseDate);
            const year = purchaseDate.getFullYear();
            const month = purchaseDate.getMonth() + 1;
            activities[`${year}-${month}`] = "purchase";

            // Update Workload collection for purchase month
            await Workload.updateOne(
                { 
                    computerId: new ObjectId(computerId),
                    year: year,
                    month: month
                },
                {
                    $set: {
                        activity: "purchase",
                        hours: 200,
                        hasFixed: false,
                        updatedAt: new Date()
                    }
                },
                { upsert: true }
            );
        }

        // Set delivery date activity
        const deliveryDate = new Date(computer.deliveryDate);
        const deliveryYear = deliveryDate.getFullYear();
        const deliveryMonth = deliveryDate.getMonth() + 1;
        activities[`${deliveryYear}-${deliveryMonth}`] = "delivery";

        // Update Workload collection for delivery month
        await Workload.updateOne(
            { 
                computerId: new ObjectId(computerId),
                year: deliveryYear,
                month: deliveryMonth
            },
            {
                $set: {
                    activity: "delivery",
                    hours: 200,
                    hasFixed: false,
                    updatedAt: new Date()
                }
            },
            { upsert: true }
        );

        // Set warranty period (from month after delivery until warranty end)
        const warrantyEnd = new Date(computer.warrantyEndDate);
        let currentDate = new Date(deliveryDate);
        currentDate.setMonth(currentDate.getMonth() + 1);
        currentDate.setDate(1);
        const warrantyEndDate = new Date(warrantyEnd);
        warrantyEndDate.setDate(1);

        while (currentDate <= warrantyEndDate) {
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth() + 1;
            activities[`${year}-${month}`] = "warranty";

            // Update Workload collection for warranty months
            await Workload.updateOne(
                { 
                    computerId: new ObjectId(computerId),
                    year: year,
                    month: month
                },
                {
                    $set: {
                        activity: "warranty",
                        hours: 200,
                        hasFixed: false,
                        updatedAt: new Date()
                    }
                },
                { upsert: true }
            );

            currentDate.setMonth(currentDate.getMonth() + 1);
        }

        // Calculate contract period
        if (computer.desiredContractLengthYears && computer.warrantyEndDate) {
            const warrantyEnd = new Date(computer.warrantyEndDate);
            const contractStart = new Date(warrantyEnd);
            const contractEnd = new Date(warrantyEnd);
            contractEnd.setFullYear(contractEnd.getFullYear() + computer.desiredContractLengthYears);

            let currentContractDate = new Date(contractStart);
            currentContractDate.setDate(1);
            contractEnd.setDate(1);

            while (currentContractDate <= contractEnd) {
                const year = currentContractDate.getFullYear();
                const month = currentContractDate.getMonth() + 1;
                activities[`${year}-${month}`] = "contract";

                // Update Workload collection for contract months
                await Workload.updateOne(
                    { 
                        computerId: new ObjectId(computerId),
                        year: year,
                        month: month
                    },
                    {
                        $set: {
                            activity: "contract",
                            hours: 200,
                            hasFixed: false,
                            updatedAt: new Date()
                        }
                    },
                    { upsert: true }
                );

                currentContractDate.setMonth(currentContractDate.getMonth() + 1);
            }
        }

        // Update the computer document with activities
        await CustomerComputers.updateOne(
            { _id: new ObjectId(computerId) },
            { 
                $set: { 
                    activities: activities,
                    updatedAt: new Date()
                } 
            }
        );

        // Get the updated computer data
        const updatedComputer = await CustomerComputers.findOne({ _id: new ObjectId(computerId) });
        if (!updatedComputer) {
            throw new Error('Failed to retrieve updated computer data');
        }

        // Get all workload data for this computer
        const workloadData = await Workload.find({ 
            computerId: new ObjectId(computerId) 
        }).toArray();

        // Calculate years range for grid
        const currentYear = new Date().getFullYear();
        const years = [currentYear - 1, currentYear, currentYear + 1, currentYear + 2];

        // Initialize workload grid
        const workloadGrid = {
            years: years,
            grid: {}
        };

        // Initialize grid with empty data
        years.forEach(year => {
            workloadGrid.grid[year] = {};
            for (let month = 1; month <= 12; month++) {
                workloadGrid.grid[year][month] = {
                    hours: 0,
                    hasFixed: false,
                    activity: null
                };
            }
        });

        // Populate grid with workload data
        workloadData.forEach(entry => {
            if (workloadGrid.grid[entry.year]) {
                workloadGrid.grid[entry.year][entry.month] = {
                    hours: entry.hours || 0,
                    hasFixed: entry.hasFixed || false,
                    activity: entry.activity || null
                };
            }
        });

        // Process workload data for summary
        const workloadSummary = workloadData.reduce((acc, entry) => {
            if (entry.activity) {
                if (!acc[entry.activity]) {
                    acc[entry.activity] = {
                        count: 0,
                        totalHours: 0,
                        months: []
                    };
                }
                acc[entry.activity].count++;
                acc[entry.activity].totalHours += entry.hours || 0;
                acc[entry.activity].months.push({
                    year: entry.year,
                    month: entry.month,
                    hours: entry.hours || 0,
                    hasFixed: entry.hasFixed || false
                });
            }
            return acc;
        }, {});

        return json({
            success: true,
            message: 'Workload dates updated successfully',
            computer: updatedComputer,
            workloadGrid,
            workloadSummary
        });
    } catch (err) {
        console.error('Error updating workload dates:', err);
        return json({
            success: false,
            message: err instanceof Error ? err.message : 'Failed to update workload dates'
        }, { status: 500 });
    } finally {
        await client.close();
    }
}
