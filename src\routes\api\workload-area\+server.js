import { json } from '@sveltejs/kit';
import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';

/** @typedef {import('@sveltejs/kit').RequestEvent} RequestEvent */

/**
 * Handles GET requests to fetch workload areas for a computer
 * @param {RequestEvent} event - The request event
 * @returns {Promise<Response>} JSON response with workload areas
 */
export async function GET(event) {
    const { url } = event;
    const computerId = url.searchParams.get('computerId');
    
    if (!computerId || !ObjectId.isValid(computerId)) {
        return json({ success: false, error: 'Invalid computer ID' }, { status: 400 });
    }
    
    try {
        const client = new MongoClient(uri);
        await client.connect();
        const db = client.db('ServiceContracts');
        const workloadAreaCollection = db.collection('WorkloadArea');
        
        // Get all workload areas for this computer
        const workloadAreas = await workloadAreaCollection.find({
            computerId: new ObjectId(computerId)
        }).sort({ name: 1 }).toArray();
        
        // Transform data for the response
        const transformedData = workloadAreas.map(area => ({
            id: area._id.toString(),
            computerId: area.computerId.toString(),
            name: area.name,
            description: area.description || '',
            createdAt: area.createdAt,
            updatedAt: area.updatedAt
        }));
        
        return json({ success: true, data: transformedData });
    } catch (error) {
        console.error('Error fetching workload areas:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return json({ success: false, error: errorMessage }, { status: 500 });
    }
}

/**
 * Handles POST requests to create or update workload areas
 * @param {RequestEvent} event - The request event
 * @returns {Promise<Response>} JSON response
 */
export async function POST(event) {
    const { request } = event;
    const client = new MongoClient(uri);
    
    try {
        await client.connect();
        const db = client.db('ServiceContracts');
        const workloadAreaCollection = db.collection('WorkloadArea');
        
        // Parse and validate request body
        let data;
        try {
            data = await request.json();
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return json({ 
                success: false, 
                error: 'Invalid JSON payload',
                details: errorMessage
            }, { status: 400 });
        }

        // Extract and validate required fields
        const { 
            id,
            computerId, 
            name,
            description = ''
        } = data;

        // Validate required fields with specific error messages
        const validationErrors = [];
        if (!computerId || !ObjectId.isValid(computerId)) {
            validationErrors.push('Invalid computer ID');
        }
        
        if (!name || name.trim() === '') {
            validationErrors.push('Name is required');
        }

        if (validationErrors.length > 0) {
            return json({ 
                success: false, 
                error: 'Validation failed',
                details: validationErrors 
            }, { status: 400 });
        }

        // Prepare workload area data
        const workloadAreaData = {
            computerId: new ObjectId(computerId),
            name: name.trim(),
            description: description.trim(),
            updatedAt: new Date()
        };

        let result;
        
        if (id && ObjectId.isValid(id)) {
            // Update existing area
            await workloadAreaCollection.updateOne(
                { _id: new ObjectId(id) },
                { $set: workloadAreaData }
            );
            
            // Fetch the updated document
            const updatedDoc = await workloadAreaCollection.findOne({ _id: new ObjectId(id) });
            
            if (!updatedDoc) {
                return json({ 
                    success: false, 
                    error: 'Failed to update workload area',
                    details: ['Failed to fetch updated workload area']
                });
            }
            
            result = {
                success: true,
                action: 'updated',
                data: {
                    id: updatedDoc._id.toString(),
                    computerId: updatedDoc.computerId.toString(),
                    name: updatedDoc.name,
                    description: updatedDoc.description || '',
                    updatedAt: updatedDoc.updatedAt,
                    createdAt: updatedDoc.createdAt
                }
            };
        } else {
            // Create new area
            const newAreaData = {
                ...workloadAreaData,
                createdAt: new Date()
            };
            
            const insertResult = await workloadAreaCollection.insertOne(newAreaData);
            const newDoc = await workloadAreaCollection.findOne({ _id: insertResult.insertedId });
            
            if (!newDoc) {
                return json({ 
                    success: false, 
                    error: 'Failed to create workload area',
                    details: ['Failed to fetch created workload area']
                });
            }
            
            result = {
                success: true,
                action: 'created',
                data: {
                    id: newDoc._id.toString(),
                    computerId: newDoc.computerId.toString(),
                    name: newDoc.name,
                    description: newDoc.description || '',
                    updatedAt: newDoc.updatedAt,
                    createdAt: newDoc.createdAt
                }
            };
        }
        
        return json(result);
    } catch (error) {
        console.error('Error handling workload area operation:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return json({ success: false, error: errorMessage }, { status: 500 });
    } finally {
        await client.close();
    }
}

/**
 * Handles DELETE requests to remove a workload area
 * @param {RequestEvent} event - The request event
 * @returns {Promise<Response>} JSON response
 */
export async function DELETE(event) {
    const { url } = event;
    const id = url.searchParams.get('id');
    
    if (!id || !ObjectId.isValid(id)) {
        return json({ success: false, error: 'Invalid workload area ID' }, { status: 400 });
    }
    
    try {
        const client = new MongoClient(uri);
        await client.connect();
        const db = client.db('ServiceContracts');
        const workloadAreaCollection = db.collection('WorkloadArea');
        
        // Delete the workload area
        const result = await workloadAreaCollection.deleteOne({ _id: new ObjectId(id) });
        
        if (result.deletedCount === 0) {
            return json({ 
                success: false, 
                error: 'Workload area not found' 
            }, { status: 404 });
        }
        
        return json({ 
            success: true, 
            message: 'Workload area deleted successfully' 
        });
    } catch (error) {
        console.error('Error deleting workload area:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return json({ success: false, error: errorMessage }, { status: 500 });
    }
}
