<script>
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { invalidateAll } from '$app/navigation';
	import QuotationRowsGrid from '$lib/components/grids/QuotationRowsGrid.svelte';

	export let data;

	let quoteRows = [];
	let loading = true;
	let computerId = $page.params.id;
	let quotationId = $page.params.quotationId;
	let errorMessage = '';
	let packageSummary = [];
	
	// Define the standard package options in the required order
	const packageOptions = [
		'Base Contract Offering [Fixed]',
		'Dealer Add-Ons [Fixed]',
		'Dealer Add-Ons [Variable/hr]',
		'Support (Self Service) [Fixed]',
		'Customer Package [Fixed]',
		'Customer Package- VODIA [Fixed]',
		'Support (Dealer) [Fixed]',
		'Support (Dealer) [Variable/hr]',
		'Retirement Plan [Variable/hr]'
	];

	// Function to calculate summary by package
	function calculatePackageSummary(rows) {
		// Group rows by package
		const packageGroups = {};
		
		rows.forEach(row => {
			// Only include rows where "Included" is "Yes"
			if (row.included !== "Yes") return;
			
			const packageName = row.package;
			
			if (!packageGroups[packageName]) {
				packageGroups[packageName] = {
					level: row.level,
					package: packageName,
					scos: 0,
					oemImport: 0,
					fleetOwner: 0,
					rrp: 0,
					labour: 0,
					parts: 0,
					customerPrice: 0,
					dealerPrice: 0,
					internalCost: 0
				};
			}
			
			// Add numeric values to sums
			packageGroups[packageName].scos += parseFloat(row.scos) || 0;
			packageGroups[packageName].oemImport += parseFloat(row.oemImport) || 0;
			packageGroups[packageName].fleetOwner += parseFloat(row.fleetOwner) || 0;
			packageGroups[packageName].rrp += parseFloat(row.rrp) || 0;
			
			// Add price fields
			packageGroups[packageName].customerPrice += parseFloat(row.CustomerPrice) || 0;
			packageGroups[packageName].dealerPrice += parseFloat(row.DealerPrice) || 0;
			packageGroups[packageName].internalCost += parseFloat(row.InternalCost) || 0;
			
			// Count labour and parts
			if (row.QuoteRowType === 'Labour') {
				packageGroups[packageName].labour += 1;
			} else if (row.QuoteRowType === 'Part') {
				packageGroups[packageName].parts += 1;
			}
		});
		
		// Convert to array and sort by packageOptions order
		return Object.values(packageGroups).sort((a, b) => {
			const indexA = packageOptions.indexOf(a.package);
			const indexB = packageOptions.indexOf(b.package);
			return indexA - indexB;
		});
	}

	// Format currency for display
	function formatCurrency(value) {
		if (value === undefined || value === null) return '£ 0.00';
		return `£ ${parseFloat(value).toFixed(2)}`;
	}
	
	// Format dollar currency for display
	function formatDollar(value) {
		if (value === undefined || value === null) return '$ 0.00';
		return `$ ${parseFloat(value).toFixed(2)}`;
	}

	onMount(async () => {
		try {
			const response = await fetch(`/api/quote-rows/by-quotation-id/${quotationId}`);
			if (response.ok) {
				quoteRows = await response.json();
				
				// Set default QuoteRowType if it doesn't exist
				quoteRows = quoteRows.map(row => {
					if (!row.QuoteRowType) {
						return { ...row, QuoteRowType: 'Labour' };
					}
					return row;
				});
				
				// Calculate summary after loading rows
				packageSummary = calculatePackageSummary(quoteRows);
			} else {
				errorMessage = await response.text();
				console.error('Failed to load quote rows:', errorMessage);
			}
		} catch (err) {
			errorMessage = err.message;
			console.error('Error loading quote rows:', err);
		} finally {
			loading = false;
		}
	});

	const handleView = (row) => {
		// Navigate to view page
		window.location.href = `/computer-id/${computerId}/computer-quote/${quotationId}/view/${row._id}`;
	};

	const handleEdit = (row) => {
		// Navigate to edit page
		window.location.href = `/computer-id/${computerId}/computer-quote/${quotationId}/edit/${row._id}`;
	};

	const handleDelete = async (row) => {
		if (confirm(`Are you sure you want to delete this quote row for ${row.service}?`)) {
			try {
				const response = await fetch(`/api/quote-rows/delete-by-id/${row._id}`, {
					method: 'DELETE'
				});
				
				if (response.ok) {
					// Remove item from array to update UI
					quoteRows = quoteRows.filter(item => item._id !== row._id);
					// Recalculate summary
					packageSummary = calculatePackageSummary(quoteRows);
				} else {
					alert('Failed to delete item');
					console.error('Delete failed:', await response.text());
				}
			} catch (err) {
				alert('Error deleting item');
				console.error('Delete error:', err);
			}
		}
	};
	
	// Function to update the "Included" field
	const updateIncluded = async (row, newValue) => {
		try {
			const response = await fetch(`/api/quote-rows/update-by-id/${row._id}`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					included: newValue
				})
			});
			
			if (response.ok) {
				// Update the row in the local array
				quoteRows = quoteRows.map(item => {
					if (item._id === row._id) {
						return { ...item, included: newValue };
					}
					return item;
				});
				
				// Recalculate summary if needed
				packageSummary = calculatePackageSummary(quoteRows);
			} else {
				alert('Failed to update item');
				console.error('Update failed:', await response.text());
			}
		} catch (err) {
			alert('Error updating item');
			console.error('Update error:', err);
		}
	};
	
	// Function to update the "QuoteRowType" field
	const updateQuoteRowType = async (row, newValue) => {
		try {
			const response = await fetch(`/api/quote-rows/update-by-id/${row._id}`, {
				method: 'PUT',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					QuoteRowType: newValue
				})
			});
			
			if (response.ok) {
				// Update the row in the local array
				quoteRows = quoteRows.map(item => {
					if (item._id === row._id) {
						return { ...item, QuoteRowType: newValue };
					}
					return item;
				});
				
				// Recalculate summary
				packageSummary = calculatePackageSummary(quoteRows);
			} else {
				alert('Failed to update item');
				console.error('Update failed:', await response.text());
			}
		} catch (err) {
			alert('Error updating item');
			console.error('Update error:', err);
		}
	};
	
	// Check if any row has part information
	$: hasParts = quoteRows.some(row => row.QuoteRowType === 'Part');

	// Handle navigation to create a new quote row
	const handleAddNew = () => {
		window.location.href = `/computer-id/${computerId}/computer-quote/${quotationId}/edit/new`;
	};

	// Function to duplicate a quote row
	const handleDuplicate = async (row) => {
		try {
			const response = await fetch(`/api/quote-rows/duplicate-by-id/${row._id}`, {
				method: 'POST'
			});
			
			if (response.ok) {
				const newRow = await response.json();
				quoteRows = [...quoteRows, newRow];
				// Recalculate summary
				packageSummary = calculatePackageSummary(quoteRows);
			} else {
				alert('Failed to duplicate item');
				console.error('Duplicate failed:', await response.text());
			}
		} catch (err) {
			alert('Error duplicating item');
			console.error('Duplicate error:', err);
		}
	};
</script>

<div class="computer-quote-container">
	<div class="header">
		<h1>Quote Management</h1>
		<div class="quote-info">
			<h2>Quotation ID: {quotationId}</h2>
		</div>
		
		{#if !loading && quoteRows.length > 0 && packageSummary.length > 0}
			<div class="summary-section">
				<h2>Quote Summary by Package</h2>
				<div class="summary-table">
					<div class="summary-header">
						<div class="cell level">Level</div>
						<div class="cell package">Package</div>
						<div class="cell scos">SCOS</div>
						<div class="cell oem">OEM Import</div>
						<div class="cell fleet">Fleet Owner</div>
						<div class="cell rrp">RRP</div>
						<div class="cell dollar">Customer ($)</div>
						<div class="cell dollar">Dealer ($)</div>
						<div class="cell dollar">Internal ($)</div>
						<div class="cell labour">Labour</div>
						<div class="cell parts">Parts</div>
					</div>
					{#each packageSummary as summary}
						<div class="summary-row">
							<div class="cell level">{summary.level}</div>
							<div class="cell package">{summary.package}</div>
							<div class="cell scos">{formatCurrency(summary.scos)}</div>
							<div class="cell oem">{formatCurrency(summary.oemImport)}</div>
							<div class="cell fleet">{formatCurrency(summary.fleetOwner)}</div>
							<div class="cell rrp">{formatCurrency(summary.rrp)}</div>
							<div class="cell dollar">{formatDollar(summary.customerPrice)}</div>
							<div class="cell dollar">{formatDollar(summary.dealerPrice)}</div>
							<div class="cell dollar">{formatDollar(summary.internalCost)}</div>
							<div class="cell labour">{summary.labour}</div>
							<div class="cell parts">{summary.parts}</div>
						</div>
					{/each}
				</div>
			</div>
		{/if}
		
		<div class="actions">
			<a href="/computer-id/{computerId}/quote-list" class="btn secondary">Back to Quote List</a>
			<button on:click={handleAddNew} class="btn primary">Add New Quote Row</button>
		</div>
	</div>

	{#if loading}
		<div class="loading">Loading quote data...</div>
	{:else if errorMessage}
		<div class="error">Error: {errorMessage}</div>
	{:else if quoteRows.length === 0}
		<div class="no-data">No quote rows found for this quotation.</div>
	{:else}
		<QuotationRowsGrid
			{quoteRows}
			{loading}
			{errorMessage}
			{hasParts}
			onUpdateIncluded={updateIncluded}
			onUpdateQuoteRowType={updateQuoteRowType}
			onEdit={handleEdit}
			onDuplicate={handleDuplicate}
			onDelete={handleDelete}
		/>
	{/if}
</div>

<style>
	.computer-quote-container {
		padding: 1rem;
		max-width: 100%;
		overflow-x: auto;
	}

	.header {
		display: flex;
		flex-direction: column;
		margin-bottom: 1.5rem;
	}

	.quote-info {
		margin-bottom: 1rem;
	}

	.actions {
		display: flex;
		gap: 1rem;
		margin-bottom: 1rem;
	}

	.btn {
		padding: 0.5rem 1rem;
		border-radius: 4px;
		text-decoration: none;
		font-weight: 500;
		cursor: pointer;
		border: none;
	}

	.primary {
		background-color: #3498db;
		color: white;
	}

	.secondary {
		background-color: #f1f1f1;
		color: #333;
	}

	/* Summary Table Styles */
	.summary-section {
		margin: 1rem 0 2rem 0;
		border: 1px solid #ddd;
		border-radius: 4px;
		padding: 1rem;
		background-color: #f9f9f9;
		max-width: 100%;
		margin-left: 0;
		margin-right: auto;
	}

	.summary-table {
		width: 100%;
		border-collapse: collapse;
	}

	.summary-header {
		display: grid;
		grid-template-columns: 60px 1fr 100px 120px 120px 100px 100px 100px 100px 80px 80px;
		background-color: #f1f1f1;
		font-weight: bold;
		border-bottom: 2px solid #ddd;
		padding: 0.5rem 0;
	}

	.summary-row {
		display: grid;
		grid-template-columns: 60px 1fr 100px 120px 120px 100px 100px 100px 100px 80px 80px;
		border-bottom: 1px solid #eee;
		padding: 0.5rem 0;
	}

	.summary-row:nth-child(even) {
		background-color: #f9f9f9;
	}

	.summary-row .cell {
		padding: 0.5rem 0.25rem;
	}

	.cell.level {
		text-align: center;
	}

	.cell.package {
		text-align: left;
	}

	.cell.scos, 
	.cell.oem, 
	.cell.fleet, 
	.cell.rrp,
	.cell.dollar {
		text-align: right;
	}
	
	.cell.labour,
	.cell.parts {
		text-align: center;
	}
	
	.cell.dollar {
		color: #0369a1;
		font-weight: 500;
	}

	/* Main table styles */
	.quote-table-header, .row {
		display: grid;
		grid-template-columns: 
			3rem      /* Level */
			minmax(7rem, 10%)   /* Package */ 
			5rem      /* Row Type */
			5rem      /* Service ID */
			minmax(10rem, 15%)  /* Service */
			5rem      /* Included */
			5rem      /* Required */
			5rem      /* SCOS */
			5rem      /* OEM Import */
			5rem      /* Fleet Owner */
			5rem      /* RRP */
			5rem      /* Customer $ */
			5rem      /* Dealer $ */
			5rem      /* Internal $ */
			5rem      /* Select Service */
			5rem      /* Qty per Yr */
			7rem;     /* Actions */
		gap: 0.25rem;
		padding: 0.25rem;
		align-items: center;
		border-bottom: 1px solid #e2e8f0;
	}
	
	.quote-table-header.has-parts, .row.has-parts {
		grid-template-columns: 
			3rem      /* Level */
			minmax(7rem, 10%)   /* Package */ 
			5rem      /* Row Type */
			7rem      /* Part Number */
			7rem      /* Part Name */
			3rem      /* Part Qty */
			5rem      /* Service ID */
			minmax(10rem, 15%)  /* Service */
			5rem      /* Included */
			5rem      /* Required */
			5rem      /* SCOS */
			5rem      /* OEM Import */
			5rem      /* Fleet Owner */
			5rem      /* RRP */
			5rem      /* Customer $ */
			5rem      /* Dealer $ */
			5rem      /* Internal $ */
			5rem      /* Select Service */
			5rem      /* Qty per Yr */
			7rem;     /* Actions */
	}
	
	.cell {
		padding: 0.25rem;
		font-size: 0.875rem;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	
	.quote-table-header .cell {
		font-weight: 600;
		text-align: center;
		color: #475569;
		background-color: #f1f5f9;
	}
	
	.row:nth-child(even) {
		background-color: #f8fafc;
	}
	
	.row:hover {
		background-color: #e9f5ff;
	}
	
	.row-type-cell select, .included-cell select {
		width: 100%;
		padding: 0.125rem 0.25rem;
		font-size: 0.75rem;
		border-radius: 0.25rem;
	}
	
	.dollar-column {
		color: #0369a1;
		font-weight: 500;
	}
	
	.action-buttons {
		display: flex;
		gap: 0.25rem;
		justify-content: center;
	}
	
	.btn-icon {
		padding: 0.25rem;
		border-radius: 0.25rem;
		background: none;
		border: none;
		cursor: pointer;
		font-size: 0.75rem;
	}
</style>
