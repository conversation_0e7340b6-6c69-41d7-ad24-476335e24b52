<script>
  import { createEventDispatcher } from 'svelte';

  // Props
  export let packages = [];
  export let title = '';
  export let showCosts = true;
  export let showOemImporter = true;
  export let showFleetOwner = true;
  export let showLevel = true;
  export let showRequired = true;
  export let currency = 'EUR';
  export let isPackageOffering = false; // New prop to distinguish package vs individual service tables
  export let showServiceDropdown = false; // New prop for service selection dropdown

  // Events
  const dispatch = createEventDispatcher();

  // Handle package selection
  function togglePackage(packageId) {
    dispatch('togglePackage', { id: packageId });
  }

  // Handle service selection from dropdown
  function selectService(packageId, serviceCode) {
    dispatch('selectService', { id: packageId, serviceCode });
  }

  // Format currency display
  function formatCurrency(amount) {
    if (!amount) return '0.00';
    return parseFloat(amount).toFixed(2);
  }

  // Check mark display
  function getCheckDisplay(value) {
    return value ? '✓' : '';
  }

  // Get background color for package offering levels
  function getPackageLevelColor(level) {
    switch(level?.toUpperCase()) {
      case 'A': return '#90EE90'; // Light green
      case 'B': return '#87CEEB'; // Sky blue
      case 'C': return '#DDA0DD'; // Plum
      default: return '#f5f5f5';
    }
  }
</script>

<div class="quotation-grid-container">
  {#if title}
    <div class="grid-title">{title}</div>
  {/if}

  <div class="quotation-grid" class:package-offering={isPackageOffering}>
    <!-- Package Offering Header (Simple) -->
    {#if isPackageOffering}
      <div class="package-header" role="rowgroup">
        <div class="column level" role="columnheader">Level</div>
        <div class="column package-name" role="columnheader">Package</div>
        <div class="column status" role="columnheader">Status</div>
      </div>
    {:else}
      <!-- Individual Service Header (Detailed) -->
      <div class="quotation-header" role="rowgroup">
        <div class="column level" role="columnheader">Level</div>
        <div class="column package" role="columnheader">Package</div>
        <div class="column service-id" role="columnheader">Service ID</div>
        <div class="column service" role="columnheader">Service</div>
        <div class="column included-cost" role="columnheader">Included in package cost</div>
        <div class="column required" role="columnheader">Required</div>
        {#if showServiceDropdown}
          <div class="column select-service" role="columnheader">Select Service (Dropdown)</div>
        {/if}
        <div class="column include-quote" role="columnheader">Include cost in quote</div>
        <div class="column cost" role="columnheader">Cost</div>
      </div>
    {/if}

    {#if packages.length > 0}
      {#each packages as pack}
        {#if isPackageOffering}
          <!-- Package Offering Row (Simple) -->
          <div class="package-row" role="row" style="background-color: {getPackageLevelColor(pack.level)}">
            <div class="column level" role="cell">{pack.level || pack.id}</div>
            <div class="column package-name" role="cell">{pack.name || pack.packageName}</div>
            <div class="column status" role="cell">{pack.status || 'N/A'}</div>
          </div>
        {:else}
          <!-- Individual Service Row (Detailed) -->
          <div class="quotation-row" role="row">
            <div class="column level" role="cell">{pack.level || pack.id}</div>
            <div class="column package" role="cell">{pack.packageName || pack.name}</div>
            <div class="column service-id" role="cell">{pack.serviceId || pack.serviceCode || pack.packLevel || ''}</div>
            <div class="column service" role="cell">{pack.ServiceActivity || pack.activity || pack.subname || ''}</div>
            <div class="column included-cost" role="cell">
              {#if pack.includedInPackage !== undefined}
                {getCheckDisplay(pack.includedInPackage)}
              {:else}
                {getCheckDisplay(pack.cost === 0)}
              {/if}
            </div>
            <div class="column required" role="cell">{pack.required ? 'Yes' : 'No'}</div>
            {#if showServiceDropdown}
              <div class="column select-service" role="cell">
                <select on:change={(e) => selectService(pack.id, e.target.value)}>
                  <option value="">Select Service</option>
                  <option value="parts-supply">Parts Supply on Request</option>
                  <option value="monitoring">Monitoring</option>
                  <option value="inspections">Inspections</option>
                  <option value="repair-parts">Repair Parts</option>
                  <option value="preventive">Preventive</option>
                  <option value="engine-health">Engine Health</option>
                  <option value="adjustments">Adjustments</option>
                  <option value="eats-mot">EATS MOT</option>
                  <option value="overhaul">Overhaul Service</option>
                  <option value="oil-sampling">Oil Sampling</option>
                  <option value="def-sampling">DEF Sampling Program</option>
                  <option value="technical-support">Technical Support</option>
                  <option value="full-service">Full Service & Repair</option>
                  <option value="replacement">Engine Replacement</option>
                </select>
              </div>
            {/if}
            <div class="column include-quote" role="cell">
              <label class="checkbox-container">
                <input
                  type="checkbox"
                  checked={pack.includeInOffer}
                  disabled={pack.required}
                  on:change={() => togglePackage(pack.id)}
                />
                <span class="checkmark"></span>
              </label>
            </div>
            <div class="column cost" role="cell">
              {#if pack.cost !== undefined && pack.cost !== null}
                {formatCurrency(pack.cost)} {currency}
              {:else}
                #REF!
              {/if}
            </div>
          </div>
        {/if}
      {/each}
    {:else}
      <div class="empty-message" role="row">
        <div role="cell">No packages available</div>
      </div>
    {/if}
  </div>
</div>

<style>
  /* CSS Grid-based layout, as per user preference */
  .quotation-grid-container {
    width: 100%;
    margin-bottom: 1.5rem;
  }

  .grid-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    background-color: #f5f5f5;
    border-radius: 4px 4px 0 0;
  }

  .quotation-grid {
    display: grid;
    grid-template-columns: 1fr;
    border: 1px solid #e0e0e0;
    border-radius: 0 0 4px 4px;
    overflow: hidden;
  }

  /* Package Offering Layout (Simple) */
  .package-header {
    display: grid;
    grid-template-columns: minmax(60px, 0.5fr) minmax(200px, 2fr) minmax(100px, 1fr);
    background-color: #f5f5f5;
    font-weight: 600;
    border-bottom: 2px solid #ddd;
  }

  .package-row {
    display: grid;
    grid-template-columns: minmax(60px, 0.5fr) minmax(200px, 2fr) minmax(100px, 1fr);
    border-bottom: 1px solid #e0e0e0;
    border: 1px solid #333;
  }

  /* Individual Service Layout (Detailed) */
  .quotation-header {
    display: grid;
    grid-template-columns: minmax(50px, 0.5fr) minmax(120px, 1fr) minmax(80px, 0.8fr) minmax(150px, 1.5fr) minmax(100px, 1fr) minmax(80px, 0.8fr) minmax(120px, 1fr) minmax(100px, 1fr) minmax(80px, 0.8fr);
    background-color: #f5f5f5;
    font-weight: 600;
    border-bottom: 2px solid #ddd;
  }

  .quotation-row {
    display: grid;
    grid-template-columns: minmax(50px, 0.5fr) minmax(120px, 1fr) minmax(80px, 0.8fr) minmax(150px, 1.5fr) minmax(100px, 1fr) minmax(80px, 0.8fr) minmax(120px, 1fr) minmax(100px, 1fr) minmax(80px, 0.8fr);
    border-bottom: 1px solid #e0e0e0;
  }

  /* Adjust grid when service dropdown is shown */
  .quotation-grid:not(.package-offering) .quotation-header:has(.select-service),
  .quotation-grid:not(.package-offering) .quotation-row:has(.select-service) {
    grid-template-columns: minmax(50px, 0.5fr) minmax(120px, 1fr) minmax(80px, 0.8fr) minmax(150px, 1.5fr) minmax(100px, 1fr) minmax(80px, 0.8fr) minmax(150px, 1.2fr) minmax(100px, 1fr) minmax(80px, 0.8fr);
  }

  .quotation-row:nth-child(even) {
    background-color: #fafafa;
  }

  .quotation-row:hover {
    background-color: #f0f7ff;
  }

  .column {
    padding: 0.75rem 0.5rem;
    display: flex;
    align-items: center;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .column.required,
  .column.included-cost,
  .column.include-quote,
  .column.status {
    justify-content: center;
  }

  .column.cost {
    justify-content: flex-end;
    font-weight: 600;
  }

  .column.select-service select {
    width: 100%;
    padding: 0.25rem;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 0.875rem;
  }

  /* Responsive adjustments */
  @media (max-width: 1200px) {
    .quotation-header,
    .quotation-row {
      grid-template-columns: minmax(50px, 0.5fr) minmax(120px, 1fr) minmax(150px, 1.5fr) minmax(100px, 1fr) minmax(100px, 1fr) minmax(80px, 0.8fr);
    }

    .column.service-id,
    .column.included-cost,
    .column.select-service {
      display: none;
    }

    .package-header,
    .package-row {
      grid-template-columns: minmax(60px, 0.5fr) minmax(200px, 2fr);
    }

    .column.status {
      display: none;
    }
  }

  @media (max-width: 768px) {
    .quotation-header,
    .quotation-row {
      grid-template-columns: minmax(50px, 0.5fr) minmax(150px, 1.5fr) minmax(100px, 1fr) minmax(80px, 0.8fr);
    }

    .column.service,
    .column.required,
    .column.select-service {
      display: none;
    }

    .package-header,
    .package-row {
      grid-template-columns: 1fr;
    }
  }

  /* Checkbox styling */
  .checkbox-container {
    position: relative;
    display: block;
    width: 22px;
    height: 22px;
    cursor: pointer;
  }

  .checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
  }

  .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 22px;
    width: 22px;
    background-color: #eee;
    border: 1px solid #ddd;
    border-radius: 3px;
  }

  .checkbox-container:hover input ~ .checkmark {
    background-color: #ccc;
  }

  .checkbox-container input:checked ~ .checkmark {
    background-color: #2196F3;
    border-color: #2196F3;
  }

  .checkbox-container input:disabled ~ .checkmark {
    background-color: #f5f5f5;
    border-color: #ddd;
    cursor: not-allowed;
  }

  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
  }

  .checkbox-container input:checked ~ .checkmark:after {
    display: block;
  }

  .checkbox-container .checkmark:after {
    left: 7px;
    top: 3px;
    width: 6px;
    height: 12px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }

  .empty-message {
    grid-column: 1 / -1;
    padding: 2rem;
    text-align: center;
    color: #757575;
  }
</style>
