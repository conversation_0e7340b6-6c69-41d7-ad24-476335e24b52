<script>
  export let value;
  export let type = "date"; // "date" or "time"
  export let id = "";
  export let required = false;
  export let label = "";

  // Format handlers
  function formatDate(dateStr) {
    try {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return "";
      
      // Format as YYYY-MM-DD
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      
      return `${year}-${month}-${day}`;
    } catch (e) {
      console.error("Error formatting date:", e);
      return "";
    }
  }

  function formatTime(timeStr) {
    try {
      // If it's already in 24-hour format (HH:MM), return it
      if (/^([01]\d|2[0-3]):([0-5]\d)$/.test(timeStr)) {
        return timeStr;
      }
      
      // Try to parse and convert from various formats
      let hours = 0;
      let minutes = 0;
      
      // Handle formats like "9:27 AM" or "9:27AM"
      const amPmMatch = timeStr.match(/(\d+):(\d+)\s*(am|pm)?/i);
      if (amPmMatch) {
        hours = parseInt(amPmMatch[1], 10);
        minutes = parseInt(amPmMatch[2], 10);
        const isPm = amPmMatch[3]?.toLowerCase() === 'pm';
        
        // Convert to 24-hour format
        if (isPm && hours < 12) hours += 12;
        if (!isPm && hours === 12) hours = 0;
      } else {
        // Try to get current time components
        const now = new Date();
        hours = now.getHours();
        minutes = now.getMinutes();
      }
      
      // Format in 24-hour notation
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    } catch (e) {
      console.error("Error formatting time:", e);
      return "";
    }
  }

  // Initialize properly formatted value
  $: formattedValue = type === "date" ? formatDate(value) : formatTime(value);

  // Handle changes and maintain the format
  function handleChange(e) {
    const newValue = e.target.value;
    if (type === "date") {
      value = formatDate(newValue);
    } else {
      value = formatTime(newValue);
    }
  }

  // For direct input without using native date/time pickers
  let rawInput = type === "date" ? formatDate(value) : formatTime(value);
  
  function handleRawChange() {
    if (type === "date") {
      value = formatDate(rawInput);
    } else {
      value = formatTime(rawInput);
    }
  }
</script>

<div class="formatted-input">
  {#if label}
    <label for={id}>{label}</label>
  {/if}
  
  <div class="field-container">
    <!-- Date input with left-aligned guidance -->
    {#if type === "date"}
      <div class="guidance">Enter date as YYYY-MM-DD (example: 2025-04-22)</div>
      <div class="input-wrapper">
        <input 
          {id} 
          type="text" 
          bind:value={rawInput} 
          on:change={handleRawChange}
          on:blur={handleRawChange}
          {required}
          class="formatted-input"
          placeholder="YYYY-MM-DD"
          pattern="\d{4}-\d{2}-\d{2}"
        />
        <span class="format-hint">YYYY-MM-DD</span>
      </div>
    <!-- Time input with left-aligned guidance -->
    {:else}
      <div class="guidance">Enter time in 24-hour format (example: 14:30 not 2:30 PM)</div>
      <div class="input-wrapper">
        <input 
          {id} 
          type="text" 
          bind:value={rawInput} 
          on:change={handleRawChange}
          on:blur={handleRawChange}
          {required}
          class="formatted-input"
          placeholder="HH:MM"
          pattern="([01]\d|2[0-3]):([0-5]\d)"
        />
        <span class="format-hint">24-hour (HH:MM)</span>
      </div>
    {/if}
  </div>
</div>

<style>
  .formatted-input {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    width: 100%;
  }
  
  .field-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    align-items: center;
    width: 100%;
  }
  
  .guidance {
    font-size: 0.85rem;
    color: #666;
    line-height: 1.3;
    padding-right: 0.5rem;
  }
  
  .input-wrapper {
    position: relative;
    width: 100%;
  }
  
  input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 0.95rem;
  }
  
  .format-hint {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.75rem;
    color: #666;
    background-color: #f9f9f9;
    padding: 0 4px;
    pointer-events: none;
  }
</style>
