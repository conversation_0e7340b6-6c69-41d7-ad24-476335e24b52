<!-- QuotePageGrid.svelte -->
<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  
  // Optional props for customization
  export let gap: string = '1.5rem';
  export let padding: string = '1.5rem';
  export let backgroundColor: string = '#ffffff';
  export let className: string = '';
</script>

<BaseGrid 
  columns="1fr" 
  rows="auto auto 1fr" 
  {gap} 
  {padding} 
  {backgroundColor}
  className="quote-page-grid {className}"
>
  <div class="header-section">
    <slot name="header" />
  </div>
  
  <div class="info-section">
    <slot name="info" />
  </div>
  
  <div class="content-section">
    <slot name="content" />
  </div>
</BaseGrid>

<style>
  .header-section {
    grid-column: 1 / -1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  
  .info-section {
    grid-column: 1 / -1;
    width: 100%;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
  }
  
  .content-section {
    grid-column: 1 / -1;
    width: 100%;
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .header-section {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
  }
</style>
