import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/**
 * @typedef {Object} ServiceCodeAction
 * @property {string} _id
 * @property {string} ProductValidityGroup
 * @property {string} ActivityPurpose
 * @property {string} ServiceActivityLabel
 * @property {string} ServiceCode
 * @property {string} ActionType
 * @property {number} PartNumber
 * @property {string} UnitOfMeasure
 * @property {number} Quantity
 * @property {number} InternalNoOfHours
 * @property {number|null} InternalNoOfMonths
 */

/**
 * @type {import('./$types').PageServerLoad}
 */
export async function load({ url }) {
    try {
        await client.connect();
        const db = client.db('ServiceContracts');
        const computersCollection = db.collection('CustomerComputers');
        const productCollection = db.collection('ProductValidityGroup');
        const pvgCollection = db.collection('ProductValidityGroupPartNumber');
        const serviceElementsCollection = db.collection('ServiceCodeAndActionType');

        // Get filter parameters from URL
        const partNumber = url.searchParams.get('partNumber');
        const designation = url.searchParams.get('designation');

        console.log('Filter Parameters:', { partNumber, designation });

        // Build product query with proper type
        /**
         * @type {{ProductPartNumber?: number, ProductDesignation?: string}}
         */
        const productQuery = {};
        if (partNumber) {
            productQuery.ProductPartNumber = parseInt(partNumber);
        }
        if (designation) {
            productQuery.ProductDesignation = designation;
        }

        // Get filtered products with standard sort order
        const products = await productCollection.find(productQuery)
            .sort({
                ProductGroupId: 1,    // Primary sort
                Id: 1,               // Secondary sort
                ProductPartNumber: 1  // Final sort - unique per product
            })
            .toArray();

        // Get service elements using the correct algorithm
        /**
         * @type {ServiceCodeAction[]}
         */
        let serviceElements = [];
        
        if (designation || partNumber) {
            console.log('Step 1: Looking up in ProductValidityGroupPartNumber');
            
            // Step 1: Look up in ProductValidityGroupPartNumber collection
            /**
             * @type {{ProductDesignation?: string, ProductPartNumber?: number}}
             */
            const pvgQuery = {};
            if (designation) {
                pvgQuery.ProductDesignation = designation;
            }
            if (partNumber) {
                pvgQuery.ProductPartNumber = parseInt(partNumber);
            }
            console.log('Searching by:', pvgQuery);

            const pvgDoc = await pvgCollection.findOne(pvgQuery);
            console.log('Found PVG doc:', pvgDoc);

            if (pvgDoc && pvgDoc.ProductValidityGroup) {
                const validityGroup = pvgDoc.ProductValidityGroup;
                console.log('Step 2: Querying ServiceCodeAndActionType');
                console.log('Using ProductValidityGroup:', validityGroup);
                
                // Step 2: Query ServiceCodeAndActionType collection
                const items = await serviceElementsCollection.find({
                    ProductValidityGroup: validityGroup
                })
                .sort({ ProductValidityGroup: 1 })
                .toArray();

                console.log('Found service elements:', items.length);

                // Map items to safe objects with default values
                serviceElements = items.map(item => ({
                    _id: item._id.toString(),
                    ProductValidityGroup: item.ProductValidityGroup || '',
                    ActivityPurpose: item.ActivityPurpose || '',
                    ServiceActivityLabel: item.ServiceActivityLabel || '',
                    ServiceCode: item.ServiceCode || '',
                    ActionType: item.ActionType || '',
                    PartNumber: item.PartNumber || 0,
                    UnitOfMeasure: item.UnitOfMeasure || '',
                    Quantity: item.Quantity || 0,
                    InternalNoOfHours: item.InternalNoOfHours || 0,
                    InternalNoOfMonths: item.InternalNoOfMonths
                }));
            } else {
                console.log('No ProductValidityGroup found for the given criteria');
            }
        }

        // Get computers
        const computers = await computersCollection.find({}).toArray();

        return {
            computers: JSON.parse(JSON.stringify(computers)),
            products: JSON.parse(JSON.stringify(products)),
            serviceElements: serviceElements
        };
    } catch (error) {
        console.error('Error:', error instanceof Error ? error.message : 'Unknown error');
        return {
            computers: [],
            products: [],
            serviceElements: []
        };
    } finally {
        await client.close();
    }
}

/**
 * @type {import('./$types').Actions}
 */
export const actions = {
    default: async ({ request }) => {
        try {
            await client.connect();
            const db = client.db('ServiceContracts');
            const pvgCollection = db.collection('ProductValidityGroupPartNumber');
            const serviceElementsCollection = db.collection('ServiceCodeAndActionType');

            const formData = await request.formData();
            const productDesignation = formData.get('productDesignation')?.toString() || '';
            const productPartNumber = formData.get('productPartNumber')?.toString() || '';

            console.log('Step 1: Looking up in ProductValidityGroupPartNumber');
            
            // Step 1: Look up in ProductValidityGroupPartNumber collection
            /**
             * @type {{ProductDesignation?: string, ProductPartNumber?: number}}
             */
            const pvgQuery = {};
            if (productDesignation) {
                pvgQuery.ProductDesignation = productDesignation;
            }
            if (productPartNumber) {
                pvgQuery.ProductPartNumber = parseInt(productPartNumber);
            }
            console.log('Searching by:', pvgQuery);

            const pvgDoc = await pvgCollection.findOne(pvgQuery);
            console.log('Found PVG doc:', pvgDoc);

            if (!pvgDoc || !pvgDoc.ProductValidityGroup) {
                console.log('No ProductValidityGroup found');
                return {
                    status: 200,
                    data: {
                        items: [],
                        formData: { productDesignation, productPartNumber }
                    }
                };
            }

            const validityGroup = pvgDoc.ProductValidityGroup;
            console.log('Step 2: Querying ServiceCodeAndActionType');
            console.log('Using ProductValidityGroup:', validityGroup);

            // Step 2: Query ServiceCodeAndActionType collection
            const items = await serviceElementsCollection.find({
                ProductValidityGroup: validityGroup
            })
            .sort({ ProductValidityGroup: 1 })
            .toArray();

            console.log('Found service elements:', items.length);

            const safeItems = items.map(item => ({
                _id: item._id.toString(),
                ProductValidityGroup: item.ProductValidityGroup || '',
                ActivityPurpose: item.ActivityPurpose || '',
                ServiceActivityLabel: item.ServiceActivityLabel || '',
                ServiceCode: item.ServiceCode || '',
                ActionType: item.ActionType || '',
                PartNumber: item.PartNumber || 0,
                UnitOfMeasure: item.UnitOfMeasure || '',
                Quantity: item.Quantity || 0,
                InternalNoOfHours: item.InternalNoOfHours || 0,
                InternalNoOfMonths: item.InternalNoOfMonths
            }));

            return {
                status: 200,
                data: {
                    items: safeItems,
                    formData: { productDesignation, productPartNumber }
                }
            };
        } catch (error) {
            console.error('Error:', error instanceof Error ? error.message : 'Unknown error');
            return {
                status: 500,
                data: {
                    items: [],
                    formData: {},
                    error: error instanceof Error ? error.message : 'Unknown error'
                }
            };
        } finally {
            await client.close();
        }
    }
};
