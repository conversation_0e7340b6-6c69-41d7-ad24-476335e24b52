import serviceContractDB from './serviceContractDB.js';
const db = serviceContractDB.db;
const COLLECTION = 'ServiceID';

// Get all ServiceIDs (with optional filter)
export async function getServiceIDs(filter = {}, options = {}) {
  const defaultOptions = { sort: { name: 1 }, ...options };
  const cursor = db.collection(COLLECTION).find(filter, defaultOptions);
  const docs = await cursor.toArray();
  return docs.map(serviceContractDB.serializeDocument);
}

// Get a single ServiceID by ID
export async function getServiceIDById(id) {
  const objectId = serviceContractDB.createObjectId(id);
  if (!objectId) return null;
  const doc = await db.collection(COLLECTION).findOne({ _id: objectId });
  return doc ? serviceContractDB.serializeDocument(doc) : null;
}

// Create a new ServiceID
export async function createServiceID(data) {
  const now = new Date();
  const item = {
    ...data,
    createdAt: now,
    updatedAt: now
  };
  const result = await db.collection(COLLECTION).insertOne(item);
  return result.insertedId.toString();
}

// Update an existing ServiceID
export async function updateServiceID(id, updates) {
  const objectId = serviceContractDB.createObjectId(id);
  if (!objectId) return null;
  const updateData = { ...updates, updatedAt: new Date() };
  if (updateData._id) delete updateData._id;
  const result = await db.collection(COLLECTION).findOneAndUpdate(
    { _id: objectId },
    { $set: updateData },
    { returnDocument: 'after' }
  );
  return result.value ? serviceContractDB.serializeDocument(result.value) : null;
}

// Delete a ServiceID
export async function deleteServiceID(id) {
  const objectId = serviceContractDB.createObjectId(id);
  if (!objectId) return false;
  const result = await db.collection(COLLECTION).deleteOne({ _id: objectId });
  return result.deletedCount > 0;
}
