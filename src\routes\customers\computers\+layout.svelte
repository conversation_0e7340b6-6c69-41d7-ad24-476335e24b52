<script>
  import { page } from '$app/stores';
</script>

<div class="computers-layout">
  <div class="subnav">
    <a 
      href="/customers" 
      class="subnav-item"
    >
      ← Back to Customers
    </a>
  </div>

  <slot />
</div>

<style>
  .computers-layout {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .subnav {
    display: flex;
    gap: 0.5rem;
    padding: 0.5rem;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  }

  .subnav-item {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    text-decoration: none;
    color: #4b5563;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .subnav-item:hover {
    background: #f3f4f6;
  }
</style>
