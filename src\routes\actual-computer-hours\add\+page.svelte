<script lang="ts">
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  import FormGrid from '$lib/components/grids/FormGrid.svelte';
  import FormattedDateTimeInput from '$lib/components/FormattedDateTimeInput.svelte';
  import { onMount } from 'svelte';
  
  // Initialize with empty values
  let formData = {
    computerId: '',
    activity: 'Running',
    hasFixed: false,
    hours: 0,
    isIdle: false,
    ReportDate: new Date().toISOString().split('T')[0], // Store in YYYY-MM-DD format
    ReportTime: new Date().toTimeString().substr(0, 5)  // Store in 24h format (HH:MM)
  };

  let loading = false;
  let error = '';
  let success = false;
  let missingFields = false;
  let fetchingComputerId = false;

  // Get computer ID from URL query parameter if available
  onMount(async () => {
    const params = new URLSearchParams(window.location.search);
    const urlComputerId = params.get('computerId');
    
    if (urlComputerId) {
      // Immediately set the computerId from URL to give instant feedback
      formData.computerId = urlComputerId;
      
      fetchingComputerId = true;
      try {
        // If this looks like a document ID (not a computer ID), fetch the actual computerId
        if (urlComputerId.length === 24) {
          // Fetch the document to get the actual computerId field
          const res = await fetch(`/api/actual-computer-hours/${urlComputerId}`);
          if (res.ok) {
            const doc = await res.json();
            // Use the computerId field from the document, not the document ID
            if (doc && doc.computerId) {
              formData.computerId = doc.computerId;
            }
          }
        }
      } catch (err) {
        console.error('Error fetching computer details:', err);
      } finally {
        fetchingComputerId = false;
      }
    }
    // If coming from a specific computer page, also try to get ID from referrer
    else if (document.referrer && document.referrer.includes('/computer-id/')) {
      const matches = document.referrer.match(/\/computer-id\/([^\/]+)/);
      if (matches && matches[1]) {
        formData.computerId = matches[1];
      }
    }
  });

  function validateForm() {
    // Check for required field
    if (!formData.computerId) {
      missingFields = true;
      return false;
    }
    
    // Validate date format (YYYY-MM-DD)
    const datePattern = /^\d{4}-\d{2}-\d{2}$/;
    if (!datePattern.test(formData.ReportDate)) {
      error = "Date must be in YYYY-MM-DD format";
      return false;
    }
    
    // Validate time format (HH:MM in 24-hour)
    const timePattern = /^([01]\d|2[0-3]):([0-5]\d)$/;
    if (!timePattern.test(formData.ReportTime)) {
      error = "Time must be in 24-hour format (HH:MM)";
      return false;
    }
    
    missingFields = false;
    return true;
  }

  async function handleSave() {
    if (!validateForm()) {
      return;
    }
    
    loading = true;
    error = '';
    success = false;
    
    try {
      // Create a new document in ActualComputerHours
      const res = await fetch('/api/actual-computer-hours', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });
      
      if (!res.ok) {
        const data = await res.json().catch(() => ({}));
        throw new Error(data.error || 'Failed to save');
      }
      
      const result = await res.json();
      success = true;
      
      // Navigate to the new document after a short delay
      setTimeout(() => {
        goto(`/actual-computer-hours/${result.id}`);
      }, 1000);
    } catch (e) {
      console.error('Save error:', e);
      error = e?.message || 'Unknown error';
    } finally {
      loading = false;
    }
  }

  function handleBack() {
    history.back();
  }
</script>

<div class="ach-card">
  <button class="back-btn" on:click={handleBack}>Back</button>
  <h2>Add New Computer Hours</h2>
  
  {#if success}
    <div class="success-msg">Document added successfully! Redirecting...</div>
  {/if}
  
  {#if error}
    <div class="error-msg">{error}</div>
  {/if}
  
  {#if missingFields}
    <div class="error-msg">Missing required fields</div>
  {/if}
  
  {#if fetchingComputerId}
    <div class="info-msg">Loading computer details...</div>
  {/if}
  
  <FormGrid>
    <label for="computerId">Computer ID <span class="required">*</span></label>
    <input 
      id="computerId" 
      type="text" 
      bind:value={formData.computerId} 
      placeholder="ObjectId required"
      class={!formData.computerId && missingFields ? 'error-field' : ''} 
    />
    
    <label for="activity">Activity</label>
    <select id="activity" bind:value={formData.activity}>
      <option value="Running">Running</option>
      <option value="Idle">Idle</option>
      <option value="Maintenance">Maintenance</option>
      <option value="Offline">Offline</option>
    </select>
    
    <label for="ReportDate">Date</label>
    <input 
      id="ReportDate" 
      type="text" 
      bind:value={formData.ReportDate} 
      placeholder="YYYY-MM-DD"
    />
    
    <label for="ReportTime">Time</label>
    <input 
      id="ReportTime" 
      type="text" 
      bind:value={formData.ReportTime} 
      placeholder="HH:MM (24-hour)"
    />
    
    <label for="hours">Hours</label>
    <input id="hours" type="number" min="0" bind:value={formData.hours} />
    
    <label for="hasFixed">Has Fixed</label>
    <input id="hasFixed" type="checkbox" bind:checked={formData.hasFixed} />
    
    <label for="isIdle">Is Idle</label>
    <input id="isIdle" type="checkbox" bind:checked={formData.isIdle} />
  </FormGrid>
  
  <div class="ach-actions">
    <button class="save-btn" on:click={handleSave} disabled={loading}>
      {loading ? 'Saving...' : 'Save'}
    </button>
  </div>
</div>

<style>
  .ach-card {
    max-width: 480px;
    margin: 3em auto;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 18px rgba(0,0,0,0.08);
    padding: 2.5em 2em 2em 2em;
  }
  h2 {
    text-align: center;
    margin-bottom: 1.5rem;
  }
  .ach-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
  }
  .error-msg {
    color: #b00020;
    padding: 0.5rem;
    margin: 1rem 0;
    background-color: #ffebee;
    border-radius: 4px;
  }
  .success-msg {
    color: #388e3c;
    padding: 0.5rem;
    margin: 1rem 0;
    background-color: #e8f5e9;
    border-radius: 4px;
  }
  .info-msg {
    color: #1976d2;
    padding: 0.5rem;
    margin: 1rem 0;
    background-color: #e3f2fd;
    border-radius: 4px;
  }
  label {
    font-weight: 500;
    color: #222;
  }
  .required {
    color: #b00020;
  }
  .error-field {
    border-color: #b00020;
    background-color: #fff8f8;
  }
  .back-btn {
    background: #f3f3f3;
    color: #333;
    border: 1px solid #e3e7ee;
    border-radius: 5px;
    padding: 0.4em 1em;
    font-weight: 500;
    cursor: pointer;
    margin-bottom: 1em;
  }
  .save-btn {
    background: #2563eb;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-weight: 600;
    padding: 0.55em 1.3em;
    font-size: 1.06em;
    cursor: pointer;
    transition: background 0.15s;
  }
  .save-btn:hover {
    background: #1747b1;
  }
  .save-btn:disabled {
    background: #93b5f3;
    cursor: not-allowed;
  }
</style>
