import { MongoClient, ObjectId } from 'mongodb';

/**
 * Initialize the Calculations collection in MongoDB
 */
async function initCalculationsCollection() {
  const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017';
  const dbName = process.env.MONGODB_DB_NAME || 'ServiceContracts';
  
  console.log(`Connecting to MongoDB at ${uri}`);
  console.log(`Using database: ${dbName}`);
  
  const client = new MongoClient(uri);
  
  try {
    // Connect to MongoDB
    await client.connect();
    console.log('Connected successfully to MongoDB');
    
    // Get database
    const db = client.db(dbName);
    
    // Check if Calculations collection exists
    const collections = await db.listCollections({ name: 'Calculations' }).toArray();
    
    if (collections.length > 0) {
      console.log('Calculations collection already exists');
    } else {
      // Create Calculations collection
      await db.createCollection('Calculations');
      console.log('Created Calculations collection');
      
      // Create indexes
      const calculationsCollection = db.collection('Calculations');
      await calculationsCollection.createIndex({ 
        customerId: 1, 
        calculationPartNumberId: 1 
      }, { unique: true });
      console.log('Created indexes for Calculations collection');
    }
    
    // List all collections in the database
    const allCollections = await db.listCollections().toArray();
    console.log('Collections in database:');
    allCollections.forEach(collection => {
      console.log(` - ${collection.name}`);
    });
    
  } catch (error) {
    console.error('Error initializing Calculations collection:', error);
  } finally {
    // Close the connection
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the function
initCalculationsCollection();
