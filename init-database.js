import { MongoClient, ObjectId } from 'mongodb';

async function initDatabase() {
  try {
    // Connection URI and database name from mongo.js
    const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017';
    const dbName = process.env.MONGODB_DB_NAME || 'ssund';
    
    console.log(`Connecting to MongoDB at: ${uri}`);
    console.log(`Using database: ${dbName}`);
    
    // Create a new MongoClient
    const client = new MongoClient(uri);
    
    // Connect to the MongoDB server
    await client.connect();
    console.log('Successfully connected to MongoDB server!');
    
    // Get reference to the database
    const db = client.db(dbName);
    
    // Check if collections already exist
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);
    console.log('Existing collections:', collectionNames.length ? collectionNames.join(', ') : 'None');
    
    // Create Customers collection if it doesn't exist
    if (!collectionNames.includes('Customers')) {
      console.log('Creating Customers collection...');
      await db.createCollection('Customers');
      
      // Insert sample customers
      const customersCollection = db.collection('Customers');
      const customerResult = await customersCollection.insertMany([
        {
          _id: new ObjectId(),
          companyName: 'ABC Fleet Services',
          email: '<EMAIL>',
          phone: '+46 70 123 4567',
          address: 'Storgatan 1',
          city: 'Stockholm',
          country: 'Sweden',
          type: 'Fleet Owner',
          notes: 'Large fleet owner with 50+ vehicles',
          createdAt: new Date()
        },
        {
          _id: new ObjectId(),
          companyName: 'XYZ Import AB',
          email: '<EMAIL>',
          phone: '+46 8 987 6543',
          address: 'Importvägen 10',
          city: 'Gothenburg',
          country: 'Sweden',
          type: 'OEM Importer',
          notes: 'Main importer for heavy machinery',
          createdAt: new Date()
        },
        {
          _id: new ObjectId(),
          companyName: 'Retail Customer 1',
          email: '<EMAIL>',
          phone: '+46 73 111 2222',
          address: 'Kundgatan 5',
          city: 'Malmö',
          country: 'Sweden',
          type: 'Retail',
          notes: 'Regular retail customer',
          createdAt: new Date()
        }
      ]);
      console.log(`Inserted ${customerResult.insertedCount} sample customers`);
    }
    
    // Create CustomerComputers collection if it doesn't exist
    if (!collectionNames.includes('CustomerComputers')) {
      console.log('Creating CustomerComputers collection...');
      await db.createCollection('CustomerComputers');
      
      // Get customer IDs for reference
      const customersCollection = db.collection('Customers');
      const customers = await customersCollection.find({}).toArray();
      
      if (customers.length > 0) {
        // Insert sample computers
        const computersCollection = db.collection('CustomerComputers');
        const computerResult = await computersCollection.insertMany([
          {
            _id: new ObjectId(),
            customerId: customers[0]._id, // Use actual ObjectId from customer
            serialNumber: 'SN12345678',
            model: 'Model X1',
            manufactureDate: new Date('2023-01-15'),
            status: 'Active',
            notes: 'Main computer for fleet management',
            createdAt: new Date()
          },
          {
            _id: new ObjectId(),
            customerId: customers[0]._id, // Same customer, different computer
            serialNumber: 'SN87654321',
            model: 'Model X2',
            manufactureDate: new Date('2023-02-20'),
            status: 'Active',
            notes: 'Secondary computer for fleet management',
            createdAt: new Date()
          },
          {
            _id: new ObjectId(),
            customerId: customers[1]._id, // Different customer
            serialNumber: 'SN11223344',
            model: 'Model Y1',
            manufactureDate: new Date('2023-03-10'),
            status: 'Active',
            notes: 'Import management system',
            createdAt: new Date()
          }
        ]);
        console.log(`Inserted ${computerResult.insertedCount} sample computers`);
      }
    }
    
    // Create Sites collection if it doesn't exist
    if (!collectionNames.includes('Sites')) {
      console.log('Creating Sites collection...');
      await db.createCollection('Sites');
      
      // Get customer IDs for reference
      const customersCollection = db.collection('Customers');
      const customers = await customersCollection.find({}).toArray();
      
      if (customers.length > 0) {
        // Insert sample sites
        const sitesCollection = db.collection('Sites');
        const siteResult = await sitesCollection.insertMany([
          {
            _id: new ObjectId(),
            customerId: customers[0]._id, // Use actual ObjectId from customer
            name: 'Main Depot',
            address: 'Depotvägen 1',
            city: 'Stockholm',
            country: 'Sweden',
            isActive: true,
            createdAt: new Date()
          },
          {
            _id: new ObjectId(),
            customerId: customers[0]._id, // Same customer, different site
            name: 'Secondary Depot',
            address: 'Depotvägen 2',
            city: 'Uppsala',
            country: 'Sweden',
            isActive: true,
            createdAt: new Date()
          },
          {
            _id: new ObjectId(),
            customerId: customers[1]._id, // Different customer
            name: 'Import Warehouse',
            address: 'Hamnvägen 5',
            city: 'Gothenburg',
            country: 'Sweden',
            isActive: true,
            createdAt: new Date()
          }
        ]);
        console.log(`Inserted ${siteResult.insertedCount} sample sites`);
      }
    }

    // Create Regions collection if it doesn't exist
    if (!collectionNames.includes('Regions')) {
      console.log('Creating Regions collection...');
      await db.createCollection('Regions');
      
      // Insert sample Regions
      const RegionsCollection = db.collection('Regions');
      const regionResult = await RegionsCollection.insertMany([
        {
          _id: new ObjectId(),
          name: 'Northern Europe',
          country: 'Sweden',
          description: 'Nordic region including major Swedish industrial areas',
          createdAt: new Date()
        },
        {
          _id: new ObjectId(),
          name: 'Central Europe',
          country: 'Germany',
          description: 'Central European manufacturing hub',
          createdAt: new Date()
        },
        {
          _id: new ObjectId(),
          name: 'Southern Europe',
          country: 'Italy',
          description: 'Mediterranean industrial region',
          createdAt: new Date()
        }
      ]);
      console.log(`Inserted ${regionResult.insertedCount} sample Regions`);
    }
    
    // Check the collections after initialization
    const updatedCollections = await db.listCollections().toArray();
    console.log('\nCollections after initialization:');
    updatedCollections.forEach(coll => console.log(`  - ${coll.name}`));
    
    // Close the connection
    await client.close();
    console.log('\nMongoDB connection closed');
    console.log('Database initialization completed successfully!');
  } catch (err) {
    console.error('Error initializing database:', err);
  }
}

// Run the initialization
initDatabase();
