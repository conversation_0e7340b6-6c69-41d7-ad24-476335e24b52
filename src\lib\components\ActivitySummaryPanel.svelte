<script lang="ts">
  import BaseGrid from './grids/BaseGrid.svelte';
  
  // Props
  export let activities: any[] = [];
  export let year: number | null = null;
  
  // Computed values
  $: filteredActivities = year 
    ? activities.filter(a => a.year === year) 
    : activities;
    
  $: totalHours = filteredActivities.reduce((sum, activity) => 
    sum + (parseFloat(activity.hours) || 0), 0).toFixed(1);
    
  $: totalFixedActivities = filteredActivities.filter(a => a.hasFixed).length;
  
  $: monthlyActivities = getMonthlyStats(filteredActivities);
  
  // Calculate monthly statistics
  function getMonthlyStats(activities: any[]) {
    const months = Array(12).fill(0).map((_, i) => ({
      month: i + 1,
      name: getMonthName(i + 1),
      hours: 0,
      hasActivity: false
    }));
    
    activities.forEach(activity => {
      if (activity.month && activity.month >= 1 && activity.month <= 12) {
        const index = activity.month - 1;
        months[index].hours += parseFloat(activity.hours) || 0;
        months[index].hasActivity = true;
      }
    });
    
    return months;
  }
  
  function getMonthName(month: number): string {
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return monthNames[(month - 1) % 12];
  }
</script>

<BaseGrid>
  <div class="summary-panel">
    <header class="panel-header">
      <h2>{year ? `${year} Activity Summary` : 'Overall Activity Summary'}</h2>
    </header>
    
    <div class="panel-content">
      <div class="summary-grid">
        <div class="summary-card">
          <div class="card-value">{filteredActivities.length}</div>
          <div class="card-label">Total Activities</div>
        </div>
        
        <div class="summary-card">
          <div class="card-value">{totalHours}</div>
          <div class="card-label">Total Hours</div>
        </div>
        
        <div class="summary-card">
          <div class="card-value">{totalFixedActivities}</div>
          <div class="card-label">Fixed Activities</div>
        </div>
      </div>
      
      <h3>Monthly Breakdown</h3>
      
      <div class="monthly-grid">
        {#each monthlyActivities as monthData}
          <div class="month-card {monthData.hasActivity ? 'has-activity' : ''}">
            <div class="month-name">{monthData.name}</div>
            <div class="month-hours">{monthData.hours.toFixed(1)}</div>
          </div>
        {/each}
      </div>
    </div>
  </div>
</BaseGrid>

<style>
  .summary-panel {
    display: grid;
    grid-template-rows: auto 1fr;
    height: 100%;
    background: #1e293b;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .panel-header {
    padding: 1rem;
    background: #0f172a;
    border-bottom: 1px solid #334155;
  }
  
  .panel-header h2 {
    margin: 0;
    color: #60a5fa;
    font-size: 1.25rem;
  }
  
  .panel-content {
    padding: 1.5rem;
    overflow-y: auto;
  }
  
  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
  }
  
  .summary-card {
    background: #0f172a;
    border-radius: 8px;
    padding: 1.25rem 1rem;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  .card-value {
    font-size: 1.8rem;
    font-weight: 600;
    color: #60a5fa;
    margin-bottom: 0.5rem;
  }
  
  .card-label {
    color: #94a3b8;
    font-size: 0.875rem;
  }
  
  h3 {
    color: #e2e8f0;
    font-size: 1.1rem;
    margin: 0 0 1rem 0;
  }
  
  .monthly-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.75rem;
  }
  
  .month-card {
    background: #0f172a;
    border-radius: 6px;
    padding: 0.75rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-left: 3px solid #334155;
    transition: transform 0.2s;
  }
  
  .month-card.has-activity {
    border-left-color: #3b82f6;
  }
  
  .month-card:hover {
    transform: translateY(-2px);
  }
  
  .month-name {
    color: #94a3b8;
    font-size: 0.75rem;
    margin-bottom: 0.4rem;
  }
  
  .month-hours {
    color: #e2e8f0;
    font-weight: 600;
    font-size: 1.1rem;
  }
</style>
