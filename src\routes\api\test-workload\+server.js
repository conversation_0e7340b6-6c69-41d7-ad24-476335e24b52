import { json } from '@sveltejs/kit';
import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';

/** @type {import('./$types').RequestHandler} */
export async function GET({ url }) {
    const computerId = url.searchParams.get('computerId');
    
    if (!computerId || !ObjectId.isValid(computerId)) {
        return json({ success: false, error: 'Invalid computer ID' }, { status: 400 });
    }
    
    try {
        const client = new MongoClient(uri);
        await client.connect();
        const db = client.db('ServiceContracts');
        const workloadCollection = db.collection('Workload');
        
        // Get all workload entries for this computer
        const workloadData = await workloadCollection.find({
            computerId: new ObjectId(computerId)
        }).sort({ year: -1, month: -1 }).toArray();
        
        await client.close();
        
        return json({ 
            success: true, 
            data: workloadData.map(entry => ({
                id: entry._id.toString(),
                computerId: entry.computerId.toString(),
                year: entry.year,
                month: entry.month,
                hours: entry.hours,
                activity: entry.activity,
                type: entry.type,
                description: entry.description || '',
                createdAt: entry.createdAt,
                updatedAt: entry.updatedAt
            }))
        });
    } catch (error) {
        console.error('Error in test-workload endpoint:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return json({ success: false, error: errorMessage }, { status: 500 });
    }
}
