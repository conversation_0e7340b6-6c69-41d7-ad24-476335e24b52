// Script to create QuoteRow collection and populate it with data from the image
import { MongoClient, ObjectId } from 'mongodb';

// MongoDB connection URI
const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function createQuoteRow() {
  try {
    // Connect to MongoDB
    await client.connect();
    console.log('Connected to MongoDB');
    
    // Get the ServiceContracts database
    const db = client.db('ServiceContracts');
    
    // Check if the collection already exists
    const collections = await db.listCollections({ name: 'QuoteRow' }).toArray();
    if (collections.length > 0) {
      console.log('QuoteRow collection already exists. Dropping it before recreating...');
      await db.collection('QuoteRow').drop();
    }
    
    // Create the QuoteRow collection (Note: Using PascalCase for collection name per project conventions)
    const quoteRow = db.collection('QuoteRow');
    console.log('QuoteRow collection created');
    
    // Quotation ID to link all rows (reusing the same one from previous script)
    const quotationId = new ObjectId("67ee6411a10d20cfa4d5e43b");
    
    // Data from the image
    const rowsData = [
      // Base Contract Offering
      {
        quotationId: quotationId,
        level: 1,
        package: "Base Contract Offering",
        serviceId: "1.1",
        service: "Registration",
        included: "Yes",
        required: "Mandatory",
        scos: 100.00,
        oemImport: 100.00,
        fleetOwner: 100.00,
        rrp: 100.00,
        selectService: "Mandatory",
        qtyPerYr: 0
      },
      {
        quotationId: quotationId,
        level: 1,
        package: "Base Contract Offering",
        serviceId: "4.1",
        service: "Parts Supply, on Request",
        included: "Yes",
        required: "Yes",
        scos: 100.00,
        oemImport: 100.00,
        fleetOwner: 100.00,
        rrp: 100.00,
        selectService: "Yes",
        qtyPerYr: 0
      },
      {
        quotationId: quotationId,
        level: 1,
        package: "Base Contract Offering",
        serviceId: "3.2",
        service: "Active Monitoring",
        included: "Yes",
        required: "Mandatory",
        scos: 100.00,
        oemImport: 100.00,
        fleetOwner: 100.00,
        rrp: 100.00,
        selectService: "Mandatory",
        qtyPerYr: 0
      },
      
      // Dealer Add-Ons
      {
        quotationId: quotationId,
        level: 2,
        package: "Dealer Add-Ons",
        serviceId: "1.2",
        service: "Delivery Inspections",
        included: "No",
        required: "Yes",
        scos: 100.00,
        oemImport: 200.00,
        fleetOwner: 330.00,
        rrp: 300.00,
        selectService: "Yes",
        qtyPerYr: 1
      },
      {
        quotationId: quotationId,
        level: 2,
        package: "Dealer Add-Ons",
        serviceId: "8.2",
        service: "Repair Parts Only",
        included: "No",
        required: "Yes",
        scos: "100,00 kr",
        oemImport: "200,00 kr",
        fleetOwner: "300,00 kr",
        rrp: "400,00 kr",
        selectService: "Yes",
        qtyPerYr: 1
      },
      {
        quotationId: quotationId,
        level: 2,
        package: "Dealer Add-Ons",
        serviceId: "8.1",
        service: "Preventative Repair Parts Only",
        included: "No",
        required: "Yes",
        scos: 214.69,
        oemImport: 216.60,
        fleetOwner: 231.04,
        rrp: 288.80,
        selectService: "Yes",
        qtyPerYr: 1
      },
      {
        quotationId: quotationId,
        level: 2,
        package: "Dealer Add-Ons",
        serviceId: "4.3",
        service: "Engine Health Inspections",
        included: "No",
        required: "Yes",
        scos: 100.00,
        oemImport: 100.00,
        fleetOwner: 100.00,
        rrp: 100.00,
        selectService: "Yes",
        qtyPerYr: 1
      },
      {
        quotationId: quotationId,
        level: 2,
        package: "Dealer Add-Ons",
        serviceId: "4.4",
        service: "Valve Adjustments",
        included: "No",
        required: "Yes",
        scos: 200.00,
        oemImport: 200.00,
        fleetOwner: 200.00,
        rrp: 200.00,
        selectService: "Yes",
        qtyPerYr: 1
      },
      {
        quotationId: quotationId,
        level: 2,
        package: "Dealer Add-Ons",
        serviceId: "4.7",
        service: "EATS M.O.T",
        included: "No",
        required: "Yes",
        scos: 200.00,
        oemImport: 200.00,
        fleetOwner: 200.00,
        rrp: 200.00,
        selectService: "Yes",
        qtyPerYr: 1
      },
      {
        quotationId: quotationId,
        level: 2,
        package: "Dealer Add-Ons",
        serviceId: "4.2",
        service: "Parts Supply, Automatic",
        included: "No",
        required: "Yes",
        scos: 300.00,
        oemImport: 300.00,
        fleetOwner: 300.00,
        rrp: 300.00,
        selectService: "Yes",
        qtyPerYr: 1
      },
      {
        quotationId: quotationId,
        level: 2,
        package: "Dealer Add-Ons",
        serviceId: "4.8",
        service: "Overhaul Services",
        included: "No",
        required: "Yes",
        scos: 400.00,
        oemImport: 400.00,
        fleetOwner: 400.00,
        rrp: 400.00,
        selectService: "Yes",
        qtyPerYr: 1
      }
    ];
    
    // Insert the data
    const result = await quoteRow.insertMany(rowsData);
    console.log(`${result.insertedCount} documents inserted into QuoteRow collection`);
  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the connection
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the function
createQuoteRow().catch(console.error);
