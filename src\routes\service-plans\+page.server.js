import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

/** @type {import('./$types').PageServerLoad} */
export async function load() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db(dbName);
    
    // Get all service plans with computer information
    const servicePlans = await db.collection('ComputerServicePlan')
      .find({})
      .sort({ savedAt: -1 })
      .toArray();
    
    // Transform data for display
    const transformedPlans = servicePlans.map(plan => ({
      _id: plan._id.toString(),
      computerId: plan.computerId.toString(),
      computerName: plan.computer?.name || 'Unknown Computer',
      productDesignation: plan.productDesignation,
      totalServices: plan.totalServices,
      totalHours: plan.workloadSummary?.totalAccumulatedHours || 0,
      generatedAt: plan.generatedAt,
      savedAt: plan.savedAt,
      dataSource: plan.dataSource,
      version: plan.version
    }));
    
    return {
      servicePlans: transformedPlans
    };
    
  } catch (error) {
    console.error('Error loading service plans:', error);
    return {
      servicePlans: [],
      error: error.message
    };
  } finally {
    await client.close();
  }
}
