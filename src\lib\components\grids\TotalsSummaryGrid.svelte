<!-- TotalsSummaryGrid.svelte -->
<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  
  export let gap: string = '1rem';
  export let padding: string = '1rem';
  export let backgroundColor: string = '#f8f9fa';
  export let className: string = '';
</script>

<BaseGrid 
  columns="repeat(auto-fill, minmax(200px, 1fr))" 
  rows="auto"
  {gap} 
  {padding} 
  {backgroundColor}
  className="totals-summary-grid {className}"
>
  <slot />
</BaseGrid>

<style>
  :global(.totals-summary-grid) {
    border-radius: 6px;
    margin-bottom: 1.5rem;
  }
</style>
