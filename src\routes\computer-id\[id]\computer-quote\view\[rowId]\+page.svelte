<script>
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import TableGrid from '$lib/components/grids/TableGrid.svelte';

	const computerId = $page.params.id;
	const rowId = $page.params.rowId;
	
	let quoteRow = null;
	let loading = true;
	let error = null;

	onMount(async () => {
		try {
			const response = await fetch(`/api/quote-rows/detail/${rowId}`);
			if (response.ok) {
				quoteRow = await response.json();
			} else {
				error = await response.text();
				console.error('Failed to load quote row details:', error);
			}
		} catch (err) {
			error = err.message;
			console.error('Error loading quote row details:', err);
		} finally {
			loading = false;
		}
	});
</script>

<div class="view-quote-container">
	<div class="header">
		<h1>Quote Row Details</h1>
		<div class="actions">
			<a href="/computer-id/{computerId}/computer-quote" class="btn secondary">Back to Quote List</a>
			<a href="/computer-id/{computerId}/computer-quote/edit/{rowId}" class="btn primary">Edit</a>
		</div>
	</div>

	{#if loading}
		<div class="loading">Loading quote details...</div>
	{:else if error}
		<div class="error">Error: {error}</div>
	{:else if quoteRow}
		<div class="quote-details">
			<TableGrid>
				<div slot="content" class="details-grid">
					<div class="detail-row">
						<div class="label">Level:</div>
						<div class="value">{quoteRow.level}</div>
					</div>
					<div class="detail-row">
						<div class="label">Package:</div>
						<div class="value">{quoteRow.package}</div>
					</div>
					<div class="detail-row">
						<div class="label">Service ID:</div>
						<div class="value">{quoteRow.serviceId}</div>
					</div>
					<div class="detail-row">
						<div class="label">Service:</div>
						<div class="value">{quoteRow.service}</div>
					</div>
					<div class="detail-row">
						<div class="label">Included in Package:</div>
						<div class="value">{quoteRow.included}</div>
					</div>
					<div class="detail-row">
						<div class="label">Required:</div>
						<div class="value">{quoteRow.required}</div>
					</div>
					<div class="detail-row">
						<div class="label">SCOS:</div>
						<div class="value">{typeof quoteRow.scos === 'number' ? `£ ${quoteRow.scos.toFixed(2)}` : quoteRow.scos}</div>
					</div>
					<div class="detail-row">
						<div class="label">OEM Import:</div>
						<div class="value">{typeof quoteRow.oemImport === 'number' ? `£ ${quoteRow.oemImport.toFixed(2)}` : quoteRow.oemImport}</div>
					</div>
					<div class="detail-row">
						<div class="label">Fleet Owner:</div>
						<div class="value">{typeof quoteRow.fleetOwner === 'number' ? `£ ${quoteRow.fleetOwner.toFixed(2)}` : quoteRow.fleetOwner}</div>
					</div>
					<div class="detail-row">
						<div class="label">RRP:</div>
						<div class="value">{typeof quoteRow.rrp === 'number' ? `£ ${quoteRow.rrp.toFixed(2)}` : quoteRow.rrp}</div>
					</div>
					<div class="detail-row">
						<div class="label">Select Service:</div>
						<div class="value">{quoteRow.selectService}</div>
					</div>
					<div class="detail-row">
						<div class="label">Quantity per Year:</div>
						<div class="value">{quoteRow.qtyPerYr}</div>
					</div>
				</div>
			</TableGrid>
		</div>
	{:else}
		<div class="no-data">Quote row not found.</div>
	{/if}
</div>

<style>
	.view-quote-container {
		padding: 1rem;
		max-width: 800px;
		margin: 0 auto;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 2rem;
	}

	.actions {
		display: flex;
		gap: 1rem;
	}

	.btn {
		padding: 0.5rem 1rem;
		border-radius: 4px;
		text-decoration: none;
		font-weight: 500;
		cursor: pointer;
	}

	.primary {
		background-color: #3498db;
		color: white;
	}

	.secondary {
		background-color: #f1f1f1;
		color: #333;
	}

	.details-grid {
		display: grid;
		grid-template-columns: 1fr;
		gap: 1rem;
	}

	.detail-row {
		display: grid;
		grid-template-columns: 200px 1fr;
		padding: 0.75rem;
		border-bottom: 1px solid #eee;
	}

	.label {
		font-weight: bold;
		color: #555;
	}

	.loading, .error, .no-data {
		padding: 2rem;
		text-align: center;
	}

	.error {
		color: #e74c3c;
	}
</style>
