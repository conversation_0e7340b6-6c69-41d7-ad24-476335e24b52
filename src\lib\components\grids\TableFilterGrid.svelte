<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  
  export let columns: Array<{
    id: string;
    label: string;
    filterPlaceholder?: string;
  }> = [];

  export let showFilters: boolean = true;
</script>

<BaseGrid>
  <div class="table-filter-grid">
    {#if showFilters}
      <div class="filter-row">
        {#each columns as column}
          <div class="filter-cell">
            {#if column.id === 'productGroup'}
              <slot name="filter-productGroup"></slot>
            {:else if column.id === 'activity'}
              <slot name="filter-activity"></slot>
            {:else if column.id === 'label'}
              <slot name="filter-label"></slot>
            {:else if column.id === 'serviceCode'}
              <slot name="filter-serviceCode"></slot>
            {:else if column.id === 'actionType'}
              <slot name="filter-actionType"></slot>
            {:else if column.id === 'partNumber'}
              <slot name="filter-partNumber"></slot>
            {:else if column.id === 'unit'}
              <slot name="filter-unit"></slot>
            {:else if column.id === 'qty'}
              <slot name="filter-qty"></slot>
            {:else if column.id === 'hours'}
              <slot name="filter-hours"></slot>
            {:else if column.id === 'months'}
              <slot name="filter-months"></slot>
            {:else if column.id === 'actions'}
              <slot name="filter-actions"></slot>
            {:else}
              <div class="filter-placeholder" title={column.filterPlaceholder || column.label}>
                {column.filterPlaceholder || column.label}
              </div>
            {/if}
          </div>
        {/each}
      </div>
    {/if}
    
    <div class="header-row">
      {#each columns as column}
        <div class="header-cell">
          {#if column.id === 'productGroup'}
            <slot name="header-productGroup">{column.label}</slot>
          {:else if column.id === 'activity'}
            <slot name="header-activity">{column.label}</slot>
          {:else if column.id === 'label'}
            <slot name="header-label">{column.label}</slot>
          {:else if column.id === 'serviceCode'}
            <slot name="header-serviceCode">{column.label}</slot>
          {:else if column.id === 'actionType'}
            <slot name="header-actionType">{column.label}</slot>
          {:else if column.id === 'partNumber'}
            <slot name="header-partNumber">{column.label}</slot>
          {:else if column.id === 'unit'}
            <slot name="header-unit">{column.label}</slot>
          {:else if column.id === 'qty'}
            <slot name="header-qty">{column.label}</slot>
          {:else if column.id === 'hours'}
            <slot name="header-hours">{column.label}</slot>
          {:else if column.id === 'months'}
            <slot name="header-months">{column.label}</slot>
          {:else if column.id === 'actions'}
            <slot name="header-actions">{column.label}</slot>
          {:else}
            {column.label}
          {/if}
        </div>
      {/each}
    </div>
    
    <slot name="body">
      <!-- Table body rows go here -->
    </slot>
  </div>
</BaseGrid>

<style>
  .table-filter-grid {
    display: grid;
    grid-template-rows: auto auto 1fr;
    width: 100%;
    border-collapse: collapse;
  }
  
  .filter-row, .header-row {
    display: grid;
    grid-template-columns: repeat(var(--column-count, 11), 1fr);
    width: 100%;
  }
  
  .filter-cell, .header-cell {
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .header-cell {
    background-color: #f7fafc;
    font-weight: 600;
    text-align: center;
    border-bottom: 2px solid #a0aec0;
  }
  
  .filter-placeholder {
    color: #a0aec0;
    font-style: italic;
    font-size: 0.9rem;
  }

  /* Responsive styles */
  @media (max-width: 1024px) {
    .filter-row, .header-row {
      grid-template-columns: repeat(var(--column-count, 11), minmax(80px, 1fr));
      overflow-x: auto;
    }
  }
</style>
