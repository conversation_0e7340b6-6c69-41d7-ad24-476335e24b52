<script>
  import { createEventDispatcher } from 'svelte';
  
  export let services = [];
  export let selectedIds = [];
  export let section = '';
  export let editable = false;
  export let allowAddRemove = false;
  
  const dispatch = createEventDispatcher();
  
  // Track which service is currently being edited
  let editingServiceId = null;
  
  // Create a temporary copy of the service being edited
  let editedService = {};
  
  // Track if we're adding a new service
  let newService = null;
  
  /**
   * Toggle service selection
   */
  function toggleService(id) {
    dispatch('toggle', { id });
  }
  
  /**
   * Start editing a service
   */
  function startEditing(service) {
    if (!editable) return;
    editingServiceId = service.id;
    editedService = { ...service };
  }
  
  /**
   * Save edited service
   */
  function saveEdit() {
    if (newService && editingServiceId === newService.id) {
      // This is a new service being saved for the first time
      dispatch('add', { service: editedService });
      newService = null;
    } else {
      // This is an existing service being updated
      dispatch('edit', { service: editedService });
    }
    editingServiceId = null;
  }
  
  /**
   * Cancel editing
   */
  function cancelEdit() {
    if (newService && editingServiceId === newService.id) {
      // Remove the new service from the local array
      services = services.filter(s => s.id !== newService.id);
      newService = null;
    }
    editingServiceId = null;
  }
  
  /**
   * Add a new service
   */
  function addService() {
    const timestamp = Date.now();
    const newId = `temp_${timestamp}`;
    const uniqueId = `addon_${timestamp}_${Math.floor(Math.random() * 1000)}`;
    
    // Create the new service object
    newService = {
      id: newId,
      uniqueId: uniqueId,
      level: services.length > 0 ? Math.max(...services.map(s => s.level || 0)) + 1 : 1,
      name: section === 'dealerAddOns' ? 'Dealer Add-On: New Service' : section,
      service: section === 'dealerAddOns' ? 'New Service' : 'New Service',
      activity: '',
      fixed: true,
      cost: 0,
      scos: 0,
      oemImporter: false,
      fleetOwner: false,
      customerSpecific: false,
      rrp: 0,
      required: false,
      variableType: 'hr'
    };
    
    // Add it to the local array
    services = [...services, newService];
    
    // Start editing it immediately
    editingServiceId = newId;
    editedService = { ...newService };
  }
  
  /**
   * Remove a service
   */
  function removeService(id) {
    dispatch('remove', { id });
  }
  
  /**
   * Handle input change for edited service
   */
  function handleInputChange(event, field) {
    const target = event.target;
    let value;
    
    if (target && target.type === 'checkbox') {
      value = target.checked;
    } else if (target && target.type === 'number') {
      value = parseFloat(target.value) || 0;
    } else if (target) {
      value = target.value;
    }
    
    // Only update the edited service, not the original
    editedService = {
      ...editedService,
      [field]: value
    };
  }
</script>

<div class="quote-grid">
  <div class="grid-header">
    <div class="header-level">Level</div>
    <div class="header-package">Package</div>
    <div class="header-activity">Activity</div>
    <div class="header-scos">SCOS</div>
    <div class="header-oem">OEM Importer</div>
    <div class="header-fleet">Fleet Owner</div>
    <div class="header-customer">Customer Specific</div>
    <div class="header-rrp">RRP</div>
    <div class="header-include">Include</div>
    {#if editable}
      <div class="header-actions">Actions</div>
    {/if}
  </div>
  
  {#each services as service (service.uniqueId || service.id)}
    <div class="grid-row {selectedIds.includes(service.id) ? 'selected' : ''}">
      {#if editingServiceId === service.id}
        <!-- Edit mode -->
        <div class="cell-level">
          <input 
            type="number" 
            value={editedService.level || service.level} 
            on:input={(e) => handleInputChange(e, 'level')}
            class="edit-input"
          />
        </div>
        <div class="cell-package">
          <input 
            type="text" 
            value={editedService.service || service.service || service.name} 
            on:input={(e) => handleInputChange(e, 'service')}
            class="edit-input"
          />
          <div class="edit-checkbox">
            <label>
              <input 
                type="checkbox" 
                checked={editedService.fixed !== undefined ? editedService.fixed : service.fixed}
                on:change={(e) => handleInputChange(e, 'fixed')}
              />
              Fixed
            </label>
            {#if !(editedService.fixed !== undefined ? editedService.fixed : service.fixed)}
              <select 
                value={editedService.variableType || service.variableType || 'hr'} 
                on:change={(e) => handleInputChange(e, 'variableType')}
                class="edit-select"
              >
                <option value="hr">hr</option>
                <option value="day">day</option>
                <option value="month">month</option>
              </select>
            {/if}
          </div>
        </div>
        <div class="cell-activity">
          <input 
            type="text" 
            value={editedService.activity || service.activity || ''} 
            on:input={(e) => handleInputChange(e, 'activity')}
            class="edit-input"
          />
        </div>
        <div class="cell-scos">
          <input 
            type="number" 
            value={editedService.scos !== undefined ? editedService.scos : service.scos || 0} 
            on:input={(e) => handleInputChange(e, 'scos')}
            class="edit-input"
            step="0.01"
          />
        </div>
        <div class="cell-oem">
          <input 
            type="number" 
            value={editedService.cost !== undefined ? editedService.cost : service.cost || 0} 
            on:input={(e) => handleInputChange(e, 'cost')}
            class="edit-input"
            step="0.01"
          />
          <div class="edit-checkbox">
            <label>
              <input 
                type="checkbox" 
                checked={editedService.oemImporter !== undefined ? editedService.oemImporter : service.oemImporter}
                on:change={(e) => handleInputChange(e, 'oemImporter')}
              />
            </label>
          </div>
        </div>
        <div class="cell-fleet">
          <div class="edit-checkbox">
            <label>
              <input 
                type="checkbox" 
                checked={editedService.fleetOwner !== undefined ? editedService.fleetOwner : service.fleetOwner}
                on:change={(e) => handleInputChange(e, 'fleetOwner')}
              />
            </label>
          </div>
        </div>
        <div class="cell-customer">
          <div class="edit-checkbox">
            <label>
              <input 
                type="checkbox" 
                checked={editedService.customerSpecific !== undefined ? editedService.customerSpecific : service.customerSpecific}
                on:change={(e) => handleInputChange(e, 'customerSpecific')}
              />
            </label>
          </div>
        </div>
        <div class="cell-rrp">
          <input 
            type="number" 
            value={editedService.rrp !== undefined ? editedService.rrp : service.rrp || 0} 
            on:input={(e) => handleInputChange(e, 'rrp')}
            class="edit-input"
            step="0.01"
          />
        </div>
        <div class="cell-include">
          <input 
            type="checkbox" 
            checked={selectedIds.includes(service.id)}
            on:change={() => toggleService(service.id)}
            disabled={editedService.required !== undefined ? editedService.required : service.required}
          />
        </div>
        <div class="cell-actions">
          <button class="action-button save" on:click={saveEdit} title="Save">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="20 6 9 17 4 12"></polyline>
            </svg>
          </button>
          <button class="action-button cancel" on:click={cancelEdit} title="Cancel">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
      {:else}
        <!-- View mode -->
        <div class="cell-level">{service.level}</div>
        <div class="cell-package">
          {service.service || service.name}
          {#if service.fixed}
            <span class="fixed-tag">[Fixed]</span>
          {:else if service.variableType}
            <span class="variable-tag">[Variable/{service.variableType}]</span>
          {/if}
        </div>
        <div class="cell-activity">{service.activity || ''}</div>
        <div class="cell-scos">£ {service.scos || 0}</div>
        <div class="cell-oem">£ {service.oemImporter ? service.cost : '-'}</div>
        <div class="cell-fleet">£ {service.fleetOwner ? service.cost : '-'}</div>
        <div class="cell-customer">{service.customerSpecific ? 'Yes' : 'No'}</div>
        <div class="cell-rrp">£ {service.rrp || '-'}</div>
        <div class="cell-include">
          <input 
            type="checkbox" 
            checked={selectedIds.includes(service.id)}
            on:change={() => toggleService(service.id)}
            disabled={service.required}
          />
        </div>
        {#if editable}
          <div class="cell-actions">
            <button class="action-button edit" on:click={() => startEditing(service)} title="Edit">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path>
              </svg>
            </button>
            {#if allowAddRemove && !service.required}
              <button class="action-button remove" on:click={() => removeService(service.id)} title="Remove">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="3 6 5 6 21 6"></polyline>
                  <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                  <line x1="10" y1="11" x2="10" y2="17"></line>
                  <line x1="14" y1="11" x2="14" y2="17"></line>
                </svg>
              </button>
            {/if}
          </div>
        {/if}
      {/if}
    </div>
  {/each}
  
  {#if allowAddRemove}
    <div class="add-row">
      <button class="add-button" on:click={addService}>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="8" x2="12" y2="16"></line>
          <line x1="8" y1="12" x2="16" y2="12"></line>
        </svg>
        Add New Service
      </button>
    </div>
  {/if}
</div>

<style>
  .quote-grid {
    display: grid;
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
  }
  
  .grid-header {
    display: grid;
    grid-template-columns: 80px 200px 200px 100px 120px 120px 120px 100px 100px 100px;
    background-color: #f8f9fa;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
  }
  
  .grid-header > div {
    padding: 12px 8px;
    text-align: center;
    border-right: 1px solid #eee;
  }
  
  .grid-row {
    display: grid;
    grid-template-columns: 80px 200px 200px 100px 120px 120px 120px 100px 100px 100px;
    border-bottom: 1px solid #ddd;
    transition: background-color 0.2s;
  }
  
  .grid-row > div {
    padding: 12px 8px;
    border-right: 1px solid #eee;
    display: flex;
    align-items: center;
  }
  
  .grid-row.selected {
    background-color: #e8f4f8;
  }
  
  .cell-level {
    text-align: center;
  }
  
  .cell-package {
    display: flex;
    flex-direction: column;
  }
  
  .fixed-tag, .variable-tag {
    font-size: 0.8rem;
    margin-top: 0.25rem;
    padding: 0.1rem 0.3rem;
    border-radius: 3px;
    display: inline-block;
  }
  
  .fixed-tag {
    background-color: #e3f2fd;
    color: #0d47a1;
  }
  
  .variable-tag {
    background-color: #fff8e1;
    color: #ff6f00;
  }
  
  .edit-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
  }
  
  .edit-select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    margin-top: 0.25rem;
  }
  
  .edit-checkbox {
    display: flex;
    align-items: center;
    margin-top: 0.5rem;
  }
  
  .edit-checkbox label {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    margin-right: 0.5rem;
  }
  
  .edit-checkbox input[type="checkbox"] {
    margin-right: 0.25rem;
  }
  
  .action-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
    margin: 0 0.25rem;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
  }
  
  .action-button:hover {
    background-color: #f0f0f0;
  }
  
  .action-button.edit {
    color: #0056b3;
  }
  
  .action-button.remove {
    color: #dc3545;
  }
  
  .action-button.save {
    color: #28a745;
  }
  
  .action-button.cancel {
    color: #dc3545;
  }
  
  .add-row {
    padding: 1rem;
    text-align: center;
    background-color: #f8f9fa;
    border-top: 1px solid #ddd;
  }
  
  .add-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
  }
  
  .add-button:hover {
    background-color: #0069d9;
  }
  
  .add-button svg {
    margin-right: 0.5rem;
  }
  
  /* Responsive adjustments */
  @media (max-width: 1200px) {
    .grid-header, .grid-row {
      grid-template-columns: 60px 180px 180px 80px 100px 100px 100px 80px 80px 80px;
    }
  }
  
  @media (max-width: 992px) {
    .quote-grid {
      overflow-x: auto;
    }
    
    .grid-header, .grid-row {
      min-width: 1140px;
    }
  }
</style>
