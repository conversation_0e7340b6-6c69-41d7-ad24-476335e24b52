import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function main() {
    try {
        await client.connect();
        console.log('Connected successfully');

        const db = client.db('ServiceContracts');
        const collection = db.collection('ProductDesignationPartnumberValidityGroup');

        // Get a sample document
        const sample = await collection.findOne({});
        if (sample) {
            console.log('Field names in collection:');
            console.log(Object.keys(sample));
            console.log('\nSample document:');
            console.log(JSON.stringify(sample, null, 2));
        } else {
            console.log('No documents found in collection');
        }

        // Get total count
        const count = await collection.countDocuments();
        console.log('\nTotal documents:', count);

    } finally {
        await client.close();
    }
}

main().catch(console.error);
