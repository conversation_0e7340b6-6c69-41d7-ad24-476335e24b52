<script>
  import EnhancedServiceOffering from '$lib/components/EnhancedServiceOffering.svelte';

  /** @type {import('./$types').PageData} */
  export let data;

  // Extract data from server load
  const { quotationPackages, computer, productDesignation, priceListItems } = data;

  function handleTogglePackage(event) {
    console.log('Package toggled:', event.detail);
    // Here you could implement logic to update the service offering
    // For example, make an API call to save the changes
  }

  function handleSelectService(event) {
    console.log('Service selected:', event.detail);
    // Here you could implement logic to handle service selection
  }

  // Calculate total services loaded
  $: totalServices = (quotationPackages.baseContract?.length || 0) + 
                    (quotationPackages.repairPackages?.length || 0) + 
                    (quotationPackages.supportServices?.length || 0) + 
                    (quotationPackages.replacementServices?.length || 0);
</script>

<svelte:head>
  <title>Service Offering - {productDesignation}</title>
</svelte:head>

<div class="service-offering-page">
  <div class="page-header">
    <h1>Service Offering</h1>
    {#if computer}
      <div class="computer-info">
        <h2>Computer: {computer.name || computer.serialNumber}</h2>
        <p><strong>Product Designation:</strong> {computer.productDesignation || productDesignation}</p>
        {#if computer.serialNumber}
          <p><strong>Serial Number:</strong> {computer.serialNumber}</p>
        {/if}
        {#if computer.model}
          <p><strong>Model:</strong> {computer.model}</p>
        {/if}
      </div>
    {:else}
      <div class="product-info">
        <h2>Product Designation: {productDesignation}</h2>
      </div>
    {/if}
    
    <div class="data-summary">
      <p><strong>Total Services:</strong> {totalServices}</p>
      <p><strong>Price List Items:</strong> {priceListItems}</p>
    </div>
  </div>

  {#if quotationPackages}
    <EnhancedServiceOffering 
      {quotationPackages}
      {computer}
      on:togglePackage={handleTogglePackage}
      on:selectService={handleSelectService}
    />
  {:else}
    <div class="no-data">
      <p>No service offering data available for this product designation.</p>
    </div>
  {/if}

  <div class="data-source-info">
    <h3>Data Sources</h3>
    <ul>
      <li><strong>Base Contract Services:</strong> BaseServices collection ({quotationPackages.baseContract?.length || 0} items)</li>
      <li><strong>Support Services:</strong> SupportServices collection ({quotationPackages.supportServices?.length || 0} items)</li>
      <li><strong>Dealer Add-Ons:</strong> DealerServices collection ({quotationPackages.repairPackages?.length || 0} items)</li>
      <li><strong>Replacement Services:</strong> ReplacementServices collection ({quotationPackages.replacementServices?.length || 0} items)</li>
      <li><strong>Pricing:</strong> PriceLIst collection ({priceListItems} items)</li>
    </ul>
    <p><em>All missing values are displayed as 0 as requested. No fake data is used.</em></p>
  </div>
</div>

<style>
  .service-offering-page {
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
  }

  .page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e0e0e0;
  }

  .page-header h1 {
    color: #333;
    margin-bottom: 1rem;
  }

  .computer-info,
  .product-info {
    background-color: #f9f9f9;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
  }

  .computer-info h2,
  .product-info h2 {
    color: #2e7d32;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
  }

  .computer-info p,
  .product-info p {
    margin: 0.25rem 0;
    color: #666;
  }

  .data-summary {
    background-color: #e3f2fd;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
  }

  .data-summary p {
    margin: 0.25rem 0;
    font-weight: 600;
    color: #1976d2;
  }

  .no-data {
    text-align: center;
    padding: 3rem;
    background-color: #fff3e0;
    border-radius: 4px;
    color: #f57c00;
  }

  .data-source-info {
    margin-top: 3rem;
    padding: 1.5rem;
    background-color: #f5f5f5;
    border-radius: 4px;
    border-left: 4px solid #2e7d32;
  }

  .data-source-info h3 {
    color: #2e7d32;
    margin-bottom: 1rem;
  }

  .data-source-info ul {
    list-style-type: disc;
    margin-left: 1.5rem;
    margin-bottom: 1rem;
  }

  .data-source-info li {
    margin: 0.5rem 0;
    color: #666;
  }

  .data-source-info p {
    font-style: italic;
    color: #888;
    margin-top: 1rem;
  }

  @media (max-width: 768px) {
    .service-offering-page {
      padding: 1rem;
    }

    .computer-info,
    .product-info,
    .data-summary {
      padding: 0.75rem;
    }
  }
</style>
