<script>
  import ServiceCodeActionTable from '$lib/components/ServiceCodeActionTable.svelte';

  /** @type {import('./$types').PageData} */
  export let data;

  // Extract data from server load - using new data structure
  const {
    productValidityGroup,
    productDesignation,
    computer,
    serviceItems,
    packageOfferings,
    totals,
    dataSource
  } = data;

  // Calculate total services loaded
  $: totalServices = Object.values(serviceItems || {}).reduce((total, services) => total + (services?.length || 0), 0);
</script>

<svelte:head>
  <title>Service Offering - {productDesignation}</title>
</svelte:head>

<div class="service-offering-page">
  <div class="page-header">
    <h1>Service Offering</h1>
    {#if computer}
      <div class="computer-info">
        <h2>Computer: {computer.name || computer.serialNumber}</h2>
        <p><strong>Product Designation:</strong> {computer.productDesignation || productDesignation}</p>
        <p><strong>Product Validity Group:</strong> {productValidityGroup}</p>
        {#if computer.serialNumber}
          <p><strong>Serial Number:</strong> {computer.serialNumber}</p>
        {/if}
        {#if computer.model}
          <p><strong>Model:</strong> {computer.model}</p>
        {/if}
      </div>
    {:else}
      <div class="product-info">
        <h2>Product Designation: {productDesignation}</h2>
        <p><strong>Product Validity Group:</strong> {productValidityGroup}</p>
      </div>
    {/if}

    <div class="data-summary">
      <p><strong>Total Services:</strong> {totalServices}</p>
      <p><strong>Data Sources:</strong> ServiceCodeAndActionType ({dataSource?.serviceCodeAndActionType || 0} items)</p>
    </div>
  </div>

  {#if serviceItems && Object.keys(serviceItems).length > 0}
    <ServiceCodeActionTable
      {productValidityGroup}
      {serviceItems}
      {totals}
      {dataSource}
    />
  {:else}
    <div class="no-data">
      <p>No service offering data available for Product Validity Group: {productValidityGroup}</p>
      <p>Please check if the ServiceCodeAndActionType collection contains data for this product validity group.</p>
    </div>
  {/if}
</div>

<style>
  .service-offering-page {
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
  }

  .page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e0e0e0;
  }

  .page-header h1 {
    color: #333;
    margin-bottom: 1rem;
  }

  .computer-info,
  .product-info {
    background-color: #f9f9f9;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
  }

  .computer-info h2,
  .product-info h2 {
    color: #2e7d32;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
  }

  .computer-info p,
  .product-info p {
    margin: 0.25rem 0;
    color: #666;
  }

  .data-summary {
    background-color: #e3f2fd;
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
  }

  .data-summary p {
    margin: 0.25rem 0;
    font-weight: 600;
    color: #1976d2;
  }

  .no-data {
    text-align: center;
    padding: 3rem;
    background-color: #fff3e0;
    border-radius: 4px;
    color: #f57c00;
  }



  @media (max-width: 768px) {
    .service-offering-page {
      padding: 1rem;
    }

    .computer-info,
    .product-info,
    .data-summary {
      padding: 0.75rem;
    }
  }
</style>
