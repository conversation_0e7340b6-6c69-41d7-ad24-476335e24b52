<script>
  import { createEventDispatcher } from 'svelte';

  // Props
  export let customerId = '';
  export let editingComputerId = '';
  export let formData = {
    serialNumber: '',
    model: '',
    productType: '',
    productDesignation: '',
    engineType: '',
    operatingHours: 0,
    installationDate: '',
    manufactureDate: '',
    isActive: true,
    notes: ''
  };

  const dispatch = createEventDispatcher();

  function handleCancel() {
    dispatch('cancel');
  }
</script>

<div class="computer-form-wrapper">
  <form
    action={editingComputerId ? '?/updateComputer' : '?/addComputer'}
    method="POST"
    class="computer-form"
  >
    <div class="form-header">
      <h2>{editingComputerId ? 'Edit Computer' : 'Add New Computer'}</h2>
      <button type="button" class="close-button" on:click={handleCancel} aria-label="Close form">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
          <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"/>
        </svg>
      </button>
    </div>

    {#if editingComputerId}
      <input type="hidden" name="_id" value={editingComputerId} />
    {/if}
    <input type="hidden" name="customerId" value={customerId} />

    <div class="form-grid">
      <div class="form-group">
        <label for="serialNumber">Serial Number*</label>
        <input 
          type="text"
          id="serialNumber"
          name="serialNumber"
          bind:value={formData.serialNumber}
          required
        />
      </div>
      
      <div class="form-group">
        <label for="model">Model*</label>
        <input 
          type="text"
          id="model"
          name="model"
          bind:value={formData.model}
          required
        />
      </div>
      
      <div class="form-group">
        <label for="productType">Product Type*</label>
        <input 
          type="text"
          id="productType"
          name="productType"
          bind:value={formData.productType}
          required
        />
      </div>
      
      <div class="form-group">
        <label for="productDesignation">Product Designation</label>
        <input 
          type="text"
          id="productDesignation"
          name="productDesignation"
          bind:value={formData.productDesignation}
        />
      </div>
      
      <div class="form-group">
        <label for="engineType">Engine Type</label>
        <input 
          type="text"
          id="engineType"
          name="engineType"
          bind:value={formData.engineType}
        />
      </div>
      
      <div class="form-group">
        <label for="operatingHours">Operating Hours*</label>
        <input 
          type="number"
          id="operatingHours"
          name="operatingHours"
          bind:value={formData.operatingHours}
          min="0"
          required
        />
      </div>
      
      <div class="form-group">
        <label for="installationDate">Installation Date</label>
        <input 
          type="date"
          id="installationDate"
          name="installationDate"
          bind:value={formData.installationDate}
        />
      </div>
      
      <div class="form-group">
        <label for="manufactureDate">Manufacture Date</label>
        <input 
          type="date"
          id="manufactureDate"
          name="manufactureDate"
          bind:value={formData.manufactureDate}
        />
      </div>
      
      <div class="form-group">
        <label class="checkbox-label">
          <input 
            type="checkbox"
            name="isActive"
            bind:checked={formData.isActive}
          />
          Active
        </label>
      </div>
      
      <div class="form-group full-width">
        <label for="notes">Notes</label>
        <textarea 
          id="notes"
          name="notes"
          bind:value={formData.notes}
          rows="3"
        ></textarea>
      </div>
    </div>
    
    <div class="form-actions">
      <button type="button" class="btn-secondary" on:click={handleCancel}>Cancel</button>
      <button type="submit" class="btn-primary">Save Computer</button>
    </div>
  </form>
</div>

<style>
  /* Using CSS Grid for the form layout */
  .computer-form-wrapper {
    background-color: #f9fafb;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
  }
  
  .form-header {
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    margin-bottom: 1.5rem;
  }
  
  h2 {
    font-size: 1.25rem;
    margin: 0;
    color: #1f2937;
  }
  
  .close-button {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s;
  }
  
  .close-button:hover {
    background-color: #f3f4f6;
    color: #1f2937;
  }
  
  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
  }
  
  .form-group {
    margin-bottom: 1rem;
  }
  
  .form-group.full-width {
    grid-column: 1 / -1;
  }
  
  label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #4b5563;
  }
  
  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
  }
  
  input[type="text"],
  input[type="number"],
  input[type="date"],
  textarea,
  select {
    width: 100%;
    padding: 0.5rem;
    font-size: 0.875rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    background-color: white;
  }
  
  input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    cursor: pointer;
  }
  
  .form-actions {
    display: grid;
    grid-template-columns: auto auto;
    justify-content: end;
    gap: 0.75rem;
    margin-top: 1.5rem;
  }
  
  .btn-primary {
    background-color: #1e40af;
    color: white;
    border: none;
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .btn-primary:hover {
    background-color: #1e3a8a;
  }
  
  .btn-secondary {
    background-color: white;
    color: #4b5563;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .btn-secondary:hover {
    background-color: #f3f4f6;
    border-color: #9ca3af;
  }
</style>
