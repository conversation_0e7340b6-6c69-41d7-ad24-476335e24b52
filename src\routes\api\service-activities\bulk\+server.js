import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';

/**
 * Bulk create/update service activities across a date range
 * @type {import('./$types').RequestHandler}
 */
export async function POST({ request }) {
    const client = new MongoClient(uri);
    
    try {
        // Parse request data
        const data = await request.json();
        
        // Validate required fields
        const { computerId, startYear, startMonth, endYear, endMonth, hours, activity = 'Regular Service', hasFixed = false, engineHours = 0 } = data;
        
        if (!computerId || !ObjectId.isValid(computerId)) {
            return json({ success: false, message: 'Valid computer ID is required' }, { status: 400 });
        }
        
        if (!validateDateRange(startYear, startMonth, endYear, endMonth)) {
            return json({ success: false, message: 'Valid date range is required' }, { status: 400 });
        }
        
        // Simple check that hours is provided
        if (hours === undefined || hours === null) {
            console.log('Hours validation failed: Value is undefined or null');
            return json({ success: false, message: 'Hours value is required' }, { status: 400 });
        }
        
        console.log('Hours received from request:', hours, 'Type:', typeof hours);
        
        // Parse hours to ensure it's an integer
        const numericHours = parseInt(hours, 10);
        console.log('Hours after parseInt:', numericHours, 'Type:', typeof numericHours);
        
        // Validate it's a positive integer
        if (isNaN(numericHours) || numericHours <= 0) {
            console.log('Hours validation failed:', { isNaN: isNaN(numericHours), isZeroOrNegative: numericHours <= 0 });
            return json({ success: false, message: 'Hours must be a positive whole number' }, { status: 400 });
        }
        
        // Use the parsed integer directly
        const hoursInteger = numericHours;
        console.log('Final hours value to be stored:', hoursInteger);
        
        // Connect to database
        await client.connect();
        const db = client.db('ServiceContracts');
        const activitiesCollection = db.collection('ServiceActivities');
        
        // Generate months in range
        const monthsInRange = generateMonthsInRange(startYear, startMonth, endYear, endMonth);
        
        // Process each month
        const results = await processActivities(
            activitiesCollection, 
            new ObjectId(computerId), 
            monthsInRange,
            hoursInteger,
            activity,
            hasFixed
        );
        
        return json({
            success: true,
            message: `Successfully processed ${results.operations.length} activities (${results.created.length} created, ${results.updated.length} updated)`,
            ...results
        });
    } catch (error) {
        console.error('Error in bulk processing:', error);
        return json({ 
            success: false, 
            message: error.message || 'An unexpected error occurred'
        }, { status: 500 });
    } finally {
        await client.close();
    }
}

/**
 * Validates the date range parameters
 */
function validateDateRange(startYear, startMonth, endYear, endMonth) {
    // Parse parameters to integers
    const parsedStartYear = parseInt(startYear, 10);
    const parsedStartMonth = parseInt(startMonth, 10);
    const parsedEndYear = parseInt(endYear, 10);
    const parsedEndMonth = parseInt(endMonth, 10);
    
    // Check for valid numbers
    if (isNaN(parsedStartYear) || isNaN(parsedStartMonth) || 
        isNaN(parsedEndYear) || isNaN(parsedEndMonth)) {
        return false;
    }
    
    // Check for valid month ranges
    if (parsedStartMonth < 1 || parsedStartMonth > 12 || 
        parsedEndMonth < 1 || parsedEndMonth > 12) {
        return false;
    }
    
    // Check that end date is not before start date
    if (parsedEndYear < parsedStartYear || 
        (parsedEndYear === parsedStartYear && parsedEndMonth < parsedStartMonth)) {
        return false;
    }
    
    return true;
}

/**
 * Generates an array of month objects for the given range
 */
function generateMonthsInRange(startYear, startMonth, endYear, endMonth) {
    const months = [];
    let currentYear = parseInt(startYear, 10);
    let currentMonth = parseInt(startMonth, 10);
    const targetEndYear = parseInt(endYear, 10);
    const targetEndMonth = parseInt(endMonth, 10);
    
    while (
        currentYear < targetEndYear || 
        (currentYear === targetEndYear && currentMonth <= targetEndMonth)
    ) {
        months.push({ year: currentYear, month: currentMonth });
        
        // Move to next month
        currentMonth++;
        if (currentMonth > 12) {
            currentMonth = 1;
            currentYear++;
        }
    }
    
    return months;
}

/**
 * Process service activities for the specified months
 * @param {import('mongodb').Collection} collection - MongoDB collection
 * @param {import('mongodb').ObjectId} computerId - Computer ID
 * @param {Array<{year: number, month: number}>} monthsInRange - Array of months
 * @param {number} hours - Hours value
 * @param {string} activity - Activity description
 * @param {boolean} hasFixed - Fixed schedule flag
 * @returns {Promise<{operations: Array<{type: string, year: number, month: number, success: boolean}>, created: Array<{year: number, month: number}>, updated: Array<{year: number, month: number}>}>}
 */
async function processActivities(collection, computerId, monthsInRange, hours, activity, hasFixed) {
    /** @type {{operations: Array<{type: string, year: number, month: number, success: boolean}>, created: Array<{year: number, month: number}>, updated: Array<{year: number, month: number}>}} */
    const results = {
        operations: [],
        created: [],
        updated: []
    };
    
    for (const { year, month } of monthsInRange) {
        // Check if activity exists
        const existingActivity = await collection.findOne({
            computerId,
            year,
            month
        });
        
        if (existingActivity) {
            // Update existing activity
            const updateResult = await collection.updateOne(
                { _id: existingActivity._id },
                { 
                    $set: { 
                        hours,
                        activity,
                        hasFixed,
                        updatedAt: new Date()
                    }
                }
            );
            
            const success = updateResult.modifiedCount > 0;
            if (success) {
                results.updated.push({ year, month });
            }
            
            results.operations.push({
                type: 'update',
                year,
                month,
                success
            });
        } else {
            // Create new activity
            const newActivity = {
                computerId,
                year,
                month,
                hours,
                activity,
                hasFixed,
                createdAt: new Date(),
                updatedAt: new Date()
            };
            
            const insertResult = await collection.insertOne(newActivity);
            const success = insertResult.acknowledged;
            
            if (success) {
                results.created.push({ year, month });
            }
            
            results.operations.push({
                type: 'create',
                year,
                month,
                success
            });
        }
    }
    
    return results;
}
