<script>
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import QuoteGrid from '$lib/components/grids/QuoteGrid.svelte';
  
  export let data;
  
  // Reactive variables
  let showSuccessMessage = false;
  let successMessage = '';
  let showErrorMessage = false;
  let errorMessage = '';
  let isLoading = false;
  
  // Format date for display
  function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  }
  
  // Format currency for display
  function formatCurrency(amount) {
    if (amount === undefined || amount === null) return '£ 0.00';
    return `£ ${parseFloat(amount).toFixed(2)}`;
  }
  
  // Calculate section totals
  function recalculateSectionTotals() {
    if (!data.serviceOfferings) return;
    
    data.sectionTotals = {
      baseContract: 0,
      dealerAddOns: 0,
      supportServices: 0,
      retirementPlans: 0,
      total: 0
    };
    
    // Calculate totals for each section
    for (const section in data.serviceOfferings) {
      data.sectionTotals[section] = data.serviceOfferings[section].reduce((total, service) => {
        return total + (parseFloat(service.cost) || 0);
      }, 0);
    }
    
    // Calculate grand total
    data.sectionTotals.total = 
      data.sectionTotals.baseContract + 
      data.sectionTotals.dealerAddOns + 
      data.sectionTotals.supportServices + 
      data.sectionTotals.retirementPlans;
  }
  
  // Initialize section totals
  recalculateSectionTotals();
  
  // Handle service selection toggle
  function handleServiceToggle(section, event) {
    const { id } = event.detail;
    
    // Find the service in the section
    const service = data.serviceOfferings[section].find(s => s.id === id);
    
    if (service) {
      // Toggle the selection
      if (data.selectedIds[section].includes(id)) {
        data.selectedIds[section] = data.selectedIds[section].filter(i => i !== id);
      } else {
        data.selectedIds[section] = [...data.selectedIds[section], id];
      }
      
      // Recalculate section totals
      recalculateSectionTotals();
    }
  }
  
  // Handle service editing
  async function handleServiceEdit(section, event) {
    const { service } = event.detail;
    
    try {
      // Special handling for Dealer Add-Ons to ensure unique identification
      if (section === 'dealerAddOns' && !service.uniqueId) {
        service.uniqueId = `addon_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
      }
      
      // Prepare data for submission
      const rowData = {
        rowId: service.rowId || service.id,
        quotationId: data.quotationHeader?._id,
        section: section,
        serviceId: service.serviceId || '',
        service: service.service,
        level: service.level,
        cost: service.cost || 0,
        fixed: service.fixed !== undefined ? service.fixed : true,
        variableType: service.variableType || 'hr',
        oemImporter: service.oemImporter !== undefined ? service.oemImporter : false,
        fleetOwner: service.fleetOwner !== undefined ? service.fleetOwner : false,
        scos: service.scos || 0,
        rrp: service.rrp || 0,
        uniqueId: service.uniqueId || null // Include uniqueId for proper identification
      };
      
      console.log('Updating service:', rowData);
      
      // Create form data
      const formData = new FormData();
      formData.append('rowData', JSON.stringify(rowData));
      
      // Send request
      const response = await fetch(`?/updateQuotationRow`, {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error(`Server returned ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        // Update the data model - only update the specific service
        if (data.serviceOfferings && data.serviceOfferings[section]) {
          data.serviceOfferings[section] = data.serviceOfferings[section].map(item => {
            // Match by uniqueId for Dealer Add-Ons, otherwise by id
            if ((section === 'dealerAddOns' && item.uniqueId === service.uniqueId) || 
                (section !== 'dealerAddOns' && item.id === service.id)) {
              return { ...service };
            }
            return item;
          });
        }
        
        // Recalculate section totals
        recalculateSectionTotals();
        
        showSuccessMessage = true;
        successMessage = 'Service updated successfully';
        setTimeout(() => {
          showSuccessMessage = false;
        }, 3000);
      } else {
        throw new Error(result.error || 'Failed to update service');
      }
    } catch (error) {
      console.error('Error updating service:', error);
      showErrorMessage = true;
      errorMessage = error.message || 'An error occurred while updating the service';
      setTimeout(() => {
        showErrorMessage = false;
      }, 3000);
    }
  }
  
  // Handle service addition
  async function handleServiceAdd(section, event) {
    const { service } = event.detail;
    
    try {
      // Prepare data for submission
      const rowData = {
        quotationId: data.quotationHeader?._id,
        section: section,
        serviceId: service.serviceId || '',
        service: service.service,
        level: service.level,
        cost: service.cost || 0,
        fixed: service.fixed !== undefined ? service.fixed : true,
        variableType: service.variableType || 'hr',
        oemImporter: service.oemImporter !== undefined ? service.oemImporter : false,
        fleetOwner: service.fleetOwner !== undefined ? service.fleetOwner : false,
        scos: service.scos || 0,
        rrp: service.rrp || 0,
        uniqueId: service.uniqueId || null
      };
      
      // Create form data
      const formData = new FormData();
      formData.append('rowData', JSON.stringify(rowData));
      
      // Send request
      const response = await fetch(`?/updateQuotationRow`, {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error(`Server returned ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        // Add the new service to the data model
        if (data.serviceOfferings && data.serviceOfferings[section]) {
          const newService = {
            ...service,
            rowId: result.rowId
          };
          data.serviceOfferings[section] = [...data.serviceOfferings[section], newService];
        }
        
        // Recalculate section totals
        recalculateSectionTotals();
        
        showSuccessMessage = true;
        successMessage = 'Service added successfully';
        setTimeout(() => {
          showSuccessMessage = false;
        }, 3000);
      } else {
        throw new Error(result.error || 'Failed to add service');
      }
    } catch (error) {
      console.error('Error adding service:', error);
      showErrorMessage = true;
      errorMessage = error.message || 'An error occurred while adding the service';
      setTimeout(() => {
        showErrorMessage = false;
      }, 3000);
    }
  }
  
  // Handle service removal
  async function handleServiceRemove(section, event) {
    const { id } = event.detail;
    
    try {
      // Find the service in the section
      const service = data.serviceOfferings[section].find(s => s.id === id);
      
      if (!service || !service.rowId) {
        throw new Error('Service not found or has no row ID');
      }
      
      // Create form data
      const formData = new FormData();
      formData.append('rowId', service.rowId);
      
      // Send request
      const response = await fetch(`?/removeQuotationRow`, {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error(`Server returned ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        // Remove the service from the data model
        if (data.serviceOfferings && data.serviceOfferings[section]) {
          data.serviceOfferings[section] = data.serviceOfferings[section].filter(s => s.id !== id);
        }
        
        // Remove from selected IDs if present
        if (data.selectedIds && data.selectedIds[section]) {
          data.selectedIds[section] = data.selectedIds[section].filter(i => i !== id);
        }
        
        // Recalculate section totals
        recalculateSectionTotals();
        
        showSuccessMessage = true;
        successMessage = 'Service removed successfully';
        setTimeout(() => {
          showSuccessMessage = false;
        }, 3000);
      } else {
        throw new Error(result.error || 'Failed to remove service');
      }
    } catch (error) {
      console.error('Error removing service:', error);
      showErrorMessage = true;
      errorMessage = error.message || 'An error occurred while removing the service';
      setTimeout(() => {
        showErrorMessage = false;
      }, 3000);
    }
  }
  
  // Save the entire quote
  async function saveQuote() {
    try {
      isLoading = true;
      
      // Prepare selected services data
      const selectedServices = [];
      
      for (const section in data.serviceOfferings) {
        data.serviceOfferings[section].forEach(service => {
          if (data.selectedIds[section].includes(service.id)) {
            selectedServices.push({
              ...service,
              section: section
            });
          }
        });
      }
      
      // Prepare quote data
      const quoteData = {
        quotationId: data.quotationHeader?._id,
        computerId: $page.params.id,
        contractLength: data.quotationHeader?.contractLength || 12,
        contractStartDate: data.quotationHeader?.contractStartDate || new Date(),
        notes: data.quotationHeader?.notes || '',
        selectedServices: selectedServices
      };
      
      // Create form data
      const formData = new FormData();
      formData.append('quoteData', JSON.stringify(quoteData));
      
      // Send request
      const response = await fetch(`?/saveQuote`, {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error(`Server returned ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        showSuccessMessage = true;
        successMessage = 'Quote saved successfully';
        setTimeout(() => {
          showSuccessMessage = false;
          // Redirect to the quote list page
          goto(`/computer-id/${$page.params.id}/quote-list`);
        }, 2000);
      } else {
        throw new Error(result.error || 'Failed to save quote');
      }
    } catch (error) {
      console.error('Error saving quote:', error);
      showErrorMessage = true;
      errorMessage = error.message || 'An error occurred while saving the quote';
      setTimeout(() => {
        showErrorMessage = false;
      }, 3000);
    } finally {
      isLoading = false;
    }
  }
  
  // Go back to the quote list
  function goBackToList() {
    goto(`/computer-id/${$page.params.id}/quote-list`);
  }
</script>

<div class="container">
  <div class="header">
    <div class="back-button-container">
      <button class="back-button" on:click={goBackToList}>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="19" y1="12" x2="5" y2="12"></line>
          <polyline points="12 19 5 12 12 5"></polyline>
        </svg>
        Back to Quotations
      </button>
    </div>
    <h1 class="title">Service Quotation</h1>
    <div class="actions">
      <button class="save-button" on:click={saveQuote} disabled={isLoading}>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2z"></path>
          <polyline points="17 21 17 13 7 13 7 21"></polyline>
          <polyline points="7 3 7 8 15 8"></polyline>
        </svg>
        Save Quote
      </button>
    </div>
  </div>
  
  {#if data.error}
    <div class="error-box">
      {data.error}
    </div>
  {:else if !data.computer}
    <div class="loading">Loading...</div>
  {:else}
    <div class="computer-info">
      <div class="info-grid">
        <div class="info-item">
          <div class="info-label">Computer ID</div>
          <div class="info-value">{data.computer.computerId || 'N/A'}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Serial Number</div>
          <div class="info-value">{data.computer.serialNumber || 'N/A'}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Model</div>
          <div class="info-value">{data.computer.model || 'N/A'}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Customer</div>
          <div class="info-value">{data.customer?.name || 'N/A'}</div>
        </div>
      </div>
    </div>
    
    <div class="quote-details">
      <div class="section-header">
        <h2>Quote Details</h2>
      </div>
      <div class="quote-info-grid">
        <div class="info-item">
          <div class="info-label">Quote Number</div>
          <div class="info-value">{data.quotationHeader?.quotationNumber || 'New Quote'}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Status</div>
          <div class="info-value">{data.quotationHeader?.status || 'Draft'}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Contract Length</div>
          <div class="info-value">{data.quotationHeader?.contractLength || 12} months</div>
        </div>
        <div class="info-item">
          <div class="info-label">Contract Start Date</div>
          <div class="info-value">{formatDate(data.quotationHeader?.contractStartDate) || 'Not set'}</div>
        </div>
      </div>
    </div>
    
    <div class="service-sections">
      <!-- Base Contract Section -->
      <div class="section">
        <div class="section-header">
          <h2>Base Contract</h2>
          <div class="section-total">Total: {formatCurrency(data.sectionTotals.baseContract)}</div>
        </div>
        <div class="grid-container">
          <div class="grid-header">
            <div class="header-cell">Row Type</div>
            <div class="header-cell">Order</div>
            <div class="header-cell">Package Name</div>
            <div class="header-cell">Activity</div>
            <div class="header-cell">Cost</div>
            <div class="header-cell">OEM Importer</div>
            <div class="header-cell">Fleet Owner</div>
            <div class="header-cell">Customer Specific</div>
            <div class="header-cell">Required</div>
            <div class="header-cell">Include in Offer</div>
            <div class="header-cell">Created</div>
            <div class="header-cell">Updated</div>
            <div class="header-cell">Actions</div>
          </div>
          {#each data.serviceOfferings.baseContract as service}
            <div class="grid-row">
              <div class="cell">{service.rowType}</div>
              <div class="cell">{service.order}</div>
              <div class="cell">{service.packageName}</div>
              <div class="cell">{service.activity}</div>
              <div class="cell">{formatCurrency(service.cost)}</div>
              <div class="cell">{service.oemImporter}</div>
              <div class="cell">{service.fleetOwner}</div>
              <div class="cell">{service.customerSpecific}</div>
              <div class="cell">{service.required}</div>
              <div class="cell">{service.includeInOffer}</div>
              <div class="cell">{formatDate(service.created)}</div>
              <div class="cell">{formatDate(service.updated)}</div>
              <div class="cell">
                <button class="action-button edit" on:click={() => handleServiceEdit('baseContract', { detail: { service } })}>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                  </svg>
                </button>
              </div>
            </div>
          {/each}
        </div>
      </div>
      
      <!-- Dealer Add-Ons Section -->
      <div class="section">
        <div class="section-header">
          <h2>Dealer Add-Ons</h2>
          <div class="section-total">Total: {formatCurrency(data.sectionTotals.dealerAddOns)}</div>
        </div>
        <div class="grid-container">
          <div class="grid-header">
            <div class="header-cell">Row Type</div>
            <div class="header-cell">Order</div>
            <div class="header-cell">Package Name</div>
            <div class="header-cell">Activity</div>
            <div class="header-cell">Cost</div>
            <div class="header-cell">OEM Importer</div>
            <div class="header-cell">Fleet Owner</div>
            <div class="header-cell">Customer Specific</div>
            <div class="header-cell">Required</div>
            <div class="header-cell">Include in Offer</div>
            <div class="header-cell">Created</div>
            <div class="header-cell">Updated</div>
            <div class="header-cell">Actions</div>
          </div>
          {#each data.serviceOfferings.dealerAddOns as service}
            <div class="grid-row">
              <div class="cell">{service.rowType}</div>
              <div class="cell">{service.order}</div>
              <div class="cell">{service.packageName}</div>
              <div class="cell">{service.activity}</div>
              <div class="cell">{formatCurrency(service.cost)}</div>
              <div class="cell">{service.oemImporter}</div>
              <div class="cell">{service.fleetOwner}</div>
              <div class="cell">{service.customerSpecific}</div>
              <div class="cell">{service.required}</div>
              <div class="cell">{service.includeInOffer}</div>
              <div class="cell">{formatDate(service.created)}</div>
              <div class="cell">{formatDate(service.updated)}</div>
              <div class="cell">
                <button class="action-button edit" on:click={() => handleServiceEdit('dealerAddOns', { detail: { service } })}>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                  </svg>
                </button>
                <button class="action-button delete" on:click={() => handleServiceRemove('dealerAddOns', { detail: { id: service.id } })}>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                </button>
              </div>
            </div>
          {/each}
        </div>
      </div>
      
      <!-- Support Services Section -->
      <div class="section">
        <div class="section-header">
          <h2>Support Services</h2>
          <div class="section-total">Total: {formatCurrency(data.sectionTotals.supportServices)}</div>
        </div>
        <div class="grid-container">
          <div class="grid-header">
            <div class="header-cell">Row Type</div>
            <div class="header-cell">Order</div>
            <div class="header-cell">Package Name</div>
            <div class="header-cell">Activity</div>
            <div class="header-cell">Cost</div>
            <div class="header-cell">OEM Importer</div>
            <div class="header-cell">Fleet Owner</div>
            <div class="header-cell">Customer Specific</div>
            <div class="header-cell">Required</div>
            <div class="header-cell">Include in Offer</div>
            <div class="header-cell">Created</div>
            <div class="header-cell">Updated</div>
            <div class="header-cell">Actions</div>
          </div>
          {#each data.serviceOfferings.supportServices as service}
            <div class="grid-row">
              <div class="cell">{service.rowType}</div>
              <div class="cell">{service.order}</div>
              <div class="cell">{service.packageName}</div>
              <div class="cell">{service.activity}</div>
              <div class="cell">{formatCurrency(service.cost)}</div>
              <div class="cell">{service.oemImporter}</div>
              <div class="cell">{service.fleetOwner}</div>
              <div class="cell">{service.customerSpecific}</div>
              <div class="cell">{service.required}</div>
              <div class="cell">{service.includeInOffer}</div>
              <div class="cell">{formatDate(service.created)}</div>
              <div class="cell">{formatDate(service.updated)}</div>
              <div class="cell">
                <button class="action-button edit" on:click={() => handleServiceEdit('supportServices', { detail: { service } })}>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                  </svg>
                </button>
                <button class="action-button delete" on:click={() => handleServiceRemove('supportServices', { detail: { id: service.id } })}>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                </button>
              </div>
            </div>
          {/each}
        </div>
      </div>
      
      <!-- Retirement Plans Section -->
      <div class="section">
        <div class="section-header">
          <h2>Retirement Plans</h2>
          <div class="section-total">Total: {formatCurrency(data.sectionTotals.retirementPlans)}</div>
        </div>
        <div class="grid-container">
          <div class="grid-header">
            <div class="header-cell">Row Type</div>
            <div class="header-cell">Order</div>
            <div class="header-cell">Package Name</div>
            <div class="header-cell">Activity</div>
            <div class="header-cell">Cost</div>
            <div class="header-cell">OEM Importer</div>
            <div class="header-cell">Fleet Owner</div>
            <div class="header-cell">Customer Specific</div>
            <div class="header-cell">Required</div>
            <div class="header-cell">Include in Offer</div>
            <div class="header-cell">Created</div>
            <div class="header-cell">Updated</div>
            <div class="header-cell">Actions</div>
          </div>
          {#each data.serviceOfferings.retirementPlans as service}
            <div class="grid-row">
              <div class="cell">{service.rowType}</div>
              <div class="cell">{service.order}</div>
              <div class="cell">{service.packageName}</div>
              <div class="cell">{service.activity}</div>
              <div class="cell">{formatCurrency(service.cost)}</div>
              <div class="cell">{service.oemImporter}</div>
              <div class="cell">{service.fleetOwner}</div>
              <div class="cell">{service.customerSpecific}</div>
              <div class="cell">{service.required}</div>
              <div class="cell">{service.includeInOffer}</div>
              <div class="cell">{formatDate(service.created)}</div>
              <div class="cell">{formatDate(service.updated)}</div>
              <div class="cell">
                <button class="action-button edit" on:click={() => handleServiceEdit('retirementPlans', { detail: { service } })}>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                  </svg>
                </button>
                <button class="action-button delete" on:click={() => handleServiceRemove('retirementPlans', { detail: { id: service.id } })}>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                </button>
              </div>
            </div>
          {/each}
        </div>
      </div>
    </div>
    
    <div class="quote-summary">
      <div class="summary-header">
        <h2>Quote Summary</h2>
      </div>
      <div class="summary-grid">
        <div class="summary-item">
          <div class="summary-label">Base Contract</div>
          <div class="summary-value">{formatCurrency(data.sectionTotals.baseContract)}</div>
        </div>
        <div class="summary-item">
          <div class="summary-label">Dealer Add-Ons</div>
          <div class="summary-value">{formatCurrency(data.sectionTotals.dealerAddOns)}</div>
        </div>
        <div class="summary-item">
          <div class="summary-label">Support Services</div>
          <div class="summary-value">{formatCurrency(data.sectionTotals.supportServices)}</div>
        </div>
        <div class="summary-item">
          <div class="summary-label">Retirement Plans</div>
          <div class="summary-value">{formatCurrency(data.sectionTotals.retirementPlans)}</div>
        </div>
        <div class="summary-item total">
          <div class="summary-label">Total</div>
          <div class="summary-value">{formatCurrency(data.sectionTotals.total)}</div>
        </div>
      </div>
    </div>
  {/if}
  
  {#if showSuccessMessage}
    <div class="message success">
      {successMessage}
    </div>
  {/if}
  
  {#if showErrorMessage}
    <div class="message error">
      {errorMessage}
    </div>
  {/if}
  
  {#if isLoading}
    <div class="loading-overlay">
      <div class="spinner"></div>
      <p>Saving quote...</p>
    </div>
  {/if}
</div>

<style>
  .container {
    width: 100%;
    max-width: 100%;
    padding: 1rem;
    box-sizing: border-box;
  }

  .grid-container {
    width: 100%;
    overflow-x: auto;
    border: 1px solid #dee2e6;
    border-radius: 8px;
  }

  .grid-header {
    display: grid;
    grid-template-columns: 
      minmax(120px, 1fr)  /* Row Type */
      minmax(80px, 1fr)   /* Order */
      minmax(150px, 1fr)  /* Package Name */
      minmax(150px, 1fr)  /* Activity */
      minmax(100px, 1fr)  /* Cost */
      minmax(120px, 1fr)  /* OEM Importer */
      minmax(120px, 1fr)  /* Fleet Owner */
      minmax(120px, 1fr)  /* Customer Specific */
      minmax(100px, 1fr)  /* Required */
      minmax(120px, 1fr)  /* Include in Offer */
      minmax(120px, 1fr)  /* Created */
      minmax(120px, 1fr)  /* Updated */
      minmax(100px, 1fr); /* Actions */
    gap: 0.5rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    position: sticky;
    top: 0;
  }

  .grid-row {
    display: grid;
    grid-template-columns: 
      minmax(120px, 1fr)  /* Row Type */
      minmax(80px, 1fr)   /* Order */
      minmax(150px, 1fr)  /* Package Name */
      minmax(150px, 1fr)  /* Activity */
      minmax(100px, 1fr)  /* Cost */
      minmax(120px, 1fr)  /* OEM Importer */
      minmax(120px, 1fr)  /* Fleet Owner */
      minmax(120px, 1fr)  /* Customer Specific */
      minmax(100px, 1fr)  /* Required */
      minmax(120px, 1fr)  /* Include in Offer */
      minmax(120px, 1fr)  /* Created */
      minmax(120px, 1fr)  /* Updated */
      minmax(100px, 1fr); /* Actions */
    gap: 0.5rem;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    align-items: center;
  }

  .grid-row:hover {
    background-color: #f8f9fa;
  }

  .header-cell {
    color: #495057;
    font-weight: 500;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .cell {
    color: #212529;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 0.5rem;
  }

  .error-box {
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    color: #721c24;
  }

  .loading {
    padding: 2rem;
    text-align: center;
    color: #6c757d;
  }

  .computer-info, .quote-details {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .info-grid, .quote-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
  }

  .info-item {
    margin-bottom: 0.5rem;
  }

  .info-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
  }

  .info-value {
    font-size: 1rem;
  }

  .section {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .section-header h2 {
    margin: 0;
    font-size: 1.2rem;
  }

  .section-total {
    font-weight: bold;
    color: #0056b3;
  }

  .quote-summary {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .summary-header {
    margin-bottom: 1rem;
  }

  .summary-header h2 {
    margin: 0;
    font-size: 1.2rem;
  }

  .summary-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .summary-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
  }

  .summary-item.total {
    font-weight: bold;
    font-size: 1.1rem;
    border-top: 2px solid #ddd;
    border-bottom: none;
    padding-top: 1rem;
  }

  .message {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    padding: 1rem;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    animation: fadeIn 0.3s, fadeOut 0.3s 2.7s;
  }

  .success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  .error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }

  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid #007bff;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
  }

  @media (max-width: 768px) {
    .header {
      flex-direction: column;
      gap: 1rem;
    }
    
    .title {
      order: -1;
    }
    
    .info-grid, .quote-info-grid {
      grid-template-columns: 1fr;
    }
  }
  
  .actions {
    display: flex;
    gap: 0.5rem;
  }

  .action-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    padding: 0;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .action-button.edit {
    background-color: #4a90e2;
    color: white;
  }

  .action-button.edit:hover {
    background-color: #357abd;
  }

  .action-button.delete {
    background-color: #dc3545;
    color: white;
  }

  .action-button.delete:hover {
    background-color: #c82333;
  }
</style>
