import { redirect } from '@sveltejs/kit';

/**
 * Redirects to the quote page or quote list based on the presence of a quote ID.
 * 
 * @param {Object} params - The route parameters.
 * @param {string} params.id - The computer ID.
 * @param {Object} url - The URL object.
 * @param {URLSearchParams} url.searchParams - The search parameters.
 * 
 * @returns {Promise<Redirect>} A redirect to the quote page or quote list.
 */
/** @type {import('./$types').PageServerLoad} */
export async function load({ params, url }) {
  const quoteId = url.searchParams.get('quoteId');
  const computerId = params.id;

  // Redirect to the quote page if we have a quote ID
  if (quoteId && typeof quoteId === 'string') {
    throw redirect(307, `/computer-id/${computerId}/quote/${quoteId}`);
  }

  // Otherwise, redirect to the quote list
  throw redirect(307, `/computer-id/${computerId}/quote-list`);
}
