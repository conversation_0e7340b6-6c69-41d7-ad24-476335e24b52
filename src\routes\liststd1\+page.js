import { error } from '@sveltejs/kit';

/**
 * Convert a string to PascalCase to match MongoDB collection naming conventions
 * @param {string} str - The string to convert
 * @returns {string} PascalCase string
 */
function toPascalCase(str) {
  // First ensure we have a string with first letter capitalized
  if (!str) return '';
  
  // For already PascalCase collections, just return as is
  if (/^[A-Z][a-zA-Z0-9]*$/.test(str)) {
    return str;
  }
  
  // Handle kebab-case, snake_case, or spaces
  return str
    .split(/[-_\s]+/)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join('');
}

/** @type {import('./$types').PageLoad} */
export async function load({ fetch, url }) {
  try {
    // Get collection from URL params
    const collection = url.searchParams.get('collection') || 'customers';
    const filterlist = url.searchParams.get('filterlist') || 'name';
    const filterValue = url.searchParams.get('filterValue') || '';
    
    // Use our new collection-data API endpoint that handles dynamic collections
    const apiUrl = `/api/collection-data?collection=${collection}&filterlist=${filterlist}`;
    
    // Add filterValue if provided
    const finalApiUrl = filterValue 
      ? `${apiUrl}&filterValue=${encodeURIComponent(filterValue)}` 
      : apiUrl;
    
    // Fetch items from the API
    const res = await fetch(finalApiUrl);
    
    if (!res.ok) {
      const errorData = await res.json().catch(() => ({}));
      throw error(res.status, errorData.error || `Failed to load ${collection} data`);
    }
    
    const items = await res.json();
    
    // Convert collection to PascalCase for MongoDB operations
    const dbCollection = toPascalCase(collection);
    
    console.log(`Loaded ${items.length} items from collection ${dbCollection}`);
    
    // Return the data with collection info
    return {
      items,
      collection,
      dbCollection,
      filterlist
    };
  } catch (err) {
    console.error('Error loading data:', err);
    throw error(500, err instanceof Error ? err.message : 'Unknown error');
  }
}
