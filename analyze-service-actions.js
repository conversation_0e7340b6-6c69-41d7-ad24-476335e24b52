import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function analyzeServiceActions() {
    const client = new MongoClient(uri);
    
    try {
        await client.connect();
        console.log('Connected to MongoDB');
        
        const db = client.db(dbName);
        const collection = db.collection('PartNumbersServiceCodeAction');
        
        // Get total count
        const totalCount = await collection.countDocuments();
        console.log('\nTotal documents in PartNumbersServiceCodeAction:', totalCount);

        // Check a sample document
        const sample = await collection.findOne({});
        console.log('\nSample document:');
        console.log(JSON.stringify(sample, null, 2));

        // Count documents where ProductValidityGroup equals ProductDesignation
        const matchingCount = await collection.countDocuments({
            $expr: {
                $eq: ["$ProductValidityGroup", "$ProductDesignation"]
            }
        });
        console.log('\nDocuments where ProductValidityGroup equals ProductDesignation:', matchingCount);

        // Show some examples where they are different
        const differentExamples = await collection.find({
            $expr: {
                $ne: ["$ProductValidityGroup", "$ProductDesignation"]
            }
        }).limit(5).toArray();

        console.log('\nExamples where ProductValidityGroup differs from ProductDesignation:');
        differentExamples.forEach(doc => {
            console.log(`\nProductDesignation: ${doc.ProductDesignation}`);
            console.log(`ProductValidityGroup: ${doc.ProductValidityGroup}`);
            console.log('---');
        });

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await client.close();
        console.log('\nConnection closed');
    }
}

analyzeServiceActions().catch(console.error);
