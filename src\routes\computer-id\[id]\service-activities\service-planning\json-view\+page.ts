import { error } from '@sveltejs/kit';
import type { PageLoad } from './$types';

export const load: PageLoad = async ({ params, fetch }) => {
  try {
    // Load computer data
    const computerResponse = await fetch(`/api/computers/${params.id}`);
    if (!computerResponse.ok) {
      throw new Error(`Failed to load computer data: ${computerResponse.statusText}`);
    }
    const computer = await computerResponse.json();
    
    // Load service planning data
    const planningResponse = await fetch(`/api/computer-id/${params.id}/service-activities/service-planning`);
    if (!planningResponse.ok) {
      throw new Error(`Failed to load service planning data: ${planningResponse.statusText}`);
    }
    const planningData = await planningResponse.json();
    
    // Load contract data if available
    let contract = null;
    if (computer.contractId) {
      const contractResponse = await fetch(`/api/contracts/${computer.contractId}`);
      if (contractResponse.ok) {
        contract = await contractResponse.json();
      }
    }
    
    return {
      computer,
      workload: planningData.workload || [],
      activeFaultCodes: planningData.activeFaultCodes || [],
      actualComputerHours: planningData.actualComputerHours || [],
      productValidityGroup: planningData.productValidityGroup || [],
      contract,
      success: true
    };
  } catch (err) {
    console.error('Error loading JSON view data:', err);
    throw error(500, {
      message: `Error loading service planning data: ${err.message}`
    });
  }
};
