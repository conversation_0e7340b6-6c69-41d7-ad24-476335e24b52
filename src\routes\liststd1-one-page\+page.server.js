import { error } from '@sveltejs/kit';
import { getCollection } from '$lib/db/mongo';
import { ObjectId } from 'mongodb';

/**
 * Load function for the liststd1-one-page route
 * @type {import('./$types').PageServerLoad}
 */
export async function load({ url }) {
  try {
    // Get parameters from URL
    const itemId = url.searchParams.get('itemId');
    const collection = url.searchParams.get('collection') || 'Customers';
    
    if (!itemId) {
      throw error(400, 'Item ID is required');
    }
    
    // Validate ObjectId format
    if (!ObjectId.isValid(itemId)) {
      throw error(400, 'Invalid item ID format');
    }
    
    // Get collection from MongoDB - using PascalCase as per project convention
    const coll = await getCollection(collection);
    
    // Find the item by ID
    const item = await coll.findOne({ _id: new ObjectId(itemId) });
    
    if (!item) {
      throw error(404, 'Item not found');
    }

    // Get related items counts and data
    let relatedItems = {};
    
    if (collection === 'Customers') {
      // Get customer computers count
      const computersCollection = await getCollection('CustomerComputers');
      const computers = await computersCollection.find({ 
        customerId: new ObjectId(itemId) 
      }).toArray();
      
      // Get service contracts count
      const contractsCollection = await getCollection('ServiceContracts');
      const contracts = await contractsCollection.find({ 
        customerId: new ObjectId(itemId) 
      }).toArray();
      
      // Convert ObjectIds to strings for client-side use
      const serializedComputers = computers.map(computer => {
        const serialized = { ...computer };
        if (serialized._id) serialized._id = serialized._id.toString();
        if (serialized.customerId) serialized.customerId = serialized.customerId.toString();
        return serialized;
      });
      
      const serializedContracts = contracts.map(contract => {
        const serialized = { ...contract };
        if (serialized._id) serialized._id = serialized._id.toString();
        if (serialized.customerId) serialized.customerId = serialized.customerId.toString();
        return serialized;
      });
      
      relatedItems = {
        computers: serializedComputers,
        computerCount: computers.length,
        serviceContracts: serializedContracts,
        serviceContractCount: contracts.length
      };
    }

    // Convert ObjectId to string for client-side use
    const itemData = { ...item };
    itemData._id = itemData._id.toString();
    
    // Return the item data with related items
    return {
      collection,
      itemId,
      item: itemData,
      relatedItems
    };
  } catch (err) {
    console.error('Error loading item:', err);
    throw error(500, err instanceof Error ? err.message : 'Unknown error');
  }
}

/**
 * Actions for the liststd1-one-page route
 * @type {import('./$types').Actions}
 */
export const actions = {
  /**
   * Update an item in the database
   */
  updateItem: async ({ request, url }) => {
    try {
      const collection = url.searchParams.get('collection') || 'Customers';
      const data = await request.formData();
      
      // Get the ID from the form data
      const id = data.get('_id');
      if (!id || typeof id !== 'string') {
        throw error(400, 'Valid item ID is required');
      }
      
      // Convert form data to object and remove _id field
      const formData = Object.fromEntries(data);
      delete formData._id;
      
      // Get MongoDB collection
      const collectionInstance = await getCollection(collection);
      
      // Update in database
      const result = await collectionInstance.updateOne(
        { _id: new ObjectId(id) },
        { $set: formData }
      );
      
      if (result.matchedCount === 0) {
        throw error(404, 'Item not found');
      }
      
      return { success: true };
    } catch (err) {
      console.error('Error updating item:', err);
      throw error(500, err instanceof Error ? err.message : 'Unknown error');
    }
  },
  
  /**
   * Delete an item from the database
   */
  deleteItem: async ({ request, url }) => {
    try {
      const collection = url.searchParams.get('collection') || 'Customers';
      const data = await request.formData();
      
      // Get the ID from the form data
      const id = data.get('_id');
      if (!id || typeof id !== 'string') {
        throw error(400, 'Valid item ID is required');
      }
      
      // Get MongoDB collection
      const collectionInstance = await getCollection(collection);
      
      // Delete document
      const result = await collectionInstance.deleteOne({ _id: new ObjectId(id) });
      
      if (result.deletedCount === 0) {
        throw error(404, 'Item not found');
      }
      
      return { success: true };
    } catch (err) {
      console.error('Error deleting item:', err);
      throw error(500, err instanceof Error ? err.message : 'Unknown error');
    }
  }
};
