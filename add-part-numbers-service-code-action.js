import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

// From memory: PartNumbersServiceCodeAction collection field names
const sampleActions = [
    {
        ServiceCode: 'S-D13', // From memory: no spaces
        PartNumber: '22677426', // From memory: no spaces
        ActionType: 'Replace', // From memory: no spaces
        'Product Validity Group': 'D13', // From memory: with spaces
        Quantity: 1,
        'Unit of Measure': 'Pieces' // From memory: with spaces
    },
    {
        ServiceCode: 'S-D13',
        PartNumber: '1067198',
        ActionType: 'Replace',
        'Product Validity Group': 'D13',
        Quantity: 6,
        'Unit of Measure': 'Pieces'
    },
    {
        ServiceCode: 'E-D13',
        PartNumber: '22677426',
        ActionType: 'Replace',
        'Product Validity Group': 'D13',
        Quantity: 1,
        'Unit of Measure': 'Pieces'
    },
    {
        ServiceCode: 'F-D13',
        PartNumber: '1067198',
        ActionType: 'Replace',
        'Product Validity Group': 'D13',
        Quantity: 6,
        'Unit of Measure': 'Pieces'
    }
];

async function main() {
    try {
        console.log('Connecting to MongoDB...');
        await client.connect();
        console.log('Connected successfully');

        // From memory: database name is ServiceContracts
        const db = client.db('ServiceContracts');
        // From memory: collection name is PartNumbersServiceCodeAction in PascalCase
        const collection = db.collection('PartNumbersServiceCodeAction');

        // Clear existing data
        try {
            await collection.deleteMany({});
            console.log('Cleared existing data');
        } catch (error) {
            console.error('Error clearing existing data:', error);
        }

        // Insert sample actions
        try {
            const result = await collection.insertMany(sampleActions);
            console.log(`Inserted ${result.insertedCount} actions`);
        } catch (error) {
            console.error('Error inserting sample actions:', error);
        }

    } catch (error) {
        console.error('Error:', error);
    } finally {
        try {
            await client.close();
            console.log('Connection closed');
        } catch (error) {
            console.error('Error closing connection:', error);
        }
    }
}

main().catch(error => {
    console.error('Unhandled error:', error);
});
