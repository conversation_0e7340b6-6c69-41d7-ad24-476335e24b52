import serviceContractDB from './serviceContractDB.js';
const db = serviceContractDB.db;
const COLLECTION = 'PartNumbersServiceCodeAction';

// Get all part number service code actions (with optional filter)
export async function getPartNumbersServiceCodeActions(filter = {}, options = {}) {
  const defaultOptions = { sort: { ServiceCode: 1 }, ...options };
  const cursor = db.collection(COLLECTION).find(filter, defaultOptions);
  const docs = await cursor.toArray();
  return docs.map(serviceContractDB.serializeDocument);
}

// Get a single action by ID
export async function getPartNumbersServiceCodeActionById(id) {
  const objectId = serviceContractDB.createObjectId(id);
  if (!objectId) return null;
  const doc = await db.collection(COLLECTION).findOne({ _id: objectId });
  return doc ? serviceContractDB.serializeDocument(doc) : null;
}

// Create a new part number service code action
export async function createPartNumbersServiceCodeAction(data) {
  const now = new Date();
  const item = {
    ...data,
    createdAt: now,
    updatedAt: now
  };
  const result = await db.collection(COLLECTION).insertOne(item);
  return result.insertedId.toString();
}

// Update an existing action
export async function updatePartNumbersServiceCodeAction(id, updates) {
  const objectId = serviceContractDB.createObjectId(id);
  if (!objectId) return null;
  const updateData = { ...updates, updatedAt: new Date() };
  if (updateData._id) delete updateData._id;
  const result = await db.collection(COLLECTION).findOneAndUpdate(
    { _id: objectId },
    { $set: updateData },
    { returnDocument: 'after' }
  );
  return result.value ? serviceContractDB.serializeDocument(result.value) : null;
}

// Delete an action
export async function deletePartNumbersServiceCodeAction(id) {
  const objectId = serviceContractDB.createObjectId(id);
  if (!objectId) return false;
  const result = await db.collection(COLLECTION).deleteOne({ _id: objectId });
  return result.deletedCount > 0;
}
