// Script to convert hours field to integer in ActualComputerHours collection

import { MongoClient, ObjectId } from 'mongodb';

async function fixHoursField() {
  // Connection URI
  const uri = 'mongodb://localhost:27017';
  const client = new MongoClient(uri);

  try {
    // Connect to MongoDB
    await client.connect();
    console.log('Connected to MongoDB');

    // Access the ServiceContracts database and ActualComputerHours collection
    const db = client.db('ServiceContracts');
    const collection = db.collection('ActualComputerHours');

    // Find all documents
    const docs = await collection.find({}).toArray();
    console.log(`Found ${docs.length} documents in ActualComputerHours collection`);

    // Track updates
    let updatedCount = 0;

    // Process each document
    for (const doc of docs) {
      if (doc.hours !== undefined) {
        // Convert to integer
        const hoursInt = parseInt(doc.hours);
        
        // Check if conversion is needed (not already an integer)
        if (!isNaN(hoursInt) && (typeof doc.hours !== 'number' || doc.hours !== hoursInt)) {
          // Update the document
          await collection.updateOne(
            { _id: doc._id },
            { $set: { hours: hoursInt } }
          );
          console.log(`Updated document ${doc._id}: hours changed from ${doc.hours} (${typeof doc.hours}) to ${hoursInt} (number)`);
          updatedCount++;
        }
      }
    }

    console.log(`Updated ${updatedCount} documents to ensure hours is an integer`);

    // Verify some updated documents
    const updatedSamples = await collection.find({}).limit(5).toArray();
    console.log('\nSample documents after update:');
    updatedSamples.forEach(doc => {
      console.log(`ID: ${doc._id}, Hours: ${doc.hours} (type: ${typeof doc.hours})`);
    });

  } catch (error) {
    console.error('Error updating documents:', error);
  } finally {
    // Close the connection
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the function
fixHoursField().catch(console.error);
