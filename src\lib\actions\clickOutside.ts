/**
 * Svelte action that detects clicks outside of the element
 * and calls the provided callback function
 */
export function clickOutside(node: HTMLElement, params: { enabled: boolean; callback: () => void }) {
  let enabled = params?.enabled ?? true;
  let callback = params?.callback ?? (() => {});
  
  function onClick(event: MouseEvent) {
    if (!enabled) return;
    
    if (node && !node.contains(event.target as Node) && !event.defaultPrevented) {
      callback();
    }
  }
  
  document.addEventListener('click', onClick, true);
  
  return {
    update(newParams: { enabled: boolean; callback: () => void }) {
      enabled = newParams?.enabled ?? true;
      callback = newParams?.callback ?? (() => {});
    },
    destroy() {
      document.removeEventListener('click', onClick, true);
    }
  };
}
