import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

export async function POST({ params, request }) {
    try {
        const db = client.db('ServiceContracts');
        const computerId = params.id;
        console.log('--- POST /computer-id/[id] ---');
        const body = await request.json();
        console.log('Received body:', body);
        if (!ObjectId.isValid(computerId)) {
            console.error('Invalid computer ID:', computerId);
            return json({ error: 'Invalid computer ID' }, { status: 400 });
        }
        // Only allow updates to all editable fields (PascalCase)
        const updateFields = {};

        // Normalize all incoming date fields to ISO string for comparison
        function normalizeDate(val) {
            if (!val) return null;
            const d = new Date(val);
            return d.toISOString();
        }

        // Accept both string (YYYY-MM-DD) and ISO string for dates, always convert to Date
        function parseDate(val) {
            if (!val) return null;
            if (val instanceof Date) return val;
            // Accept YYYY-MM-DD or YYYY-MM-DDTHH:mm:ss.sssZ
            if (/^\d{4}-\d{2}-\d{2}$/.test(val)) {
                return new Date(val + 'T00:00:00.000Z');
            }
            return new Date(val);
        }

        // Fetch current document for comparison
        const current = await db.collection('CustomerComputers').findOne({ _id: new ObjectId(computerId) });

        // Only add to updateFields if value is different (normalize date comparison)
        if ('PurchaseDate' in body) {
            const newVal = normalizeDate(body.PurchaseDate);
            const oldVal = current?.PurchaseDate ? normalizeDate(current.PurchaseDate) : null;
            if (newVal !== oldVal) updateFields.PurchaseDate = parseDate(body.PurchaseDate);
        }
        if ('DeliveryDate' in body) {
            const newVal = normalizeDate(body.DeliveryDate);
            const oldVal = current?.DeliveryDate ? normalizeDate(current.DeliveryDate) : null;
            if (newVal !== oldVal) updateFields.DeliveryDate = parseDate(body.DeliveryDate);
        }
        if ('ContractDate' in body) {
            const newVal = normalizeDate(body.ContractDate);
            const oldVal = current?.ContractDate ? normalizeDate(current.ContractDate) : null;
            if (newVal !== oldVal) updateFields.ContractDate = parseDate(body.ContractDate);
        }
        if ('WarrantyEndDate' in body) {
            const newVal = normalizeDate(body.WarrantyEndDate);
            const oldVal = current?.WarrantyEndDate ? normalizeDate(current.WarrantyEndDate) : null;
            if (newVal !== oldVal) updateFields.WarrantyEndDate = parseDate(body.WarrantyEndDate);
        }
        if ('InstallDate' in body) {
            const newVal = normalizeDate(body.InstallDate);
            const oldVal = current?.InstallDate ? normalizeDate(current.InstallDate) : null;
            if (newVal !== oldVal) updateFields.InstallDate = parseDate(body.InstallDate);
        }
        if ('ServiceContractStart' in body) {
            const newVal = normalizeDate(body.ServiceContractStart);
            const oldVal = current?.ServiceContractStart ? normalizeDate(current.ServiceContractStart) : null;
            if (newVal !== oldVal) updateFields.ServiceContractStart = parseDate(body.ServiceContractStart);
        }
        if ('ServiceContractEnd' in body) {
            const newVal = normalizeDate(body.ServiceContractEnd);
            const oldVal = current?.ServiceContractEnd ? normalizeDate(current.ServiceContractEnd) : null;
            if (newVal !== oldVal) updateFields.ServiceContractEnd = parseDate(body.ServiceContractEnd);
        }

        // Non-date fields (always update if value is different)
        function updateIfChanged(key) {
            if (key in body && body[key] !== current?.[key]) {
                updateFields[key] = body[key];
            }
        }
        updateIfChanged('Name');
        updateIfChanged('Model');
        updateIfChanged('SerialNumber');
        updateIfChanged('Type');
        updateIfChanged('OperatingSystem');
        updateIfChanged('ProductName');
        updateIfChanged('SupplierProductName');
        updateIfChanged('HoursAtContractStart');

        console.log('Update fields:', updateFields);
        if (Object.keys(updateFields).length === 0) {
            console.error('No valid fields to update.');
            return json({ error: 'No valid fields to update.' }, { status: 400 });
        }
        const result = await db.collection('CustomerComputers').updateOne(
            { _id: new ObjectId(computerId) },
            { $set: updateFields }
        );
        console.log('Update result:', result);
        if (result.modifiedCount === 0) {
            console.error('No changes made.');
            return json({ error: 'No changes made.' }, { status: 400 });
        }
        return json({ success: true });
    } catch (err) {
        console.error('Error updating computer:', err);
        return json({ error: err instanceof Error ? err.message : 'Internal server error' }, { status: 500 });
    }
}
