import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function updateFieldName() {
    try {
        await client.connect();
        const db = client.db('ServiceContracts');
        const collection = db.collection('ProductValidityGroupPartNumber');

        const result = await collection.updateMany(
            { "Product Validity GRoup": { $exists: true } },
            [
                { 
                    $addFields: { 
                        "ProductValidityGroup": "$Product Validity GRoup"
                    }
                },
                { 
                    $unset: "Product Validity GRoup"
                }
            ]
        );

        console.log(`Updated ${result.modifiedCount} documents`);
    } catch (error) {
        console.error('Error:', error);
    } finally {
        await client.close();
    }
}

updateFieldName();
