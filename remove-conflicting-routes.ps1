# Script to remove conflicting routes

Write-Host "Removing conflicting routes..."

# Define paths to conflicting routes
$route1 = "C:\SvelteProjects\Svelte-Mongo3\src\routes\api\quote-rows\update\[id]"
$route2 = "C:\SvelteProjects\Svelte-Mongo3\src\routes\api\quote-rows\update\[rowId]"

# Check if routes exist
$route1Exists = Test-Path $route1
$route2Exists = Test-Path $route2

Write-Host "Route 1 exists: $route1Exists"
Write-Host "Route 2 exists: $route2Exists"

# Remove route 1 if it exists
if ($route1Exists) {
    Write-Host "Removing route 1: $route1"
    Remove-Item -Path $route1 -Recurse -Force
    if (Test-Path $route1) {
        Write-Host "Failed to remove route 1" -ForegroundColor Red
    } else {
        Write-Host "Successfully removed route 1" -ForegroundColor Green
    }
}

# Remove route 2 if it exists
if ($route2Exists) {
    Write-Host "Removing route 2: $route2"
    Remove-Item -Path $route2 -Recurse -Force
    if (Test-Path $route2) {
        Write-Host "Failed to remove route 2" -ForegroundColor Red
    } else {
        Write-Host "Successfully removed route 2" -ForegroundColor Green
    }
}

# Check update directory (parent directory of conflicting routes)
$updateDir = "C:\SvelteProjects\Svelte-Mongo3\src\routes\api\quote-rows\update"
if (Test-Path $updateDir) {
    # List contents to see if anything remains
    $remainingItems = Get-ChildItem -Path $updateDir
    if ($remainingItems.Count -eq 0) {
        Write-Host "Update directory is empty, removing it..."
        Remove-Item -Path $updateDir -Force
    } else {
        Write-Host "Update directory still contains items:"
        foreach ($item in $remainingItems) {
            Write-Host "  - $($item.Name)"
        }
    }
}

Write-Host "Route removal complete. Please restart your development server."
