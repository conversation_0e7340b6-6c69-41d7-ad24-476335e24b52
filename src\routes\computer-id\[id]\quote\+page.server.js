import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/** @type {import('./$types').PageServerLoad} */
export async function load({ params }) {
    try {
        const db = client.db('ServiceContracts');
        const computerId = params.id;
        
        console.log(`Loading quote data for computer ID: ${computerId}`);
        
        // Get computer details
        const computer = await db.collection('CustomerComputers')
            .findOne({ _id: new ObjectId(computerId) });
            
        if (!computer) {
            throw new Error('Computer not found');
        }
        
        // Get existing quotation header for this computer
        const quotationHeader = await db.collection('QuotationHeader')
            .findOne({ computerId: computerId });
            
        // Get quotation rows if header exists
        let quotationRows = [];
        if (quotationHeader) {
            quotationRows = await db.collection('QuotationRows')
                .find({ quotationId: new ObjectId(quotationHeader._id) })
                .toArray();
        }
        
        // Get service packages
        const servicePackages = await db.collection('ServicePackages')
            .find({ 
                active: true,
                $or: [
                    { universalOffer: true },
                    { computerCategories: { $in: [computer.Category || ''] } }
                ]
            })
            .sort({ packageLevel: 1 })
            .toArray();
            
        // Get base service offerings
        const baseServices = await db.collection('BaseServices')
            .find({
                $or: [
                    { universalOffer: true },
                    { ProductValidityGroup: computer.ProductValidityGroup || "" }
                ]
            })
            .toArray();
            
        // Get support services
        const supportServices = await db.collection('SupportServices')
            .find({
                $or: [
                    { universalOffer: true },
                    { applicableCategories: { $in: [computer.Category || ''] } }
                ]
            })
            .toArray();
            
        // Get dealer add-ons
        const dealerAddOns = await db.collection('DealerAddOns')
            .find({
                $or: [
                    { universalOffer: true },
                    { applicableCategories: { $in: [computer.Category || ''] } }
                ]
            })
            .toArray();
            
        // Get retirement plans
        const retirementPlans = await db.collection('RetirementPlans')
            .find({
                $or: [
                    { universalOffer: true },
                    { applicableCategories: { $in: [computer.Category || ''] } }
                ]
            })
            .toArray();
            
        // Transform data for client
        const transformedComputer = {
            _id: computer._id.toString(),
            name: computer.name || '',
            serialNumber: computer.serialNumber || '',
            productDesignation: computer.ProductDesignation || '',
            productPartNumber: computer.ProductPartNumber || '',
            category: computer.Category || '',
            productValidityGroup: computer.ProductValidityGroup || '',
            contractNumber: computer.ContractNumber || '',
            engineHp: computer.EngineHp || 0,
            tracHp: computer.TracHp || 0,
            contractHoursLimit: computer.ContractHoursLimit || 10000,
            contractAgeLimit: computer.ContractAgeLimit || 5,
            estimatedUtilization: computer.EstimatedUtilization || 1500,
            engineAge: computer.EngineAge || 0,
            contractLength: computer.DesiredContractLength || 12,
            contractLengthHrs: computer.DesiredContractLengthHrs || 4500,
            desiredContractStartDate: computer.DesiredContractStartDate || new Date(),
            deliveryDate: computer.DeliveryDate || null
        };
        
        // Build service offerings and map with existing quotation rows if they exist
        const serviceOfferings = {
            baseContract: mapServiceOfferings(mapBaseServices(baseServices, quotationRows), quotationRows, 'baseContract'),
            dealerAddOns: mapServiceOfferings(mapDealerAddOns(dealerAddOns), quotationRows, 'dealerAddOns'),
            supportServices: mapServiceOfferings(mapSupportServices(supportServices), quotationRows, 'supportServices'),
            retirementPlans: mapServiceOfferings(mapRetirementPlans(retirementPlans), quotationRows, 'retirementPlans')
        };
        
        // Calculate section totals
        const sectionTotals = {
            baseContract: calculateSectionTotal(serviceOfferings.baseContract),
            dealerAddOns: calculateSectionTotal(serviceOfferings.dealerAddOns),
            supportServices: calculateSectionTotal(serviceOfferings.supportServices),
            retirementPlans: calculateSectionTotal(serviceOfferings.retirementPlans),
            total: 0
        };
        
        sectionTotals.total = sectionTotals.baseContract + 
                              sectionTotals.dealerAddOns + 
                              sectionTotals.supportServices + 
                              sectionTotals.retirementPlans;
        
        return {
            computer: transformedComputer,
            serviceOfferings,
            sectionTotals,
            quotationHeader: quotationHeader ? {
                _id: quotationHeader._id.toString(),
                quotationNumber: quotationHeader.quotationNumber || '',
                status: quotationHeader.status || 'Draft',
                createdAt: quotationHeader.createdAt,
                updatedAt: quotationHeader.updatedAt,
                contractLength: quotationHeader.contractLength || 12,
                contractStartDate: quotationHeader.contractStartDate || new Date(),
                notes: quotationHeader.notes || ''
            } : null,
            success: true
        };
        
    } catch (error) {
        console.error('Error loading quote data:', error);
        return {
            computer: null,
            serviceOfferings: null,
            sectionTotals: null,
            quotationHeader: null,
            success: false,
            error: error instanceof Error ? error.message : 'Failed to load quote data'
        };
    }
}

/**
 * Map service offerings with existing quotation rows
 */
function mapServiceOfferings(services, quotationRows, section) {
    return services.map(service => {
        // Find if this service exists in the quotation rows
        const existingRow = quotationRows.find(row => 
            row.serviceId === service.serviceId && 
            row.section === section
        );
        
        if (existingRow) {
            return {
                ...service,
                id: existingRow._id.toString(), // Use the quotation row ID
                cost: existingRow.cost,
                includedInPackage: true,
                rowId: existingRow._id.toString(),
                uniqueId: existingRow.uniqueId || null // Include uniqueId for Dealer Add-Ons
            };
        }
        
        return {
            ...service,
            includedInPackage: false
        };
    });
}

/**
 * Calculate total for a section
 */
function calculateSectionTotal(services) {
    return services
        .filter(service => service.includedInPackage)
        .reduce((total, service) => total + (parseFloat(service.cost) || 0), 0);
}

/**
 * Map base services to the format shown in the image
 */
function mapBaseServices(baseServices, quotationRows) {
    // Check if we have quotation rows data in the database
    const baseContractRows = quotationRows.filter(row => row.rowType === 'BaseContract');
    
    if (baseContractRows && baseContractRows.length > 0) {
        console.log(`Found ${baseContractRows.length} Base Contract rows in QuotationRows`);
        
        // Use the quotation rows data directly
        return baseContractRows.map((row, index) => {
            return {
                id: row._id.toString(),
                uniqueId: `base_${row._id.toString()}`,
                name: row.packageName || 'Base Contract Offering',
                level: row.rowOrder || (index + 1),
                service: row.packageName || '',
                ServiceActivity: row.ServiceActivity || row.activity || '',
                fixed: true,
                variableType: 'hr',
                serviceId: row.packageId?.toString() || '',
                cost: row.cost || 0,
                oemImporter: row.oemImporter !== undefined ? row.oemImporter : false,
                fleetOwner: row.fleetOwner !== undefined ? row.fleetOwner : false,
                customerSpecific: row.customerSpecific !== undefined ? row.customerSpecific : false,
                scos: 0,
                rrp: 0,
                includedInPackage: row.includeInOffer !== undefined ? row.includeInOffer : false,
                targetService: false,
                required: row.required !== undefined ? row.required : false,
                rowType: row.rowType || 'BaseContract'
            };
        });
    }
    
    // If no quotation rows, check for base services
    if (!baseServices || !Array.isArray(baseServices) || baseServices.length === 0) {
        console.log('No base services found in database, providing default template');
        // Return a default template for adding new items
        return [
            { 
                id: 'base_default_1',
                uniqueId: 'base_default_1',
                name: 'Base Contract Offering',
                level: 1,
                service: 'Registration',
                ServiceActivity: '',
                fixed: true,
                variableType: 'hr',
                serviceId: '',
                cost: 0,
                oemImporter: true,
                fleetOwner: false,
                customerSpecific: false,
                scos: 0,
                rrp: 0,
                includedInPackage: true,
                targetService: false,
                required: true,
                rowType: 'BaseContract'
            },
            { 
                id: 'base_default_2',
                uniqueId: 'base_default_2',
                name: 'Base Contract Offering',
                level: 2,
                service: 'Parts Supply on Request',
                ServiceActivity: '',
                fixed: true,
                variableType: 'hr',
                serviceId: '',
                cost: 0,
                oemImporter: true,
                fleetOwner: false,
                customerSpecific: false,
                scos: 0,
                rrp: 0,
                includedInPackage: true,
                targetService: false,
                required: true,
                rowType: 'BaseContract'
            },
            { 
                id: 'base_default_3',
                uniqueId: 'base_default_3',
                name: 'Base Contract Offering',
                level: 3,
                service: 'Software Updates',
                ServiceActivity: '',
                fixed: true,
                variableType: 'hr',
                serviceId: '',
                cost: 0,
                oemImporter: false,
                fleetOwner: false,
                customerSpecific: false,
                scos: 0,
                rrp: 0,
                includedInPackage: true,
                targetService: false,
                required: false,
                rowType: 'BaseContract'
            }
        ];
    }
    
    console.log(`Found ${baseServices.length} base services in database`);
    
    // Map the base services from the database
    return baseServices.map((service, index) => {
        if (!service) return null;
        
        // Create a unique ID for each service
        const uniqueId = `base_${service.ServiceCode || index + 1}`;
        const serviceLabel = service["Service activity Label"] || '';
        
        return {
            id: uniqueId,
            uniqueId: uniqueId,
            name: 'Base Contract Offering',
            level: index + 1,
            service: serviceLabel,
            ServiceActivity: service.ServiceActivity || '',
            fixed: service.Fixed !== undefined ? service.Fixed : true,
            variableType: service.VariableType || 'hr',
            serviceId: service.ServiceCode || '',
            cost: service.Cost || 0,
            oemImporter: service.OEMImporter !== undefined ? service.OEMImporter : true,
            fleetOwner: service.FleetOwner !== undefined ? service.FleetOwner : false,
            customerSpecific: false,
            scos: service.SCOS || 0,
            rrp: service.RRP || 0,
            includedInPackage: true,
            targetService: service.TargetService || false,
            required: ['Registration', 'Parts Supply on Request'].includes(serviceLabel),
            rowType: 'BaseContract'
        };
    }).filter(Boolean); // Remove any null entries
}

/**
 * Map dealer add-ons to the format shown in the image
 */
function mapDealerAddOns(dealerAddOns) {
    // Get the actual dealer add-ons from the database
    // Instead of hardcoding the add-ons, we'll only use what's in the database
    if (!dealerAddOns || !Array.isArray(dealerAddOns) || dealerAddOns.length === 0) {
        console.log('No dealer add-ons found in database, providing default template');
        // Return a default template for adding new items
        return [
            {
                id: 'addon_default',
                uniqueId: 'addon_default',
                name: 'Dealer Add-On: New Service',
                level: 1,
                service: 'New Service',
                fixed: true,
                variableType: 'hr',
                serviceId: '',
                cost: 0,
                oemImporter: false,
                fleetOwner: false,
                scos: 0,
                rrp: 0,
                includedInPackage: false,
                targetService: false
            }
        ];
    }
    
    console.log(`Found ${dealerAddOns.length} dealer add-ons in database`);
    
    // Map the dealer add-ons from the database
    return dealerAddOns.map((addOn, index) => {
        if (!addOn) return null;
        
        // Create a unique ID for each add-on based on its database properties
        const uniqueId = `addon_${addOn.serviceLevel || index + 1}`;
        const serviceName = addOn.serviceName || '';
        
        return {
            id: uniqueId,
            uniqueId: uniqueId,
            name: `Dealer Add-On: ${serviceName}`,
            level: addOn.serviceLevel || (index + 1),
            service: serviceName,
            fixed: addOn.fixed !== undefined ? addOn.fixed : true,
            variableType: addOn.variableType || 'hr',
            serviceId: addOn.ServiceId || '',
            cost: addOn.Cost || 0,
            oemImporter: addOn.OEMImporter || false,
            fleetOwner: addOn.FleetOwner || false,
            scos: addOn.SCOS || 0,
            rrp: addOn.RRP || 0,
            includedInPackage: false,
            targetService: addOn.TargetService || false
        };
    }).filter(Boolean); // Remove any null entries
}

/**
 * Map support services to the format shown in the image
 */
function mapSupportServices(supportServices) {
    const supportMappings = [
        { id: 1, name: 'Support (Self Service)', level: 1, service: 'Oil Sampling Program', fixed: true, required: false },
        { id: 2, name: 'Support (Self Service)', level: 2, service: 'Off Sampling Program', fixed: true, required: false },
        { id: 3, name: 'Support (Dealer)', level: 1, service: 'Technical Support via Dealer', fixed: true, required: false },
        { id: 4, name: 'Support (Dealer)', level: 2, service: 'Full Service & Maintenance', fixed: false, variableType: 'hr', required: false },
        { id: 5, name: 'Support (Dealer)', level: 3, service: 'Full Repair Coverage', fixed: true, required: false }
    ];
    
    return supportMappings.map(mapping => {
        const matchingSupport = supportServices.find(s => 
            s && s.supportLevel === mapping.level && 
            s.serviceName && s.serviceName.includes(mapping.service)
        );
        
        return {
            ...mapping,
            serviceId: matchingSupport?.ServiceId || '',
            cost: matchingSupport?.Cost || 0,
            oemImporter: matchingSupport?.OEMImporter || false,
            fleetOwner: matchingSupport?.FleetOwner || false,
            scos: matchingSupport?.SCOS || 0,
            rrp: matchingSupport?.RRP || 0,
            includedInPackage: false,
            targetService: matchingSupport?.TargetService || false,
            ServiceActivity: matchingSupport?.ServiceActivity || '' // Update the field name from activity to ServiceActivity
        };
    });
}

/**
 * Map retirement plans to the format shown in the image
 */
function mapRetirementPlans(retirementPlans) {
    const retirementMappings = [
        { id: 1, name: 'Retirement Plan', level: 1, service: 'Basic Retirement Plan', fixed: true, required: false },
        { id: 2, name: 'Retirement Plan', level: 2, service: 'Advanced Retirement Plan', fixed: true, required: false },
        { id: 3, name: 'Retirement Plan', level: 3, service: 'Premium Retirement Plan', fixed: true, required: false }
    ];
    
    return retirementMappings.map(mapping => {
        const matchingPlan = retirementPlans.find(p => 
            p && p.planLevel === mapping.level && 
            p.planName && p.planName.includes(mapping.service)
        );
        
        return {
            ...mapping,
            serviceId: matchingPlan?.PlanId || '',
            cost: matchingPlan?.Cost || 0,
            oemImporter: matchingPlan?.OEMImporter || false,
            fleetOwner: matchingPlan?.FleetOwner || false,
            scos: matchingPlan?.SCOS || 0,
            rrp: matchingPlan?.RRP || 0,
            includedInPackage: false,
            targetService: matchingPlan?.TargetService || false
        };
    });
}

/** @type {import('./$types').Actions} */
export const actions = {
  updateQuotationRow: async ({ request, params, locals }) => {
    try {
      const data = await request.formData();
      const rowDataStr = data.get('rowData');
      
      if (!rowDataStr) {
        return { success: false, error: 'No row data provided' };
      }
      
      const rowData = JSON.parse(rowDataStr);
      console.log('Received row data:', rowData);
      
      // Get the MongoDB client
      const { db } = await locals.mongo();
      
      // Prepare the update data
      const updateData = {
        quotationId: rowData.quotationId ? new ObjectId(rowData.quotationId) : null,
        rowType: getRowTypeFromSection(rowData.section),
        rowOrder: rowData.level || 0,
        packageId: rowData.serviceId ? parseInt(rowData.serviceId, 10) || 0 : 0,
        packageName: rowData.service || '',
        ServiceActivity: rowData.ServiceActivity || rowData.activity || '',
        cost: rowData.cost || 0,
        oemImporter: rowData.oemImporter || false,
        fleetOwner: rowData.fleetOwner || false,
        customerSpecific: rowData.customerSpecific || false,
        required: rowData.required || false,
        includeInOffer: true,
        updatedAt: new Date()
      };
      
      // For dealer add-ons, include the unique identifier
      if (rowData.section === 'dealerAddOns' && rowData.uniqueId) {
        updateData.uniqueId = rowData.uniqueId;
      }
      
      let result;
      
      // If rowId exists, update the existing row
      if (rowData.rowId && rowData.rowId !== 'temp_' + rowData.rowId.split('_')[1]) {
        const rowId = rowData.rowId.startsWith('temp_') ? null : rowData.rowId;
        
        if (rowId) {
          // Update existing row
          result = await db.collection('QuotationRows').updateOne(
            { _id: new ObjectId(rowId) },
            { $set: updateData }
          );
          
          console.log(`Updated row ${rowId}:`, result);
          
          if (result.matchedCount === 0) {
            return { success: false, error: 'Row not found' };
          }
        } else {
          // Insert new row
          updateData.createdAt = new Date();
          result = await db.collection('QuotationRows').insertOne(updateData);
          console.log('Inserted new row:', result);
        }
      } else {
        // Insert new row
        updateData.createdAt = new Date();
        result = await db.collection('QuotationRows').insertOne(updateData);
        console.log('Inserted new row:', result);
      }
      
      return { 
        success: true, 
        rowId: result.insertedId ? result.insertedId.toString() : rowData.rowId 
      };
    } catch (error) {
      console.error('Error updating quotation row:', error);
      return { success: false, error: error.message };
    }
  },
  
  removeQuotationRow: async ({ request, locals }) => {
    try {
      const data = await request.formData();
      const rowId = data.get('rowId');
      
      if (!rowId) {
        return { success: false, error: 'No row ID provided' };
      }
      
      // Get the MongoDB client
      const { db } = await locals.mongo();
      
      // Delete the row
      const result = await db.collection('QuotationRows').deleteOne({
        _id: new ObjectId(rowId)
      });
      
      if (result.deletedCount === 0) {
        return { success: false, error: 'Row not found' };
      }
      
      return { success: true };
    } catch (error) {
      console.error('Error removing quotation row:', error);
      return { success: false, error: error.message };
    }
  },
  
  saveQuote: async ({ request }) => {
    try {
      const formData = await request.formData();
      const quoteDataStr = formData.get('quoteData');
      
      if (!quoteDataStr || typeof quoteDataStr !== 'string') {
        console.error('Invalid quote data format received');
        return { success: false, error: 'Invalid quote data format' };
      }
      
      console.log('Quote data received:', quoteDataStr);
      
      let quoteData;
      try {
        quoteData = JSON.parse(quoteDataStr);
      } catch (parseError) {
        console.error('Failed to parse quote data:', parseError);
        return { success: false, error: 'Failed to parse quote data' };
      }
      
      const computerId = quoteData.computerId;
      
      if (!computerId) {
        console.error('Computer ID is missing');
        return { success: false, error: 'Computer ID is required' };
      }
      
      // Validate computerId is a valid ObjectId
      if (!ObjectId.isValid(computerId)) {
        console.error('Invalid computer ID format:', computerId);
        return { success: false, error: 'Invalid computer ID format' };
      }
      
      // Connect to database
      const db = client.db('ServiceContracts');
      
      // First, verify the computer exists
      const computer = await db.collection('CustomerComputers').findOne({ 
        _id: new ObjectId(computerId) 
      });
      
      if (!computer) {
        console.error('Computer not found:', computerId);
        return { success: false, error: 'Computer not found' };
      }
      
      console.log('Computer found:', computer._id.toString());
      
      // Create or update quotation header
      const quotationHeader = {
        computerId: new ObjectId(computerId),
        updatedAt: new Date(),
        status: 'Draft',
        contractLength: quoteData.contractLength || 12,
        contractStartDate: new Date(quoteData.contractStartDate) || new Date(),
        totalAmount: calculateTotalAmount(quoteData) || 0,
        notes: quoteData.notes || ''
      };
      
      console.log('Preparing quotation header:', quotationHeader);
      
      let quotationId;
      let result;
      
      if (quoteData.quotationId) {
        // Validate quotationId format
        if (!ObjectId.isValid(quoteData.quotationId)) {
          console.error('Invalid quotation ID format:', quoteData.quotationId);
          return { success: false, error: 'Invalid quotation ID format' };
        }
        
        console.log('Updating existing quotation:', quoteData.quotationId);
        
        // Update existing quotation header
        result = await db.collection('QuotationHeader').updateOne(
          { _id: new ObjectId(quoteData.quotationId) },
          { $set: quotationHeader }
        );
        
        if (result.matchedCount === 0) {
          console.error('Quotation header not found:', quoteData.quotationId);
          return { success: false, error: 'Quotation not found' };
        }
        
        quotationId = quoteData.quotationId;
      } else {
        console.log('Creating new quotation');
        
        // Create new quotation header
        quotationHeader.createdAt = new Date();
        quotationHeader.quotationNumber = await generateQuotationNumber(db);
        
        try {
          result = await db.collection('QuotationHeader').insertOne(quotationHeader);
          quotationId = result.insertedId.toString();
          console.log('Created new quotation with ID:', quotationId);
        } catch (insertError) {
          console.error('Failed to insert quotation header:', insertError);
          return { success: false, error: 'Failed to create quotation header' };
        }
      }
      
      // Delete existing quotation rows for this quotation
      try {
        console.log('Deleting existing quotation rows for quotation:', quotationId);
        await db.collection('QuotationRows').deleteMany({ 
          quotationId: new ObjectId(quotationId) 
        });
      } catch (deleteError) {
        console.error('Failed to delete existing rows:', deleteError);
        return { success: false, error: 'Failed to update quotation rows' };
      }
      
      // Create new quotation rows
      if (quoteData.selectedServices && quoteData.selectedServices.length > 0) {
        console.log(`Creating ${quoteData.selectedServices.length} new quotation rows`);
        
        try {
          const quotationRows = quoteData.selectedServices.map(service => {
            // Ensure all required fields have appropriate defaults
            return {
              quotationId: new ObjectId(quotationId),
              section: service.section || '',
              serviceId: service.serviceId || '',
              service: service.service || service.name || '',
              level: parseInt(service.level) || 0,
              package: getRowTypeFromSection(service.section) || 'Base Contract',
              QuoteRowType: 'Labour',
              included: 'Yes',
              required: 'Yes',
              scos: parseFloat(service.scos) || 0,
              oemImport: parseFloat(service.oemImporter) || 0,
              fleetOwner: parseFloat(service.fleetOwner) || 0,
              rrp: parseFloat(service.rrp) || 0,
              CustomerPrice: parseFloat(service.cost) || 0,
              DealerPrice: parseFloat(service.cost * 0.8) || 0,
              InternalCost: parseFloat(service.cost * 0.6) || 0,
              uniqueId: service.uniqueId || null,
              createdAt: new Date(),
              updatedAt: new Date()
            };
          });
          
          if (quotationRows.length > 0) {
            await db.collection('QuotationRows').insertMany(quotationRows);
            console.log(`Successfully inserted ${quotationRows.length} quotation rows`);
          }
        } catch (rowInsertError) {
          console.error('Failed to insert quotation rows:', rowInsertError);
          return { success: false, error: 'Failed to create quotation rows' };
        }
      } else {
        console.log('No services selected, skipping row creation');
      }
      
      return {
        success: true,
        quotationId: quotationId,
        message: 'Quote saved successfully'
      };
      
    } catch (error) {
      console.error('Unexpected error saving quote:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to save quote'
      };
    }
  }
};

/**
 * Calculate the total amount of the quote
 */
function calculateTotalAmount(quoteData) {
  let total = 0;
  
  if (Array.isArray(quoteData.selectedServices)) {
    quoteData.selectedServices.forEach(service => {
      if (service && service.cost) {
        total += parseFloat(service.cost) || 0;
      }
    });
  }
  
  return total;
}

/**
 * Generate a unique quotation number
 */
async function generateQuotationNumber(db) {
  const currentYear = new Date().getFullYear();
  const prefix = `Q${currentYear}-`;
  
  // Find the highest quotation number for this year
  const latestQuotation = await db.collection('QuotationHeader')
    .find({ quotationNumber: { $regex: `^${prefix}` } })
    .sort({ quotationNumber: -1 })
    .limit(1)
    .toArray();
  
  let nextNumber = 1;
  if (latestQuotation.length > 0) {
    const latestNumber = latestQuotation[0].quotationNumber;
    const numberPart = latestNumber.substring(prefix.length);
    nextNumber = parseInt(numberPart, 10) + 1;
  }
  
  // Format with leading zeros (e.g., Q2025-0001)
  return `${prefix}${nextNumber.toString().padStart(4, '0')}`;
}

/**
 * Get the row type based on the section
 */
function getRowTypeFromSection(section) {
  switch (section) {
    case 'baseContract':
      return 'BaseContract';
    case 'dealerAddOns':
      return 'DealerAddOn';
    case 'supportServices':
      return 'SupportService';
    case 'retirementPlans':
      return 'RetirementPlan';
    default:
      return 'Unknown';
  }
}
