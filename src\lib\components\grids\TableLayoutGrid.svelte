<!-- TableLayoutGrid.svelte -->
<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  
  // Props for table layout configuration
  export let headerBgColor: string = '#f5f5f5';
  export let headerTextColor: string = '#333';
  export let rowGap: string = '0.5rem';
  export let columnGap: string = '1rem';
  export let padding: string = '1rem';
  export let columns: string = 'repeat(auto-fit, minmax(150px, 1fr))';
</script>

<BaseGrid 
  columns={columns}
  gap={`${rowGap} ${columnGap}`}
  padding={padding}
  className="table-layout-grid"
>
  <div class="header">
    <slot name="header" />
  </div>
  
  <div class="content">
    <slot name="content" />
  </div>
</BaseGrid>

<style>
  .header {
    background-color: var(--header-bg-color, #f5f5f5);
    color: var(--header-text-color, #333);
    font-weight: bold;
    padding: 1rem;
    border-bottom: 1px solid #e0e0e0;
    grid-column: 1 / -1;
  }

  .content {
    grid-column: 1 / -1;
    min-height: 100px;
  }

  /* Responsive styles */
  @media (max-width: 768px) {
    .header {
      padding: 0.75rem;
    }
  }

  @media (max-width: 480px) {
    .header {
      padding: 0.5rem;
    }
  }
</style>
