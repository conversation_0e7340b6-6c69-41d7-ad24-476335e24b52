import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/**
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function PUT({ params, request }) {
  try {
    const db = client.db('ServiceContracts');
    const rowId = params.id;
    
    // Validate ObjectId format
    if (!ObjectId.isValid(rowId)) {
      return json({ success: false, message: 'Invalid row ID format' }, { status: 400 });
    }
    
    // Get update data from request body
    const updateData = await request.json();
    console.log(`Updating quote row ${rowId} with data:`, updateData);
    
    // Remove _id if present (can't update MongoDB _id)
    if (updateData._id) {
      delete updateData._id;
    }
    
    // Handle ObjectId fields if present
    if (updateData.quotationId && ObjectId.isValid(updateData.quotationId)) {
      updateData.quotationId = new ObjectId(updateData.quotationId);
    }
    
    // Add timestamp
    updateData.updatedAt = new Date();
    
    // Update the document in QuotationRows collection (using PascalCase per convention)
    const result = await db.collection('QuotationRows').updateOne(
      { _id: new ObjectId(rowId) },
      { $set: updateData }
    );
    
    if (result.matchedCount === 0) {
      return json({ success: false, message: 'Quote row not found' }, { status: 404 });
    }
    
    return json({ 
      success: true, 
      message: 'Quote row updated successfully',
      modifiedCount: result.modifiedCount
    });
  } catch (error) {
    console.error('Error updating quote row:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return json({ success: false, message: errorMessage }, { status: 500 });
  }
}
