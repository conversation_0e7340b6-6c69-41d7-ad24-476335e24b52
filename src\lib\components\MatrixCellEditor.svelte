<script>
  import { createEventDispatcher, onMount } from 'svelte';
  import { fade } from 'svelte/transition';
  import { clickOutside } from '$lib/actions/clickOutside';
  
  // Props
  export let cellData;
  export let position = { x: 0, y: 0 };
  export let computerId;
  export let isNew = false;
  export let activityOptions = [
    'Regular Service',
    'Contract Service',
    'Maintenance',
    'Repair',
    'Inspection',
    'Installation',
    'Remote Support',
    'Software Update',
    'Hardware Update',
    'Training',
    'Consultation'
  ];
  
  // Local state
  let editedData = { ...cellData };
  let loading = false;
  let error = '';
  let formElement = null;
  let customActivity = '';
  let showCustom = false;
  
  // Event dispatcher
  const dispatch = createEventDispatcher();
  
  onMount(() => {
    // Always make sure we're clear on whether this is a new or existing activity
    isNew = !editedData._id || editedData._id === '';
    
    console.log(`MatrixCellEditor initialized: ${isNew ? 'New Activity' : 'Edit Activity'}`, editedData);
    
    // Focus on hours input when component mounts
    setTimeout(() => {
      const hoursInput = formElement?.querySelector('input[name="hours"]');
      if (hoursInput) {
        hoursInput.focus();
        hoursInput.select();
      }
    }, 50);
    
    // Set custom activity mode if activity doesn't match any option
    if (editedData.activity && !activityOptions.includes(editedData.activity)) {
      customActivity = editedData.activity;
      showCustom = true;
    }
  });
  
  // Handle form submission
  async function handleSubmit() {
    if (loading) return;
    
    try {
      loading = true;
      error = '';
      
      // Validate form fields
      if (editedData.hours === undefined || editedData.hours === null || editedData.hours === '') {
        throw new Error('Hours are required');
      }
      
      if (!showCustom && (!editedData.activity || editedData.activity === '')) {
        throw new Error('Please select an activity');
      }
      
      if (showCustom && (!customActivity || customActivity.trim() === '')) {
        throw new Error('Please enter a custom activity');
      }
      
      // Prepare data
      const finalData = {
        ...editedData,
        computerId,
        activity: showCustom ? customActivity.trim() : editedData.activity,
        hours: parseFloat(editedData.hours) || 0
      };
      
      // Double-check if we need to create or update
      const needsCreate = !finalData._id || finalData._id === '';
      const method = needsCreate ? 'POST' : 'PUT';
      const url = `/api/service-activities/${needsCreate ? 'create' : 'update'}`;
      
      console.log(`Saving activity using ${method} to ${url}`, finalData);
      
      // Send data to server
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(finalData)
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        // Check if the error was due to not finding the activity
        if (response.status === 404 && !needsCreate) {
          console.error('Attempt to update failed - activity not found. Trying to create a new one instead.');
          
          // Try to create a new activity instead
          const createResponse = await fetch('/api/service-activities/create', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              ...finalData,
              _id: undefined // Remove any _id for creation
            })
          });
          
          const createResult = await createResponse.json();
          
          if (!createResponse.ok) {
            throw new Error(createResult.message || 'Failed to create activity after update failed');
          }
          
          // Successfully created new activity
          dispatch('saved', createResult.data);
          closeEditor();
          return;
        }
        
        throw new Error(result.message || 'Failed to save data');
      }
      
      // Notify parent of successful save
      dispatch('saved', result.data || finalData);
      
      // Close the editor
      closeEditor();
    } catch (err) {
      console.error('Error saving cell data:', err);
      error = err.message || 'An error occurred while saving';
      dispatch('error', { message: error });
    } finally {
      loading = false;
    }
  }
  
  function closeEditor() {
    dispatch('close');
  }
  
  function handleKeydown(event) {
    if (event.key === 'Escape') {
      event.preventDefault();
      closeEditor();
    } else if (event.key === 'Enter' && event.ctrlKey) {
      event.preventDefault();
      handleSubmit();
    }
  }
  
  function toggleCustomActivity() {
    showCustom = !showCustom;
    
    if (showCustom) {
      // Focus on custom activity input when switching to custom
      setTimeout(() => {
        const customInput = formElement?.querySelector('input[name="customActivity"]');
        if (customInput) {
          customInput.focus();
        }
      }, 50);
    }
  }
</script>

<!-- svelte-ignore a11y-no-static-element-interactions -->
<div 
  class="editor-overlay"
  transition:fade={{ duration: 150 }}
  on:keydown={handleKeydown}
  role="dialog"
  aria-modal="true"
  aria-labelledby="editor-title"
>
  <div 
    class="editor-container"
    style="left: {position.x}px; top: {position.y}px;"
    use:clickOutside={{ enabled: true, callback: closeEditor }}
  >
    <form 
      bind:this={formElement}
      on:submit|preventDefault={handleSubmit}
      class="editor-form"
    >
      <header class="editor-header">
        <h3 id="editor-title">{isNew ? 'Add Activity' : 'Edit Activity'}</h3>
        <button 
          type="button" 
          class="close-button" 
          on:click={closeEditor}
          aria-label="Close editor"
        >×</button>
      </header>
      
      {#if error}
        <div class="error-message" role="alert">{error}</div>
      {/if}
      
      <div class="form-fields">
        <div class="form-group">
          <label for="hours-input">Hours</label>
          <input 
            type="number" 
            id="hours-input"
            name="hours"
            bind:value={editedData.hours}
            min="0"
            step="0.5"
            class="form-input"
            required
          />
        </div>
        
        <div class="form-group">
          <label for="activity-input">Activity</label>
          
          <div class="activity-input-group">
            {#if !showCustom}
              <select 
                id="activity-input"
                name="activity"
                bind:value={editedData.activity}
                class="form-select"
                required
              >
                <option value="">-- Select activity --</option>
                {#each activityOptions as option}
                  <option value={option}>{option}</option>
                {/each}
              </select>
            {:else}
              <input 
                type="text"
                id="custom-activity-input"
                name="customActivity"
                bind:value={customActivity}
                placeholder="Enter custom activity"
                class="form-input custom-input"
                required
              />
            {/if}
            
            <button 
              type="button" 
              class="toggle-button"
              on:click={toggleCustomActivity}
              title={showCustom ? "Choose from options" : "Enter custom activity"}
            >
              {showCustom ? 'Select' : 'Custom'}
            </button>
          </div>
        </div>
        
        <div class="form-group checkbox-group">
          <input 
            type="checkbox"
            id="fixed-checkbox"
            name="hasFixed" 
            bind:checked={editedData.hasFixed}
          />
          <label for="fixed-checkbox">Fixed Schedule</label>
        </div>
        
        <div class="form-info">
          Editing: {editedData.year}-{editedData.month}
          {#if !isNew && editedData._id}
            <div class="id-info">ID: {editedData._id}</div>
          {/if}
        </div>
      </div>
      
      <footer class="editor-footer">
        <button
          type="button"
          class="cancel-button"
          on:click={closeEditor}
          disabled={loading}
        >
          Cancel
        </button>
        <button
          type="submit"
          class="save-button"
          disabled={loading}
        >
          {#if loading}
            Saving...
          {:else}
            Save
          {/if}
        </button>
      </footer>
    </form>
  </div>
</div>

<style>
  .editor-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(2px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  
  .editor-container {
    position: absolute;
    max-width: 350px;
    width: 100%;
    background: linear-gradient(145deg, #1c2c3f, #182338);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 6px 10px rgba(0, 0, 0, 0.2);
    transform: translate(-50%, -50%);
    border: 1px solid rgba(59, 130, 246, 0.2);
    animation: pop-in 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  }
  
  @keyframes pop-in {
    0% { transform: translate(-50%, -50%) scale(0.9); opacity: 0.5; }
    100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
  }
  
  .editor-form {
    display: grid;
    grid-template-rows: auto auto 1fr auto;
    width: 100%;
    height: 100%;
  }
  
  .editor-header {
    background: linear-gradient(to right, #2d3a4f, #1e293b);
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(51, 65, 85, 0.5);
  }
  
  .editor-header h3 {
    margin: 0;
    color: #60a5fa;
    font-size: 1.1rem;
    font-weight: 500;
  }
  
  .close-button {
    background: transparent;
    border: none;
    color: #94a3b8;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    line-height: 1;
  }
  
  .close-button:hover {
    color: #e2e8f0;
  }
  
  .form-fields {
    padding: 1.5rem;
    display: grid;
    gap: 1.25rem;
  }
  
  .form-group {
    display: grid;
    gap: 0.5rem;
  }
  
  .activity-input-group {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 0.5rem;
  }
  
  label {
    font-size: 0.875rem;
    color: #94a3b8;
    font-weight: 500;
  }
  
  .form-input, .form-select {
    background-color: rgba(15, 23, 42, 0.8);
    border: 1px solid #334155;
    border-radius: 8px;
    color: #e2e8f0;
    padding: 0.625rem 0.75rem;
    font-size: 0.95rem;
    transition: border-color 0.2s, box-shadow 0.2s;
  }
  
  .form-input:focus, .form-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  }
  
  .custom-input {
    font-style: italic;
  }
  
  .checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }
  
  .checkbox-group input {
    width: 1rem;
    height: 1rem;
    accent-color: #3b82f6;
  }
  
  .form-info {
    font-size: 0.8rem;
    color: #94a3b8;
    text-align: center;
    margin-top: 0.5rem;
    background-color: rgba(15, 23, 42, 0.3);
    padding: 0.5rem;
    border-radius: 6px;
  }
  
  .id-info {
    font-size: 0.7rem;
    color: #64748b;
    margin-top: 0.25rem;
    word-break: break-all;
  }
  
  .editor-footer {
    padding: 1rem;
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    border-top: 1px solid rgba(51, 65, 85, 0.5);
    background: linear-gradient(to right, #2d3a4f, #1e293b);
  }
  
  .cancel-button, .save-button, .toggle-button {
    padding: 0.625rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .cancel-button {
    background-color: transparent;
    border: 1px solid #475569;
    color: #e2e8f0;
  }
  
  .cancel-button:hover:not(:disabled) {
    background-color: rgba(71, 85, 105, 0.2);
  }
  
  .save-button {
    background: linear-gradient(to right, #3b82f6, #2563eb);
    color: white;
    border: none;
    box-shadow: 0 2px 5px rgba(37, 99, 235, 0.3);
  }
  
  .save-button:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(37, 99, 235, 0.4);
  }
  
  .save-button:active:not(:disabled) {
    transform: translateY(0);
  }
  
  .save-button:disabled, .cancel-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  .toggle-button {
    background: linear-gradient(to right, #64748b, #475569);
    color: white;
    border: none;
    font-size: 0.8rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }
  
  .toggle-button:hover {
    background: linear-gradient(to right, #7f8ea3, #566882);
  }
  
  .error-message {
    background-color: rgba(239, 68, 68, 0.15);
    border-left: 3px solid #ef4444;
    color: #fca5a5;
    padding: 0.75rem;
    margin: 0 1rem;
    font-size: 0.875rem;
    border-radius: 0 4px 4px 0;
  }
</style>
