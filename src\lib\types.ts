export interface QuotationRow {
  _id: string;
  RowType: string;
  RowOrder: number;
  PackageName?: string;
  ServiceActivity: string;
  Cost: number;
  OemImporter: number;
  FleetOwner: number;
  CustomerSpecific?: boolean;
  Required: boolean;
  IncludeInOffer: boolean;
  QuoteId?: string;
  QuotationId?: string;
  QuoteNumber?: string;
  CreatedAt: string;
  UpdatedAt: string;
  SSP?: number;
  
  // Financial fields
  ListPrice?: number;
  DiscountPercentage?: number;
  DiscountAmount?: number;
  NetPrice?: number;
  
  // Tax fields
  TaxRate?: number;
  TaxAmount?: number;
  
  // Margin fields
  CostPrice?: number;
  MarginPercentage?: number;
  MarginAmount?: number;
  
  // Currency fields
  Currency?: string;
  ExchangeRate?: number;
  
  // Billing fields
  BillingFrequency?: string; // Monthly, Quarterly, Annually, One-time
  BillingCycle?: number; // Number of billing cycles
  
  // Totals
  TotalPrice?: number; // NetPrice + TaxAmount
  
  // Flags
  IsRecurring?: boolean;
  IsTaxable?: boolean;
}

export interface QuoteGroup {
  Title: string;
  Rows: QuotationRow[];
  Subtotal: number;
}

export interface FormattedQuote {
  _id: string;
  QuoteNumber: string;
  [key: string]: any;
}

export interface QuoteData {
  CurrentQuote: FormattedQuote;
  GroupedRows: Record<string, QuoteGroup>;
  GrandTotal: number;
}
