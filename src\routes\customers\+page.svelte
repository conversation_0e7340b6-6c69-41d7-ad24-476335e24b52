<script>
  import { enhance } from '$app/forms';
  import { invalidate, goto } from '$app/navigation';
  import { onMount } from 'svelte';
  import { CUSTOMER_TYPES, CUSTOMER_DIVISIONS, DEFAULT_CUSTOMER_DIVISION, VERSATILE_OPTIONS } from '$lib/constants';
  import CustomersGrid from '$lib/components/CustomersGrid.svelte';
  import CustomerComputersGrid from '$lib/components/grids/CustomerComputersGrid.svelte';

  /**
   * @typedef {Object} CustomerData
   * @property {string} _id - MongoDB ObjectId as string
   * @property {string} companyName - Company name
   * @property {string} email - Email address
   * @property {string} phone - Phone number
   * @property {string} address - Street address
   * @property {string} city - City
   * @property {string} country - Country
   * @property {string} type - Customer type
   * @property {string} division - Customer division (Marine or Industrial)
   * @property {string[]} versatile - Versatile options
   * @property {string} notes - Additional notes
   * @property {number} computerCount - Number of associated computers
   */

  /** @type {import('./$types').PageData} */
  export let data;

  /** @type {CustomerData[]} */
  let customers = /** @type {CustomerData[]} */ (data.customers);
  
  // Get computers for the selected customer from the data
  // (initialized below with other computer management variables)
  
  // No need to refresh on mount - data is already loaded from server
  // Only refresh after operations like create, update, delete
  
  // Function to refresh customers data with a full page reload
  async function refreshCustomers() {
    try {
      // Force a complete page refresh using browser's reload functionality
      window.location.reload();
      console.log('Full page refresh initiated');
    } catch (error) {
      console.error('Error refreshing customers page:', error);
    }
  }
  
  // Special function specifically for handling new customer creation
  function refreshAfterCreate() {
    console.log('New customer added - refreshing page');
    // Small delay to ensure form closure completes
    setTimeout(() => {
      window.location.reload();
    }, 300); // Longer delay to ensure MongoDB operation completes
  }
  /** @type {readonly string[]} */
  let customerTypes = CUSTOMER_TYPES;
  /** @type {Record<string, Array<{_id: string, name: string, description: string}>>} */
  let regionsByCountry = data.regionsByCountry || {};
  let searchTerm = '';
  let filterType = 'all';
  let countryFilter = '';
  
  // Computer management variables
  /** 
   * @typedef {Object} ComputerData
   * @property {string} _id - MongoDB ObjectId as string
   * @property {string} customerId - Customer ID reference
   * @property {string} serialNumber - Serial number
   * @property {string} model - Computer model
   * @property {string} productType - Type of product
   * @property {string} [productDesignation] - Product designation if available
   * @property {string} [engineType] - Engine type if available
   * @property {number} operatingHours - Operating hours count
   * @property {string} [installationDate] - Date of installation
   * @property {string} [manufactureDate] - Date of manufacture
   * @property {boolean} isActive - Whether computer is active
   * @property {string} [notes] - Additional notes
   */
  
  /** @type {ComputerData[]} */
  let customerComputers = data.customerComputers || [];
  let showComputerForm = false;
  /** @type {string|null} */
  let editingComputerId = null;
  /** @type {string|null} */
  let selectedComputerId = null;
  /** @type {ComputerData|null} */
  let selectedComputer = null;
  let showComputerDetails = false;
  
  /** @type {Object} */
  let computerFormData = {
    serialNumber: '',
    model: '',
    productType: '',
    productDesignation: '',
    engineType: '',
    operatingHours: 0,
    installationDate: '',
    manufactureDate: '',
    isActive: true,
    notes: ''
  };
  let showForm = false;
  /** @type {CustomerData|null} */
  let editingCustomer = null;
  /** @type {string|null} */
  let expandedCountry = null;
  /** @type {string|null} */
  let expandedCustomerId = null;

  /** @type {CustomerData} */
  let newCustomer = {
    _id: '',
    companyName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    country: '',
    type: 'Retail',
    division: DEFAULT_CUSTOMER_DIVISION, // Set to 'Industrial' by default
    versatile: [], // Initialize as empty array
    notes: '',
    computerCount: 0
  };

  /** @type {CustomerData} */
  let formData = newCustomer;

  /** @type {CustomerData|null} */
  let selectedCustomer = null;

  $: {
    formData = editingCustomer || newCustomer;
  }

  $: filteredCustomers = customers.filter(customer => {
    const matchesSearch = searchTerm === '' || 
      customer.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.phone.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.country.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = filterType === 'all' || customer.type === filterType;
    
    const matchesCountry = !countryFilter || customer.country.toLowerCase().includes(countryFilter.toLowerCase());
    
    return matchesSearch && matchesType && matchesCountry;
  });

  function resetForm() {
    editingCustomer = null;
    newCustomer = {
      _id: '',
      companyName: '',
      email: '',
      phone: '',
      address: '',
      city: '',
      country: '',
      type: 'Retail',
      division: DEFAULT_CUSTOMER_DIVISION,
      versatile: [],
      notes: '',
      computerCount: 0
    };
    showForm = false;
  }
  
  // Computer management functions
  function resetComputerForm() {
    computerFormData = {
      serialNumber: '',
      model: '',
      productType: '',
      productDesignation: '',
      engineType: '',
      operatingHours: 0,
      installationDate: '',
      manufactureDate: '',
      isActive: true,
      notes: ''
    };
    showComputerForm = false;
    editingComputerId = null;
  }
  
  function addComputer() {
    resetComputerForm();
    showComputerForm = true;
  }
  
  function editComputer(computer) {
    editingComputerId = computer._id;
    computerFormData = {
      ...computer,
      // Format dates for HTML date inputs if present
      installationDate: computer.installationDate ? computer.installationDate.substring(0, 10) : '',
      manufactureDate: computer.manufactureDate ? computer.manufactureDate.substring(0, 10) : ''
    };
    showComputerForm = true;
  }
  
  function deleteComputer(computer) {
    if (confirm(`Are you sure you want to delete computer ${computer.serialNumber}?`)) {
      // Create a form for submission
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = '?/deleteComputer';
      
      // Add computer ID
      const computerIdInput = document.createElement('input');
      computerIdInput.type = 'hidden';
      computerIdInput.name = '_id';
      computerIdInput.value = computer._id;
      form.appendChild(computerIdInput);
      
      // Add customer ID
      const customerIdInput = document.createElement('input');
      customerIdInput.type = 'hidden';
      customerIdInput.name = 'customerId';
      customerIdInput.value = selectedCustomer._id;
      form.appendChild(customerIdInput);
      
      // Submit the form
      document.body.appendChild(form);
      form.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const formData = new FormData(form);
        const response = await fetch('?/deleteComputer', {
          method: 'POST',
          body: formData
        });
        
        if (response.ok) {
          // Reload the page to reflect changes
          window.location.reload();
        }
        
        document.body.removeChild(form);
      });
      
      form.requestSubmit();
    }
  }
  
  // Load computers for the selected customer
  $: if (selectedCustomer) {
    fetch(`/api/customers/${selectedCustomer._id}/computers`)
      .then(response => response.json())
      .then(data => {
        customerComputers = data.computers || [];
      })
      .catch(error => {
        console.error('Error loading customer computers:', error);
        customerComputers = [];
      });
  }

  /**
   * Toggle expanded country to show Regions
   * @param {string} country - Country name
   */
  function toggleCountryRegions(country) {
    expandedCountry = expandedCountry === country ? null : country;
  }

  /**
   * Toggle expanded customer to show Regions
   * @param {string} customerId - Customer ID
   * @param {Event|null} event - Click event or null
   */
  function toggleCustomerRegions(customerId, event) {
    if (event) event.stopPropagation();
    expandedCustomerId = expandedCustomerId === customerId ? null : customerId;
  }

  /**
   * Get count of Regions for a country
   * @param {string} country - Country name
   * @returns {number} - Number of Regions
   */
  function getRegionCount(country) {
    return regionsByCountry[country]?.length || 0;
  }

  /**
   * Edit an existing customer or create a new one
   * @param {CustomerData|null} customer - Customer to edit or null for new
   */
  function editCustomer(customer) {
    if (customer) {
      editingCustomer = {...customer};
    } else {
      editingCustomer = null;
    }
    showForm = true;
  }
  
  /**
   * Handle customer selection and show details
   * @param {{detail: {customer: CustomerData}}} event - Selection event with customer data
   */
  function handleCustomerSelect(event) {
    const { customer } = event.detail;
    selectedCustomer = customer;
    console.log('Selected customer:', customer);
  }
</script>

<div class="container">
  <div class="header-content">
    <h1>Customers</h1>
    <div class="header-actions">
      <div class="search-filter-grid">
        <input 
          type="text" 
          placeholder="Search by name or email..."
          bind:value={searchTerm}
          class="search-input"
        />
        <select bind:value={filterType} class="filter-select">
          <option value="all">All Types</option>
          {#each customerTypes as type}
            <option value={type}>{type}</option>
          {/each}
        </select>
        <input 
          type="text" 
          placeholder="Filter by country..."
          bind:value={countryFilter}
          class="search-input"
        />
      </div>
      <button class="btn-primary" on:click={() => { showForm = true; editingCustomer = null; }}>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
        </svg>
        Add Customer
      </button>
    </div>
  </div>

  {#if filteredCustomers.length === 0}
    <div class="empty-state">
      <h2>No customers found</h2>
      <p>Try adjusting your search or filter criteria, or add a new customer.</p>
      <button class="btn-primary" on:click={() => { showForm = true; editingCustomer = null; }}>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
        </svg>
        Add Customer
      </button>
    </div>
  {:else}
    <div class="page-layout">
      <div class="grid-container">
        <CustomersGrid 
          customers={filteredCustomers} 
          selectedId={selectedCustomer ? selectedCustomer._id : null}
          onEdit={(customer) => editCustomer(customer)}
          onDelete={(customer) => {
            if (!confirm(`Are you sure you want to delete customer ${customer.companyName}?`)) return;
            
            // For delete, we'll use a form submission
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '?/deleteCustomer';
            
            const idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.name = '_id';
            idInput.value = customer._id;
            form.appendChild(idInput);
            
            document.body.appendChild(form);
            
            form.addEventListener('submit', async (event) => {
              event.preventDefault();
              
              const formData = new FormData(form);
              const response = await fetch('?/deleteCustomer', {
                method: 'POST',
                body: formData
              });
              
              if (response.ok) {
                refreshCustomers();
              }
              
              document.body.removeChild(form);
            });
            
            form.requestSubmit();
          }}
          on:select={handleCustomerSelect}
        />
      </div>
      
      {#if selectedCustomer}
        <div class="customer-details">
          <div class="details-header">
            <h2>{selectedCustomer.companyName}</h2>
            <div class="badge-container">
              <span class="badge badge-{selectedCustomer.type.toLowerCase().replace(' ', '-')}">
                {selectedCustomer.type}
              </span>
              <span class="badge badge-division badge-{selectedCustomer.division?.toLowerCase() || 'industrial'}">
                {selectedCustomer.division || 'Industrial'}
              </span>
            </div>
            
            {#if selectedCustomer.versatile && selectedCustomer.versatile.length > 0}
              <div class="versatile-tags">
                {#each selectedCustomer.versatile as option}
                  <span class="versatile-tag">{option}</span>
                {/each}
              </div>
            {/if}
          </div>
          
          <div class="details-content">
            <div class="info-grid">
              <div class="info-item">
                <span class="label">Email:</span>
                <span>{selectedCustomer.email || 'Not provided'}</span>
              </div>
              <div class="info-item">
                <span class="label">Phone:</span>
                <span>{selectedCustomer.phone || 'Not provided'}</span>
              </div>
              <div class="info-item">
                <span class="label">Address:</span>
                <span>{selectedCustomer.address || 'Not provided'}</span>
              </div>
              <div class="info-item">
                <span class="label">City:</span>
                <span>{selectedCustomer.city || 'Not provided'}</span>
              </div>
              <div class="info-item">
                <span class="label">Country:</span>
                <span>{selectedCustomer.country || 'Not provided'}</span>
              </div>
              {#if selectedCustomer.notes}
                <div class="info-item notes full-width">
                  <span class="label">Notes:</span>
                  <p>{selectedCustomer.notes}</p>
                </div>
              {/if}
            </div>
            
            <!-- Computers section using CSS Grid layout -->
            <div class="computers-section">
              <div class="section-header">
                <h3>Computers ({customerComputers.length})</h3>
                <button class="btn-primary" on:click={addComputer}>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                  </svg>
                  Add New Computer
                </button>
              </div>
              
              {#if showComputerForm}
                <div class="computer-form-container">
                  <h4>{editingComputerId ? 'Edit Computer' : 'Add New Computer'}</h4>
                  <form
                    action={editingComputerId ? '?/updateComputer' : '?/addComputer'}
                    method="POST"
                    use:enhance={() => {
                      return async ({ result }) => {
                        if (result.type === 'success') {
                          resetComputerForm();
                          window.location.reload();
                        }
                      };
                    }}
                  >
                    {#if editingComputerId}
                      <input type="hidden" name="_id" value={editingComputerId} />
                    {/if}
                    <input type="hidden" name="customerId" value={selectedCustomer._id} />
                    
                    <div class="form-grid">
                      <div class="form-group">
                        <label for="serialNumber">Serial Number*</label>
                        <input 
                          type="text"
                          id="serialNumber"
                          name="serialNumber"
                          bind:value={computerFormData.serialNumber}
                          required
                        />
                      </div>
                      
                      <div class="form-group">
                        <label for="model">Model*</label>
                        <input 
                          type="text"
                          id="model"
                          name="model"
                          bind:value={computerFormData.model}
                          required
                        />
                      </div>
                      
                      <div class="form-group">
                        <label for="productType">Product Type*</label>
                        <input 
                          type="text"
                          id="productType"
                          name="productType"
                          bind:value={computerFormData.productType}
                          required
                        />
                      </div>
                      
                      <div class="form-group">
                        <label for="productDesignation">Product Designation</label>
                        <input 
                          type="text"
                          id="productDesignation"
                          name="productDesignation"
                          bind:value={computerFormData.productDesignation}
                        />
                      </div>
                      
                      <div class="form-group">
                        <label for="engineType">Engine Type</label>
                        <input 
                          type="text"
                          id="engineType"
                          name="engineType"
                          bind:value={computerFormData.engineType}
                        />
                      </div>
                      
                      <div class="form-group">
                        <label for="operatingHours">Operating Hours*</label>
                        <input 
                          type="number"
                          id="operatingHours"
                          name="operatingHours"
                          bind:value={computerFormData.operatingHours}
                          min="0"
                          required
                        />
                      </div>
                      
                      <div class="form-group">
                        <label for="installationDate">Installation Date</label>
                        <input 
                          type="date"
                          id="installationDate"
                          name="installationDate"
                          bind:value={computerFormData.installationDate}
                        />
                      </div>
                      
                      <div class="form-group">
                        <label for="manufactureDate">Manufacture Date</label>
                        <input 
                          type="date"
                          id="manufactureDate"
                          name="manufactureDate"
                          bind:value={computerFormData.manufactureDate}
                        />
                      </div>
                      
                      <div class="form-group">
                        <label class="checkbox-label">
                          <input 
                            type="checkbox"
                            name="isActive"
                            bind:checked={computerFormData.isActive}
                          />
                          Active
                        </label>
                      </div>
                      
                      <div class="form-group full-width">
                        <label for="notes">Notes</label>
                        <textarea 
                          id="notes"
                          name="notes"
                          bind:value={computerFormData.notes}
                          rows="3"
                        ></textarea>
                      </div>
                    </div>
                    
                    <div class="form-actions">
                      <button type="button" class="btn-secondary" on:click={resetComputerForm}>Cancel</button>
                      <button type="submit" class="btn-primary">Save Computer</button>
                    </div>
                  </form>
                </div>
              {/if}
              
              <div class="computer-grid-container">
                {#if customerComputers && customerComputers.length > 0}
                  <div class="computers-header-info">
                    <h4>All Computers ({customerComputers.length})</h4>
                    <span class="info-text">Click on any computer to select it</span>
                  </div>
                  
                  <CustomerComputersGrid 
                    computers={customerComputers} 
                    selectedId={selectedComputerId}
                    onSelect={(computerId) => {
                      // Navigate directly to the computer details page
                      goto(`/customer-computers/${computerId}`);
                    }}
                  />

                  
                  <div class="all-computers-link">
                    <a href="/customer-computers?customerId={selectedCustomer._id}" class="btn-secondary">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
                      </svg>
                      View All Computers
                    </a>
                  </div>
                {:else}
                  <div class="empty-state">
                    <p>No computers found for this customer. Click "Add New Computer" to add one.</p>
                  </div>
                {/if}
              </div>
            </div>
            
            <div class="related-collections">
              <a href="/customers/sites?customerId={selectedCustomer._id}" class="related-link">
                <div class="related-name">Sites</div>
              </a>
              <a href="/customers/service-contracts?customerId={selectedCustomer._id}" class="related-link">
                <div class="related-name">Service Contracts</div>
              </a>
            </div>
            
            <div class="action-buttons">
              <a href="/customer-computers?customerId={selectedCustomer._id}" class="btn-secondary">
                Show Computers
              </a>
              <button class="btn-primary" on:click={() => editCustomer(selectedCustomer)}>
                Edit Customer
              </button>
            </div>
          </div>
        </div>
      {/if}
    </div>
  {/if}

  {#if showForm}
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h2 class="modal-title">{editingCustomer ? 'Edit Customer' : 'Add New Customer'}</h2>
          <button 
            type="button" 
            class="btn-icon" 
            on:click={resetForm}
            aria-label="Close modal"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <form
          action={editingCustomer ? '?/updateCustomer' : '?/createCustomer'}
          method="POST"
          use:enhance={() => {
            return async ({ result }) => {
              console.log('Form submission result:', result);
              if (result.type === 'success') {
                // Reset form to close the popup
                resetForm();
                
                // When a new customer is created (not an update), use our dedicated refresh function
                if (!editingCustomer) {
                  refreshAfterCreate();
                } else {
                  // For updates, use the regular refresh method
                  await refreshCustomers();
                }
              }
            };
          }}
        >
          {#if editingCustomer}
            <input type="hidden" name="_id" value={formData._id} />
          {/if}
          
          <div class="form-grid">
            <div class="form-group">
              <label for="companyName">Company Name</label>
              <input
                type="text"
                id="companyName"
                name="companyName"
                required
                bind:value={formData.companyName}
              />
            </div>
            
            <div class="form-group">
              <label for="type">Customer Type</label>
              <select
                id="type"
                name="type"
                required
                bind:value={formData.type}
              >
                {#each customerTypes as type}
                  <option value={type}>{type}</option>
                {/each}
              </select>
            </div>
            
            <div class="form-group">
              <label for="division">Division</label>
              <select
                id="division"
                name="division"
                required
                bind:value={formData.division}
              >
                {#each CUSTOMER_DIVISIONS as division}
                  <option value={division}>{division}</option>
                {/each}
              </select>
            </div>
            
            <div class="form-group versatile-group">
              <label id="versatile-options-label" for="versatile-options">Versatile Options</label>
              <div class="versatile-options-grid" id="versatile-options" aria-labelledby="versatile-options-label">
                {#each VERSATILE_OPTIONS as option}
                  <div class="checkbox-item">
                    <input
                      type="checkbox"
                      id={`versatile-${option.replace(/\s+/g, '-').toLowerCase()}`}
                      name="versatile"
                      value={option}
                      checked={formData.versatile?.includes(option) || false}
                      on:change={(e) => {
                        // Initialize versatile array if undefined
                        if (!formData.versatile) formData.versatile = [];
                        
                        const isChecked = e.currentTarget.checked;
                        
                        if (isChecked) {
                          // Add option if checked and not already included
                          if (!formData.versatile.includes(option)) {
                            formData.versatile = [...formData.versatile, option];
                          }
                        } else {
                          // Remove option if unchecked
                          formData.versatile = formData.versatile.filter(item => item !== option);
                        }
                        console.log(`${option} is ${isChecked ? 'checked' : 'unchecked'}, versatile options:`, formData.versatile);
                      }}
                    />
                    <label for={`versatile-${option.replace(/\s+/g, '-').toLowerCase()}`}>{option}</label>
                  </div>
                {/each}
                
                <!-- Add a single hidden input with JSON string of all versatile options -->
                <input 
                  type="hidden" 
                  name="versatile_json" 
                  value={JSON.stringify(formData.versatile || [])} 
                />
              </div>
            </div>
            
            <div class="form-group">
              <label for="email">Email</label>
              <input
                type="email"
                id="email"
                name="email"
                required
                bind:value={formData.email}
              />
            </div>
            
            <div class="form-group">
              <label for="phone">Phone</label>
              <input
                type="tel"
                id="phone"
                name="phone"
                bind:value={formData.phone}
              />
            </div>
            
            <div class="form-group">
              <label for="address">Address</label>
              <input
                type="text"
                id="address"
                name="address"
                bind:value={formData.address}
              />
            </div>
            
            <div class="form-group">
              <label for="city">City</label>
              <input
                type="text"
                id="city"
                name="city"
                bind:value={formData.city}
              />
            </div>
            
            <div class="form-group">
              <label for="country">Country</label>
              <input
                type="text"
                id="country"
                name="country"
                bind:value={formData.country}
              />
            </div>
            
            <div class="form-group full-width">
              <label for="notes">Notes</label>
              <textarea
                id="notes"
                name="notes"
                rows="3"
                bind:value={formData.notes}
              ></textarea>
            </div>
          </div>
          
          <div class="form-actions">
            <button type="button" class="btn-secondary" on:click={resetForm}>Cancel</button>
            <button type="submit" class="btn-primary">{editingCustomer ? 'Update' : 'Create'}</button>
          </div>
        </form>
      </div>
    </div>
  {/if}
</div>

<style>
  /* Base styles */
  .container {
    max-width: 1400px;
    margin: 0 auto; /* Center the container */
    padding: 2rem 1rem;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: #f8fafc;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  
  /* Header */
  .header-content {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 2rem;
    align-items: center;
    margin-bottom: 2rem;
    padding-left: 3rem; /* Add left padding to shift content right */
  }
  
  h1 {
    font-size: 1.875rem;
    font-weight: 700;
    color: white;
    margin: 0;
    letter-spacing: -0.025em;
  }
  
  .header-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
  }
  
  .search-container {
    display: flex;
    gap: 1rem;
    align-items: center;
  }
  
  .search-input {
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    min-width: 200px;
  }
  
  .filter-select {
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    background-color: white;
  }
  
  /* Empty state */
  .empty-state {
    text-align: center;
    padding: 3rem 1.5rem;
    background-color: white;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  }
  
  .empty-state h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 1rem;
  }
  
  .empty-state p {
    color: #64748b;
    margin-bottom: 1.5rem;
  }
  
  /* Page Layout using CSS Grid */
  .page-layout {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    width: 100%;
    
    @media (min-width: 1200px) {
      grid-template-columns: 3fr 2fr; /* Change ratio to give more space to customer list */
      align-items: start;
      margin-right: -2rem; /* Adjust right margin to balance the layout */
    }
  }
  
  /* Grid Container */
  .grid-container {
    background-color: white;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    overflow: hidden;
  }
  
  /* Customer Details */
  .customer-details {
    background-color: white;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }
  
  .details-header {
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    gap: 1rem;
    background-color: #1e3a8a;
    color: white;
    padding: 1rem;
  }
  
  .details-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
  }
  
  .details-content {
    padding: 1.5rem;
  }
  
  .details-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
    
    @media (min-width: 640px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  .detail-group {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .detail-group:last-child {
    grid-column: 1 / -1;
  }
  
  .detail-group h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #1e3a8a;
    margin: 0 0 0.5rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e2e8f0;
  }
  
  .detail-item {
    display: grid;
    grid-template-columns: 100px 1fr;
  }
  
  .detail-item.notes {
    grid-template-columns: 1fr;
  }
  
  .detail-label {
    font-weight: 500;
    color: #64748b;
  }
  
  .detail-value {
    color: #334155;
  }
  
  .related-collections {
    border-top: 1px solid #e2e8f0;
    padding-top: 1.5rem;
  }
  
  .related-collections h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #1e3a8a;
    margin: 0 0 1rem 0;
  }
  
  .related-links {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .related-link {
    display: grid;
    grid-template-rows: auto 1fr;
    padding: 1rem;
    text-align: center;
    background-color: #f8fafc;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    color: #1e3a8a;
    text-decoration: none;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }
  
  .related-link:hover {
    background-color: #eff6ff;
    border-color: #bfdbfe;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  
  .related-count {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e3a8a;
  }
  
  .related-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #64748b;
  }
  
  .action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
  }
  
  /* Badge styles */
  .badge-container {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }
  
  .badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    color: white;
  }
  
  .badge-retail {
    background-color: #1e3a8a;
  }
  
  .badge-wholesale {
    background-color: #0f766e;
  }
  
  .badge-oem {
    background-color: #b45309;
  }
  
  .badge-distributor {
    background-color: #4f46e5;
  }
  
  .badge-education {
    background-color: #7e22ce;
  }
  
  .badge-government {
    background-color: #b91c1c;
  }
  
  /* Division badges */
  .badge-division {
    font-size: 0.7rem;
  }
  
  .badge-industrial {
    background-color: #475569;
  }
  
  .badge-marine {
    background-color: #0891b2;
  }
  
  /* Versatile tags styling */
  .versatile-tags {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 0.5rem;
    margin-top: 0.75rem;
  }
  
  .versatile-tag {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    background-color: #f1f5f9;
    color: #475569;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 500;
    border-left: 3px solid #64748b;
  }
  
  /* Buttons */
  .btn-primary {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    background-color: #1e3a8a;
    color: white;
    font-weight: 500;
    padding: 0.625rem 1.25rem;
    border-radius: 0.375rem;
    border: none;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }
  
  .btn-primary:hover {
    background-color: #1e40af;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  
  .btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }
  
  .btn-secondary {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    background-color: white;
    color: #1e3a8a;
    font-weight: 500;
    padding: 0.625rem 1.25rem;
    border-radius: 0.375rem;
    border: 1px solid #e2e8f0;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }
  
  .btn-secondary:hover {
    background-color: #f8fafc;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  
  .btn-secondary:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }
  
  .btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.25rem;
    height: 2.25rem;
    border-radius: 0.375rem;
    border: 1px solid #e2e8f0;
    background-color: white;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }
  
  .btn-icon svg {
    width: 1.25rem;
    height: 1.25rem;
  }
  
  .btn-icon:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  
  .btn-icon:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }
  
  .btn-icon.view:hover {
    background-color: #eff6ff;
    color: #2563eb;
    border-color: #bfdbfe;
  }
  
  .btn-icon.edit:hover {
    background-color: #f0fdf4;
    color: #16a34a;
    border-color: #bbf7d0;
  }
  
  .btn-icon.delete:hover {
    background-color: #fef2f2;
    color: #dc2626;
    border-color: #fecaca;
  }
  
  /* Modal */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
    backdrop-filter: blur(4px);
  }
  
  .modal-content {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    width: 100%;
    max-width: 32rem;
    max-height: 90vh;
    overflow-y: auto;
    padding: 1.5rem;
    animation: modal-appear 0.3s ease-out;
  }
  
  @keyframes modal-appear {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
  }
  
  .modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e3a8a;
    margin: 0;
  }
  
  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .versatile-group {
    grid-column: 1 / -1; /* Span all columns */
  }
  
  .computer-grid-container {
    display: grid;
    grid-template-rows: auto auto 1fr auto auto;
    gap: 1rem;
    margin-top: 1rem;
    height: 100%;
    min-height: 400px;
  }
  
  .computers-header-info {
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
  }
  
  .computers-header-info h4 {
    margin: 0;
    font-size: 1.1rem;
    color: #1e40af;
  }
  
  .info-text {
    font-size: 0.9rem;
    color: #6b7280;
    font-style: italic;
  }
  
  .selected-computer-details {
    display: grid;
    grid-template-rows: auto 1fr auto;
    gap: 1rem;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 1rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  }
  
  .selected-computer-details h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    color: #1e40af;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 0.5rem;
  }
  
  .details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
  }
  
  .detail-row {
    display: grid;
    grid-template-columns: 150px 1fr;
    gap: 0.5rem;
    align-items: center;
  }
  
  .detail-label {
    font-weight: 600;
    color: #4b5563;
  }
  
  .computer-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1rem;
  }
  
  .all-computers-link {
    display: flex;
    justify-content: flex-end;
    margin-top: 1rem;
  }
  
  .versatile-options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 0.75rem;
    margin-top: 0.5rem;
  }
  
  .checkbox-item {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 0.5rem;
    align-items: center;
  }
  
  .form-group {
    margin-bottom: 1.5rem;
  }
  
  .form-group.full-width {
    grid-column: span 2;
  }
  
  .form-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #334155;
    margin-bottom: 0.5rem;
  }
  
  .form-group input,
  .form-group select,
  .form-group textarea {
    width: 100%;
    padding: 0.625rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }
  
  .form-group input:focus,
  .form-group select:focus,
  .form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
  }
  
  .form-group textarea {
    resize: vertical;
    min-height: 6rem;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e2e8f0;
  }
  
  /* Status badges */
  .badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
  }
  
  .badge-retail {
    background-color: #1e3a8a;
    color: white;
  }
  
  .badge-wholesale {
    background-color: #0f766e;
    color: white;
  }
  
  .badge-oem {
    background-color: #b45309;
    color: white;
  }
  
  .badge-distributor {
    background-color: #4f46e5;
    color: white;
  }
  
  .badge-education {
    background-color: #7e22ce;
    color: white;
  }
  
  .badge-government {
    background-color: #b91c1c;
    color: white;
  }
  
  /* Contact and location info */
  .contact-info,
  .location-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .text-gray-400 {
    color: #94a3b8;
  }
  
  .text-gray-500 {
    color: #64748b;
  }
  
  .font-medium {
    font-weight: 500;
    color: #1e3a8a;
  }
  
  .flex {
    display: flex;
  }
  
  .items-center {
    align-items: center;
  }
  
  .ml-2 {
    margin-left: 0.5rem;
  }
  
  /* Links */
  a {
    color: #2563eb;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
  }
  
  a:hover {
    color: #1d4ed8;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .header-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
    
    .header-actions {
      width: 100%;
      flex-direction: column;
    }
    
    .search-input,
    .filter-select {
      width: 100%;
    }
    
    .form-grid {
      grid-template-columns: 1fr;
    }
    
    .form-group.full-width {
      grid-column: span 1;
    }
  }
</style>