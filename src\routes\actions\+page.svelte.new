<script lang="ts">
    import { enhance } from '$app/forms';

    interface ServiceCodeAction {
        _id: { $oid: string };
        "Product Validity GRoup": string;
        ActivityPurpose: string;
        ServiceActivityLabel: string;
        ServiceCode: string;
        ActionType: string;
        PartNumber: number;
        "Unit of Measure": string;
        Quantity: number;
        InternalNoOfHours: number;
        InternalNoOfMonths: number | null;
    }

    export let data: { items: ServiceCodeAction[] };
    export let form;
</script>

<div class="p-4">
    <h1 class="text-2xl font-bold mb-4">Services</h1>

    <div class="bg-white p-4 rounded shadow mb-4">
        <form method="POST" action="?/search" use:enhance>
            <div class="grid grid-cols-3 gap-4">
                <div>
                    <label for="productDesignation" class="block text-sm font-medium text-gray-700 mb-1">
                        Product Designation
                    </label>
                    <input
                        type="text"
                        id="productDesignation"
                        name="productDesignation"
                        class="w-full p-2 border rounded"
                        placeholder="e.g., D1-13"
                        required
                    />
                </div>
                <div>
                    <label for="productPartNumber" class="block text-sm font-medium text-gray-700 mb-1">
                        Product Part Number
                    </label>
                    <input
                        type="number"
                        id="productPartNumber"
                        name="productPartNumber"
                        class="w-full p-2 border rounded"
                        placeholder="e.g., 868975"
                        required
                    />
                </div>
                <div class="flex items-end">
                    <button
                        type="submit"
                        class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                    >
                        Search
                    </button>
                </div>
            </div>
            {#if form?.error}
                <p class="text-red-500 mt-2">{form.error}</p>
            {/if}
        </form>
    </div>

    {#if data.items.length > 0}
        <div class="overflow-x-auto bg-white rounded shadow">
            <table class="min-w-full">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="p-2 text-left">Service Code</th>
                        <th class="p-2 text-left">Action Type</th>
                        <th class="p-2 text-left">Activity Label</th>
                        <th class="p-2 text-left">Purpose</th>
                        <th class="p-2 text-left">Product Group</th>
                        <th class="p-2 text-left">Part Number</th>
                        <th class="p-2 text-left">Quantity</th>
                        <th class="p-2 text-left">Unit</th>
                        <th class="p-2 text-left">Hours</th>
                        <th class="p-2 text-left">Months</th>
                    </tr>
                </thead>
                <tbody>
                    {#each data.items as item}
                        <tr class="border-b hover:bg-gray-50">
                            <td class="p-2">{item.ServiceCode}</td>
                            <td class="p-2">{item.ActionType}</td>
                            <td class="p-2">{item.ServiceActivityLabel}</td>
                            <td class="p-2">{item.ActivityPurpose}</td>
                            <td class="p-2">{item["Product Validity GRoup"]}</td>
                            <td class="p-2">{item.PartNumber}</td>
                            <td class="p-2">{item.Quantity}</td>
                            <td class="p-2">{item["Unit of Measure"]}</td>
                            <td class="p-2">{item.InternalNoOfHours}</td>
                            <td class="p-2">{item.InternalNoOfMonths ?? '-'}</td>
                        </tr>
                    {/each}
                </tbody>
            </table>
        </div>
    {:else}
        <p class="text-gray-500 text-center p-4">Enter a product designation and part number to search for service actions.</p>
    {/if}
</div>
