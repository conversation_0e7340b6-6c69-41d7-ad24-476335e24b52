import { MongoClient } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);
const dbName = 'ServiceContracts';

/**
 * @param {Object} options
 * @param {Object} options.params
 * @param {string} options.params.group - The ProductValidityGroup value to filter by
 */
export async function GET({ params }) {
    try {
        await client.connect();
        const db = client.db(dbName);
        const collection = db.collection('ServiceCodeAndActionType');
        
        // Use ProductValidityGroup field to filter by the provided group parameter
        const documents = await collection.find({
            ProductValidityGroup: params.group
        })
        .sort({ ServiceCode: 1, ActionType: 1 })
        .toArray();
            
        return json(documents);
    } catch (error) {
        console.error('Error fetching service code action types by product validity group:', error);
        return new Response(JSON.stringify({ error: error instanceof Error ? error.message : 'Unknown error' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    } finally {
        await client.close();
    }
}
