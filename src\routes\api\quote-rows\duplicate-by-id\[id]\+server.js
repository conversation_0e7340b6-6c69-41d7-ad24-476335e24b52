import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/**
 * POST: Duplicate a quote row by ID
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function POST({ params }) {
  try {
    const db = client.db('ServiceContracts');
    const rowId = params.id;
    
    // Validate ObjectId format
    if (!ObjectId.isValid(rowId)) {
      return json({ success: false, message: 'Invalid row ID format' }, { status: 400 });
    }
    
    // Find the original quote row
    const originalRow = await db.collection('QuotationRows').findOne({ 
      _id: new ObjectId(rowId) 
    });
    
    if (!originalRow) {
      return json({ success: false, message: 'Quote row not found' }, { status: 404 });
    }
    
    // Create a duplicate without the _id field (MongoDB will generate a new one)
    const { _id, ...duplicateData } = originalRow;
    
    // Add timestamp and mark as a duplicate
    duplicateData.createdAt = new Date();
    duplicateData.isDuplicate = true;
    duplicateData.originalRowId = rowId;
    duplicateData.service = `${duplicateData.service} (Copy)`;
    
    // Insert the duplicate row
    const result = await db.collection('QuotationRows').insertOne(duplicateData);
    
    // Get the complete new row data
    const newRow = await db.collection('QuotationRows').findOne({ 
      _id: result.insertedId 
    });
    
    // Convert ObjectIds to strings for response
    const responseData = {
      ...newRow,
      _id: newRow._id.toString(),
      quotationId: newRow.quotationId.toString(),
      originalRowId: newRow.originalRowId.toString()
    };
    
    return json(responseData);
  } catch (error) {
    console.error('Error duplicating quote row:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return json({ success: false, message: errorMessage }, { status: 500 });
  }
}
