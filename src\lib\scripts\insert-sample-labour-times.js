import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function insertSampleLabourTimes() {
    const client = new MongoClient(uri);
    
    try {
        await client.connect();
        console.log('Connected to MongoDB');
        
        const db = client.db(dbName);
        const labourTimeCollection = db.collection('LabourTime');

        // Sample data
        const sampleData = [
            {
                productDesignation: 'TAD1381-85VE',
                laborHours: 4.5,
                category: 'Engine Maintenance',
                description: 'Standard engine maintenance procedure',
                createdAt: new Date(),
                updatedAt: new Date()
            },
            {
                productDesignation: 'TAD1382-87VE',
                laborHours: 6.0,
                category: 'Major Repair',
                description: 'Complete engine overhaul',
                createdAt: new Date(),
                updatedAt: new Date()
            },
            {
                productDesignation: 'TAD1383-88VE',
                laborHours: 2.5,
                category: 'Inspection',
                description: 'Routine inspection and diagnostics',
                createdAt: new Date(),
                updatedAt: new Date()
            },
            {
                productDesignation: 'TAD1384-89VE',
                laborHours: 3.0,
                category: 'Parts Replacement',
                description: 'Standard parts replacement procedure',
                createdAt: new Date(),
                updatedAt: new Date()
            },
            {
                productDesignation: 'TAD1385-90VE',
                laborHours: 5.5,
                category: 'System Upgrade',
                description: 'Software and hardware system upgrade',
                createdAt: new Date(),
                updatedAt: new Date()
            }
        ];

        // Insert the sample data
        const result = await labourTimeCollection.insertMany(sampleData);
        console.log(`Successfully inserted ${result.insertedCount} labour time records`);
        
        // Verify the data
        const count = await labourTimeCollection.countDocuments();
        console.log(`Total labour time records in collection: ${count}`);
        
        const samples = await labourTimeCollection.find({}).limit(2).toArray();
        console.log('Sample records:', samples);
        
    } catch (error) {
        console.error('Error inserting sample data:', error);
    } finally {
        await client.close();
        console.log('Disconnected from MongoDB');
    }
}

// Run the insertion
insertSampleLabourTimes().catch(console.error);
