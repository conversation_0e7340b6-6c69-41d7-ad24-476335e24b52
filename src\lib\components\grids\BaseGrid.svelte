<!-- BaseGrid.svelte -->
<script lang="ts">
  // Props for grid configuration
  export let columns: string = 'repeat(12, 1fr)';  // Default 12-column grid
  export let rows: string = 'auto';
  export let gap: string = '1rem';
  export let padding: string = '1rem';
  export let minHeight: string = 'auto';
  export let maxWidth: string = '100%';
  export let backgroundColor: string = 'transparent';
  export let fullScreen: boolean = true; // Added fullScreen prop with default true

  // Optional class for additional styling
  export let className: string = '';
</script>

<div class="basegrid {className} {fullScreen ? 'full-width' : ''}" 
  style:--columns={columns} 
  style:--rows={rows} 
  style:--gap={gap} 
  style:--padding={padding} 
  style:--min-height={minHeight} 
  style:--max-width={maxWidth} 
  style:--background-color={backgroundColor}>
  <slot />
</div>

<style>
  .basegrid {
    display: grid;
    grid-template-columns: var(--columns);
    grid-template-rows: var(--rows);
    gap: var(--gap);
    padding: var(--padding);
    min-height: var(--min-height);
    max-width: var(--max-width);
    background-color: var(--background-color);
    width: 100%;
    box-sizing: border-box;
  }

  .full-width {
    width: 100%;
    height: 100%;
    max-width: 100%;
  }

  /* Responsive grid adjustments */
  @media (max-width: 768px) {
    .basegrid {
      grid-template-columns: repeat(6, 1fr); /* Adjust for tablets */
      gap: calc(var(--gap) * 0.75);
    }
  }

  @media (max-width: 480px) {
    .basegrid {
      grid-template-columns: repeat(4, 1fr); /* Adjust for mobile */
      gap: calc(var(--gap) * 0.5);
    }
  }
</style>
