<script lang="ts">
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  import FormGrid from '$lib/components/grids/FormGrid.svelte';
  import { onMount } from 'svelte';
  
  // Initialize with empty values
  let formData = {
    computerId: '',
    DOID: '',
    ProprietaryName: '',
    activity: 'VM Request Electronic Data Link',
    description: '',
    priority: 'Medium',
    status: 'Active'
  };

  let loading = false;
  let error = '';
  let success = false;
  let missingFields = false;
  let fetchingComputerId = false;

  // Get computer ID from URL query parameter if available
  onMount(async () => {
    const params = new URLSearchParams(window.location.search);
    const urlComputerId = params.get('computerId');
    
    if (urlComputerId) {
      // Immediately set the computerId from URL to give instant feedback
      formData.computerId = urlComputerId;
      
      fetchingComputerId = true;
      try {
        // If this looks like a document ID (not a computer ID), fetch the actual computerId
        if (urlComputerId.length === 24) {
          // Fetch the document to get the actual computerId field
          const res = await fetch(`/api/active-faultcode/${urlComputerId}`);
          if (res.ok) {
            const doc = await res.json();
            // Use the computerId field from the document, not the document ID
            if (doc && doc.computerId) {
              formData.computerId = doc.computerId;
            }
          }
        }
      } catch (err) {
        console.error('Error fetching computer details:', err);
      } finally {
        fetchingComputerId = false;
      }
    }
    // If coming from a specific computer page, also try to get ID from referrer
    else if (document.referrer && document.referrer.includes('/computer-id/')) {
      const matches = document.referrer.match(/\/computer-id\/([^\/]+)/);
      if (matches && matches[1]) {
        formData.computerId = matches[1];
      }
    }
  });

  function validateForm() {
    if (!formData.computerId || !formData.DOID) {
      missingFields = true;
      return false;
    }
    missingFields = false;
    return true;
  }

  async function handleSave() {
    if (!validateForm()) {
      error = 'Please fill in all required fields';
      return;
    }
    
    loading = true;
    error = '';
    success = false;
    
    try {
      // Create a new document in ActiveFaultCodes
      const res = await fetch('/api/active-faultcode', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });
      
      if (!res.ok) {
        const data = await res.json().catch(() => ({}));
        throw new Error(data.error || 'Failed to save');
      }
      
      const result = await res.json();
      success = true;
      
      // Navigate to the new document after a short delay
      setTimeout(() => {
        goto(`/active-faultcode/${result.id}`);
      }, 1000);
    } catch (e) {
      console.error('Save error:', e);
      error = e?.message || 'Unknown error';
    } finally {
      loading = false;
    }
  }

  function handleBack() {
    history.back();
  }
</script>

<div class="ach-card">
  <button class="back-btn" on:click={handleBack}>Back</button>
  <h2>Add New Fault Code</h2>
  
  {#if success}
    <div class="success-msg">Fault code added successfully! Redirecting...</div>
  {/if}
  
  {#if error}
    <div class="error-msg">{error}</div>
  {/if}
  
  {#if missingFields}
    <div class="error-msg">Missing required fields</div>
  {/if}
  
  {#if fetchingComputerId}
    <div class="info-msg">Loading computer details...</div>
  {/if}
  
  <FormGrid>
    <label for="computerId">Computer ID <span class="required">*</span></label>
    <input 
      id="computerId" 
      type="text" 
      bind:value={formData.computerId} 
      placeholder="ObjectId required"
      class={!formData.computerId && missingFields ? 'error-field' : ''} 
    />
    
    <label for="DOID">DO ID <span class="required">*</span></label>
    <input 
      id="DOID" 
      type="text" 
      bind:value={formData.DOID} 
      placeholder="e.g. D174Z"
      class={!formData.DOID && missingFields ? 'error-field' : ''} 
    />
    
    <label for="ProprietaryName">Proprietary Name</label>
    <input id="ProprietaryName" type="text" bind:value={formData.ProprietaryName} />
    
    <label for="activity">Activity</label>
    <select id="activity" bind:value={formData.activity}>
      <option value="VM Request Electronic Data Link">VM Request Electronic Data Link</option>
      <option value="Maintenance Code">Maintenance Code</option>
      <option value="System Error">System Error</option>
      <option value="Hardware Error">Hardware Error</option>
    </select>
    
    <label for="description">Description</label>
    <textarea id="description" bind:value={formData.description} rows="3"></textarea>
    
    <label for="priority">Priority</label>
    <select id="priority" bind:value={formData.priority}>
      <option value="Low">Low</option>
      <option value="Medium">Medium</option>
      <option value="High">High</option>
      <option value="Critical">Critical</option>
    </select>
    
    <label for="status">Status</label>
    <select id="status" bind:value={formData.status}>
      <option value="Active">Active</option>
      <option value="Resolved">Resolved</option>
      <option value="Pending">Pending</option>
    </select>
  </FormGrid>
  
  <div class="ach-actions">
    <button class="save-btn" on:click={handleSave} disabled={loading}>
      {loading ? 'Saving...' : 'Save'}
    </button>
  </div>
</div>

<style>
  .ach-card {
    max-width: 480px;
    margin: 3em auto;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 18px rgba(0,0,0,0.08);
    padding: 2.5em 2em 2em 2em;
  }
  h2 {
    text-align: center;
    margin-bottom: 1.5rem;
  }
  .ach-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
  }
  .error-msg {
    color: #b00020;
    padding: 0.5rem;
    margin: 1rem 0;
    background-color: #ffebee;
    border-radius: 4px;
  }
  .success-msg {
    color: #388e3c;
    padding: 0.5rem;
    margin: 1rem 0;
    background-color: #e8f5e9;
    border-radius: 4px;
  }
  .info-msg {
    color: #1976d2;
    padding: 0.5rem;
    margin: 1rem 0;
    background-color: #e3f2fd;
    border-radius: 4px;
  }
  .required {
    color: #b00020;
  }
  .error-field {
    border-color: #b00020;
    background-color: #fff8f8;
  }
  .back-btn {
    background: #f3f3f3;
    color: #333;
    border: 1px solid #e3e7ee;
    border-radius: 5px;
    padding: 0.4em 1em;
    font-weight: 500;
    cursor: pointer;
    margin-bottom: 1em;
  }
  .save-btn {
    background: #059669;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-weight: 600;
    padding: 0.55em 1.3em;
    font-size: 1.06em;
    cursor: pointer;
    transition: background 0.15s;
  }
  .save-btn:hover {
    background: #047857;
  }
  .save-btn:disabled {
    background: #93b5f3;
    cursor: not-allowed;
  }
  textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-family: inherit;
    font-size: 1em;
  }
</style>
