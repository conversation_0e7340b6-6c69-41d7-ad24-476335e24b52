import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function splitServiceCode() {
    const client = new MongoClient(uri);
    
    try {
        await client.connect();
        console.log('Connected to MongoDB');
        
        const db = client.db(dbName);
        const labourTimeCollection = db.collection('LabourTime');

        // Get all documents
        const documents = await labourTimeCollection.find({}).toArray();
        console.log(`Found ${documents.length} documents to update`);

        // Update each document
        for (const doc of documents) {
            const serviceCode = doc['Service Code'] || '';
            const [servicePart, computerPart] = serviceCode.split('-');

            const updates = {
                $set: {
                    'ServicePhase': servicePart || '',
                    'ComputerCategory': computerPart || ''
                }
            };

            const result = await labourTimeCollection.updateOne(
                { _id: doc._id },
                updates
            );

            console.log(`Updated document ${doc._id}: Service Code "${serviceCode}" -> ServicePhase: "${servicePart}", ComputerCategory: "${computerPart}"`);
        }

        // Verify updates
        console.log('\nVerifying updates...');
        const updatedDocs = await labourTimeCollection.find({}).limit(5).toArray();
        console.log('Sample updated documents:');
        updatedDocs.forEach(doc => {
            console.log({
                _id: doc._id,
                'Service Code': doc['Service Code'],
                'ServicePhase': doc['ServicePhase'],
                'ComputerCategory': doc['ComputerCategory']
            });
        });
        
    } catch (error) {
        console.error('Error updating collection:', error);
    } finally {
        await client.close();
        console.log('\nDisconnected from MongoDB');
    }
}

// Run the update
splitServiceCode().catch(console.error);
