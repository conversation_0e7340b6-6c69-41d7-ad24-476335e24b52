import { MongoClient, ObjectId } from 'mongodb';
import { MONGODB_URI } from '$env/static/private';

// Use fallback if env variable not set
const uri = MONGODB_URI || 'mongodb://localhost:27017';
const client = new MongoClient(uri);

// Connect to the database
await client.connect();
console.log('Connected to MongoDB for Service Contracts');

// MongoDB database name
const DATABASE_NAME = 'ServiceContracts';

// Get database instance
const db = client.db(DATABASE_NAME);

// Collection names using PascalCase as per convention
export const COLLECTIONS = {
  CONTRACT_HEADERS: 'ContractHeaders',
  CONTRACT_LINES: 'ContractLines',
  CONTRACT_TEMPLATES: 'ContractTemplates', 
  SERVICE_LEVELS: 'ServiceLevels',
  PRICING_RULES: 'PricingRules',
  QUOTATION_HEADERS: 'QuotationHeaders',
  QUOTATION_LINES: 'QuotationLines'
};

/**
 * Helper function to convert MongoDB ObjectIds to strings for client-side use
 * @param {Object} doc - MongoDB document
 * @returns {Object} - Document with ObjectIds converted to strings
 */
export function serializeDocument(doc) {
  if (!doc) return null;
  
  const serialized = { ...doc };
  
  // Convert ObjectId to string
  for (const [key, value] of Object.entries(serialized)) {
    if (value instanceof ObjectId) {
      serialized[key] = value.toString();
    } else if (value instanceof Date) {
      serialized[key] = value.toISOString();
    } else if (Array.isArray(value)) {
      serialized[key] = value.map(item => {
        if (item instanceof ObjectId) {
          return item.toString();
        } else if (typeof item === 'object' && item !== null) {
          return serializeDocument(item);
        }
        return item;
      });
    } else if (typeof value === 'object' && value !== null) {
      serialized[key] = serializeDocument(value);
    }
  }
  
  return serialized;
}

/**
 * Helper function to safely create an ObjectId from a string
 * @param {string} id - String ID to convert
 * @returns {ObjectId|null} - MongoDB ObjectId or null if invalid
 */
export function createObjectId(id) {
  try {
    return new ObjectId(id);
  } catch (error) {
    console.error('Invalid ObjectId:', id, error);
    return null;
  }
}

/**
 * Generic function to handle MongoDB operations with proper error handling
 * @param {Function} operation - Function that performs MongoDB operation
 * @param {string} errorMessage - Custom error message
 * @returns {Promise<any>} - Result of operation or throws error
 */
export async function executeDbOperation(operation, errorMessage) {
  try {
    return await operation();
  } catch (error) {
    console.error(`${errorMessage}:`, error);
    throw new Error(`${errorMessage}: ${error.message}`);
  }
}

export default {
  client,
  db,
  COLLECTIONS,
  serializeDocument,
  createObjectId,
  executeDbOperation
};
