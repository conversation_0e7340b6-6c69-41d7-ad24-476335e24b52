<script>
  import ServiceOfferingTable from './ServiceOfferingTable.svelte';
  import { createEventDispatcher } from 'svelte';

  const dispatch = createEventDispatcher();

  // Props
  export let quotationPackages = null;
  export let computer = null;

  // Package offering data (levels A, B, C) - matching the image structure
  const packageOfferings = [
    { level: 'A', name: 'N/A', status: 'Yes' },
    { level: 'B', name: 'N/A', status: 'Yes' },
    { level: 'C', name: 'Yes', status: 'Yes' }
  ];

  // Transform quotation data to match the individual service structure
  function transformToIndividualServices(quotationPackages) {
    if (!quotationPackages) return [];

    const services = [];
    let serviceIndex = 1;

    // Base Contract Offering
    if (quotationPackages.baseContract) {
      quotationPackages.baseContract.forEach(service => {
        services.push({
          level: serviceIndex,
          packageName: 'Base Contract Offering',
          serviceId: service.serviceId || service.serviceCode || '4.1',
          ServiceActivity: service.serviceName || service.ServiceActivity || 'Parts Supply on Request',
          includedInPackage: service.includedInPackageCost || service.cost === 0,
          required: service.required || service.isRequired,
          includeInOffer: service.includeInQuote || service.includeInOffer || service.isEnabled,
          cost: service.cost || service.unitPrice || 0,
          id: service._id || service.id
        });
        serviceIndex++;
      });
    }

    // Dealer Add-Ons
    if (quotationPackages.repairPackages) {
      quotationPackages.repairPackages.forEach(service => {
        services.push({
          level: serviceIndex,
          packageName: 'Dealer Add-Ons',
          serviceId: service.serviceId || service.serviceCode || '1.2',
          ServiceActivity: service.serviceName || service.ServiceActivity || 'Inspections',
          includedInPackage: service.includedInPackageCost || service.cost === 0,
          required: service.required || service.isRequired,
          includeInOffer: service.includeInQuote || service.includeInOffer || service.isEnabled,
          cost: service.cost || service.unitPrice || 0,
          id: service._id || service.id
        });
        serviceIndex++;
      });
    }

    // Support Services
    if (quotationPackages.supportServices) {
      quotationPackages.supportServices.forEach(service => {
        services.push({
          level: serviceIndex,
          packageName: 'Support (Self Service)',
          serviceId: service.serviceId || service.serviceCode || '2.1',
          ServiceActivity: service.serviceName || service.ServiceActivity || 'Oil Sampling',
          includedInPackage: service.includedInPackageCost || service.cost === 0,
          required: service.required || service.isRequired,
          includeInOffer: service.includeInQuote || service.includeInOffer || service.isEnabled,
          cost: service.cost || service.unitPrice || 0,
          id: service._id || service.id
        });
        serviceIndex++;
      });
    }

    // Replacement Services
    if (quotationPackages.replacementServices) {
      quotationPackages.replacementServices.forEach(service => {
        services.push({
          level: serviceIndex,
          packageName: 'Retirement Plan',
          serviceId: service.serviceId || service.serviceCode || '10.1',
          ServiceActivity: service.serviceName || service.ServiceActivity || 'Engine Replacement',
          includedInPackage: service.includedInPackageCost || service.cost === 0,
          required: service.required || service.isRequired,
          includeInOffer: service.includeInQuote || service.includeInOffer || service.isEnabled,
          cost: service.cost || service.unitPrice || 0,
          id: service._id || service.id
        });
        serviceIndex++;
      });
    }

    return services;
  }

  // Get grouped services summary
  function getGroupedServices(individualServices) {
    const groups = {};

    individualServices.forEach(service => {
      if (service.includeInOffer) {
        const packageName = service.packageName;
        if (!groups[packageName]) {
          groups[packageName] = [];
        }
        groups[packageName].push(service);
      }
    });

    return Object.entries(groups).map(([packageName, services], index) => ({
      level: index + 1,
      packageName: `${packageName} (Fixed)`,
      services: services.length
    }));
  }

  $: individualServices = transformToIndividualServices(quotationPackages);
  $: groupedServices = getGroupedServices(individualServices);

  // Event handlers
  function handlePackageToggle(event) {
    dispatch('togglePackage', event.detail);
  }

  function handleServiceSelect(event) {
    dispatch('selectService', event.detail);
  }

  // Calculate total cost
  function calculateTotalCost(services) {
    return services
      .filter(service => service.includeInOffer)
      .reduce((total, service) => total + (service.cost || 0), 0);
  }

  $: totalCost = calculateTotalCost(individualServices);
</script>

<div class="enhanced-service-offering">
  <!-- Package Offering Section -->
  <ServiceOfferingTable
    title="Package offering"
    services={packageOfferings}
    isPackageOffering={true}
    on:toggleService={handlePackageToggle}
    on:selectService={handleServiceSelect}
  />

  <!-- Individual Service Section -->
  <ServiceOfferingTable
    title="Individual service"
    services={individualServices}
    isPackageOffering={false}
    on:toggleService={handlePackageToggle}
    on:selectService={handleServiceSelect}
  />

  <!-- Total Cost Section -->
  <div class="total-section">
    <table class="total-table">
      <tbody>
        <tr>
          <td class="total-label">Total cost in quote</td>
          <td class="total-value">#REF!</td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Grouped Services Section -->
  <div class="grouped-section">
    <table class="grouped-table">
      <thead>
        <tr>
          <th colspan="2">Grouped services</th>
        </tr>
        <tr>
          <th>Level</th>
          <th>Package</th>
        </tr>
      </thead>
      <tbody>
        {#each groupedServices as group}
          <tr>
            <td class="level-cell">{group.level}</td>
            <td class="package-cell">{group.packageName}</td>
          </tr>
        {/each}
      </tbody>
    </table>
  </div>
</div>

<style>
  .enhanced-service-offering {
    padding: 1rem;
    max-width: 1400px;
    margin: 0 auto;
  }

  .total-section {
    margin: 1rem 0;
  }

  .total-table {
    width: 100%;
    border-collapse: collapse;
    border: 2px solid #333;
  }

  .total-table td {
    padding: 0.5rem;
    border: 1px solid #333;
    text-align: center;
    font-weight: bold;
  }

  .total-label {
    background-color: #f5f5f5;
    text-align: left;
  }

  .total-value {
    background-color: #FFD700;
    color: #333;
    text-align: right;
  }

  .grouped-section {
    margin: 1rem 0;
  }

  .grouped-table {
    width: 100%;
    border-collapse: collapse;
    border: 2px solid #333;
  }

  .grouped-table th {
    background-color: #f5f5f5;
    padding: 0.5rem;
    border: 1px solid #333;
    text-align: center;
    font-weight: bold;
  }

  .grouped-table td {
    padding: 0.5rem;
    border: 1px solid #333;
    text-align: center;
  }

  .level-cell {
    width: 60px;
    font-weight: bold;
  }

  .package-cell {
    text-align: left;
  }

  @media (max-width: 768px) {
    .enhanced-service-offering {
      padding: 0.5rem;
    }
  }
</style>
