<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import BaseGrid from './grids/BaseGrid.svelte';
  
  // Props
  export let showRefreshButton: boolean = true;
  export let showAddButton: boolean = true;
  export let showBulkButton: boolean = true;
  export let isLoading: boolean = false;
  
  // Event dispatcher
  const dispatch = createEventDispatcher<{
    refresh: void;
    add: void;
    bulk: void;
  }>();
  
  // Handle actions
  function handleRefresh() {
    dispatch('refresh');
  }
  
  function handleAdd() {
    dispatch('add');
  }
  
  function handleBulk() {
    dispatch('bulk');
  }
</script>

<BaseGrid>
  <div class="actions-toolbar">
    <div class="toolbar-section">
      {#if showRefreshButton}
        <button 
          type="button" 
          class="btn-icon" 
          on:click={handleRefresh}
          disabled={isLoading}
          title="Refresh data"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M23 4v6h-6"></path>
            <path d="M1 20v-6h6"></path>
            <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
          </svg>
          <span>Refresh</span>
        </button>
      {/if}
    </div>
    
    <div class="toolbar-section">
      {#if showAddButton}
        <button 
          type="button" 
          class="btn-primary" 
          on:click={handleAdd}
          disabled={isLoading}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
          <span>Add Activity</span>
        </button>
      {/if}
      
      {#if showBulkButton}
        <button 
          type="button" 
          class="btn-secondary" 
          on:click={handleBulk}
          disabled={isLoading}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="8" y1="6" x2="21" y2="6"></line>
            <line x1="8" y1="12" x2="21" y2="12"></line>
            <line x1="8" y1="18" x2="21" y2="18"></line>
            <line x1="3" y1="6" x2="3.01" y2="6"></line>
            <line x1="3" y1="12" x2="3.01" y2="12"></line>
            <line x1="3" y1="18" x2="3.01" y2="18"></line>
          </svg>
          <span>Bulk Operations</span>
        </button>
      {/if}
    </div>
  </div>
</BaseGrid>

<style>
  .actions-toolbar {
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    padding: 0.75rem;
    background: #1e293b;
    border-radius: 8px;
    margin-bottom: 1.5rem;
  }
  
  .toolbar-section {
    display: flex;
    gap: 0.75rem;
    align-items: center;
  }
  
  button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    border-radius: 6px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
  }
  
  button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  .btn-primary {
    background: #3b82f6;
    color: white;
  }
  
  .btn-primary:hover:not(:disabled) {
    background: #2563eb;
  }
  
  .btn-secondary {
    background: transparent;
    color: #e2e8f0;
    border: 1px solid #475569;
  }
  
  .btn-secondary:hover:not(:disabled) {
    background: rgba(71, 85, 105, 0.2);
  }
  
  .btn-icon {
    background: transparent;
    color: #94a3b8;
    border: none;
    padding: 0.5rem;
  }
  
  .btn-icon:hover:not(:disabled) {
    color: #e2e8f0;
    background: rgba(148, 163, 184, 0.1);
  }
  
  svg {
    flex-shrink: 0;
  }
</style>
