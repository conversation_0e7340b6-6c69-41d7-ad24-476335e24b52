<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  
  // Props
  export let leftColumnWidth: string = "30%";
  export let rightColumnWidth: string = "70%";
  export let gap: string = "1.5rem";
</script>

<BaseGrid>
  <div class="two-column-grid" style="--left-width: {leftColumnWidth}; --right-width: {rightColumnWidth}; --gap: {gap}">
    <div class="left-column">
      <slot name="left"></slot>
    </div>
    
    <div class="right-column">
      <slot name="right"></slot>
    </div>
  </div>
</BaseGrid>

<style>
  .two-column-grid {
    display: grid;
    grid-template-columns: var(--left-width) var(--right-width);
    gap: var(--gap);
    height: 100%;
    width: 100%;
  }
  
  @media (max-width: 768px) {
    .two-column-grid {
      grid-template-columns: 1fr;
      grid-template-rows: auto 1fr;
    }
  }
  
  .left-column, .right-column {
    min-width: 0; /* Prevents overflow issues */
    height: 100%;
  }
</style>
