import { json } from '@sveltejs/kit';
import { ObjectId } from 'mongodb';
import { getCollection } from '$lib/db/mongo.js';

// Log API activities
function logServerActivity(activity, details) {
  console.log(`[API LOG] ${activity}`, details || '');
}

// Calculate hours per month based on previous records
async function calculateHoursPerMonth(computerId, hours, reportDate) {
  let calculated = 0; // Always start at 0
  try {
    const parsedHours = parseFloat(hours) || 0;
    if (!computerId || !reportDate) return calculated;
    const collection = await getCollection('ActualComputerHours');
    // Find previous record for this computer, before the current reportDate
    const prev = await collection.find({
      computerId: typeof computerId === 'string' ? new ObjectId(computerId) : computerId,
      ReportDate: { $lt: reportDate }
    }).sort({ ReportDate: -1 }).limit(1).toArray();
    if (prev.length === 0) return calculated;
    const prevRec = prev[0];
    // Calculate time delta in months
    const prevDate = new Date(prevRec.ReportDate);
    const currDate = new Date(reportDate);
    const msPerMonth = 1000 * 60 * 60 * 24 * 30.4375; // avg month
    const months = (currDate - prevDate) / msPerMonth;
    if (months <= 0) return calculated;
    const hoursDelta = parsedHours - (parseFloat(prevRec.hours) || 0);
    calculated = hoursDelta / months;
    if (calculated < 0) {
      throw new Error('CalculatedHoursPerMonth is negative. Please check the input data.');
    }
    return calculated;
  } catch (error) {
    if (error && error.message && error.message.includes('CalculatedHoursPerMonth is negative')) {
      // Custom error for negative value, propagate to API response
      throw error;
    }
    console.error('Error calculating hours per month:', error);
    return calculated;
  }
}

export async function POST({ request }) {
  try {
    const data = await request.json();
    logServerActivity('POST /api/actual-computer-hours request', data);
    // Validate essential fields
    if (!data.computerId || !data.activity) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }), 
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }
    let calculatedHours;
    try {
      calculatedHours = await calculateHoursPerMonth(
        data.computerId,
        Number(data.hours || 0),
        data.ReportDate || new Date().toISOString().split('T')[0]
      );
    } catch (err) {
      if (err && err.message && err.message.includes('CalculatedHoursPerMonth is negative')) {
        return new Response(
          JSON.stringify({
            error: 'CalculatedHoursPerMonth is negative. Please check the entered hours and report date. This value cannot be negative.'
          }),
          { status: 400, headers: { 'Content-Type': 'application/json' } }
        );
      }
      throw err;
    }
    // Prepare document for MongoDB
    const doc = {
      computerId: data.computerId && new ObjectId(data.computerId),
      activity: data.activity,
      hasFixed: !!data.hasFixed,
      hours: Number(data.hours || 0),
      isIdle: !!data.isIdle,
      ReportDate: data.ReportDate || new Date().toISOString().split('T')[0],
      ReportTime: data.ReportTime || new Date().toTimeString().substr(0, 5),
      CalculatedHoursPerMonth: calculatedHours,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    // Insert into database
    const collection = await getCollection('ActualComputerHours');
    const result = await collection.insertOne(doc);
    if (!result.acknowledged) {
      throw new Error('Database operation failed');
    }
    logServerActivity('Document created', { id: result.insertedId, calculatedHours });
    return json({
      success: true,
      id: result.insertedId.toString()
    });
  } catch (err) {
    logServerActivity('POST error', err);
    let errorMsg = err && err.message ? err.message : 'An error occurred while creating the document';
    return new Response(
      JSON.stringify({ 
        error: errorMsg
      }), 
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}

// Handle PUT requests to update existing documents
export async function PUT({ request }) {
  try {
    const data = await request.json();
    logServerActivity('PUT /api/actual-computer-hours request', data);
    // Validate essential fields
    if (!data._id) {
      return new Response(
        JSON.stringify({ error: 'Missing document ID' }), 
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }
    // Calculate new CalculatedHoursPerMonth
    let calculatedHours;
    try {
      calculatedHours = await calculateHoursPerMonth(
        data.computerId,
        Number(data.hours || 0),
        data.ReportDate || new Date().toISOString().split('T')[0]
      );
    } catch (err) {
      if (err && err.message && err.message.includes('CalculatedHoursPerMonth is negative')) {
        return new Response(
          JSON.stringify({
            error: 'CalculatedHoursPerMonth is negative. Please check the entered hours and report date. This value cannot be negative.'
          }),
          { status: 400, headers: { 'Content-Type': 'application/json' } }
        );
      }
      throw err;
    }
    // Update document in MongoDB
    const collection = await getCollection('ActualComputerHours');
    const updateDoc = {
      ...data,
      computerId: data.computerId && new ObjectId(data.computerId),
      hours: Number(data.hours || 0),
      ReportDate: data.ReportDate || new Date().toISOString().split('T')[0],
      ReportTime: data.ReportTime || new Date().toTimeString().substr(0, 5),
      CalculatedHoursPerMonth: calculatedHours,
      updatedAt: new Date()
    };
    // Remove _id from update document (can't update _id)
    delete updateDoc._id;
    const result = await collection.updateOne(
      { _id: new ObjectId(data._id) },
      { $set: updateDoc }
    );
    if (result.matchedCount === 0) {
      return new Response(
        JSON.stringify({ error: 'Document not found' }), 
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }
    // Update all subsequent documents to recalculate their CalculatedHoursPerMonth
    await recalculateSubsequentRecords(data.computerId);
    return json({
      success: true,
      id: data._id
    });
  } catch (err) {
    logServerActivity('PUT error', err);
    return new Response(
      JSON.stringify({ 
        error: err.message || 'An error occurred while updating the document' 
      }), 
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}

// Recalculate CalculatedHoursPerMonth for all records after the updated one
async function recalculateSubsequentRecords(computerId) {
  try {
    const collection = await getCollection('ActualComputerHours');
    // Get all records for this computer in chronological order
    const allRecords = await collection.find({
      computerId: new ObjectId(computerId)
    }).sort({ ReportDate: 1, ReportTime: 1 }).toArray();
    let runningTotal = 0;
    // Process each record in order
    for (const record of allRecords) {
      // Add current record's hours to running total
      runningTotal += record.hours || 0;
      // Update the CalculatedHoursPerMonth field
      await collection.updateOne(
        { _id: record._id },
        { $set: { CalculatedHoursPerMonth: runningTotal } }
      );
    }
    return true;
  } catch (error) {
    console.error('Error recalculating subsequent records:', error);
    return false;
  }
}

// GET handler to fetch all ActualComputerHours records
export async function GET({ url }) {
  try {
    const collection = await getCollection('ActualComputerHours');
    // Check if we need to filter by computerId
    const computerId = url.searchParams.get('computerId');
    let query = {};
    if (computerId) {
      try {
        query.computerId = new ObjectId(computerId);
      } catch (error) {
        console.error('Invalid ObjectId for computerId:', error);
      }
    }
    const hours = await collection.find(query).sort({ ReportDate: -1, ReportTime: -1 }).toArray();
    // Get current date and time for consistent formatting
    const currentDate = new Date('2025-04-22T12:17:41+02:00');
    const formattedDate = currentDate.toISOString().split('T')[0]; // YYYY-MM-DD
    const formattedTime = `${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;
    // Ensure each record has ReportDate and ReportTime fields
    const updatedHours = hours.map(record => {
      // Make a copy we can modify
      const newRecord = { ...record };
      // Convert _id to string for client-side use
      if (newRecord._id) {
        newRecord._id = newRecord._id.toString();
      }
      // Remove ReportDateTime field if it exists
      if (newRecord.ReportDateTime !== undefined) {
        delete newRecord.ReportDateTime;
      }
      // Always set ReportDate and ReportTime to current values if not present
      if (!newRecord.ReportDate) {
        newRecord.ReportDate = formattedDate;
      }
      if (!newRecord.ReportTime) {
        newRecord.ReportTime = formattedTime;
      }
      return newRecord;
    });
    // Update the database with ReportDate and ReportTime for all records
    // This ensures these fields will be present for future requests
    for (const record of hours) {
      if (record._id && (!record.ReportDate || !record.ReportTime)) {
        try {
          await collection.updateOne(
            { _id: record._id },
            { 
              $set: { 
                ReportDate: record.ReportDate || formattedDate,
                ReportTime: record.ReportTime || formattedTime
              },
              $unset: {
                ReportDateTime: ""  // Remove this field
              }
            },
            { upsert: false }
          );
        } catch (err) {
          console.error('Error updating record:', err);
        }
      }
    }
    return json(updatedHours);
  } catch (err) {
    console.error('Error fetching ActualComputerHours:', err);
    return new Response(
      JSON.stringify({ 
        error: err.message || 'An error occurred while fetching records' 
      }), 
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
