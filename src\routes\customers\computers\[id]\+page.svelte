<script lang="ts">
    interface Customer {
        _id: string;
        companyName?: string;
        name?: string;
        type: string;
        city?: string;
        country?: string;
    }

    interface Computer {
        _id: string;
        customerId: string;
        name: string;
        type?: string;
        model?: string;
        manufacturer?: string;
        purchaseDate: Date | null;
        warrantyEndDate: Date | null;
        createdAt: Date;
        serialNumber?: string;
        notes?: string;
        status?: string;
    }

    interface PageData {
        computer: Computer;
        customer: Customer;
    }

    export let data: PageData;
    const { computer, customer } = data;

    function formatDate(date: Date | null): string {
        if (!date) return 'Not set';
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    function getWarrantyStatus(warrantyEndDate: Date | null) {
        if (!warrantyEndDate) return { status: 'Unknown', class: 'unknown' };
        
        const today = new Date();
        const warrantyDate = new Date(warrantyEndDate);
        const timeDiff = warrantyDate.getTime() - today.getTime();
        const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
        
        if (daysDiff < 0) {
            return { status: 'Expired', class: 'expired' };
        } else if (daysDiff < 30) {
            return { status: 'Expiring soon', class: 'expiring' };
        } else {
            return { status: 'Active', class: 'active' };
        }
    }
</script>

<svelte:head>
    <title>Computer Details - {computer.name}</title>
</svelte:head>

<div class="container">
    <header>
        <nav class="breadcrumb">
            <a href="/customers">Customers</a>
            <span>/</span>
            <a href="/customers/{computer.customerId}">
                {customer.companyName || customer.name}
            </a>
            <span>/</span>
            <span class="current">{computer.name}</span>
        </nav>
    </header>

    <div class="content">
        <div class="computer-header">
            <div class="title-section">
                <h1>{computer.name}</h1>
                {#if computer.model || computer.manufacturer}
                    <div class="subtitle">
                        {#if computer.model}
                            <span class="model">{computer.model}</span>
                        {/if}
                        {#if computer.manufacturer}
                            <span class="manufacturer">by {computer.manufacturer}</span>
                        {/if}
                    </div>
                {/if}
            </div>
            <div class="status-section">
                {#if computer.warrantyEndDate}
                    {@const warranty = getWarrantyStatus(computer.warrantyEndDate)}
                    <div class="warranty-badge {warranty.class}">
                        {warranty.status}
                    </div>
                {:else}
                    <div class="warranty-badge unknown">
                        Unknown
                    </div>
                {/if}
            </div>
        </div>

        <div class="info-grid">
            <div class="info-card">
                <h2>Customer Information</h2>
                <div class="info-content">
                    <div class="info-row">
                        <span class="label">Company</span>
                        <span class="value">{customer.companyName || customer.name}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Type</span>
                        <span class="value customer-type {customer.type.toLowerCase().replace(' ', '-')}">
                            {customer.type}
                        </span>
                    </div>
                    {#if customer.city || customer.country}
                        <div class="info-row">
                            <span class="label">Location</span>
                            <span class="value">
                                {[customer.city, customer.country].filter(Boolean).join(', ')}
                            </span>
                        </div>
                    {/if}
                </div>
            </div>

            <div class="info-card">
                <h2>Computer Details</h2>
                <div class="info-content">
                    <div class="info-row">
                        <span class="label">Type</span>
                        <span class="value">{computer.type || 'Not specified'}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Model</span>
                        <span class="value">{computer.model || 'Not specified'}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Manufacturer</span>
                        <span class="value">{computer.manufacturer || 'Not specified'}</span>
                    </div>
                    {#if computer.serialNumber}
                        <div class="info-row">
                            <span class="label">Serial Number</span>
                            <span class="value">{computer.serialNumber}</span>
                        </div>
                    {/if}
                </div>
            </div>

            <div class="info-card">
                <h2>Warranty Information</h2>
                <div class="info-content">
                    <div class="info-row">
                        <span class="label">Purchase Date</span>
                        <span class="value">{formatDate(computer.purchaseDate)}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Warranty End</span>
                        <span class="value">{formatDate(computer.warrantyEndDate)}</span>
                    </div>
                    {#if computer.status}
                        <div class="info-row">
                            <span class="label">Status</span>
                            <span class="value">{computer.status}</span>
                        </div>
                    {/if}
                </div>
            </div>

            {#if computer.notes}
                <div class="info-card full-width">
                    <h2>Notes</h2>
                    <div class="info-content">
                        <p class="notes">{computer.notes}</p>
                    </div>
                </div>
            {/if}
        </div>
    </div>
</div>

<style>
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }

    header {
        margin-bottom: 2rem;
    }

    .breadcrumb {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: #6b7280;
    }

    .breadcrumb a {
        color: #3b82f6;
        text-decoration: none;
    }

    .breadcrumb a:hover {
        text-decoration: underline;
    }

    .breadcrumb .current {
        color: #111827;
        font-weight: 500;
    }

    .computer-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 2rem;
    }

    .title-section h1 {
        font-size: 2rem;
        font-weight: 700;
        color: #111827;
        margin: 0;
    }

    .subtitle {
        margin-top: 0.5rem;
        color: #6b7280;
    }

    .model {
        font-weight: 500;
    }

    .manufacturer {
        margin-left: 0.5rem;
    }

    .warranty-badge {
        display: inline-block;
        padding: 0.5rem 1rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .warranty-badge.active {
        background-color: #d1e7dd;
        color: #0f5132;
    }

    .warranty-badge.expiring {
        background-color: #fff3cd;
        color: #664d03;
    }

    .warranty-badge.expired {
        background-color: #f8d7da;
        color: #842029;
    }

    .warranty-badge.unknown {
        background-color: #e9ecef;
        color: #495057;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .info-card {
        background: white;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
    }

    .info-card.full-width {
        grid-column: 1 / -1;
    }

    .info-card h2 {
        font-size: 1.25rem;
        font-weight: 600;
        color: #111827;
        margin: 0 0 1rem 0;
    }

    .info-content {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .label {
        color: #6b7280;
        font-size: 0.875rem;
    }

    .value {
        font-weight: 500;
        color: #111827;
    }

    .customer-type {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .customer-type.retail {
        background-color: #e9ecef;
        color: #495057;
    }

    .customer-type.oem-importer {
        background-color: #cff4fc;
        color: #055160;
    }

    .customer-type.fleet-owner {
        background-color: #d1e7dd;
        color: #0f5132;
    }

    .notes {
        white-space: pre-wrap;
        color: #374151;
        line-height: 1.5;
        margin: 0;
    }
</style>
