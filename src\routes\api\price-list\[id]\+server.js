import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

// MongoDB connection URI
const uri = 'mongodb://localhost:27017';

// GET: Fetch a specific price list item by ID
export async function GET({ params }) {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    
    // Using PascalCase for collection name per project convention
    const priceList = db.collection('PriceList');
    
    // Validate ObjectId format
    if (!ObjectId.isValid(params.id)) {
      return new Response(JSON.stringify({ error: 'Invalid item ID format' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Convert string ID to ObjectId
    const itemId = new ObjectId(params.id);
    
    // Find the price list item
    const item = await priceList.findOne({ _id: itemId });
    
    if (!item) {
      return new Response(JSON.stringify({ error: 'Price list item not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    return json(item);
    
  } catch (error) {
    console.error('Error fetching price list item:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  } finally {
    await client.close();
  }
}

// DELETE: Remove a price list item by ID
export async function DELETE({ params }) {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    
    // Using PascalCase for collection name per project convention
    const priceList = db.collection('PriceList');
    
    // Validate ObjectId format
    if (!ObjectId.isValid(params.id)) {
      return new Response(JSON.stringify({ error: 'Invalid item ID format' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Convert string ID to ObjectId
    const itemId = new ObjectId(params.id);
    
    // Delete the price list item
    const result = await priceList.deleteOne({ _id: itemId });
    
    if (result.deletedCount === 0) {
      return new Response(JSON.stringify({ error: 'Price list item not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    return json({ 
      success: true,
      message: 'Price list item deleted successfully' 
    });
    
  } catch (error) {
    console.error('Error deleting price list item:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  } finally {
    await client.close();
  }
}
