<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import type { Activity } from '$lib/types/activity';
  
  // Props
  export let activity: Activity | null = null;
  export let isSaving: boolean = false;
  export let monthNames: string[] = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  
  // Local state
  let hoursValue: number | string = '';
  let activityName: string = '';
  let hasFixedValue: boolean = false;
  
  // Event dispatcher
  const dispatch = createEventDispatcher<{
    save: { hours: number | string; activity: string; hasFixed: boolean };
    cancel: void;
  }>();
  
  // Update local state when activity changes
  $: if (activity) {
    hoursValue = activity.value || 0;
    activityName = activity.activity || '';
    hasFixedValue = activity.hasFixed || false;
  }
  
  // Get month name
  $: monthName = activity && activity.month ? monthNames[activity.month - 1] : '';
  
  // Handle save
  function handleSave() {
    dispatch('save', {
      hours: hoursValue,
      activity: activityName,
      hasFixed: hasFixedValue
    });
  }
  
  // Handle cancel
  function handleCancel() {
    dispatch('cancel');
  }
</script>

<div class="details-panel">
  {#if activity}
    <h3>Edit Activity for {monthName} {activity.year}</h3>
    
    <div class="form-grid">
      <div class="form-group">
        <label for="hours">Hours:</label>
        <input 
          type="number" 
          id="hours" 
          bind:value={hoursValue} 
          min="0"
          step="0.1"
          class="form-control"
        />
        <small>Number of hours for this activity</small>
      </div>
      
      <div class="form-group">
        <label for="activity">Activity:</label>
        <input 
          type="text" 
          id="activity" 
          bind:value={activityName}
          placeholder="Enter activity description"
          class="form-control" 
        />
        <small>Brief description of this activity</small>
      </div>
      
      <div class="form-group checkbox-group">
        <label class="checkbox-label">
          <input 
            type="checkbox" 
            bind:checked={hasFixedValue} 
          />
          <span>Fixed Schedule</span>
        </label>
        <small>Check if this is a fixed schedule activity</small>
      </div>
      
      <div class="action-buttons">
        <button 
          type="button" 
          class="btn-secondary" 
          on:click={handleCancel}
          disabled={isSaving}
        >
          Cancel
        </button>
        <button 
          type="button" 
          class="btn-primary" 
          on:click={handleSave}
          disabled={isSaving}
        >
          {isSaving ? 'Saving...' : 'Save Changes'}
        </button>
      </div>
    </div>
  {:else}
    <div class="empty-state">
      <p>Select an activity to edit details</p>
    </div>
  {/if}
</div>

<style>
  .details-panel {
    background: #1e293b;
    border-radius: 8px;
    padding: 1.5rem;
    height: 100%;
  }
  
  h3 {
    color: #60a5fa;
    margin-top: 0;
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
  }
  
  .form-grid {
    display: grid;
    gap: 1.2rem;
  }
  
  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.4rem;
  }
  
  label {
    color: #e2e8f0;
    font-weight: 500;
  }
  
  .form-control {
    background-color: #0f172a;
    border: 1px solid #334155;
    border-radius: 6px;
    color: #e2e8f0;
    padding: 0.6rem 0.8rem;
    font-size: 1rem;
    transition: border-color 0.2s;
  }
  
  .form-control:focus {
    border-color: #60a5fa;
    outline: none;
  }
  
  small {
    color: #94a3b8;
    font-size: 0.75rem;
  }
  
  .checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
  }
  
  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
  }
  
  .checkbox-label input[type="checkbox"] {
    width: 1.2rem;
    height: 1.2rem;
    accent-color: #3b82f6;
  }
  
  .checkbox-label span {
    color: #e2e8f0;
  }
  
  .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 0.8rem;
    margin-top: 1rem;
  }
  
  .btn-primary, .btn-secondary {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    font-size: 0.95rem;
    cursor: pointer;
    transition: background-color 0.2s;
    border: none;
  }
  
  .btn-primary {
    background-color: #3b82f6;
    color: #ffffff;
  }
  
  .btn-primary:hover:not(:disabled) {
    background-color: #2563eb;
  }
  
  .btn-secondary {
    background-color: transparent;
    border: 1px solid #475569;
    color: #e2e8f0;
  }
  
  .btn-secondary:hover:not(:disabled) {
    background-color: rgba(71, 85, 105, 0.2);
  }
  
  button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #94a3b8;
  }
</style>
