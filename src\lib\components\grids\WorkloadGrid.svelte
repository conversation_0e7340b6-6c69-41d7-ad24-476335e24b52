<!-- WorkloadGrid.svelte -->
<script lang="ts">
    import BaseGrid from '$lib/components/grids/BaseGrid.svelte';
    import WorkloadEditor from '$lib/components/WorkloadEditor.svelte';
    import { createEventDispatcher, onMount } from 'svelte';
    
    const dispatch = createEventDispatcher();
    
    interface WorkloadItem {
        _id: string | {$oid: string};
        computerId: string | {$oid: string};
        year: number;
        month: number;
        hours: number;
        activity: string;
        type?: string;
        hasFixed?: boolean;
        isIdle?: boolean;
        updatedAt?: string | {$date: string};
        createdAt?: string | {$date: string};
    }

    export let workload: WorkloadItem[] = [];
    export let onEdit: (item: WorkloadItem) => void = () => {};
    
    let selectedCell: { year: number; month: number; hours: number; activity: string } | null = null;
    let editingWorkloadItem: WorkloadItem | null = null;
    let showEditor = false;

    // Debug logs
    onMount(() => {
        console.log('WorkloadGrid mounted with', workload.length, 'items');
        if (workload.length > 0) {
            console.log('Sample item:', workload[0]);
        }
    });

    // Define month names
    const months = [
        "January", "February", "March", "April", "May", "June", 
        "July", "August", "September", "October", "November", "December"
    ];
    
    const shortMonths = [
        "Jan", "Feb", "Mar", "Apr", "May", "Jun", 
        "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
    ];

    // Process workload data to handle MongoDB ObjectId and Date formatting
    $: processedWorkload = workload.map(item => {
        const newItem = {...item};
        
        // Convert MongoDB ObjectId to string if needed
        if (newItem._id && typeof newItem._id === 'object' && '_id' in newItem && '$oid' in newItem._id) {
            newItem._id = newItem._id.$oid;
        }
        if (newItem.computerId && typeof newItem.computerId === 'object' && 'computerId' in newItem && '$oid' in newItem.computerId) {
            newItem.computerId = newItem.computerId.$oid;
        }
        
        // Convert MongoDB Date to string if needed
        if (newItem.updatedAt && typeof newItem.updatedAt === 'object' && 'updatedAt' in newItem && '$date' in newItem.updatedAt) {
            newItem.updatedAt = new Date(newItem.updatedAt.$date).toISOString();
        }
        if (newItem.createdAt && typeof newItem.createdAt === 'object' && 'createdAt' in newItem && '$date' in newItem.createdAt) {
            newItem.createdAt = new Date(newItem.createdAt.$date).toISOString();
        }
        
        return newItem;
    });
    
    // Extract unique years from workload
    $: years = [...new Set(processedWorkload.map(item => item.year))].sort();
    
    // Create a workload map for easy lookup
    $: workloadMap = processedWorkload.reduce((acc, item) => {
        const key = `${item.year}-${item.month}`;
        acc[key] = item;
        return acc;
    }, {} as Record<string, WorkloadItem>);
    
    $: console.log('Years detected:', years);
    $: console.log('Workload map keys:', Object.keys(workloadMap));
    
    // Get cell data for a specific year and month
    function getCellData(year: number, month: number) {
        const key = `${year}-${month}`;
        return workloadMap[key] || null;
    }
    
    function selectCell(year: number, month: number) {
        const item = getCellData(year, month);
        
        if (item) {
            // Open editor for existing item
            editingWorkloadItem = { ...item };
        } else {
            // Create a new item template
            editingWorkloadItem = {
                _id: `temp-${Date.now()}`,
                computerId: workload?.[0]?.computerId || '',
                year,
                month,
                hours: 0,
                activity: '',
                type: 'Workload',
                hasFixed: false
            };
        }
        
        // Show the editor
        showEditor = true;
    }

    function handleEditSave(event: CustomEvent<WorkloadItem>) {
        const updatedItem = event.detail;
        console.log('Workload item updated:', updatedItem);
        
        // Update local data right away for immediate UI update
        const key = `${updatedItem.year}-${updatedItem.month}`;
        workloadMap[key] = updatedItem;
        
        // Save to database
        onEdit(updatedItem);
        
        // Close editor
        showEditor = false;
    }
    
    function handleEditCancel() {
        // Just close the editor without saving
        showEditor = false;
    }

    function handleKeydown(event: KeyboardEvent) {
        if (event.key === 'Escape' && showEditor) {
            showEditor = false;
        }
    }
    
    // If we have no years, generate default years
    $: if (years.length === 0) {
        const currentYear = new Date().getFullYear();
        years = [currentYear - 1, currentYear, currentYear + 1];
    }
</script>

<svelte:window on:keydown={handleKeydown}/>

<!-- Workload editor modal -->
{#if editingWorkloadItem}
<WorkloadEditor 
    isOpen={showEditor}
    item={editingWorkloadItem}
    on:save={handleEditSave}
    on:cancel={handleEditCancel}
/>
{/if}

<div class="workload-grid-container">
    <div class="workload-grid">
        <!-- Header row with months -->
        <div class="grid-header year-label">Year</div>
        {#each shortMonths as month, i}
            <div class="grid-header month-label">{month}</div>
        {/each}
        
        <!-- Data rows -->
        {#each years as year}
            <div class="grid-year-cell">{year}</div>
            {#each Array(12).fill(0).map((_, i) => i + 1) as month}
                {@const cellData = getCellData(year, month)}
                <div 
                    class="grid-cell"
                    class:has-data={cellData !== null}
                    class:cell-success={cellData && cellData.hasFixed}
                    class:cell-warning={cellData && !cellData.hasFixed && cellData.hours > 0}
                    on:click={() => selectCell(year, month)}
                    role="button"
                    tabindex="0"
                    on:keypress={(e) => e.key === 'Enter' && selectCell(year, month)}
                >
                    {#if cellData && cellData.hours > 0}
                        <div class="cell-content">
                            <div class="hours">{cellData.hours}<span class="asterisk">{cellData.hasFixed ? '*' : ''}</span></div>
                            <div class="activity">{cellData.activity || ''}</div>
                        </div>
                    {/if}
                </div>
            {/each}
        {/each}
        
        {#if years.length === 0}
            <div class="no-data" style="grid-column: 1 / span 13;">No workload data found. Click any cell to add data.</div>
        {/if}
    </div>
</div>

<style>
    .workload-grid-container {
        width: 100%;
        overflow-x: auto;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-bottom: 1rem;
    }
    
    .workload-grid {
        display: grid;
        grid-template-columns: 80px repeat(12, 1fr);
        width: 100%;
        font-size: 0.9rem;
        color: #102a54;
    }
    
    .grid-header {
        padding: 0.75rem;
        text-align: center;
        font-weight: 600;
        background-color: #f8fafc;
        border-bottom: 2px solid #e5e7eb;
        position: sticky;
        top: 0;
        z-index: 10;
    }
    
    .year-label {
        position: sticky;
        left: 0;
        z-index: 20;
        border-right: 2px solid #e5e7eb;
    }
    
    .grid-year-cell {
        width: 80px;
        padding: 0.75rem;
        text-align: center;
        font-weight: 600;
        background-color: #f8fafc;
        color: #102a54;
        position: sticky;
        left: 0;
        z-index: 5;
        border-right: 2px solid #e5e7eb;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .grid-cell {
        min-height: 4rem;
        border: 1px solid #e5e7eb;
        padding: 0;
        position: relative;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .grid-cell:hover {
        background-color: rgba(16, 42, 84, 0.05);
    }
    
    .has-data {
        background-color: #f0f9ff;
    }
    
    .cell-success {
        background-color: rgba(52, 199, 89, 0.2);
    }
    
    .cell-warning {
        background-color: rgba(255, 153, 0, 0.2);
    }
    
    .no-data {
        padding: 2rem;
        text-align: center;
        color: #64748b;
        background-color: #f9fafb;
        grid-column: span 13;
    }
    
    .cell-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        padding: 0.25rem;
    }
    
    .hours {
        font-weight: bold;
        font-size: 1.5rem;
        line-height: 1.2;
    }

    .asterisk {
        color: #ef4444;
        font-weight: bold;
        font-size: 1.5rem;
    }
    
    .activity {
        font-size: 0.85rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
        color: #4b5563;
        font-weight: 500;
    }
</style>
