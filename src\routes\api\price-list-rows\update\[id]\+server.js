import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

// MongoDB connection URI
const uri = 'mongodb://localhost:27017';

// PUT: Update a price list row by ID
export async function PUT({ params, request }) {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    
    // Using PascalCase for collection name per project convention
    const priceList = db.collection('PriceList');
    
    // Get the update data from the request body
    const updateData = await request.json();
    
    // Validate required fields
    if (!updateData['Service Category'] || !updateData['Service ID'] || !updateData['Offering']) {
      return new Response(JSON.stringify({ 
        error: 'Service Category, Service ID, and Offering are required fields' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Validate ObjectId format
    if (!ObjectId.isValid(params.id)) {
      return new Response(JSON.stringify({ error: 'Invalid item ID format' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Convert string ID to ObjectId
    const itemId = new ObjectId(params.id);
    
    // Update the price list item
    const result = await priceList.updateOne(
      { _id: itemId },
      { $set: updateData }
    );
    
    if (result.matchedCount === 0) {
      return new Response(JSON.stringify({ error: 'Price list item not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    return json({ 
      success: true, 
      message: 'Price list item updated successfully',
      id: params.id
    });
    
  } catch (error) {
    console.error('Error updating price list item:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  } finally {
    await client.close();
  }
}
