// Script to find similar ProductDesignation values
// This script searches for ProductDesignation values that might be similar to "D13B-N MH R1" or "D13B-N MH R2"

import { MongoClient } from 'mongodb';

// Connection URL and Database name
const url = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';
const collectionName = 'ProductValidityGroup';

async function findSimilarDesignations() {
  let client;

  try {
    // Connect to MongoDB
    client = new MongoClient(url);
    await client.connect();
    console.log('Connected to MongoDB server');

    // Get database and collection
    const db = client.db(dbName);
    const collection = db.collection(collectionName);

    // Search for documents with ProductDesignation containing "D13B"
    const d13bDocs = await collection.find({
      ProductDesignation: { $regex: /D13B/i }
    }).toArray();
    
    console.log(`Found ${d13bDocs.length} documents with ProductDesignation containing "D13B"`);
    
    if (d13bDocs.length > 0) {
      console.log('Documents with ProductDesignation containing "D13B":');
      d13bDocs.forEach(doc => {
        console.log(`ID: ${doc._id}, ProductDesignation: ${doc.ProductDesignation}, ProductDesignationShort: ${doc.ProductDesignationShort}`);
      });
    }

    // Search for documents with ProductDesignation containing "MH"
    const mhDocs = await collection.find({
      ProductDesignation: { $regex: /MH/i }
    }).toArray();
    
    console.log(`\nFound ${mhDocs.length} documents with ProductDesignation containing "MH"`);
    
    if (mhDocs.length > 0) {
      console.log('Documents with ProductDesignation containing "MH":');
      mhDocs.forEach(doc => {
        console.log(`ID: ${doc._id}, ProductDesignation: ${doc.ProductDesignation}, ProductDesignationShort: ${doc.ProductDesignationShort}`);
      });
    }

    // Get a sample of all documents to see what's actually in the collection
    console.log("\nSample of documents in the collection:");
    const sampleDocs = await collection.find({}).limit(10).toArray();
    sampleDocs.forEach(doc => {
      console.log(`ID: ${doc._id}, ProductDesignation: ${doc.ProductDesignation}, ProductDesignationShort: ${doc.ProductDesignationShort}`);
    });

  } catch (err) {
    console.error('Error finding documents:', err);
  } finally {
    // Close the connection
    if (client) {
      await client.close();
      console.log('MongoDB connection closed');
    }
  }
}

// Run the function
findSimilarDesignations().catch(console.error);
