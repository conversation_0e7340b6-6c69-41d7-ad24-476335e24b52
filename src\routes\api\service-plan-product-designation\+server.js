import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

/** @type {import('./$types').RequestHandler} */
export async function DELETE({ request }) {
  const client = new MongoClient(uri);
  
  try {
    const { itemIds } = await request.json();
    
    if (!itemIds || !Array.isArray(itemIds) || itemIds.length === 0) {
      return json({ error: 'No item IDs provided' }, { status: 400 });
    }
    
    await client.connect();
    const db = client.db(dbName);
    
    // Convert string IDs to ObjectIds
    const objectIds = itemIds.map(id => new ObjectId(id));
    
    // Delete the items
    const result = await db.collection('ServicePlanProductDesignation').deleteMany({
      _id: { $in: objectIds }
    });
    
    return json({
      success: true,
      deletedCount: result.deletedCount,
      message: `Successfully deleted ${result.deletedCount} items`
    });
    
  } catch (err) {
    console.error('Error deleting service plan items:', err);
    return json({ error: 'Failed to delete items' }, { status: 500 });
  } finally {
    await client.close();
  }
}
