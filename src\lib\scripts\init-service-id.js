import { MongoClient } from 'mongodb';

// MongoDB connection URI
const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function initServiceIDCollection() {
  const client = new MongoClient(uri);
  
  try {
    // Connect to MongoDB
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(dbName);
    
    // Check if ServiceID collection exists
    const collections = await db.listCollections({ name: 'ServiceID' }).toArray();
    
    if (collections.length > 0) {
      console.log('ServiceID collection already exists');
      
      // Count documents in the collection
      const count = await db.collection('ServiceID').countDocuments();
      console.log(`ServiceID collection has ${count} documents`);
      
      if (count === 0) {
        console.log('ServiceID collection is empty, adding sample data...');
        await addSampleData(db);
      }
    } else {
      console.log('ServiceID collection does not exist, creating it with sample data...');
      await db.createCollection('ServiceID');
      await addSampleData(db);
    }
    
    console.log('ServiceID collection initialization complete');
  } catch (error) {
    console.error('Error initializing ServiceID collection:', error);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

async function addSampleData(db) {
  const sampleServices = [
    {
      serviceCode: 'S-D13',
      serviceName: 'Standard Maintenance D13',
      description: 'Standard maintenance service for D13 product line',
      serviceType: 'Maintenance',
      ProductValidityGroup: 'D13',
      internalHours: 4.5,
      internalMonths: 6,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      serviceCode: 'R-D13',
      serviceName: 'Repair Service D13',
      description: 'Repair service for D13 product line',
      serviceType: 'Repair',
      ProductValidityGroup: 'D13',
      internalHours: 8,
      internalMonths: 1,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      serviceCode: 'I-D5',
      serviceName: 'Installation D5',
      description: 'Installation service for D5 product line',
      serviceType: 'Installation',
      ProductValidityGroup: 'D5',
      internalHours: 12,
      internalMonths: 1,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      serviceCode: 'C-G10',
      serviceName: 'Consultation G10',
      description: 'Consultation service for G10 product line',
      serviceType: 'Consultation',
      ProductValidityGroup: 'G10',
      internalHours: 2,
      internalMonths: 1,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      serviceCode: 'S-D5',
      serviceName: 'Standard Maintenance D5',
      description: 'Standard maintenance service for D5 product line',
      serviceType: 'Maintenance',
      ProductValidityGroup: 'D5',
      internalHours: 3.5,
      internalMonths: 6,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];
  
  const result = await db.collection('ServiceID').insertMany(sampleServices);
  console.log(`${result.insertedCount} sample services added to ServiceID collection`);
}

// Run the initialization function
initServiceIDCollection().catch(console.error);
