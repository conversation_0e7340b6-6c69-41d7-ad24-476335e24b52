import { error } from '@sveltejs/kit';
import { ObjectId } from 'mongodb';
import { getCollection } from '$lib/db/mongo';
import serviceContractDB, { COLLECTIONS } from '$lib/server/serviceContractDB.js';

/**
 * @typedef {Object} ServiceOfferItem
 * @property {string} _id
 * @property {string} serviceCode
 * @property {string} description
 * @property {string} activity
 * @property {number} quantity
 * @property {number} unitPrice
 * @property {number} totalPrice
 * @property {boolean} isEnabled
 * @property {boolean} isRequired
 * @property {string} category
 * @property {string} notes
 */

/**
 * @typedef {Object} ServiceOffer
 * @property {string} _id
 * @property {string} customerId
 * @property {string} computerId
 * @property {string} offerNumber
 * @property {string} status
 * @property {Date} validUntil
 * @property {ServiceOfferItem[]} items
 * @property {number} totalAmount
 * @property {string} notes
 * @property {Date} createdAt
 * @property {Date} updatedAt
 */

/** @type {import('./$types').PageServerLoad} */
export async function load({ url }) {
  try {
    const computerId = url.searchParams.get('computerId');
    const productDesignation = url.searchParams.get('productDesignation');

    if (!computerId) {
      throw error(400, 'Computer ID is required');
    }

    // Get computer and customer information
    const computerCollection = await getCollection('CustomerComputers');
    const computer = await computerCollection.findOne({ _id: new ObjectId(computerId) });

    if (!computer) {
      throw error(404, 'Computer not found');
    }

    const customerCollection = await getCollection('Customers');
    const customer = await customerCollection.findOne({ _id: new ObjectId(computer.customerId) });

    if (!customer) {
      throw error(404, 'Customer not found');
    }

    // Get existing service offer for this computer if any
    const serviceOfferCollection = await getCollection('ServiceOffers');
    const existingOffer = await serviceOfferCollection.findOne({
      computerId: new ObjectId(computerId),
      status: { $in: ['Draft', 'Pending'] }
    });

    // Use the product designation from URL or computer
    const targetProductDesignation = productDesignation || computer.productDesignation;

    // Get Product Validity Group from computer or lookup
    let productValidityGroup = computer['Product Validity Group'] || computer.ProductValidityGroup;

    if (!productValidityGroup && targetProductDesignation) {
      try {
        const pvgCollection = await getCollection('ProductValidityGroupPartNumber');
        const pvgDoc = await pvgCollection.findOne({
          ProductDesignation: targetProductDesignation
        });
        if (pvgDoc) {
          productValidityGroup = pvgDoc.ProductValidityGroup || pvgDoc['Product Validity Group'];
        }
      } catch (err) {
        console.log('ProductValidityGroupPartNumber collection not found');
      }
    }

    // Default to the product designation if no PVG found
    if (!productValidityGroup) {
      productValidityGroup = targetProductDesignation;
    }

    // Get service items from ServiceCodeAndActionType collection based on Product Validity Group
    let serviceItems = [];
    try {
      const serviceCodeCollection = await getCollection('ServiceCodeAndActionType');
      serviceItems = await serviceCodeCollection.find({
        ProductValidityGroup: productValidityGroup
      }).toArray();
      console.log(`Found ${serviceItems.length} service items for PVG: ${productValidityGroup}`);
    } catch (err) {
      console.log('ServiceCodeAndActionType collection not found:', err.message);
    }

    // Get pricing data from ServiceID and PriceList collections
    let serviceIDPrices = new Map();
    let priceListPrices = new Map();

    // Get ServiceID collection for service pricing
    try {
      const serviceIDCollection = await getCollection('ServiceID');
      const serviceIDItems = await serviceIDCollection.find({}).toArray();
      console.log(`Found ${serviceIDItems.length} ServiceID items`);

      serviceIDItems.forEach(item => {
        const serviceCode = item.serviceCode || item.ServiceCode;
        const price = item.price || item.Price || 0;
        if (serviceCode) {
          serviceIDPrices.set(serviceCode, parseFloat(price) || 0);
        }
      });
    } catch (err) {
      console.log('ServiceID collection not found:', err.message);
    }

    // Get PriceList data for part pricing
    try {
      const priceListCollection = await getCollection('PriceLIst');
      const priceListItems = await priceListCollection.find({}).toArray();
      console.log(`Found ${priceListItems.length} price list items`);

      priceListItems.forEach(item => {
        const partNo = item['Part No'] || item.PartNo || item.partNo;
        const serviceId = item['Service ID'] || item.ServiceID || item.serviceId;
        const price = parseFloat(item['Price excl VAT']?.toString().replace(',', '.')) || 0;

        if (partNo) {
          priceListPrices.set(partNo.toString(), price);
        }
        if (serviceId) {
          priceListPrices.set(serviceId.toString(), price);
        }
      });
    } catch (err) {
      console.log('PriceLIst collection not found:', err.message);
    }

    // Helper function to get price for a service item
    const getPrice = (item) => {
      // First try ServiceID collection by ServiceCode
      const serviceCode = item.ServiceCode || item.serviceCode;
      if (serviceCode && serviceIDPrices.has(serviceCode)) {
        return serviceIDPrices.get(serviceCode);
      }

      // Then try PriceList collection by PartNumber
      const partNumber = item.PartNumber || item.partNumber || item['Part Number'];
      if (partNumber && priceListPrices.has(partNumber.toString())) {
        return priceListPrices.get(partNumber.toString());
      }

      // Try PriceList collection by ServiceCode as Service ID
      if (serviceCode && priceListPrices.has(serviceCode.toString())) {
        return priceListPrices.get(serviceCode.toString());
      }

      // Return 0 for missing values as requested
      return 0;
    };

    // Build service offering structure matching the image layout
    const packageOffering = [
      { level: 'A', package: 'N/A', status: 'N/A' },
      { level: 'B', package: 'N/A', status: 'N/A' },
      { level: 'C', package: 'N/A', status: 'N/A' }
    ];

    // Transform ServiceCodeAndActionType items into individual service format matching your image
    const allServiceItems = [];
    let serviceIndex = 1;

    // Create ServiceID mapping based on the pattern in your image (4.1, 5.2, 1.2, etc.)
    const serviceIdMap = new Map();
    let baseServiceId = 4; // Starting from 4.1 as shown in your image
    let subServiceId = 1;

    // Process each service activity label (S, A, B, C, D, E)
    const servicesByLabel = {
      'S': [], // Service level S
      'A': [], // Service level A
      'B': [], // Service level B
      'C': [], // Service level C
      'D': [], // Service level D
      'E': []  // Service level E
    };

    // Group service items by their Service Activity Label
    serviceItems.forEach(item => {
      const label = item.ServiceActivityLabel || item.serviceActivityLabel || '';
      if (servicesByLabel[label]) {
        servicesByLabel[label].push(item);
      }
    });

    // Transform each group into service items with proper ServiceID format
    Object.entries(servicesByLabel).forEach(([label, items]) => {
      items.forEach((item, index) => {
        // Generate ServiceID in the format shown in your image (4.1, 5.2, etc.)
        const serviceId = `${baseServiceId}.${subServiceId}`;

        // Determine package name based on service activity label
        let packageName = 'Base Contract Offering';
        if (label === 'A' || label === 'B' || label === 'C') {
          packageName = 'Dealer Add-Ons';
        } else if (label === 'D') {
          packageName = 'Support (Self Service)';
        } else if (label === 'E') {
          packageName = 'Retirement Plan';
        }

        const price = getPrice(item);
        const transformedItem = {
          _id: item._id ? item._id.toString() : `${item.ServiceCode}_${label}`,
          level: serviceIndex,
          package: packageName,
          packageName: packageName,
          serviceId: serviceId, // This is the key fix - proper ServiceID format
          serviceName: `${item.ActionType} - ${item.ActivityPurpose}` || item.ActionType || '',
          service: `${item.ActionType} - ${item.ActivityPurpose}` || item.ActionType || '',
          includedInPackageCost: price === 0,
          includedInPackage: price === 0,
          required: label === 'S', // S level services are typically required
          selectService: item.ActionType || '',
          includeInQuote: true,
          includeInOffer: true,
          cost: price,
          unitPrice: price,
          totalPrice: price,
          quantity: 1,
          isEnabled: true,
          isRequired: label === 'S',
          category: packageName,
          notes: '',
          source: 'ServiceCodeAndActionType',
          // Additional fields from ServiceCodeAndActionType
          serviceCode: item.ServiceCode || '',
          actionType: item.ActionType || '',
          activityPurpose: item.ActivityPurpose || '',
          serviceActivityLabel: label,
          partNumber: item.PartNumber || '',
          hours: item.InternalNoOfHours || 0
        };

        allServiceItems.push(transformedItem);
        serviceIndex++;

        // Increment ServiceID counters
        subServiceId++;
        if (subServiceId > 2) {
          subServiceId = 1;
          baseServiceId++;
        }
      });
    });

    // If there's an existing offer, merge with available services
    let finalServiceItems = allServiceItems;
    if (existingOffer && existingOffer.items) {
      const existingItemsMap = new Map(existingOffer.items.map(item => [item.serviceCode || item.serviceId, item]));

      finalServiceItems = allServiceItems.map(item => {
        const existing = existingItemsMap.get(item.serviceId);
        if (existing) {
          return {
            ...item,
            ...existing,
            _id: existing._id || item._id,
            isEnabled: existing.isEnabled !== undefined ? existing.isEnabled : item.isEnabled,
            includeInQuote: existing.includeInQuote !== undefined ? existing.includeInQuote : item.includeInQuote
          };
        }
        return item;
      });
    }

    // Create grouped services summary
    const groupedServices = [];
    const enabledItems = finalServiceItems.filter(item => item.isEnabled && item.includeInQuote);
    const groupMap = new Map();

    enabledItems.forEach(item => {
      const packageName = item.packageName;
      if (!groupMap.has(packageName)) {
        groupMap.set(packageName, []);
      }
      groupMap.get(packageName).push(item);
    });

    let groupLevel = 1;
    for (const [packageName, items] of groupMap) {
      groupedServices.push({
        level: groupLevel++,
        packageName: `${packageName} (Fixed)`,
        itemCount: items.length
      });
    }

    return {
      computer: {
        ...computer,
        _id: computer._id.toString(),
        customerId: computer.customerId.toString()
      },
      customer: {
        ...customer,
        _id: customer._id.toString()
      },
      offer: existingOffer ? {
        ...existingOffer,
        _id: existingOffer._id.toString(),
        customerId: existingOffer.customerId.toString(),
        computerId: existingOffer.computerId.toString()
      } : null,
      packageOffering,
      serviceItems: finalServiceItems,
      groupedServices,
      productDesignation: targetProductDesignation,
      productValidityGroup,
      dataSource: {
        serviceCodeAndActionType: serviceItems.length,
        serviceIDPrices: serviceIDPrices.size,
        priceListPrices: priceListPrices.size
      }
    };
  } catch (err) {
    console.error('Error loading service offer data:', err);
    throw error(500, 'Failed to load service offer data');
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  createOffer: async ({ request }) => {
    try {
      const formData = await request.formData();
      const offerData = JSON.parse(formData.get('offerData'));

      const serviceOfferCollection = await getCollection('ServiceOffers');

      // Generate offer number
      const offerCount = await serviceOfferCollection.countDocuments();
      const offerNumber = `SO-${new Date().getFullYear()}-${String(offerCount + 1).padStart(4, '0')}`;

      const newOffer = {
        customerId: new ObjectId(offerData.customerId),
        computerId: new ObjectId(offerData.computerId),
        offerNumber,
        status: 'Draft',
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        items: offerData.items,
        totalAmount: offerData.totalAmount,
        notes: offerData.notes || '',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const result = await serviceOfferCollection.insertOne(newOffer);

      return {
        success: true,
        offerId: result.insertedId.toString(),
        offerNumber
      };
    } catch (err) {
      console.error('Error creating service offer:', err);
      return {
        success: false,
        error: 'Failed to create service offer'
      };
    }
  },

  updateOffer: async ({ request }) => {
    try {
      const formData = await request.formData();
      const offerData = JSON.parse(formData.get('offerData'));

      const serviceOfferCollection = await getCollection('ServiceOffers');

      const updateData = {
        items: offerData.items,
        totalAmount: offerData.totalAmount,
        notes: offerData.notes || '',
        updatedAt: new Date()
      };

      const result = await serviceOfferCollection.updateOne(
        { _id: new ObjectId(offerData._id) },
        { $set: updateData }
      );

      return {
        success: result.modifiedCount > 0,
        error: result.modifiedCount === 0 ? 'No changes made' : null
      };
    } catch (err) {
      console.error('Error updating service offer:', err);
      return {
        success: false,
        error: 'Failed to update service offer'
      };
    }
  },

  generateContract: async ({ request }) => {
    try {
      const formData = await request.formData();
      const offerData = JSON.parse(formData.get('offerData'));

      // Create contract header
      const contractHeader = {
        customerId: new ObjectId(offerData.customerId),
        computerId: new ObjectId(offerData.computerId),
        contractNumber: `SC-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`,
        status: 'Draft',
        startDate: new Date(),
        endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
        totalAmount: offerData.totalAmount,
        offerId: new ObjectId(offerData._id),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const contractHeaderCollection = await getCollection('ContractHeaders');
      const headerResult = await contractHeaderCollection.insertOne(contractHeader);

      // Create contract lines
      const contractLines = offerData.items
        .filter(item => item.isEnabled)
        .map((item, index) => ({
          contractId: headerResult.insertedId,
          lineNumber: index + 1,
          serviceCode: item.serviceCode,
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.totalPrice,
          isRequired: item.isRequired,
          category: item.category,
          notes: item.notes,
          createdAt: new Date(),
          updatedAt: new Date()
        }));

      const contractLinesCollection = await getCollection('ContractLines');
      await contractLinesCollection.insertMany(contractLines);

      // Update offer status
      const serviceOfferCollection = await getCollection('ServiceOffers');
      await serviceOfferCollection.updateOne(
        { _id: new ObjectId(offerData._id) },
        { $set: { status: 'Converted', updatedAt: new Date() } }
      );

      return {
        success: true,
        contractId: headerResult.insertedId.toString(),
        contractNumber: contractHeader.contractNumber
      };
    } catch (err) {
      console.error('Error generating contract:', err);
      return {
        success: false,
        error: 'Failed to generate contract'
      };
    }
  }
};
