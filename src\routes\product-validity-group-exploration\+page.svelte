<script>
  import { fade, fly } from 'svelte/transition';
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  import ProductGroupsGrid from '$lib/components/ProductGroupsGrid.svelte';

  /** @type {import('./$types').PageData} */
  export let data;

  let productValidityGroups = data.productValidityGroups;
  let allDesignations = data.allDesignations;
  let selectedDesignation = data.selectedDesignation;
  let totalItems = data.totalItems;
  let filteredItems = data.filteredItems;

  let loading = false;
  let error = null;
  let searchTerm = '';
  let showStats = true;

  // Filter products based on search term
  $: filteredProducts = productValidityGroups.filter(product => {
    if (!searchTerm) return true;
    const search = searchTerm.toLowerCase();
    return (
      (product.ProductPartNumber && product.ProductPartNumber.toString().includes(search)) ||
      (product.ProductDesignation && product.ProductDesignation.toLowerCase().includes(search)) ||
      (product.ProductName && product.ProductName.toLowerCase().includes(search)) ||
      (product.Description && product.Description.toLowerCase().includes(search))
    );
  });

  // Calculate statistics
  $: stats = {
    totalDesignations: new Set(filteredProducts.map(p => p.ProductDesignation)).size,
    totalProducts: filteredProducts.length,
    totalGroups: new Set(filteredProducts.map(p => p.ProductGroupId)).size,
    partNumbers: new Set(filteredProducts.map(p => p.ProductPartNumber)).size
  };

  // Handle designation filter change
  async function handleDesignationChange(event) {
    const designation = event.target.value;
    const url = new URL($page.url);

    if (designation) {
      url.searchParams.set('designation', designation);
    } else {
      url.searchParams.delete('designation');
    }

    loading = true;
    await goto(url.toString());
    loading = false;
  }

  // Clear all filters
  async function clearFilters() {
    const url = new URL($page.url);
    url.searchParams.delete('designation');
    searchTerm = '';

    loading = true;
    await goto(url.toString());
    loading = false;
  }
</script>

<div class="container" transition:fade>
  <!-- Header Section -->
  <div class="header-section" in:fly={{ y: -20, duration: 500 }}>
    <h1>🔍 Product Validity Group Explorer</h1>
    <p class="subtitle">Explore and analyze product validity groups with advanced filtering and insights</p>
  </div>

  <!-- Statistics Dashboard -->
  {#if showStats}
    <div class="stats-dashboard" in:fly={{ y: 20, duration: 500, delay: 100 }}>
      <div class="stat-card">
        <div class="stat-icon">🏷️</div>
        <div class="stat-content">
          <div class="stat-number">{stats.totalDesignations}</div>
          <div class="stat-label">Designations</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">📦</div>
        <div class="stat-content">
          <div class="stat-number">{stats.totalProducts}</div>
          <div class="stat-label">Total Products</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">📊</div>
        <div class="stat-content">
          <div class="stat-number">{stats.totalGroups}</div>
          <div class="stat-label">Product Groups</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">🔢</div>
        <div class="stat-content">
          <div class="stat-number">{stats.partNumbers}</div>
          <div class="stat-label">Part Numbers</div>
        </div>
      </div>
    </div>
  {/if}

  <!-- Filter Controls -->
  <div class="filter-section" in:fly={{ y: 20, duration: 500, delay: 200 }}>
    <div class="filter-row">
      <!-- ProductDesignation Filter -->
      <div class="filter-group">
        <label for="designation-filter">🏷️ Product Designation</label>
        <select
          id="designation-filter"
          value={selectedDesignation}
          on:change={handleDesignationChange}
          disabled={loading}
        >
          <option value="">All Designations ({allDesignations.length})</option>
          {#each allDesignations as designation}
            <option value={designation}>{designation}</option>
          {/each}
        </select>
      </div>

      <!-- Search Filter -->
      <div class="filter-group">
        <label for="search-filter">🔍 Search Products</label>
        <input
          id="search-filter"
          type="text"
          placeholder="Search by part number, designation, name..."
          bind:value={searchTerm}
          disabled={loading}
        />
      </div>

      <!-- Clear Filters Button -->
      <div class="filter-group">
        <label>&nbsp;</label>
        <button class="clear-btn" on:click={clearFilters} disabled={loading}>
          🗑️ Clear Filters
        </button>
      </div>
    </div>

    <!-- Filter Results Info -->
    {#if selectedDesignation || searchTerm}
      <div class="filter-info" transition:fade>
        <span class="filter-badge">
          {#if selectedDesignation}
            Designation: {selectedDesignation}
          {/if}
          {#if searchTerm}
            Search: "{searchTerm}"
          {/if}
        </span>
        <span class="results-count">
          Showing {filteredProducts.length} of {totalItems} products
        </span>
      </div>
    {/if}
  </div>

  <!-- Loading State -->
  {#if loading}
    <div class="loading-container" transition:fade>
      <div class="loading-spinner"></div>
      <p>Loading product validity data...</p>
    </div>
  {:else if error}
    <!-- Error State -->
    <div class="error-message" transition:fade>
      <h3>❌ Error loading data</h3>
      <p>{error}</p>
      <button on:click={() => window.location.reload()}>🔄 Try Again</button>
    </div>
  {:else if filteredProducts.length === 0}
    <!-- No Data State -->
    <div class="no-data" transition:fade>
      {#if selectedDesignation || searchTerm}
        <h3>🔍 No products found</h3>
        <p>No products match your current filters. Try adjusting your search criteria.</p>
        <button on:click={clearFilters}>🗑️ Clear Filters</button>
      {:else}
        <h3>📦 No product validity groups found</h3>
        <p>The database appears to be empty or there was an issue loading the data.</p>
      {/if}
    </div>
  {:else}
    <!-- Main Content -->
    <div class="content-section" in:fly={{ y: 20, duration: 500, delay: 300 }}>
      <ProductGroupsGrid products={filteredProducts} />
    </div>
  {/if}
</div>

<style>
  .container {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
  }

  /* Header Section */
  .header-section {
    text-align: center;
    margin-bottom: 2rem;
    color: white;
  }

  .header-section h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  }

  .subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 0;
  }

  /* Statistics Dashboard */
  .stats-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .stat-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
  }

  .stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
  }

  .stat-content {
    flex: 1;
  }

  .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    line-height: 1;
  }

  .stat-label {
    font-size: 0.9rem;
    color: #718096;
    margin-top: 0.25rem;
  }

  /* Filter Section */
  .filter-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    align-items: end;
  }

  .filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .filter-group label {
    font-weight: 600;
    color: #2d3748;
    font-size: 0.9rem;
  }

  .filter-group select,
  .filter-group input {
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    background: white;
  }

  .filter-group select:focus,
  .filter-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }

  .clear-btn {
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .clear-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(238, 90, 36, 0.3);
  }

  .clear-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  /* Filter Info */
  .filter-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
  }

  .filter-badge {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
  }

  .results-count {
    color: #718096;
    font-weight: 500;
  }

  /* Content Section */
  .content-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Loading State */
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  }

  .loading-spinner {
    border: 4px solid rgba(102, 126, 234, 0.2);
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  .loading-container p {
    color: #4a5568;
    font-weight: 500;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Error and No Data States */
  .error-message,
  .no-data {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 3rem;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .error-message {
    border-left: 4px solid #e53e3e;
  }

  .error-message h3,
  .no-data h3 {
    color: #2d3748;
    margin-bottom: 1rem;
    font-size: 1.5rem;
  }

  .error-message p,
  .no-data p {
    color: #718096;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
  }

  .error-message button,
  .no-data button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .error-message button:hover,
  .no-data button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .container {
      padding: 1rem;
    }

    .header-section h1 {
      font-size: 2rem;
    }

    .subtitle {
      font-size: 1rem;
    }

    .stats-dashboard {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 1rem;
    }

    .stat-card {
      padding: 1rem;
    }

    .stat-number {
      font-size: 1.5rem;
    }

    .filter-section {
      padding: 1.5rem;
    }

    .filter-row {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .filter-info {
      flex-direction: column;
      gap: 0.5rem;
      align-items: flex-start;
    }
  }
</style>
