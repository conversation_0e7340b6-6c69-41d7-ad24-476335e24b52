<script>
  import { enhance } from '$app/forms';
  import { invalidate, goto } from '$app/navigation';
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import GridLayout from '$lib/components/GridLayout.svelte';
  
  /**
   * @typedef {Object} ServiceCodeAndActionTypeData
   * @property {string} _id - MongoDB ObjectId as string
   * @property {string} serviceCode - Service code identifier
   * @property {string} actionType - Action type identifier
   * @property {string} description - Description of the service code and action type
   * @property {string} category - Category of the service
   * @property {string} status - Status of the service code (active, deprecated, etc.)
   * @property {string} notes - Additional notes
   */

  /** @type {import('./$types').PageData} */
  export let data;

  // Always use ServiceCodeAndActionType collection
  $: collection = 'ServiceCodeAndActionType';
  $: filterlist = $page.url.searchParams.get('filterlist') || 'serviceCode';
  
  /** @type {ServiceCodeAndActionTypeData[]} */
  let items = /** @type {ServiceCodeAndActionTypeData[]} */ (data.items || []);
  
  // Function to refresh data with a full page reload
  async function refreshData() {
    try {
      // Force a complete page refresh using browser's reload functionality
      window.location.reload();
      console.log('Full page refresh initiated');
    } catch (error) {
      console.error('Error refreshing page:', error);
    }
  }
  
  // Function to filter items using the API with filterValue
  /** @param {string} value - The filter value to apply */
  async function applyFilter(value) {
    try {
      if (!value) {
        // If filter is empty, just reload the page without filter
        window.location.href = `/liststd1ServiceCodeAndActionType`;
        return;
      }
      
      // Navigate to the same page with filter parameter
      window.location.href = `/liststd1ServiceCodeAndActionType?filterlist=${filterlist}&filterValue=${encodeURIComponent(value)}`;
    } catch (error) {
      console.error('Error applying filter:', error);
    }
  }
  
  // Special function specifically for handling new item creation
  function refreshAfterCreate() {
    console.log('New item added - refreshing page');
    // Small delay to ensure form closure completes
    setTimeout(() => {
      window.location.reload();
    }, 300); // Longer delay to ensure MongoDB operation completes
  }
  
  // Initialize searchTerm from URL parameter
  let searchTerm = $page.url.searchParams.get('filterValue') || '';
  let categoryFilter = 'all';
  let statusFilter = '';
  
  let showForm = false;
  /** @type {ServiceCodeAndActionTypeData|null} */
  let editingItem = null;
  /** @type {ServiceCodeAndActionTypeData|null} */
  let selectedItem = null;

  /** @type {ServiceCodeAndActionTypeData} */
  let newItem = {
    _id: '',
    serviceCode: '',
    actionType: '',
    description: '',
    category: '',
    status: 'active',
    notes: ''
  };

  /** @type {ServiceCodeAndActionTypeData} */
  let formData = newItem;

  $: {
    formData = editingItem || newItem;
  }

  $: filteredItems = items.filter(item => {
    const matchesSearch = searchTerm === '' || 
      item.serviceCode?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.actionType?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.category?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.status?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.notes?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = categoryFilter === 'all' || item.category === categoryFilter;
    
    const matchesStatus = !statusFilter || item.status?.toLowerCase().includes(statusFilter.toLowerCase());
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  function resetForm() {
    editingItem = null;
    newItem = {
      _id: '',
      serviceCode: '',
      actionType: '',
      description: '',
      category: '',
      status: 'active',
      notes: ''
    };
    showForm = false;
  }
  
  /** @param {ServiceCodeAndActionTypeData} item - The item to edit */
  function editItem(item) {
    editingItem = { ...item };
    showForm = true;
  }
</script>

<div class="container">
  <div class="header-content">
    <h1>{collection}</h1>
    <div class="header-actions">
      <div class="grid-container-actions">
        <div class="search-container">
          <form on:submit|preventDefault={() => applyFilter(searchTerm)}>
            <div class="search-grid">
              <input 
                type="text" 
                placeholder="Search by {filterlist}..." 
                class="search-input" 
                bind:value={searchTerm}
              />
              <button type="submit" class="btn-search">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" width="20" height="20">
                  <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                </svg>
                Search
              </button>
              {#if $page.url.searchParams.get('filterValue')}
                <button 
                  type="button" 
                  class="btn-secondary" 
                  on:click={() => applyFilter('')}
                >
                  Clear Filter
                </button>
              {/if}
            </div>
          </form>
          
          <select class="filter-select" bind:value={categoryFilter}>
            <option value="all">All Categories</option>
            <option value="maintenance">Maintenance</option>
            <option value="repair">Repair</option>
            <option value="installation">Installation</option>
            <option value="inspection">Inspection</option>
            <option value="upgrade">Upgrade</option>
            <option value="other">Other</option>
          </select>
          
          <input 
            type="text" 
            placeholder="Filter by status..." 
            class="filter-input" 
            bind:value={statusFilter}
          />
        </div>
        
        <button 
          class="btn-primary" 
          on:click={() => { showForm = true; }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" width="20" height="20">
            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
          </svg>
          Add New
        </button>
        
        <button 
          class="btn-secondary" 
          on:click={refreshData}
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" width="20" height="20">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
          </svg>
          Refresh
        </button>
      </div>
    </div>
  </div>
  
  {#if items.length === 0}
    <div class="empty-state">
      <h2>No {collection} found</h2>
      <p>Get started by adding your first service code and action type.</p>
      <button 
        class="btn-primary" 
        on:click={() => { showForm = true; }}
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" width="20" height="20">
          <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
        </svg>
        Add Service Code & Action Type
      </button>
    </div>
  {:else}
    <div class="page-layout">
      <GridLayout columns="1fr" gap="1.5rem">
        {#each filteredItems as item}
          <div class="service-code-card">
            <div class="service-code-header">
              <h3>{item.serviceCode}</h3>
              <span class="action-type">{item.actionType}</span>
              <span class="status-badge {item.status}">{item.status}</span>
            </div>
            <div class="service-code-content">
              <p class="description">{item.description || 'No description available'}</p>
              <div class="meta-info">
                <span class="category">Category: {item.category || 'Uncategorized'}</span>
              </div>
              {#if item.notes}
                <p class="notes">Notes: {item.notes}</p>
              {/if}
            </div>
            <div class="service-code-actions">
              <button class="btn-edit" on:click={() => editItem(item)}>Edit</button>
              <form method="POST" action="?/deleteItem" on:submit|preventDefault={(event) => {
                if (confirm('Are you sure you want to delete this item?')) {
                  event.target.submit();
                }
              }}>
                <input type="hidden" name="_id" value={item._id} />
                <button type="submit" class="btn-delete">Delete</button>
              </form>
            </div>
          </div>
        {/each}
      </GridLayout>
    </div>
  {/if}

  {#if showForm}
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h2 class="modal-title">{editingItem ? 'Edit Service Code & Action Type' : 'Add New Service Code & Action Type'}</h2>
          <button class="modal-close" on:click={resetForm}>×</button>
        </div>
        <div class="modal-body">
          <form 
            method="POST" 
            action="?/{editingItem ? 'updateItem' : 'createItem'}" 
            use:enhance={() => {
              return async ({ result }) => {
                if (result.type === 'success') {
                  resetForm();
                  refreshAfterCreate();
                }
              };
            }}
          >
            {#if editingItem}
              <input type="hidden" name="_id" value={formData._id} />
            {/if}
            
            <div class="form-group">
              <label for="serviceCode">Service Code</label>
              <input 
                type="text" 
                id="serviceCode" 
                name="serviceCode" 
                bind:value={formData.serviceCode} 
                required 
              />
            </div>
            
            <div class="form-group">
              <label for="actionType">Action Type</label>
              <input 
                type="text" 
                id="actionType" 
                name="actionType" 
                bind:value={formData.actionType} 
                required 
              />
            </div>
            
            <div class="form-group">
              <label for="description">Description</label>
              <textarea 
                id="description" 
                name="description" 
                bind:value={formData.description} 
                rows="3"
              ></textarea>
            </div>
            
            <div class="form-group">
              <label for="category">Category</label>
              <select 
                id="category" 
                name="category" 
                bind:value={formData.category} 
                required
              >
                <option value="maintenance">Maintenance</option>
                <option value="repair">Repair</option>
                <option value="installation">Installation</option>
                <option value="inspection">Inspection</option>
                <option value="upgrade">Upgrade</option>
                <option value="other">Other</option>
              </select>
            </div>
            
            <div class="form-group">
              <label for="status">Status</label>
              <select 
                id="status" 
                name="status" 
                bind:value={formData.status} 
                required
              >
                <option value="active">Active</option>
                <option value="deprecated">Deprecated</option>
                <option value="pending">Pending</option>
                <option value="archived">Archived</option>
              </select>
            </div>
            
            <div class="form-group">
              <label for="notes">Notes</label>
              <textarea 
                id="notes" 
                name="notes" 
                bind:value={formData.notes} 
                rows="3"
              ></textarea>
            </div>
            
            <div class="form-actions">
              <button type="button" class="btn-secondary" on:click={resetForm}>Cancel</button>
              <button type="submit" class="btn-primary">
                {editingItem ? 'Update' : 'Create'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  /* Base styles */
  .container {
    max-width: 1400px;
    margin: 0 auto; /* Center the container */
    padding: 2rem 1rem;
  }
  
  .header-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
  
  h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #0a2463;
    margin: 0;
  }
  
  .grid-container-actions {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 1rem;
    align-items: start;
  }
  
  .search-container {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .search-grid {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 0.5rem;
  }
  
  .search-input {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 1rem;
  }
  
  .filter-select, .filter-input {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
  }
  
  .btn-primary, .btn-secondary, .btn-search, .btn-edit, .btn-delete {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .btn-primary {
    background-color: #0a2463;
    color: white;
    border: none;
  }
  
  .btn-primary:hover {
    background-color: #061a47;
  }
  
  .btn-secondary {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
  }
  
  .btn-secondary:hover {
    background-color: #e5e7eb;
  }
  
  .btn-search {
    background-color: #3b82f6;
    color: white;
    border: none;
  }
  
  .btn-search:hover {
    background-color: #2563eb;
  }
  
  .btn-edit {
    background-color: #3b82f6;
    color: white;
    border: none;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .btn-edit:hover {
    background-color: #2563eb;
  }
  
  .btn-delete {
    background-color: #ef4444;
    color: white;
    border: none;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .btn-delete:hover {
    background-color: #dc2626;
  }
  
  /* Empty state */
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 4rem 2rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .empty-state h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0.5rem;
  }
  
  .empty-state p {
    color: #6b7280;
    margin-bottom: 2rem;
  }
  
  /* Service code card */
  .service-code-card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  
  .service-code-header {
    display: flex;
    align-items: center;
    padding: 1rem;
    background-color: #f3f4f6;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .service-code-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
    margin: 0;
    margin-right: auto;
  }
  
  .action-type {
    padding: 0.25rem 0.75rem;
    background-color: #dbeafe;
    color: #1e40af;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-right: 0.5rem;
  }
  
  .status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
  }
  
  .status-badge.active {
    background-color: #d1fae5;
    color: #065f46;
  }
  
  .status-badge.deprecated {
    background-color: #fee2e2;
    color: #991b1b;
  }
  
  .status-badge.pending {
    background-color: #fef3c7;
    color: #92400e;
  }
  
  .status-badge.archived {
    background-color: #e5e7eb;
    color: #4b5563;
  }
  
  .service-code-content {
    padding: 1rem;
    flex-grow: 1;
  }
  
  .description {
    margin-top: 0;
    margin-bottom: 1rem;
    color: #4b5563;
  }
  
  .meta-info {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
  }
  
  .category {
    font-size: 0.875rem;
    color: #6b7280;
  }
  
  .notes {
    font-size: 0.875rem;
    color: #6b7280;
    font-style: italic;
    margin-bottom: 0;
  }
  
  .service-code-actions {
    display: flex;
    gap: 0.5rem;
    padding: 1rem;
    border-top: 1px solid #e5e7eb;
    background-color: #f9fafb;
  }
  
  /* Modal */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
  }
  
  .modal-content {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
  }
  
  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
    margin: 0;
  }
  
  .modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6b7280;
    cursor: pointer;
  }
  
  .modal-close:hover {
    color: #111827;
  }
  
  .modal-body {
    padding: 1rem;
  }
  
  /* Form */
  .form-group {
    margin-bottom: 1rem;
  }
  
  .form-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
  }
  
  .form-group input,
  .form-group select,
  .form-group textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 1rem;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 1.5rem;
  }
  
  /* Responsive */
  @media (max-width: 768px) {
    .grid-container-actions {
      grid-template-columns: 1fr;
    }
    
    .search-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
