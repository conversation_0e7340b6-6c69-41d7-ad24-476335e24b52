// Script to add StartDateTime and StopDateTime fields to all ActiveFaultCodes
import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function main() {
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('ActiveFaultCodes');

    const updateResult = await collection.updateMany(
      {
        $or: [
          { StartDateTime: { $exists: false } },
          { StopDateTime: { $exists: false } }
        ]
      },
      {
        $set: {
          StartDateTime: null,
          StopDateTime: null
        }
      }
    );
    console.log(`Updated ${updateResult.modifiedCount} documents.`);
  } catch (err) {
    console.error('Error updating ActiveFaultCodes:', err);
  } finally {
    await client.close();
  }
}

main();
