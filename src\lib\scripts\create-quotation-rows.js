// Script to create QuotationRows collection and populate it with data
const { MongoClient, ObjectId } = require('mongodb');

// MongoDB connection URI
const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function createQuotationRows() {
  try {
    // Connect to MongoDB
    await client.connect();
    console.log('Connected to MongoDB');
    
    // Get the ServiceContracts database
    const db = client.db('ServiceContracts');
    
    // Check if the collection already exists
    const collections = await db.listCollections({ name: 'QuotationRows' }).toArray();
    if (collections.length > 0) {
      console.log('QuotationRows collection already exists. Dropping it before recreating...');
      await db.collection('QuotationRows').drop();
    }
    
    // Create the QuotationRows collection
    const quotationRows = db.collection('QuotationRows');
    console.log('QuotationRows collection created');
    
    // Quotation ID to link all rows
    const quotationId = new ObjectId("67ee6411a10d20cfa4d5e43b");
    
    // Data from the image - complete representation of the table
    const rowsData = [
      // Base Service Offering
      {
        quotationId: quotationId,
        level: 1,
        package: "Base Service Offering",
        serviceId: "1.1",
        service: "Registration",
        includedInPackage: "Yes",
        required: "Mandatory",
        rcog: null,
        oemImporter: null, 
        endOwner: 1260.71,
        end: 1260.71,
        selectService: "Mandatory"
      },
      {
        quotationId: quotationId,
        level: 1,
        package: "Base Service Offering",
        serviceId: "2.1",
        service: "Service Yearly on Request",
        includedInPackage: "Yes",
        required: "Yes",
        rcog: 969.63,
        oemImporter: 1260.71,
        endOwner: 1260.71,
        end: 1260.71,
        selectService: "Mandatory"
      },
      {
        quotationId: quotationId,
        level: 1,
        package: "Base Service Offering",
        serviceId: "2.1",
        service: "Active Monitoring",
        includedInPackage: "Yes",
        required: "Mandatory",
        rcog: 969.63,
        oemImporter: 1260.71,
        endOwner: 1260.71,
        end: 1260.71,
        selectService: "Mandatory"
      },
      
      // Dealer Add-Ons
      {
        quotationId: quotationId,
        level: 2,
        package: "Dealer Add-Ons",
        serviceId: "3.1",
        service: "Software Upgrade",
        includedInPackage: "No",
        required: "Yes",
        rcog: 283.92,
        oemImporter: null,
        endOwner: null,
        end: null,
        selectService: "Yes"
      },
      {
        quotationId: quotationId,
        level: 2,
        package: "Dealer Add-Ons",
        serviceId: "3.2",
        service: "Repair Parts Only",
        includedInPackage: "No",
        required: "Yes",
        rcog: null,
        oemImporter: 5087.02,
        endOwner: 5087.02,
        end: 5087.02,
        selectService: "Yes"
      },
      {
        quotationId: quotationId,
        level: 2,
        package: "Dealer Add-Ons",
        serviceId: "3.3",
        service: "Preventative Repair Parts Only",
        includedInPackage: "No",
        required: "Yes",
        rcog: null,
        oemImporter: null,
        endOwner: null,
        end: null,
        selectService: "Yes"
      },
      {
        quotationId: quotationId,
        level: 2,
        package: "Dealer Add-Ons",
        serviceId: "4.1",
        service: "Engine Health Inspections",
        includedInPackage: "No",
        required: "Yes",
        rcog: 1025.25,
        oemImporter: 1307.06,
        endOwner: 1307.06,
        end: 1307.06,
        selectService: "Yes"
      },
      {
        quotationId: quotationId,
        level: 2,
        package: "Dealer Add-Ons",
        serviceId: "4.2",
        service: "Refill DEF Liquid",
        includedInPackage: "No",
        required: "Yes",
        rcog: 744.51,
        oemImporter: 1196.01,
        endOwner: 1205.57,
        end: 1292.14,
        selectService: "Yes"
      },
      {
        quotationId: quotationId,
        level: 2,
        package: "Dealer Add-Ons",
        serviceId: "4.2",
        service: "Parts Supply Automatic",
        includedInPackage: "No",
        required: "Yes",
        rcog: null,
        oemImporter: null,
        endOwner: null,
        end: null,
        selectService: "Yes"
      },
      {
        quotationId: quotationId,
        level: 2,
        package: "Dealer Add-Ons",
        serviceId: "4.3",
        service: "Optional Services",
        includedInPackage: "No",
        required: "Yes",
        rcog: null,
        oemImporter: null,
        endOwner: null,
        end: null,
        selectService: "Yes"
      },
      
      // Support (Self Service)
      {
        quotationId: quotationId,
        level: 3,
        package: "Support (Self Service)",
        serviceId: "2.1",
        service: "IOT Sampling Program",
        includedInPackage: "No",
        required: "Yes",
        rcog: 214.69,
        oemImporter: 215.60,
        endOwner: 231.04,
        end: 298.80,
        selectService: "Yes"
      },
      {
        quotationId: quotationId,
        level: 3,
        package: "Support (Self Service)",
        serviceId: "2.1",
        service: "KPI Sampling Program",
        includedInPackage: "No",
        required: "Yes",
        rcog: 214.69,
        oemImporter: 215.60,
        endOwner: 231.04,
        end: 298.80,
        selectService: "Yes"
      },
      
      // Support (Dealer)
      {
        quotationId: quotationId,
        level: 4,
        package: "Support (Dealer)",
        serviceId: "9.2",
        service: "Technical Support via Dealer",
        includedInPackage: "Yes",
        required: "Yes",
        rcog: 9288.00,
        oemImporter: 11145.60,
        endOwner: 11145.60,
        end: 11145.60,
        selectService: "Yes"
      },
      {
        quotationId: quotationId,
        level: 4,
        package: "Support (Dealer)",
        serviceId: "9.4",
        service: "Full Service & Maintenance",
        includedInPackage: "No",
        required: "Yes",
        rcog: 211.31,
        oemImporter: 1407.66,
        endOwner: 1508.17,
        end: 1854.11,
        selectService: "Yes"
      },
      {
        quotationId: quotationId,
        level: 4,
        package: "Support (Dealer)",
        serviceId: "9.4",
        service: "Repair only",
        includedInPackage: "No",
        required: "Yes",
        rcog: null,
        oemImporter: null,
        endOwner: null,
        end: null,
        selectService: "Yes"
      },
      
      // Retirement Plan
      {
        quotationId: quotationId,
        level: 5,
        package: "Retirement Plan",
        serviceId: "10.1",
        service: "Engine Replacement at x hrs",
        includedInPackage: "No",
        required: "Yes",
        rcog: 1.89,
        oemImporter: 5.70,
        endOwner: 5.98,
        end: 6.48,
        selectService: "Yes"
      },
      {
        quotationId: quotationId,
        level: 5,
        package: "Retirement Plan",
        serviceId: "10.2",
        service: "Scheduled Retirement (Standard)",
        includedInPackage: "No",
        required: "Yes",
        rcog: 1.89,
        oemImporter: 5.70,
        endOwner: 5.98,
        end: 6.48,
        selectService: "Yes"
      }
    ];
    
    // Insert the data
    const result = await quotationRows.insertMany(rowsData);
    console.log(`${result.insertedCount} documents inserted into QuotationRows collection`);
  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the connection
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the function
createQuotationRows().catch(console.error);
