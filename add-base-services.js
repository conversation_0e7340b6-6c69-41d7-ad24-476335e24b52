import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

const sampleServices = [
    {
        ServiceCode: 'A-D13',
        'Service activity Label': 'Advanced Service D13',
        'Activity purpose': 'Advanced maintenance for D13 engines',
        'Product Validity Group': 'D13',
        'Internal No of Hours': 4,
        'Internal No of Months': 12,
        createdAt: new Date('2025-03-18'),
        updatedAt: new Date('2025-03-18')
    },
    {
        ServiceCode: 'B-D13',
        'Service activity Label': 'Basic Service D13',
        'Activity purpose': 'Basic maintenance for D13 engines',
        'Product Validity Group': 'D13',
        'Internal No of Hours': 1,
        'Internal No of Months': 3,
        createdAt: new Date('2025-03-18'),
        updatedAt: new Date('2025-03-18')
    },
    {
        ServiceCode: 'B1-D13',
        'Service activity Label': 'Basic Level 1 Service D13',
        'Activity purpose': 'Basic level 1 maintenance for D13 engines',
        'Product Validity Group': 'D13',
        'Internal No of Hours': 1.5,
        'Internal No of Months': 3,
        createdAt: new Date('2025-03-18'),
        updatedAt: new Date('2025-03-18')
    },
    {
        ServiceCode: 'B2-D13',
        'Service activity Label': 'Basic Level 2 Service D13',
        'Activity purpose': 'Basic level 2 maintenance for D13 engines',
        'Product Validity Group': 'D13',
        'Internal No of Hours': 2,
        'Internal No of Months': 3,
        createdAt: new Date('2025-03-18'),
        updatedAt: new Date('2025-03-18')
    },
    {
        ServiceCode: 'B3-D13',
        'Service activity Label': 'Basic Level 3 Service D13',
        'Activity purpose': 'Basic level 3 maintenance for D13 engines',
        'Product Validity Group': 'D13',
        'Internal No of Hours': 2.5,
        'Internal No of Months': 3,
        createdAt: new Date('2025-03-18'),
        updatedAt: new Date('2025-03-18')
    },
    {
        ServiceCode: 'C-D13',
        'Service activity Label': 'Comprehensive Service D13',
        'Activity purpose': 'Comprehensive maintenance for D13 engines',
        'Product Validity Group': 'D13',
        'Internal No of Hours': 6,
        'Internal No of Months': 12,
        createdAt: new Date('2025-03-18'),
        updatedAt: new Date('2025-03-18')
    },
    {
        ServiceCode: 'D-D13',
        'Service activity Label': 'Diagnostic Service D13',
        'Activity purpose': 'Diagnostic check for D13 engines',
        'Product Validity Group': 'D13',
        'Internal No of Hours': 1,
        'Internal No of Months': 1,
        createdAt: new Date('2025-03-18'),
        updatedAt: new Date('2025-03-18')
    },
    {
        ServiceCode: 'E-D13',
        'Service activity Label': 'Extended Service D13',
        'Activity purpose': 'Extended maintenance for D13 engines',
        'Product Validity Group': 'D13',
        'Internal No of Hours': 8,
        'Internal No of Months': 24,
        createdAt: new Date('2025-03-18'),
        updatedAt: new Date('2025-03-18')
    },
    {
        ServiceCode: 'F-D13',
        'Service activity Label': 'Full Service D13',
        'Activity purpose': 'Full maintenance for D13 engines',
        'Product Validity Group': 'D13',
        'Internal No of Hours': 10,
        'Internal No of Months': 24,
        createdAt: new Date('2025-03-18'),
        updatedAt: new Date('2025-03-18')
    },
    {
        ServiceCode: 'S-D13',
        'Service activity Label': 'Standard Service D13',
        'Activity purpose': 'Standard maintenance for D13 engines',
        'Product Validity Group': 'D13',
        'Internal No of Hours': 2,
        'Internal No of Months': 6,
        createdAt: new Date('2025-03-18'),
        updatedAt: new Date('2025-03-18')
    }
];

async function main() {
    try {
        console.log('Connecting to MongoDB...');
        await client.connect();
        console.log('Connected successfully');

        // From memory: database name is ServiceContracts
        const db = client.db('ServiceContracts');
        // From memory: collection name is BaseServices in PascalCase
        const collection = db.collection('BaseServices');

        // Clear existing data
        try {
            await collection.deleteMany({});
            console.log('Cleared existing data');
        } catch (error) {
            console.error('Error clearing existing data:', error);
        }

        // Insert sample services
        try {
            const result = await collection.insertMany(sampleServices);
            console.log(`Inserted ${result.insertedCount} services`);
        } catch (error) {
            console.error('Error inserting sample services:', error);
        }

    } catch (error) {
        console.error('Error:', error);
    } finally {
        try {
            await client.close();
            console.log('Connection closed');
        } catch (error) {
            console.error('Error closing connection:', error);
        }
    }
}

main().catch(console.error);
