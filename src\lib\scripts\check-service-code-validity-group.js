import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function checkServiceCodeValidityGroup() {
    try {
        await client.connect();
        console.log('Connected to MongoDB');
        
        const db = client.db('ServiceContracts');
        const collection = db.collection('ServiceCodeAndActionType');
        
        // Search for documents with ProductValidityGroup field
        const documents = await collection.find({
            ProductValidityGroup: { $exists: true }
        }).toArray();
        
        console.log(`\nFound ${documents.length} documents with ProductValidityGroup field`);
        
        if (documents.length > 0) {
            console.log('\nSample documents:');
            documents.slice(0, 5).forEach((doc, index) => {
                console.log(`\nDocument ${index + 1}:`);
                console.log(JSON.stringify(doc, null, 2));
            });
        }
        
    } catch (error) {
        console.error('Error:', error);
    } finally {
        await client.close();
        console.log('\nConnection closed');
    }
}

checkServiceCodeValidityGroup().catch(console.error);
