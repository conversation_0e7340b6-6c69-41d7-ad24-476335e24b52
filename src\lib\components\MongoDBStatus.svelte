<script>
  import { onMount, onD<PERSON>roy } from 'svelte';

  let isConnected = false;
  let checkingConnection = true;
  let intervalId;

  async function checkConnection() {
    try {
      checkingConnection = true;
      const response = await fetch('/api/mongodb-status');
      const data = await response.json();
      isConnected = data.connected;
    } catch (error) {
      console.error('Error checking MongoDB connection:', error);
      isConnected = false;
    } finally {
      checkingConnection = false;
    }
  }

  onMount(() => {
    // Check connection immediately
    checkConnection();
    
    // Set up interval to check connection status every 30 seconds
    intervalId = setInterval(checkConnection, 30000);
    
    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  });

  onDestroy(() => {
    if (intervalId) clearInterval(intervalId);
  });
</script>

<div class="mongodb-status" title="MongoDB Connection Status: {isConnected ? 'Connected' : 'Disconnected'}">
  <div class="status-label">ServiceContracts Database:</div>
  <div class="status-icon {isConnected ? 'connected' : 'disconnected'}" class:checking={checkingConnection}>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z"></path>
      <path d="M12 16v-4"></path>
      <path d="M12 8h.01"></path>
    </svg>
  </div>
  <div class="status-text">
    {#if checkingConnection}
      Checking...
    {:else}
      {isConnected ? 'Connected' : 'Disconnected'}
    {/if}
  </div>
</div>

<style>
  .mongodb-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
  }

  .status-label {
    font-weight: 500;
    margin-right: 0.25rem;
  }

  .status-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    margin: 0 0.25rem;
  }

  .status-icon.connected {
    color: #10b981; /* Green */
  }

  .status-icon.disconnected {
    color: #ef4444; /* Red */
  }

  .status-icon.checking {
    color: #f59e0b; /* Amber */
    animation: pulse 1.5s infinite;
  }

  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.6;
    }
  }

  .status-text {
    font-weight: 500;
  }
</style>
