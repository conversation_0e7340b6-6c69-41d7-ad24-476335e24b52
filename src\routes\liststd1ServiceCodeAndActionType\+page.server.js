import { MongoClient, ObjectId } from 'mongodb';
import { error } from '@sveltejs/kit';

// MongoDB Connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017';
// Create a new MongoClient for each request to avoid connection issues
let mongoClient = null;

/**
 * Get a MongoDB collection from the ServiceContracts database
 * @param {string} collectionName - The name of the collection
 * @returns {Promise<any>} - MongoDB collection or static data fallback
 */
async function getCollection(collectionName) {
  // Connect to MongoDB for all collections including ServiceCodeAndActionType
  try {
    // Create a new client for each connection to avoid issues with stale connections
    mongoClient = new MongoClient(MONGODB_URI);
    await mongoClient.connect();
    const db = mongoClient.db('ServiceContracts');
    return db.collection(collectionName);
  } catch (err) {
    console.error('MongoDB connection error:', err);
    // Return a mock collection with basic methods if can't connect
    return {
      find: () => ({
        toArray: async () => []
      }),
      insertOne: async (/** @type {any} */ doc) => ({ acknowledged: true, insertedId: new ObjectId() }),
      updateOne: async (/** @type {any} */ filter, /** @type {any} */ update) => ({ matchedCount: 1, modifiedCount: 1 }),
      deleteOne: async (/** @type {any} */ filter) => ({ deletedCount: 1 })
    };
  }
}

/** @type {import('./$types').PageServerLoad} */
export async function load({ url }) {
  // Always use ServiceCodeAndActionType collection for this route
  const collectionName = 'ServiceCodeAndActionType';
  
  try {
    // Get MongoDB collection
    const collection = await getCollection(collectionName);
    
    // Build filter object based on URL parameters
    /** @type {Record<string, string>} */
    let filter = {};
    
    // Check for each filter field
    const filterFields = [
      'ProductValidityGroup',
      'ActivityPurpose',
      'ServiceActivityLabel',
      'ServiceCode', 
      'ActionType'
    ];
    
    // Add filters for any field that has a value in the URL
    filterFields.forEach(field => {
      const value = url.searchParams.get(field);
      if (value && value.trim() !== '') {
        filter[field] = value;
      }
    });
    
    // For backward compatibility with the old filter system
    const filterlist = url.searchParams.get('filterlist');
    const filterValue = url.searchParams.get('filterValue');
    
    // Handle the case when filterlist parameter exists but filterValue is not provided
    if (filterlist) {
      // Only set the filter if filterValue is provided and not empty
      if (filterValue && filterValue.trim() !== '') {
        filter[filterlist] = filterValue;
      }
      // If filterValue is not provided, we don't add any filter constraint
      // This handles the case when URL has only filterlist parameter
    }
    
    console.log('Applied filters:', filter);
    
    // Count total documents for statistics
    const totalCount = await collection.countDocuments(filter);
    console.log(`Total documents matching filter: ${totalCount}`);
    
    // Ensure we fetch all documents by setting a high limit
    // MongoDB will fetch documents in batches internally
    const items = await collection.find(filter).limit(10000).toArray();
    
    console.log(`Retrieved ${items.length} documents from MongoDB`);
    
    // Convert ObjectIds to strings for client-side use
    const processedItems = items.map(/** @param {any} item */ (item) => ({
      ...item,
      _id: item._id.toString()
    }));
    
    return {
      items: processedItems,
      totalCount,
      collection: collectionName,
      filterlist: url.searchParams.get('filterlist') || 'ProductValidityGroup'
    };
  } catch (err) {
    console.error('Error loading data:', err);
    // Return empty items array instead of throwing an error
    return {
      items: [],
      totalCount: 0,
      collection: collectionName,
      filterlist: url.searchParams.get('filterlist') || 'ProductValidityGroup'
    };
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  createItem: async ({ request }) => {
    const collectionName = 'ServiceCodeAndActionType';
    const data = await request.formData();
    const formData = Object.fromEntries(data);
    
    try {
      // Get MongoDB collection
      const collection = await getCollection(collectionName);
      
      // Process form data - handle numeric fields
      /** @type {Record<string, any>} */
      const itemData = { ...formData };
      
      // Convert numeric fields from strings to numbers
      if (itemData.PartNumber) itemData.PartNumber = Number(itemData.PartNumber);
      if (itemData.Quantity) itemData.Quantity = Number(itemData.Quantity);
      if (itemData.InternalNoOfHours) itemData.InternalNoOfHours = Number(itemData.InternalNoOfHours);
      
      // Handle InternalNoOfMonths (can be null)
      if (itemData.InternalNoOfMonths === '') {
        itemData.InternalNoOfMonths = null;
      } else if (itemData.InternalNoOfMonths) {
        itemData.InternalNoOfMonths = Number(itemData.InternalNoOfMonths);
      }
      
      // Remove _id if present in new item creation
      delete itemData._id;
      
      // Insert document
      const result = await collection.insertOne(itemData);
      
      if (!result.acknowledged) {
        throw error(500, 'Failed to create item');
      }
      
      return { success: true, id: result.insertedId };
    } catch (err) {
      console.error('Error creating item:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Unknown error'
      };
    }
  },
  
  updateItem: async ({ request }) => {
    const collectionName = 'ServiceCodeAndActionType';
    const data = await request.formData();
    const formData = Object.fromEntries(data);
    
    try {
      const id = formData._id;
      if (!id || typeof id !== 'string') {
        return { success: false, error: 'Item ID is required for update' };
      }
      
      // Validate ObjectId format as per application rule #4
      if (!ObjectId.isValid(id)) {
        return { success: false, error: 'Invalid ID format' };
      }
      
      // Get MongoDB collection
      const collection = await getCollection(collectionName);
      
      // Process form data
      /** @type {Record<string, any>} */
      const itemData = { ...formData };
      delete itemData._id; // Remove _id from update data
      
      // Convert numeric fields from strings to numbers
      if (itemData.PartNumber) itemData.PartNumber = Number(itemData.PartNumber);
      if (itemData.Quantity) itemData.Quantity = Number(itemData.Quantity);
      if (itemData.InternalNoOfHours) itemData.InternalNoOfHours = Number(itemData.InternalNoOfHours);
      
      // Handle InternalNoOfMonths (can be null)
      if (itemData.InternalNoOfMonths === '') {
        itemData.InternalNoOfMonths = null;
      } else if (itemData.InternalNoOfMonths) {
        itemData.InternalNoOfMonths = Number(itemData.InternalNoOfMonths);
      }
      
      // Update document
      const result = await collection.updateOne(
        { _id: new ObjectId(id) },
        { $set: itemData }
      );
      
      if (!result.matchedCount) {
        return { success: false, error: 'Item not found' };
      }
      
      return { success: true };
    } catch (err) {
      console.error('Error updating item:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Unknown error'
      };
    }
  },
  
  deleteItem: async ({ request }) => {
    const collectionName = 'ServiceCodeAndActionType';
    const data = await request.formData();
    const id = data.get('id');
    
    if (!id || typeof id !== 'string') {
      return { success: false, error: 'Item ID is required for deletion' };
    }
    
    // Validate ObjectId format as per application rule #4
    if (!ObjectId.isValid(id)) {
      return { success: false, error: 'Invalid ID format' };
    }
    
    try {
      // Get MongoDB collection
      const collection = await getCollection(collectionName);
      
      // Delete document
      const result = await collection.deleteOne({ _id: new ObjectId(id) });
      
      if (!result.deletedCount) {
        return { success: false, error: 'Item not found' };
      }
      
      return { success: true };
    } catch (err) {
      console.error('Error deleting item:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Unknown error'
      };
    }
  }
};
