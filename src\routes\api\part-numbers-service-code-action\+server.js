import { json } from '@sveltejs/kit';
import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/** @type {import('./$types').RequestHandler} */
export async function GET({ url }) {
  try {
    console.log('API: Connecting to MongoDB...');
    await client.connect();
    console.log('API: Connected to MongoDB');

    const db = client.db('ServiceContracts');
    console.log('API: Using ServiceContracts database');

    const collection = db.collection('PartNumbersServiceCodeAction');
    console.log('API: Using PartNumbersServiceCodeAction collection');

    // Parse query parameters
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '2500');
    const skip = (page - 1) * limit;

    // Build filter based on query parameters
    const filter = {};

    // Define the mapping of URL parameter names to actual field names
    // IMPORTANT: Exact field names with spaces as they appear in the database
    const fieldMapping = {
      'ServiceCode': 'ServiceCode',
      'PartNumber': 'PartNumber',
      'ActionType': 'ActionType',
      'Product_Validity_Group': 'Product Validity Group',
      'Quantity': 'Quantity',
      'Unit_of_Measure': 'Unit of Measure'
    };

    // Add filters for each field if provided in the query
    for (const [key, value] of url.searchParams.entries()) {
      // Skip pagination parameters
      if (['page', 'limit'].includes(key)) continue;

      if (value) {
        // Map the URL parameter name to the actual field name
        const fieldName = fieldMapping[key] || key;

        // Use regex for string fields to enable partial matching
        if (typeof value === 'string') {
          filter[fieldName] = { $regex: value, $options: 'i' };
        } else {
          filter[fieldName] = value;
        }
      }
    }

    console.log('API: Filter:', filter);

    // Get total count for pagination
    const totalItems = await collection.countDocuments(filter);
    console.log(`API: Total items in collection with filter: ${totalItems}`);

    // Get a sample document to verify field names
    const sampleDoc = await collection.findOne({});
    console.log('API: Sample document field names:', Object.keys(sampleDoc || {}));
    console.log('API: Sample document:', JSON.stringify(sampleDoc, null, 2));

    // Get items with pagination and sorting
    const items = await collection.find(filter)
      .sort({ ServiceCode: 1 }) // Sort by ServiceCode for better readability
      .skip(skip)
      .limit(limit)
      .toArray();

    console.log(`API: Found ${items.length} items out of ${totalItems} total`);

    // Check field names in the first item
    if (items.length > 0) {
      console.log('API: First item field names:', Object.keys(items[0]));
      console.log('API: First item Product Validity Group value:', items[0]['Product Validity Group']);
      console.log('API: First item:', JSON.stringify(items[0], null, 2));
    }

    // Convert ObjectId to string to avoid serialization issues
    // IMPORTANT: Preserve the exact field names with spaces
    const serializedItems = items.map(item => ({
      ...item,
      _id: item._id.toString()
    }));

    const response = {
      items: serializedItems,
      totalItems,
      page,
      limit,
      totalPages: Math.ceil(totalItems / limit)
    };

    console.log(`API: Sending response with ${serializedItems.length} items`);

    return json(response);
  } catch (error) {
    console.error('API Error fetching data:', error);
    return json({ error: 'Failed to fetch data: ' + error.message }, { status: 500 });
  }
}

/** @type {import('./$types').RequestHandler} */
export async function POST({ request }) {
  try {
    const data = await request.json();

    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('PartNumbersServiceCodeAction');

    // Add timestamps
    data.createdAt = new Date();
    data.updatedAt = new Date();

    const result = await collection.insertOne(data);

    return json({
      success: true,
      id: result.insertedId,
      message: 'Item created successfully'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating item:', error);
    return json({ error: 'Failed to create item' }, { status: 500 });
  } finally {
    await client.close();
  }
}
