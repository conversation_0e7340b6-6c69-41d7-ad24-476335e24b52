import { MongoClient, ObjectId } from 'mongodb';
import { error, fail } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

/** @type {import('./$types').PageServerLoad} */
export async function load({ params }) {
  const client = new MongoClient(uri);

  try {
    await client.connect();
    const db = client.db(dbName);

    // Get the specific item by ID
    const item = await db.collection('ServicePlanProductDesignation')
      .findOne({ _id: new ObjectId(params.id) });

    if (!item) {
      throw error(404, 'Service plan item not found');
    }

    // Convert ObjectId to string for client-side use
    const serializedItem = {
      ...item,
      _id: item._id.toString()
    };

    return {
      item: serializedItem
    };
  } catch (err) {
    console.error('Error loading service plan item:', err);
    if (err.status === 404) {
      throw err;
    }
    throw error(500, 'Failed to load service plan item');
  } finally {
    await client.close();
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  update: async ({ request, params }) => {
    const client = new MongoClient(uri);

    try {
      const data = await request.formData();

      // Extract form data
      const updateData = {
        productDesignation: data.get('productDesignation')?.toString() || '',
        serviceCode: data.get('serviceCode')?.toString() || '',
        actionType: data.get('actionType')?.toString() || '',
        activityPurpose: data.get('activityPurpose')?.toString() || '',
        serviceActivityLabel: data.get('serviceActivityLabel')?.toString() || '',
        partNumber: parseInt(data.get('partNumber')?.toString() || '0'),
        unitOfMeasure: data.get('unitOfMeasure')?.toString() || '',
        quantity: parseInt(data.get('quantity')?.toString() || '0'),
        accHours: parseInt(data.get('accHours')?.toString() || '0'),
        frequency: parseInt(data.get('frequency')?.toString() || '1000'),
        sequenceNumber: parseInt(data.get('sequenceNumber')?.toString() || '1'),
        updatedAt: new Date()
      };

      // Validate required fields
      if (!updateData.productDesignation || !updateData.serviceCode || !updateData.actionType) {
        return fail(400, {
          error: 'Product Designation, Service Code, and Action Type are required'
        });
      }

      await client.connect();
      const db = client.db(dbName);

      // Update the item
      const result = await db.collection('ServicePlanProductDesignation').findOneAndUpdate(
        { _id: new ObjectId(params.id) },
        { $set: updateData },
        { returnDocument: 'after' }
      );

      if (!result.value) {
        return fail(404, {
          error: 'Service plan item not found'
        });
      }

      // Convert ObjectId to string for client-side use
      const serializedItem = {
        ...result.value,
        _id: result.value._id.toString()
      };

      return {
        success: true,
        message: 'Service plan item updated successfully',
        item: serializedItem
      };

    } catch (err) {
      console.error('Error updating service plan item:', err);
      return fail(500, {
        error: 'Failed to update service plan item'
      });
    } finally {
      await client.close();
    }
  }
};


