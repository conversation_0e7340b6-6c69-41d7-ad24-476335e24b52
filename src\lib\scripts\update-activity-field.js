import { MongoClient } from 'mongodb';

// MongoDB connection
const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function updateActivityField() {
  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db('ServiceContracts');
    
    // Only update the QuotationLines collection
    const collectionName = 'QuotationLines';
    
    console.log(`Updating ${collectionName} collection...`);
    
    // Find documents with 'activity' field
    const collection = db.collection(collectionName);
    const documentsToUpdate = await collection.find({ activity: { $exists: true } }).toArray();
    
    console.log(`Found ${documentsToUpdate.length} documents with 'activity' field in ${collectionName}`);
    
    // Update each document
    for (const doc of documentsToUpdate) {
      // Create the ServiceActivity field with the value from activity
      await collection.updateOne(
        { _id: doc._id },
        { 
          $set: { ServiceActivity: doc.activity },
          $unset: { activity: "" }
        }
      );
    }
    
    console.log(`Updated ${documentsToUpdate.length} documents in ${collectionName}`);
    console.log('Field update completed successfully');
  } catch (error) {
    console.error('Error updating field:', error);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the update function
updateActivityField();
