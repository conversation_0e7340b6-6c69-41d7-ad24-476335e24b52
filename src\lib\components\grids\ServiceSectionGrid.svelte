<!-- ServiceSectionGrid.svelte -->
<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  
  export let title: string = '';
  export let gap: string = '1rem';
  export let padding: string = '1rem';
  export let backgroundColor: string = '#ffffff';
  export let className: string = '';
</script>

<BaseGrid 
  columns="1fr" 
  rows="auto 1fr" 
  {gap} 
  {padding} 
  {backgroundColor}
  className="service-section-grid {className}"
>
  <div class="section-header">
    <h3>{title}</h3>
    <slot name="header-actions" />
  </div>
  
  <div class="section-content">
    <slot />
  </div>
</BaseGrid>

<style>
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 0.5rem;
  }
  
  .section-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
  }
  
  .section-content {
    width: 100%;
  }
</style>
