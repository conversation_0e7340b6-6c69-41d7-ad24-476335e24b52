<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  
  // Props
  export let title: string = "";
  export let subtitle: string = "";
  export let columns: number = 1;
  export let gap: string = "1.5rem";
</script>

<BaseGrid>
  <div class="form-layout-grid">
    <header class="form-header">
      {#if title}
        <h2>{title}</h2>
      {/if}
      
      {#if subtitle}
        <p class="subtitle">{subtitle}</p>
      {/if}
      
      <slot name="header-actions"></slot>
    </header>
    
    <div 
      class="form-content" 
      style="--columns: {columns}; --gap: {gap}"
    >
      <slot></slot>
    </div>
    
    <footer class="form-footer">
      <slot name="footer"></slot>
    </footer>
  </div>
</BaseGrid>

<style>
  .form-layout-grid {
    display: grid;
    grid-template-rows: auto 1fr auto;
    height: 100%;
    width: 100%;
    background: #1e293b;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .form-header {
    display: flex;
    flex-direction: column;
    padding: 1.5rem;
    border-bottom: 1px solid #334155;
    background: #0f172a;
  }
  
  .form-header h2 {
    color: #60a5fa;
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
  }
  
  .subtitle {
    color: #94a3b8;
    margin: 0;
    font-size: 0.95rem;
  }
  
  .form-content {
    display: grid;
    grid-template-columns: repeat(var(--columns), 1fr);
    gap: var(--gap);
    padding: 1.5rem;
    overflow-y: auto;
  }
  
  .form-footer {
    display: flex;
    justify-content: flex-end;
    padding: 1.5rem;
    border-top: 1px solid #334155;
    background: #0f172a;
  }
  
  @media (max-width: 768px) {
    .form-content {
      grid-template-columns: 1fr !important;
    }
  }
</style>
