const { MongoClient } = require('mongodb');

const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

// Collections to update based on the standardization requirement
const collectionsToUpdate = [
    {
        name: 'ProductValidityGroup',
        oldField: 'Product Validity GRoup',  // Note: This collection has a typo in "GRoup"
        newField: 'ProductValidityGroup'
    }
];

async function updateCollection(db, collectionInfo) {
    console.log(`\nProcessing collection: ${collectionInfo.name}`);
    const collection = db.collection(collectionInfo.name);

    // Check current state
    const beforeDoc = await collection.findOne({ [collectionInfo.oldField]: { $exists: true } });
    if (beforeDoc) {
        console.log(`Found documents with old field name "${collectionInfo.oldField}"`);
        console.log('Sample document before update:');
        console.log(JSON.stringify(beforeDoc, null, 2));
        
        // Update documents
        const result = await collection.updateMany(
            { [collectionInfo.oldField]: { $exists: true } },
            [
                {
                    $set: {
                        [collectionInfo.newField]: `$${collectionInfo.oldField}`
                    }
                },
                {
                    $unset: [collectionInfo.oldField]
                }
            ]
        );

        console.log(`Updated ${result.modifiedCount} documents`);

        // Verify the changes
        if (beforeDoc._id) {
            const afterDoc = await collection.findOne({ _id: beforeDoc._id });
            console.log('\nSame document after update:');
            console.log(JSON.stringify(afterDoc, null, 2));
        }
    } else {
        // Check if documents already have the new field name
        const newFieldDoc = await collection.findOne({ [collectionInfo.newField]: { $exists: true } });
        if (newFieldDoc) {
            console.log(`Collection already using new field name "${collectionInfo.newField}"`);
            console.log('Sample document:');
            console.log(JSON.stringify(newFieldDoc, null, 2));
        } else {
            console.log(`No documents found with either field name in ${collectionInfo.name}`);
        }
    }
}

async function updateProductValidityField() {
    const client = new MongoClient(uri);
    
    try {
        console.log('Connecting to MongoDB...');
        await client.connect();
        console.log('Connected successfully to MongoDB server');
        
        const db = client.db(dbName);
        console.log(`Using database: ${dbName}`);

        // Process each collection
        for (const collectionInfo of collectionsToUpdate) {
            try {
                await updateCollection(db, collectionInfo);
            } catch (error) {
                console.error(`Error processing collection ${collectionInfo.name}:`, error);
            }
        }

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await client.close();
        console.log('\nDisconnected from MongoDB');
    }
}

updateProductValidityField().catch(console.error);
