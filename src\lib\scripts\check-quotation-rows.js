import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function checkQuotationRows() {
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    
    // List all collections
    const collections = await db.listCollections().toArray();
    console.log('\nAvailable collections:', collections.map(c => c.name));

    // Check QuotationHeader collection
    const quotesCollection = db.collection('QuotationHeader');
    const quotes = await quotesCollection.find({}).toArray();
    console.log('\nFound quotes:', quotes.length);
    console.log('Quote details:', quotes.map(q => ({
      _id: q._id.toString(),
      QuoteNumber: q.QuoteNumber
    })));

    // Check QuotationRows collection
    const rowsCollection = db.collection('QuotationRows');
    const rows = await rowsCollection.find({}).toArray();
    console.log('\nFound rows in QuotationRows:', rows.length);
    
    // Group rows by QuoteId
    const rowsByQuote = rows.reduce((acc, row) => {
      const quoteId = row.QuoteId?.toString() || row.QuotationId?.toString();
      if (!quoteId) return acc;
      acc[quoteId] = acc[quoteId] || [];
      acc[quoteId].push({
        _id: row._id.toString(),
        QuoteId: quoteId,
        RowType: row.RowType,
        RowOrder: row.RowOrder,
        PackageName: row.PackageName
      });
      return acc;
    }, {});

    console.log('\nRows by quote:', rowsByQuote);
    console.log('\nRows per quote:');
    Object.entries(rowsByQuote).forEach(([quoteId, rows]) => {
      const quote = quotes.find(q => q._id.toString() === quoteId);
      console.log(`- Quote ${quote?.QuoteNumber || quoteId}: ${rows.length} rows`);
    });

  } catch (err) {
    console.error('Error checking collections:', err);
  } finally {
    await client.close();
  }
}

checkQuotationRows();
