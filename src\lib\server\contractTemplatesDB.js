import { COLLECTIONS, serializeDocument, createObjectId, executeDbOperation } from './serviceContractDB.js';
import serviceContractDB from './serviceContractDB.js';

const db = serviceContractDB.db;
const collection = COLLECTIONS.CONTRACT_TEMPLATES;

/**
 * Create a new contract template
 * @param {Object} template - Contract template data
 * @returns {Promise<Object>} Inserted document with ID
 */
export async function createContractTemplate(template) {
  // Required fields validation
  if (!template.name || !template.productValidityGroup) {
    throw new Error('Template name and product validity group are required');
  }
  
  // Add timestamps
  const now = new Date();
  const documentToInsert = {
    ...template,
    active: template.active !== false, // Default to active if not specified
    createdAt: now,
    updatedAt: now
  };
  
  return executeDbOperation(async () => {    
    const result = await db.collection(collection).insertOne(documentToInsert);
    if (!result.acknowledged) {
      throw new Error('Failed to create contract template');
    }
    
    return {
      ...serializeDocument(documentToInsert),
      _id: result.insertedId.toString()
    };
  }, 'Error creating contract template');
}

/**
 * Get all contract templates with optional filtering
 * @param {Object} filter - MongoDB filter object
 * @param {Object} options - MongoDB options (sort, limit, etc.)
 * @returns {Promise<Array>} Array of contract templates
 */
export async function getContractTemplates(filter = {}, options = {}) {
  return executeDbOperation(async () => {
    const defaultOptions = {
      sort: { name: 1 },
      ...options
    };
    
    const cursor = db.collection(collection).find(filter, defaultOptions);
    const documents = await cursor.toArray();
    return documents.map(doc => serializeDocument(doc));
  }, 'Error retrieving contract templates');
}

/**
 * Get a single contract template by ID
 * @param {string} id - Contract template ID
 * @returns {Promise<Object|null>} Contract template or null if not found
 */
export async function getContractTemplateById(id) {
  return executeDbOperation(async () => {
    const objectId = createObjectId(id);
    if (!objectId) return null;
    
    const document = await db.collection(collection).findOne({ _id: objectId });
    return document ? serializeDocument(document) : null;
  }, 'Error retrieving contract template');
}

/**
 * Update a contract template
 * @param {string} id - Contract template ID
 * @param {Object} updates - Fields to update
 * @returns {Promise<Object|null>} Updated contract template or null if not found
 */
export async function updateContractTemplate(id, updates) {
  return executeDbOperation(async () => {
    const objectId = createObjectId(id);
    if (!objectId) return null;
    
    // Don't allow updating the _id field
    if (updates._id) delete updates._id;
    
    // Add updated timestamp
    const updateData = {
      ...updates,
      updatedAt: new Date()
    };
    
    const result = await db.collection(collection).findOneAndUpdate(
      { _id: objectId },
      { $set: updateData },
      { returnDocument: 'after' }
    );
    
    return result.value ? serializeDocument(result.value) : null;
  }, 'Error updating contract template');
}

/**
 * Delete a contract template
 * @param {string} id - Contract template ID
 * @returns {Promise<boolean>} True if deleted, false if not found
 */
export async function deleteContractTemplate(id) {
  return executeDbOperation(async () => {
    const objectId = createObjectId(id);
    if (!objectId) return false;
    
    const result = await db.collection(collection).deleteOne({ _id: objectId });
    return result.deletedCount > 0;
  }, 'Error deleting contract template');
}

/**
 * Get contract templates by product validity group
 * @param {string} productValidityGroup - Product validity group code
 * @param {boolean} activeOnly - If true, only return active templates
 * @returns {Promise<Array>} Array of contract templates
 */
export async function getTemplatesByProductGroup(productValidityGroup, activeOnly = true) {
  return executeDbOperation(async () => {
    if (!productValidityGroup) return [];
    
    const filter = {
      productValidityGroup: productValidityGroup
    };
    
    if (activeOnly) {
      filter.active = true;
    }
    
    const documents = await db.collection(collection)
      .find(filter)
      .sort({ name: 1 })
      .toArray();
    
    return documents.map(doc => serializeDocument(doc));
  }, 'Error retrieving contract templates by product group');
}

/**
 * Clone a contract template
 * @param {string} id - Source template ID
 * @param {string} newName - Name for the cloned template
 * @returns {Promise<Object>} Newly created template
 */
export async function cloneContractTemplate(id, newName) {
  return executeDbOperation(async () => {
    if (!newName) {
      throw new Error('New template name is required');
    }
    
    const sourceTemplate = await getContractTemplateById(id);
    if (!sourceTemplate) {
      throw new Error('Source template not found');
    }
    
    // Create new template based on source
    const newTemplate = {
      ...sourceTemplate,
      name: newName,
      active: true,
      isCloned: true,
      clonedFrom: id
    };
    
    // Remove ID and timestamps
    delete newTemplate._id;
    delete newTemplate.createdAt;
    delete newTemplate.updatedAt;
    
    // Create new template
    return createContractTemplate(newTemplate);
  }, 'Error cloning contract template');
}

export default {
  createContractTemplate,
  getContractTemplates,
  getContractTemplateById,
  updateContractTemplate,
  deleteContractTemplate,
  getTemplatesByProductGroup,
  cloneContractTemplate
};
