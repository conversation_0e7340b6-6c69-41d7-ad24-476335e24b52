<script>
    import { page } from '$app/stores';
    import { onMount } from 'svelte';
    import { invalidate } from '$app/navigation';
    /** @type {import('./$types').PageData} */
    export let data;

    let editMode = false;
    let form = {
        Name: data.computer.name || data.computer.Name || '',
        Model: data.computer.model || data.computer.Model || '',
        SerialNumber: data.computer.serialNumber || data.computer.SerialNumber || '',
        Type: data.computer.type || data.computer.Type || '',
        OperatingSystem: data.computer.operatingSystem || data.computer.OperatingSystem || '',
        ProductName: data.computer.productName || data.computer.ProductName || '',
        ProductDesignation: data.computer.productDesignation || data.computer.ProductDesignation || '',
        SupplierProductName: data.computer.supplierProductName || data.computer.SupplierProductName || '',
        HoursAtContractStart: data.computer.hoursAtContractStart || data.computer.HoursAtContractStart || '',
        PurchaseDate: data.computer.purchaseDate ? data.computer.purchaseDate.slice(0, 10) : (data.computer.PurchaseDate ? new Date(data.computer.PurchaseDate).toISOString().slice(0, 10) : ''),
        DeliveryDate: data.computer.deliveryDate ? data.computer.deliveryDate.slice(0, 10) : (data.computer.DeliveryDate ? new Date(data.computer.DeliveryDate).toISOString().slice(0, 10) : ''),
        ContractDate: data.computer.contractDate ? data.computer.contractDate.slice(0, 10) : (data.computer.ContractDate ? new Date(data.computer.ContractDate).toISOString().slice(0, 10) : ''),
        WarrantyEndDate: data.computer.warrantyEndDate ? data.computer.warrantyEndDate.slice(0, 10) : (data.computer.WarrantyEndDate ? new Date(data.computer.WarrantyEndDate).toISOString().slice(0, 10) : ''),
        InstallDate: data.computer.installDate ? data.computer.installDate.slice(0, 10) : (data.computer.InstallDate ? new Date(data.computer.InstallDate).toISOString().slice(0, 10) : ''),
        ServiceContractStart: data.computer.serviceContractStart ? data.computer.serviceContractStart.slice(0, 10) : (data.computer.ServiceContractStart ? new Date(data.computer.ServiceContractStart).toISOString().slice(0, 10) : ''),
        ServiceContractEnd: data.computer.serviceContractEnd ? data.computer.serviceContractEnd.slice(0, 10) : (data.computer.ServiceContractEnd ? new Date(data.computer.ServiceContractEnd).toISOString().slice(0, 10) : '')
    };
    let saveError = '';
    let saveSuccess = false;

    function normalizeFormDates(obj) {
        const out = { ...obj };
        [
            'PurchaseDate',
            'DeliveryDate',
            'ContractDate',
            'WarrantyEndDate',
            'InstallDate',
            'ServiceContractStart',
            'ServiceContractEnd'
        ].forEach(key => {
            if (out[key]) {
                // If already ISO, keep; else, try to convert
                try {
                    const d = new Date(out[key]);
                    if (!isNaN(d.getTime())) out[key] = d.toISOString().slice(0, 10);
                } catch {}
            }
        });
        return out;
    }

    async function save() {
        saveError = '';
        saveSuccess = false;
        const normalizedForm = normalizeFormDates(form);
        console.log('Saving form:', normalizedForm);
        const res = await fetch(`/computer-id/${$page.params.id}?/edit`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(normalizedForm)
        });
        if (res.ok) {
            // After saving, force a full reload from the server to ensure fresh data
            window.location.reload();
        } else {
            saveError = (await res.json()).error || 'Failed to save.';
            console.error('Save error:', saveError);
        }
    }
</script>

<div class="container">
    <div class="header">
        <h1>Computer Details</h1>
        <div class="button-group">
            <a href="/computer-id/{$page.params.id}/service-activities/service-planning?productValidityGroup={data.productValidityGroup || ''}&category={data.computer?.category || ''}&hoursAtContractStart={data.computer?.hoursAtContractStart || '0'}" class="primary-button">
                Service Planning
            </a>
            <a href="/computer-id/{$page.params.id}/service-elements?productValidityGroup={data.productValidityGroup || ''}&computerCategory={data.computer?.category || ''}" class="primary-button">
                Service Elements
            </a>
            <a href="/computer-id/{$page.params.id}/quote-list" class="primary-button">
                Service Quotations
            </a>
            <a href="/computer-id/{$page.params.id}/quotation-rows" class="primary-button">
                Quotation Rows
            </a>
        </div>
    </div>

    {#if data.error}
        <div class="error-box">
            {data.error}
        </div>
    {:else if !data.computer}
        <div class="loading">Loading...</div>
    {:else}
        <div class="content">
            <!-- Basic Info Card -->
            <div class="card">
                <h2>Basic Information</h2>
                <div class="info-grid">
                    {#if editMode}
                        <div class="info-row"><span class="label">Name:</span><input class="value" bind:value={form.Name} /></div>
                        <div class="info-row"><span class="label">Model:</span><input class="value" bind:value={form.Model} /></div>
                        <div class="info-row"><span class="label">Serial Number:</span><input class="value" bind:value={form.SerialNumber} /></div>
                        <div class="info-row"><span class="label">Type:</span><input class="value" bind:value={form.Type} /></div>
                        <div class="info-row"><span class="label">Operating System:</span><input class="value" bind:value={form.OperatingSystem} /></div>
                        <div class="info-row"><span class="label">Product Name:</span><input class="value" bind:value={form.ProductName} /></div>
                        <div class="info-row"><span class="label">Product Designation:</span><input class="value" bind:value={form.ProductDesignation} /></div>
                        <div class="info-row"><span class="label">Supplier Product Name:</span><input class="value" bind:value={form.SupplierProductName} /></div>
                        <div class="info-row"><span class="label">Hours At Contract Start:</span><input class="value" type="number" bind:value={form.HoursAtContractStart} /></div>
                        <div class="info-row"><span class="label">Purchase Date:</span><input class="value" type="date" bind:value={form.PurchaseDate} /></div>
                        <div class="info-row"><span class="label">Delivery Date:</span><input class="value" type="date" bind:value={form.DeliveryDate} /></div>
                        <div class="info-row"><span class="label">Contract Date:</span><input class="value" type="date" bind:value={form.ContractDate} /></div>
                        <div class="info-row"><span class="label">Warranty End Date:</span><input class="value" type="date" bind:value={form.WarrantyEndDate} /></div>
                        <div class="info-row"><span class="label">Install Date:</span><input class="value" type="date" bind:value={form.InstallDate} /></div>
                        <div class="info-row"><span class="label">Service Contract Start:</span><input class="value" type="date" bind:value={form.ServiceContractStart} /></div>
                        <div class="info-row"><span class="label">Service Contract End:</span><input class="value" type="date" bind:value={form.ServiceContractEnd} /></div>
                        <div class="info-row">
                            <button class="primary-button" on:click={save}>Save</button>
                            <button class="secondary-button" on:click={() => editMode = false}>Cancel</button>
                        </div>
                        {#if saveError}
                            <div class="error-box">{saveError}</div>
                        {/if}
                        {#if saveSuccess}
                            <div class="success-box">Saved!</div>
                        {/if}
                    {:else}
                        <div class="info-row"><span class="label">Name:</span><span class="value">{data.computer.name || data.computer.Name || 'N/A'}</span></div>
                        <div class="info-row"><span class="label">Model:</span><span class="value">{data.computer.model || data.computer.Model || 'N/A'}</span></div>
                        <div class="info-row"><span class="label">Serial Number:</span><span class="value">{data.computer.serialNumber || data.computer.SerialNumber || 'N/A'}</span></div>
                        <div class="info-row"><span class="label">Type:</span><span class="value">{data.computer.type || data.computer.Type || 'N/A'}</span></div>
                        <div class="info-row"><span class="label">Operating System:</span><span class="value">{data.computer.operatingSystem || data.computer.OperatingSystem || 'N/A'}</span></div>
                        <div class="info-row"><span class="label">Product Name:</span><span class="value">{data.computer.productName || data.computer.ProductName || 'N/A'}</span></div>
                        <div class="info-row"><span class="label">Product Designation:</span><span class="value">{data.computer.productDesignation || data.computer.ProductDesignation || 'N/A'}</span></div>
                        <div class="info-row"><span class="label">Supplier Product Name:</span><span class="value">{data.computer.supplierProductName || data.computer.SupplierProductName || 'N/A'}</span></div>
                        <div class="info-row"><span class="label">Hours At Contract Start:</span><span class="value">{data.computer.hoursAtContractStart || data.computer.HoursAtContractStart || 'N/A'}</span></div>
                        <div class="info-row"><span class="label">Purchase Date:</span><span class="value">{(data.computer.purchaseDate || data.computer.PurchaseDate) ? new Date(data.computer.purchaseDate || data.computer.PurchaseDate).toLocaleDateString() : 'N/A'}</span></div>
                        <div class="info-row"><span class="label">Delivery Date:</span><span class="value">{(data.computer.deliveryDate || data.computer.DeliveryDate) ? new Date(data.computer.deliveryDate || data.computer.DeliveryDate).toLocaleDateString() : 'N/A'}</span></div>
                        <div class="info-row"><span class="label">Contract Date:</span><span class="value">{(data.computer.contractDate || data.computer.ContractDate) ? new Date(data.computer.contractDate || data.computer.ContractDate).toLocaleDateString() : 'N/A'}</span></div>
                        <div class="info-row"><span class="label">Warranty End Date:</span><span class="value">{(data.computer.warrantyEndDate || data.computer.WarrantyEndDate) ? new Date(data.computer.warrantyEndDate || data.computer.WarrantyEndDate).toLocaleDateString() : 'N/A'}</span></div>
                        <div class="info-row"><span class="label">Install Date:</span><span class="value">{(data.computer.installDate || data.computer.InstallDate) ? new Date(data.computer.installDate || data.computer.InstallDate).toLocaleDateString() : 'N/A'}</span></div>
                        <div class="info-row"><span class="label">Service Contract Start:</span><span class="value">{(data.computer.serviceContractStart || data.computer.ServiceContractStart) ? new Date(data.computer.serviceContractStart || data.computer.ServiceContractStart).toLocaleDateString() : 'N/A'}</span></div>
                        <div class="info-row"><span class="label">Service Contract End:</span><span class="value">{(data.computer.serviceContractEnd || data.computer.ServiceContractEnd) ? new Date(data.computer.serviceContractEnd || data.computer.ServiceContractEnd).toLocaleDateString() : 'N/A'}</span></div>
                        <div class="info-row">
                            <button class="primary-button" on:click={() => editMode = true}>Edit</button>
                        </div>
                    {/if}
                </div>
            </div>
            <!-- Validity Groups Card -->
            {#if data.productValidityGroups && data.productValidityGroups.length > 0}
                <div class="card">
                    <h2>Service Basic Info</h2>
                    <div class="validity-grid">
                        {#each data.productValidityGroups as group}
                            <div class="validity-card {group.productPartNumber === data.computer.productPartNumber ? 'matching' : ''}">
                                <div class="validity-info">
                                    <div class="info-row">
                                        <span class="label">Product Name:</span>
                                        <span class="value">{group.productName || 'N/A'}</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="label">Part Number:</span>
                                        <span class="value">{group.productPartNumber || 'N/A'}</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="label">Designation:</span>
                                        <span class="value">{group.productDesignation || 'N/A'}</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="label">Group:</span>
                                        <span class="value">{group.productValidityGroup || 'N/A'}</span>
                                    </div>
                                </div>
                                {#if group.productPartNumber === data.computer.productPartNumber}
                                    <div class="match-badge">Exact Match</div>
                                {/if}
                            </div>
                        {/each}
                    </div>
                </div>
            {/if}
        </div>
    {/if}
</div>

<style>
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }
    .button-group {
        display: flex;
        gap: 1rem;
    }
    .primary-button, .secondary-button {
        padding: 0.5rem 1.2rem;
        border-radius: 4px;
        border: none;
        cursor: pointer;
        font-weight: 600;
    }
    .primary-button {
        background: #1a357a;
        color: #fff;
    }
    .secondary-button {
        background: #e0e0e0;
        color: #222;
    }
    .error-box {
        color: #c00;
        background: #ffeaea;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        margin-top: 1rem;
    }
    .success-box {
        color: #006400;
        background: #eaffea;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        margin-top: 1rem;
    }
    .card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem 2rem;
    }
    .info-row {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0.5rem;
    }
    .label {
        font-weight: bold;
        min-width: 120px;
    }
    .value, input.value {
        flex: 1;
        border: none;
        background: transparent;
        font-size: 1rem;
    }
    input.value {
        border-bottom: 1px solid #ccc;
        background: #f8f8f8;
        padding: 0.2rem 0.5rem;
    }
    .validity-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 1rem;
    }
    .validity-card {
        background: #f6f9ff;
        border-radius: 6px;
        padding: 1rem;
        box-shadow: 0 1px 4px rgba(0,0,0,0.03);
        border: 2px solid transparent;
    }
    .validity-card.matching {
        border-color: #1a357a;
    }
    .match-badge {
        background: #1a357a;
        color: #fff;
        padding: 2px 8px;
        border-radius: 3px;
        font-size: 0.9em;
        margin-top: 0.5rem;
        display: inline-block;
    }
</style>
