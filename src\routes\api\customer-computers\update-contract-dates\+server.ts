import { init } from '$lib/db';
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async () => {
    try {
        const db = await init();
        const CustomerComputers = db.collection("CustomerComputers");
        
        // Get all customer computers
        const computers = await CustomerComputers.find({}).toArray();
        let updatedCount = 0;
        
        for (const computer of computers) {
            // Skip if no purchase date
            if (!computer.purchaseDate) continue;

            const purchaseDate = new Date(computer.purchaseDate);
            const updateFields: any = {
                updatedAt: new Date()
            };

            // Set contract dates based on purchase date
            if (!computer.contractStartDate) {
                updateFields.contractStartDate = purchaseDate;
            }

            // Update the document
            const result = await CustomerComputers.updateOne(
                { _id: computer._id },
                { $set: updateFields }
            );

            if (result.modifiedCount > 0) {
                updatedCount++;
            }
        }

        return json({
            success: true,
            message: `Updated ${updatedCount} computers with contract dates`,
            updatedCount
        });
    } catch (error) {
        console.error('Error updating contract dates:', error);
        return json(
            { 
                success: false, 
                message: 'Failed to update contract dates',
                error: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
};
