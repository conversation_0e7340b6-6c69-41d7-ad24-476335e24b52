<script>
  export let items = [];
  export let loading = false;
  export let error = '';
  // Filter fields
  export let filterServiceCode = '';
  export let filterActivityLabel = '';
  export let filterPurpose = '';
  export let filterValidityGroup = '';

  $: filteredItems = items.filter(item => {
    if (!item) return false;
    const matchesServiceCode = !filterServiceCode || (item.ServiceCode && item.ServiceCode.toLowerCase().includes(filterServiceCode.toLowerCase()));
    const matchesActivityLabel = !filterActivityLabel || (item['Service activity Label'] && item['Service activity Label'].toLowerCase().includes(filterActivityLabel.toLowerCase()));
    const matchesPurpose = !filterPurpose || (item['Activity purpose'] && item['Activity purpose'].toLowerCase().includes(filterPurpose.toLowerCase()));
    const matchesValidityGroup = !filterValidityGroup || (item['Product Validity Group'] && item['Product Validity Group'].toLowerCase().includes(filterValidityGroup.toLowerCase()));
    return matchesServiceCode && matchesActivityLabel && matchesPurpose && matchesValidityGroup;
  });
</script>

<div class="services-card">
  <h2>Services</h2>
  <div class="filter-bar">
    <input placeholder="Service Code" bind:value={filterServiceCode} />
    <input placeholder="Activity Label" bind:value={filterActivityLabel} />
    <input placeholder="Purpose" bind:value={filterPurpose} />
    <input placeholder="Product Validity Group" bind:value={filterValidityGroup} />
    <button class="clear-btn" on:click={() => { filterServiceCode = ''; filterActivityLabel = ''; filterPurpose = ''; filterValidityGroup = ''; }}>Clear</button>
  </div>
  {#if loading}
    <div class="loading">Loading...</div>
  {:else if error}
    <div class="error">{error}</div>
  {:else}
    <div class="services-grid">
      <div class="header cell">Service Code</div>
      <div class="header cell">Activity Label</div>
      <div class="header cell">Purpose</div>
      <div class="header cell">Product Validity Group</div>
      <div class="header cell">Internal No of Hours</div>
      <div class="header cell">Internal No of Months</div>
      {#if filteredItems.length === 0}
        <div class="cell empty" style="grid-column: 1 / -1;">No services found.</div>
      {:else}
        {#each filteredItems as item}
          <div class="cell">{item.ServiceCode ?? ''}</div>
          <div class="cell">{item['Service activity Label'] ?? ''}</div>
          <div class="cell">{item['Activity purpose'] ?? ''}</div>
          <div class="cell">{item['Product Validity Group'] ?? ''}</div>
          <div class="cell">{item['Internal No of Hours'] ?? ''}</div>
          <div class="cell">{item['Internal No of Months'] ?? ''}</div>
        {/each}
      {/if}
    </div>
  {/if}
</div>

<style>
  .services-card {
    background: #fff;
    border-radius: 1.1rem;
    box-shadow: 0 2px 12px 0 #0001;
    padding: 2rem;
    margin: 2rem auto;
    max-width: 1200px;
  }
  .filter-bar {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
  }
  .filter-bar input {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    font-size: 1rem;
    min-width: 180px;
  }
  .clear-btn {
    background: #eee;
    border: none;
    padding: 0.5rem 1.2rem;
    border-radius: 6px;
    cursor: pointer;
    color: #333;
    font-weight: 500;
  }
  .services-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 0.2rem;
    background: #f8f9fb;
    border-radius: 0.8rem;
    overflow-x: auto;
  }
  .header.cell {
    font-weight: 700;
    background: #e3e6ef;
    color: #222;
    padding: 0.7rem 1rem;
    border-bottom: 2px solid #d0d3db;
    border-radius: 0.8rem 0.8rem 0 0;
  }
  .cell {
    padding: 0.7rem 1rem;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
    font-size: 1rem;
    word-break: break-all;
  }
  .cell.empty {
    text-align: center;
    color: #888;
    font-style: italic;
    background: #f8f8f8;
    border-bottom: none;
  }
  .loading {
    text-align: center;
    color: #1976d2;
    font-size: 1.1rem;
    margin: 2rem 0;
  }
  .error {
    color: #d32f2f;
    background: #fdecea;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    text-align: center;
  }
</style>
