<script>
    /** @type {string[]} */
    export let headers = [];
    
    // Note: rows prop removed as it's not being used
</script>

<div class="plan-grid">
    <table>
        <thead>
            <tr>
                {#each headers as header}
                    <th>{header}</th>
                {/each}
            </tr>
        </thead>
        <tbody>
            <slot />
        </tbody>
    </table>
</div>

<style>
    .plan-grid {
        background-color: #e6f3ff;  /* Light blue background */
        border-radius: 0.375rem;
        overflow-x: auto;
        padding: 0.5rem;
    }

    table {
        width: 100%;
        border-collapse: collapse;
        background-color: #f0f7ff;  /* Very light blue for table */
    }

    th {
        background-color: #dbeafe;  /* Light blue for headers */
        border: 1px solid #93c5fd;  /* Slightly darker blue for borders */
        font-weight: 600;
        padding: 0.75rem 1rem;
        text-align: center;
    }

    :global(td) {
        border: 1px solid #93c5fd;
        padding: 0.75rem 1rem;
        background-color: #f8fafc;  /* Almost white blue tint */
        text-align: center;
    }

    :global(td:first-child) {
        text-align: left;
        font-weight: 500;
    }

    :global(td:hover) {
        background-color: #dbeafe;  /* Light blue on hover */
        cursor: pointer;
    }

    :global(tr:last-child td:first-child) {
        border-bottom-left-radius: 0.375rem;
    }

    :global(tr:last-child td:last-child) {
        border-bottom-right-radius: 0.375rem;
    }

    :global(thead tr:first-child th:first-child) {
        border-top-left-radius: 0.375rem;
    }

    :global(thead tr:first-child th:last-child) {
        border-top-right-radius: 0.375rem;
    }
</style>
