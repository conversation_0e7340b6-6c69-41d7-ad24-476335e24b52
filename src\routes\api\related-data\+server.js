// src/routes/api/related-data/+server.js
import { json } from '@sveltejs/kit';
import { MongoClient } from 'mongodb';

// Connection URL
const url = process.env.MONGODB_URI || 'mongodb://localhost:27017';
const client = new MongoClient(url);
const dbName = 'ServiceContracts';

/** @type {import('./$types').RequestHandler} */
export async function GET(event) {
    const designation = event.url.searchParams.get('designation');

    if (!designation) {
        return json({ data: [], message: 'No designation provided' });
    }

    try {
        await client.connect();
        const db = client.db(dbName);

        // Example: Fetch data from PartNumbersServiceCodeAction collection
        const partNumbers = await db.collection('PartNumbersServiceCodeAction')
            .find({ ProductValidityGroup: designation })
            .limit(10)
            .toArray();

        // Example: Fetch data from BaseServices collection
        const baseServices = await db.collection('BaseServices')
            .find({ 'Product Validity Group': designation })
            .limit(10)
            .toArray();

        const relatedData = [
            ...partNumbers.map(item => ({
                collection: 'PartNumbersServiceCodeAction',
                field: 'ProductValidityGroup',
                value: item.ProductValidityGroup,
                item
            })),
            ...baseServices.map(item => ({
                collection: 'BaseServices',
                field: 'Product Validity Group',
                value: item['Product Validity Group'],
                item
            }))
        ];

        return json({ data: relatedData });

    } catch (e) {
        console.error(e);
        return json({ data: [], message: 'Error fetching related data' });
    } finally {
        await client.close();
    }
}
