import { MongoClient, ObjectId } from 'mongodb';
import { error } from '@sveltejs/kit';

// MongoDB Connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017';
// Create a new MongoClient for each request to avoid connection issues
let mongoClient = null;

/**
 * Get a MongoDB collection from the ServiceContracts database
 * @param {string} collectionName - The name of the collection
 * @returns {Promise<any>} - MongoDB collection or static data fallback
 */
async function getCollection(collectionName) {
  try {
    // Create a new client for each connection to avoid issues with stale connections
    mongoClient = new MongoClient(MONGODB_URI);
    await mongoClient.connect();
    const db = mongoClient.db('ServiceContracts');
    return db.collection(collectionName);
  } catch (err) {
    console.error('MongoDB connection error:', err);
    // Return a mock collection with basic methods if can't connect
    return {
      find: () => ({
        toArray: async () => []
      }),
      insertOne: async (/** @type {any} */ doc) => ({ acknowledged: true, insertedId: new ObjectId() }),
      updateOne: async (/** @type {any} */ filter, /** @type {any} */ update) => ({ matchedCount: 1, modifiedCount: 1 }),
      deleteOne: async (/** @type {any} */ filter) => ({ deletedCount: 1 })
    };
  } finally {
    // Close MongoDB connection when done
    if (mongoClient) {
      try {
        await mongoClient.close();
      } catch (error) {
        console.error('Error closing MongoDB connection:', error);
      }
    }
  }
}

/** @type {import('./$types').PageServerLoad} */
export async function load({ url }) {
  // Always use ServiceCodes collection for this route (PascalCase for MongoDB collections)
  const collectionName = 'ServiceCodes';
  
  try {
    // Get MongoDB collection
    const collection = await getCollection(collectionName);
    
    // Build filter object based on URL parameters
    /** @type {Record<string, any>} */
    const filter = {};
    
    // Get pagination parameters
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const skip = (page - 1) * limit;
    
    // Get search parameter
    const search = url.searchParams.get('search') || '';
    if (search) {
      // If search is provided, create a text search filter
      filter['$or'] = [
        { ServiceCode: { $regex: search, $options: 'i' } },
        { Description: { $regex: search, $options: 'i' } }
      ];
    }
    
    // Count total documents for pagination
    const totalCount = await collection.countDocuments(filter);
    
    // Fetch documents with pagination
    const items = await collection.find(filter)
      .skip(skip)
      .limit(limit)
      .toArray();
    
    // Convert ObjectIds to strings for client-side use
    const processedItems = items.map(/** @param {any} item */ (item) => ({
      ...item,
      _id: item._id.toString()
    }));
    
    return {
      items: processedItems,
      totalCount,
      currentPage: page,
      totalPages: Math.ceil(totalCount / limit),
      limit
    };
  } catch (err) {
    console.error('Error loading data:', err);
    throw error(500, 'Failed to load service codes');
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  // Delete one or multiple service codes
  deleteItems: async ({ request }) => {
    const formData = await request.formData();
    const itemIds = formData.get('ids')?.toString().split(',') || [];
    
    if (itemIds.length === 0) {
      return { success: false, error: 'No items selected for deletion' };
    }
    
    try {
      const collection = await getCollection('ServiceCodes');
      let deletedCount = 0;
      
      // Delete each item one by one
      for (const id of itemIds) {
        // Validate ObjectId format
        if (!ObjectId.isValid(id)) {
          continue;
        }
        
        const result = await collection.deleteOne({ _id: new ObjectId(id) });
        deletedCount += result.deletedCount;
      }
      
      return {
        success: true,
        deletedCount,
        message: `Successfully deleted ${deletedCount} item(s)`
      };
    } catch (err) {
      console.error('Error deleting items:', err);
      return {
        success: false,
        error: err instanceof Error ? err.message : 'Unknown error during deletion'
      };
    }
  }
};
