// Script to list all items in PartNumbersServiceCodeAction where ProductDesignationShort equals "D13B-N MH"
// This script doesn't modify any data, it only displays matching documents

import { MongoClient } from 'mongodb';

// Connection URL and Database name
const url = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';
const collectionName = 'PartNumbersServiceCodeAction';

async function listSpecificDesignations() {
  let client;

  try {
    // Connect to MongoDB
    client = new MongoClient(url);
    await client.connect();
    console.log('Connected to MongoDB server');

    // Get database and collection
    const db = client.db(dbName);
    const collection = db.collection(collectionName);

    // Count total documents in collection
    const totalDocuments = await collection.countDocuments();
    console.log(`Total documents in ${collectionName}: ${totalDocuments}`);

    // Find documents with ProductDesignationShort = "D13B-N MH"
    const matchingDocs = await collection.find({
      ProductDesignationShort: "D13B-N MH"
    }).toArray();
    
    console.log(`\nFound ${matchingDocs.length} documents with ProductDesignationShort = "D13B-N MH"`);
    
    if (matchingDocs.length > 0) {
      console.log('\nMatching documents:');
      matchingDocs.forEach((doc, index) => {
        console.log(`\nDocument ${index + 1}:`);
        console.log(`ID: ${doc._id}`);
        console.log(`ServiceCode: ${doc.ServiceCode || 'N/A'}`);
        console.log(`PartNumber: ${doc.PartNumber || 'N/A'}`);
        console.log(`ActionType: ${doc.ActionType || 'N/A'}`);
        console.log(`Product Validity Group: ${doc['Product Validity Group'] || doc.ProductValidityGroup || 'N/A'}`);
        console.log(`ProductDesignationShort: ${doc.ProductDesignationShort || 'N/A'}`);
        console.log(`Quantity: ${doc.Quantity || 'N/A'}`);
        console.log(`Unit of Measure: ${doc['Unit of Measure'] || 'N/A'}`);
      });
    } else {
      // If no exact matches, try to find documents with similar ProductDesignationShort values
      console.log('\nNo exact matches found. Checking for similar values...');
      
      // First, let's check if the field exists at all
      const hasField = await collection.findOne({ ProductDesignationShort: { $exists: true } });
      if (!hasField) {
        console.log('No documents found with ProductDesignationShort field. This field might not exist or might be empty.');
      }
      
      // Look for documents with ProductDesignationShort containing part of the target value
      const similarDocs = await collection.find({
        ProductDesignationShort: { $regex: /D13B/i }
      }).toArray();
      
      console.log(`Found ${similarDocs.length} documents with ProductDesignationShort containing "D13B"`);
      
      if (similarDocs.length > 0) {
        console.log('\nDocuments with similar ProductDesignationShort:');
        similarDocs.forEach((doc, index) => {
          console.log(`\nDocument ${index + 1}:`);
          console.log(`ID: ${doc._id}`);
          console.log(`ServiceCode: ${doc.ServiceCode || 'N/A'}`);
          console.log(`ProductDesignationShort: ${doc.ProductDesignationShort || 'N/A'}`);
        });
      }
      
      // Show all unique ProductDesignationShort values in the collection
      const allValues = await collection.distinct('ProductDesignationShort');
      console.log('\nAll unique ProductDesignationShort values in the collection:');
      console.log(allValues);
    }

  } catch (err) {
    console.error('Error finding documents:', err);
  } finally {
    // Close the connection
    if (client) {
      await client.close();
      console.log('\nMongoDB connection closed');
    }
  }
}

// Run the function
listSpecificDesignations().catch(console.error);
