import { error } from '@sveltejs/kit';
import { ObjectId } from 'mongodb';
import { getCollection } from '$lib/db/mongo';
import { DEFAULT_CUSTOMER_DIVISION } from '$lib/constants';

// Valid customer types based on system rules
/**
 * @typedef {'Retail'|'OEM Importer'|'Fleet Owner'|'Operator'|'Dealer'|'OEM'|'OEM Dealer'} CustomerType
 */

const CUSTOMER_TYPES = /** @type {const} */ (['Retail', 'OEM Importer', 'Fleet Owner', 'Operator', 'Dealer', 'OEM', 'OEM Dealer']);
const DEFAULT_CUSTOMER_TYPE = CUSTOMER_TYPES[0];

/**
 * @typedef {Object} Customer
 * @property {ObjectId} _id - MongoDB ObjectId
 * @property {string} companyName - Company name
 * @property {string} email - Email address
 * @property {string} phone - Phone number
 * @property {string} address - Street address
 * @property {string} city - City
 * @property {string} country - Country
 * @property {CustomerType} type - Customer type
 * @property {string} notes - Additional notes
 * @property {number} [computerCount] - Number of associated computers
 */

/**
 * @typedef {Object} Region
 * @property {ObjectId} _id - MongoDB ObjectId
 * @property {string} name - Region name
 * @property {string} description - Region description
 * @property {string} country - Country this region belongs to
 * @property {Date} createdAt - Creation date
 */

/**
 * @type {import('./$types').PageServerLoad}
 */
export async function load({ url }) {
  // Check if a customer ID is provided in the URL for computer loading
  const selectedCustomerId = url.searchParams.get('customerId');
  try {
    console.log('Loading customers list');
    
    // Get collections using centralized MongoDB helpers
    const customersCollection = await getCollection('Customers');  
    const regionsCollection = await getCollection('Regions');

    // Get all customers with their computer counts
    const customersData = await customersCollection.aggregate([
      {
        $lookup: {
          from: 'CustomerComputers',  
          localField: '_id',  
          foreignField: 'customerId',  
          as: 'computers'
        }
      },
      {
        $addFields: {
          computerCount: { $size: '$computers' }
        }
      }
    ]).toArray();

    // Convert MongoDB documents to Customer type with proper ObjectId handling
    /** @type {Customer[]} */
    const customers = customersData.map(doc => ({
      _id: doc._id instanceof ObjectId ? doc._id : new ObjectId(doc._id),
      companyName: doc.companyName || '',
      email: doc.email || '',
      phone: doc.phone || '',
      address: doc.address || '',
      city: doc.city || '',
      country: doc.country || '',
      type: (doc.type && CUSTOMER_TYPES.includes(doc.type)) ? doc.type : DEFAULT_CUSTOMER_TYPE,
      division: doc.division || 'Industrial',
      versatile: Array.isArray(doc.versatile) ? doc.versatile : [],
      notes: doc.notes || '',
      computerCount: doc.computerCount || 0
    }));

    console.log(`Found ${customers.length} customers`);

    // Get all regions
    const regionsData = await regionsCollection.find({}).toArray();
    
    /** @type {Region[]} */
    const regions = regionsData.map(doc => ({
      _id: doc._id instanceof ObjectId ? doc._id : new ObjectId(doc._id),
      name: doc.name || '',
      description: doc.description || '',
      country: doc.country || '',
      createdAt: doc.createdAt || new Date()
    }));
    
    console.log(`Found ${regions.length} regions`);
    
    // Group regions by country
    /** @type {Record<string, Array<{_id: string, name: string, description: string}>>} */
    const regionsByCountry = {};
    regions.forEach(region => {
      if (!regionsByCountry[region.country]) {
        regionsByCountry[region.country] = [];
      }
      regionsByCountry[region.country].push({
        _id: region._id.toString(),
        name: region.name,
        description: region.description
      });
    });

    // Convert ObjectIds to strings only for client-side use
    const convertedCustomers = customers.map(customer => ({
      ...customer,
      _id: customer._id.toString()
    }));

    console.log('Successfully converted customers for client');
    
    // If a specific customer ID is requested, fetch its computers too
    /** @type {Array<{_id: string, customerId: string, serialNumber: string, model: string, productType: string, productDesignation?: string, engineType?: string, operatingHours: number, installationDate?: string, manufactureDate?: string, isActive: boolean, notes?: string, createdAt: Date, updatedAt: Date}>} */
    let customerComputers = [];
    if (selectedCustomerId && ObjectId.isValid(selectedCustomerId)) {
      try {
        const customerComputersCollection = await getCollection('CustomerComputers');
        const computersData = await customerComputersCollection
          .find({ customerId: new ObjectId(selectedCustomerId) })
          .toArray();
        
        // Convert MongoDB documents to client-friendly format
        customerComputers = computersData.map(computer => ({
          ...computer,
          _id: computer._id.toString(),
          customerId: computer.customerId.toString()
        }));
        
        console.log(`Found ${customerComputers.length} computers for customer ${selectedCustomerId}`);
      } catch (err) {
        console.error('Error loading customer computers:', err);
      }
    }

    return {
      customers: convertedCustomers,
      customerTypes: CUSTOMER_TYPES,
      regionsByCountry,
      customerComputers
    };
  } catch (err) {
    console.error('Error loading customers:', err instanceof Error ? err.message : 'Unknown error');
    throw error(500, { message: 'Failed to load customers' });
  }
}

/**
 * @type {import('./$types').Actions}
 */
export const actions = {
  createCustomer: async ({ request }) => {
    try {
      const formData = await request.formData();
      
      // Extract and validate required fields
      const companyName = formData.get('companyName');
      const type = formData.get('type');
      const division = formData.get('division');
      
      if (!companyName) {
        return { success: false, error: 'Company name is required' };
      }

      // Use helper function to validate and get customer type and division
      const customerType = type ? type : DEFAULT_CUSTOMER_TYPE;
      // Default to Industrial if no division is provided
      const customerDivision = division ? division : 'Industrial';
      
      // Process versatile options - form returns them as multiple entries with the same name
      const versatileOptions = formData.getAll('versatile');
      
      // Create customer object
      const customerData = {
        companyName,
        email: formData.get('email') || '',
        phone: formData.get('phone') || '',
        address: formData.get('address') || '',
        city: formData.get('city') || '',
        country: formData.get('country') || '',
        type: customerType,
        division: customerDivision, // Add division field
        versatile: versatileOptions, // Add versatile options array
        notes: formData.get('notes') || ''
      };

      // Check for duplicate company name
      const customersCollection = await getCollection('Customers');  
      const existingCustomer = await customersCollection.findOne({
        companyName: customerData.companyName
      });

      if (existingCustomer) {
        return { success: false, error: 'A customer with this company name already exists' };
      }
      
      // Add timestamps
      const newCustomer = {
        ...customerData,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      console.log('Creating new customer:', newCustomer);
      
      const result = await customersCollection.insertOne(newCustomer);
      console.log('Insert result:', result);
      
      if (!result.acknowledged) {
        return { success: false, error: 'Failed to create customer' };
      }
      
      // Get the newly created customer with its MongoDB ObjectId
      const createdCustomerDoc = await customersCollection.findOne({ _id: result.insertedId });
      if (!createdCustomerDoc) {
        return { success: false, error: 'Failed to retrieve created customer' };
      }
      
      // Convert to client-friendly format with string ID
      const createdCustomer = {
        ...createdCustomerDoc,
        _id: createdCustomerDoc._id.toString(),
        computerCount: 0
      };
      
      return { 
        success: true, 
        type: 'success',
        data: { customer: createdCustomer }
      };
    } catch (err) {
      console.error('Error creating customer:', err instanceof Error ? err.message : 'Unknown error');
      return { success: false, error: err instanceof Error ? err.message : 'Operation failed' };
    }
  },
  
  updateCustomer: async ({ request }) => {
    try {
      const formData = await request.formData();
      const idValue = formData.get('_id');
      
      // Validate ObjectId format before operations
      if (!idValue || typeof idValue !== 'string' || !ObjectId.isValid(idValue)) {
        return { success: false, error: 'Invalid customer ID' };
      }

      const companyName = formData.get('companyName');
      const type = formData.get('type');
      const division = formData.get('division');
      
      if (!companyName) {
        return { success: false, error: 'Company name is required' };
      }

      // Use helper function to validate and get customer type and division
      const customerType = type ? type : DEFAULT_CUSTOMER_TYPE;
      const customerDivision = division ? division : DEFAULT_CUSTOMER_DIVISION;
      
      // Process versatile options - form returns them as multiple entries with the same name
      const versatileOptions = formData.getAll('versatile');
      
      const updateData = {
        companyName,
        email: formData.get('email') || '',
        phone: formData.get('phone') || '',
        address: formData.get('address') || '',
        city: formData.get('city') || '',
        country: formData.get('country') || '',
        type: customerType,
        division: customerDivision, // Add division field
        versatile: versatileOptions, // Add versatile options array
        notes: formData.get('notes') || '',
        updatedAt: new Date()
      };

      // Check for duplicate company name, excluding current customer
      const customersCollection = await getCollection('Customers');  
      const existingCustomer = await customersCollection.findOne({
        _id: { $ne: new ObjectId(idValue) },  
        companyName: updateData.companyName
      });

      if (existingCustomer) {
        return { success: false, error: 'A customer with this company name already exists' };
      }
      
      // Use centralized MongoDB helpers for update
      const result = await customersCollection.updateOne(
        { _id: new ObjectId(idValue) },  
        { $set: updateData }
      );
      
      if (result.matchedCount === 0) {
        return { success: false, error: 'Customer not found' };
      }
      
      // Get the updated customer to return to the UI
      const updatedCustomer = await customersCollection.findOne({ _id: new ObjectId(idValue) });
      if (!updatedCustomer) {
        return { success: false, error: 'Failed to retrieve updated customer' };
      }
      
      // Get computer count for the updated customer
      const computersCollection = await getCollection('CustomerComputers');
      const computerCount = await computersCollection.countDocuments({ 
        customerId: new ObjectId(idValue) 
      });
      
      // Convert to client-friendly format
      const clientCustomer = {
        ...updatedCustomer,
        _id: updatedCustomer._id.toString(),
        computerCount
      };
      
      return { 
        success: true, 
        type: 'success',
        data: { customer: clientCustomer }
      };
    } catch (err) {
      console.error('Error updating customer:', err instanceof Error ? err.message : 'Unknown error');
      return { success: false, error: err instanceof Error ? err.message : 'Operation failed' };
    }
  },
  
  deleteCustomer: async ({ request }) => {
    const formData = await request.formData();
    const idValue = formData.get('_id');

    try {
      // Validate ObjectId format before operations
      if (!idValue || typeof idValue !== 'string' || !ObjectId.isValid(idValue)) {
        return { success: false, error: 'Invalid customer ID' };
      }

      // Convert string ID to MongoDB ObjectId for database operations
      const id = new ObjectId(idValue);

      const customersCollection = await getCollection('Customers');  
      const computersCollection = await getCollection('CustomerComputers');  

      // Check if customer has any computers using native ObjectId
      const hasComputers = await computersCollection.findOne({ 
        customerId: id
      });

      if (hasComputers) {
        return { success: false, error: 'Cannot delete customer with associated computers. Please delete the computers first.' };
      }
      
      // Delete the customer using native ObjectId
      const result = await customersCollection.deleteOne({ 
        _id: id
      });
      
      if (result.deletedCount === 0) {
        return { success: false, error: 'Customer not found' };
      }
      
      return { success: true, type: 'success' };
    } catch (err) {
      console.error('Error deleting customer:', err instanceof Error ? err.message : 'Unknown error');
      return { success: false, error: err instanceof Error ? err.message : 'Failed to delete customer' };
    }
  },
  
  // Computer management actions
  addComputer: async ({ request }) => {
    try {
      const formData = await request.formData();
      
      // Get required fields and ensure they are strings
      const customerId = formData.get('customerId')?.toString();
      const serialNumber = formData.get('serialNumber')?.toString();
      const model = formData.get('model')?.toString();
      const productType = formData.get('productType')?.toString();
      
      // Validate required fields
      if (!customerId || !serialNumber || !model || !productType) {
        return { success: false, error: 'Missing required fields' };
      }
      
      // Validate ObjectId format
      if (!ObjectId.isValid(customerId)) {
        return { success: false, error: 'Invalid customer ID format' };
      }
      
      // Create the computer object with proper string handling
      const computerData = {
        customerId: new ObjectId(customerId),
        serialNumber,
        model,
        productType,
        productDesignation: formData.get('productDesignation')?.toString() || '',
        engineType: formData.get('engineType')?.toString() || '',
        operatingHours: parseInt(formData.get('operatingHours')?.toString() || '0', 10),
        installationDate: formData.get('installationDate')?.toString() || null,
        manufactureDate: formData.get('manufactureDate')?.toString() || null,
        isActive: formData.get('isActive')?.toString() === 'on',
        notes: formData.get('notes')?.toString() || '',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      // Insert the computer into the database
      const customerComputersCollection = await getCollection('CustomerComputers');
      const result = await customerComputersCollection.insertOne(computerData);
      
      if (!result.acknowledged) {
        return { success: false, error: 'Failed to add computer' };
      }
      
      // Update the computer count for the customer
      const customersCollection = await getCollection('Customers');
      await customersCollection.updateOne(
        { _id: new ObjectId(customerId) },
        { $inc: { computerCount: 1 } }
      );
      
      return { 
        success: true, 
        type: 'success',
        data: { computerId: result.insertedId.toString() }
      };
    } catch (err) {
      console.error('Error adding computer:', err instanceof Error ? err.message : 'Unknown error');
      return { success: false, error: err instanceof Error ? err.message : 'Operation failed' };
    }
  },
  
  updateComputer: async ({ request }) => {
    try {
      const formData = await request.formData();
      
      // Get required fields and ensure they are strings
      const computerId = formData.get('_id')?.toString();
      const customerId = formData.get('customerId')?.toString();
      const serialNumber = formData.get('serialNumber')?.toString();
      const model = formData.get('model')?.toString();
      const productType = formData.get('productType')?.toString();
      
      // Validate required fields
      if (!computerId || !customerId || !serialNumber || !model || !productType) {
        return { success: false, error: 'Missing required fields' };
      }
      
      // Validate ObjectId format
      if (!ObjectId.isValid(computerId) || !ObjectId.isValid(customerId)) {
        return { success: false, error: 'Invalid ID format' };
      }
      
      // Create the update data
      const updateData = {
        serialNumber,
        model,
        productType,
        productDesignation: formData.get('productDesignation') || '',
        engineType: formData.get('engineType') || '',
        operatingHours: parseInt(formData.get('operatingHours') || '0', 10),
        installationDate: formData.get('installationDate') || null,
        manufactureDate: formData.get('manufactureDate') || null,
        isActive: formData.get('isActive') === 'on',
        notes: formData.get('notes') || '',
        updatedAt: new Date()
      };
      
      // Update the computer in the database
      const customerComputersCollection = await getCollection('CustomerComputers');
      const result = await customerComputersCollection.updateOne(
        { _id: new ObjectId(computerId) },
        { $set: updateData }
      );
      
      if (result.matchedCount === 0) {
        return { success: false, error: 'Computer not found' };
      }
      
      return { 
        success: true, 
        type: 'success',
        data: { computerId }
      };
    } catch (err) {
      console.error('Error updating computer:', err instanceof Error ? err.message : 'Unknown error');
      return { success: false, error: err instanceof Error ? err.message : 'Operation failed' };
    }
  },
  
  deleteComputer: async ({ request }) => {
    try {
      const formData = await request.formData();
      
      // Get required fields
      const computerId = formData.get('_id');
      const customerId = formData.get('customerId');
      
      // Validate required fields
      if (!computerId || !customerId) {
        return { success: false, error: 'Missing required fields' };
      }
      
      // Validate ObjectId format
      if (!ObjectId.isValid(computerId) || !ObjectId.isValid(customerId)) {
        return { success: false, error: 'Invalid ID format' };
      }
      
      // Delete the computer from the database
      const customerComputersCollection = await getCollection('CustomerComputers');
      const result = await customerComputersCollection.deleteOne({ _id: new ObjectId(computerId) });
      
      if (result.deletedCount === 0) {
        return { success: false, error: 'Computer not found' };
      }
      
      // Update the computer count for the customer
      const customersCollection = await getCollection('Customers');
      await customersCollection.updateOne(
        { _id: new ObjectId(customerId) },
        { $inc: { computerCount: -1 } }
      );
      
      return { 
        success: true, 
        type: 'success'
      };
    } catch (err) {
      console.error('Error deleting computer:', err instanceof Error ? err.message : 'Unknown error');
      return { success: false, error: err instanceof Error ? err.message : 'Operation failed' };
    }
  }
};
