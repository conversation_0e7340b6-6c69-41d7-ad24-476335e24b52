import { MongoClient } from 'mongodb';

async function checkCollections() {
  const uri = 'mongodb://localhost:27017';
  const client = new MongoClient(uri);

  try {
    console.log('Connecting to MongoDB...');
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db('ServiceContracts');
    console.log('Using ServiceContracts database');

    // List all collections
    const collections = await db.listCollections().toArray();
    console.log('Available collections:');
    collections.forEach(c => console.log(` - ${c.name}`));

    // Check if ServiceCodeHeader exists
    const hasServiceCodeHeader = collections.some(c => c.name === 'ServiceCodeHeader');
    console.log(`ServiceCodeHeader collection exists: ${hasServiceCodeHeader}`);

    if (hasServiceCodeHeader) {
      // Get count of documents in ServiceCodeHeader
      const count = await db.collection('ServiceCodeHeader').countDocuments();
      console.log(`ServiceCodeHeader has ${count} documents`);

      // Get a sample document
      if (count > 0) {
        const sample = await db.collection('ServiceCodeHeader').findOne();
        console.log('Sample document from ServiceCodeHeader:');
        console.log(JSON.stringify(sample, null, 2));
      }
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
    console.log('Connection closed');
  }
}

checkCollections();
