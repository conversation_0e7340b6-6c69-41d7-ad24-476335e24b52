import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function checkCollections() {
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    
    // List all collections
    const collections = await db.listCollections().toArray();
    console.log('Available collections:', collections.map(c => c.name));

    // Check QuotationHeader collection
    const quotesCollection = db.collection('QuotationHeader');
    const quotes = await quotesCollection.find({}).toArray();
    console.log('Found quotes:', quotes);

    // Check QuotationRow collection
    const rowsCollection = db.collection('QuotationRow');
    const rows = await rowsCollection.find({}).toArray();
    console.log('Found rows in QuotationRow:', rows);

    // Check QuotationRows collection (old name)
    const oldRowsCollection = db.collection('QuotationRows');
    const oldRows = await oldRowsCollection.find({}).toArray();
    console.log('Found rows in QuotationRows:', oldRows);

    // Count total rows
    console.log('Total rows:', rows.length + oldRows.length);

  } catch (err) {
    console.error('Error checking collections:', err);
  } finally {
    await client.close();
  }
}

checkCollections();
