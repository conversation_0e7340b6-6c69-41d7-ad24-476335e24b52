<script>
  import { createEventDispatcher } from 'svelte';
  
  /** @type {Array<{_id: string, companyName: string, email: string, phone: string, address: string, city: string, country: string, type: string, division: string, versatile: string[], notes: string, computerCount: number}>} */
  export let customers = [];
  // Use inline type annotations to avoid conflicts
  export let onEdit = (/** @type {{_id: string, companyName: string, email: string, phone: string, address: string, city: string, country: string, type: string, division: string, versatile: string[], notes: string, computerCount: number}} */ customer) => {};
  export let onDelete = (/** @type {{_id: string, companyName: string, email: string, phone: string, address: string, city: string, country: string, type: string, division: string, versatile: string[], notes: string, computerCount: number}} */ customer) => {};
  
  /** @type {string|null} */
  export let selectedId = null;
  
  const dispatch = createEventDispatcher();
  
  /**
   * Format customer location
   * @param {{city?: string, country?: string}} customer - Customer object
   * @returns {string} - Formatted location
   */
  function formatLocation(customer) {
    const parts = [];
    if (customer.city) parts.push(customer.city);
    if (customer.country) parts.push(customer.country);
    return parts.join(', ') || 'No location';
  }
  
  /**
   * Handle customer selection
   * @param {{_id: string}} customer - The customer to select
   */
  function handleSelect(customer) {
    dispatch('select', { customer });
  }
</script>

<div class="customers-grid">
  <div class="grid-header">
    <div class="cell">Company Name</div>
    <div class="cell">Type</div>
    <div class="cell">Contact</div>
    <div class="cell">Location</div>
    <div class="cell">Actions</div>
  </div>
  
  {#if customers.length === 0}
    <div class="empty-message">No customers found</div>
  {:else}
    {#each customers as customer (customer._id)}
      <div 
        class="grid-row {selectedId === customer._id ? 'selected' : ''}" 
        on:click={() => handleSelect(customer)}
        on:keydown={(e) => e.key === 'Enter' && handleSelect(customer)}
        tabindex="0"
        role="button"
        aria-label="Select {customer.companyName}">
        <div class="cell company-name">{customer.companyName}</div>
        <div class="cell">
          <div class="type-container">
            {#if customer.type}
              <span class="customer-type">{customer.type}</span>
            {/if}
            {#if customer.division}
              <span class="customer-division">{customer.division}</span>
            {/if}
          </div>
        </div>
        <div class="cell">
          <div class="contact-info">
            {#if customer.email}
              <div>{customer.email}</div>
            {/if}
            {#if customer.phone}
              <div>{customer.phone}</div>
            {/if}
          </div>
        </div>
        <div class="cell">
          {formatLocation(customer)}
        </div>
        <div class="cell actions">
          <button 
            class="btn-icon edit" 
            on:click|stopPropagation={() => onEdit(customer)}
            aria-label="Edit customer"
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="16" height="16">
              <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
            </svg>
          </button>
          <button 
            class="btn-icon delete" 
            on:click|stopPropagation={() => onDelete(customer)}
            aria-label="Delete customer"
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="16" height="16">
              <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </button>
          <div class="computer-count">
            <span>{customer.computerCount} computers</span>
          </div>
        </div>
      </div>
    {/each}
  {/if}
</div>

<style>
  .customers-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1.5fr 1.5fr 1fr;
    gap: 0;
    width: 100%;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    overflow: hidden;
    background-color: white;
  }
  
  .grid-header {
    display: contents;
  }
  
  .grid-header .cell {
    background-color: #1e3a8a;
    color: white;
    font-weight: 600;
    padding: 1rem;
    position: sticky;
    top: 0;
    z-index: 10;
    text-align: left;
  }
  
  .grid-row {
    display: contents;
    cursor: pointer;
  }
  
  .grid-row .cell {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
  }
  
  .grid-row:hover .cell {
    background-color: #f8fafc;
  }
  
  /* Selected row styling */
  .grid-row.selected .cell {
    background-color: #dbeafe;
    border-left: 3px solid #1e3a8a;
  }
  
  .grid-row.selected:hover .cell {
    background-color: #bfdbfe;
  }
  
  .cell.company-name {
    font-weight: 500;
    color: #1e3a8a;
  }
  
  .contact-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .empty-message {
    grid-column: 1 / -1;
    text-align: center;
    padding: 2rem;
    color: #64748b;
  }
  
  .cell.actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-start;
    align-items: center;
  }
  
  .btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 0.375rem;
    border: none;
    background-color: transparent;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .btn-icon:hover {
    background-color: #f1f5f9;
    color: #0f172a;
  }
  
  .btn-icon.edit:hover {
    color: #2563eb;
  }
  
  .btn-icon.delete:hover {
    color: #dc2626;
  }
  
  .type-container {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .customer-type {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background-color: #e0f2fe;
    color: #0369a1;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
  }
  
  .customer-division {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background-color: #f1f5f9;
    color: #475569;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 500;
  }
  
  .customer-division:empty {
    display: none;
  }
  
  .computer-count {
    margin-left: auto;
    font-size: 0.75rem;
    color: #64748b;
  }
</style>
