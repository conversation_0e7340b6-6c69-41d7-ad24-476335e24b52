<script>
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import QuoteGrid from '$lib/components/grids/QuoteGrid.svelte';
  import QuotePageGrid from '$lib/components/grids/QuotePageGrid.svelte';
  import QuoteInfoGrid from '$lib/components/grids/QuoteInfoGrid.svelte';
  import TotalsSummaryGrid from '$lib/components/grids/TotalsSummaryGrid.svelte';
  import ServiceSectionGrid from '$lib/components/grids/ServiceSectionGrid.svelte';
  
  /** @type {import('./$types').PageData} */
  export let data;
  
  let selectedServices = {
    baseContract: [],
    dealerAddOns: [],
    supportServices: [],
    retirementPlans: []
  };
  
  let showSuccessMessage = false;
  let successMessage = '';
  let showErrorMessage = false;
  let errorMessage = '';
  
  // Initialize selected services from data
  $: if (data.serviceOfferings) {
    selectedServices = {
      baseContract: data.serviceOfferings.baseContract
        .filter(s => s.includedInPackage)
        .map(s => s.id),
      dealerAddOns: data.serviceOfferings.dealerAddOns
        .filter(s => s.includedInPackage)
        .map(s => s.id),
      supportServices: data.serviceOfferings.supportServices
        .filter(s => s.includedInPackage)
        .map(s => s.id),
      retirementPlans: data.serviceOfferings.retirementPlans
        .filter(s => s.includedInPackage)
        .map(s => s.id)
    };
  }
  
  // Format date for display
  function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  }
  
  // Format currency for display
  function formatCurrency(amount) {
    return `£ ${parseFloat(amount).toFixed(2)}`;
  }
  
  // Toggle service selection in a section
  function toggleService(section, event) {
    const { id } = event.detail;
    
    // Find the index of the ID in the selected services
    const index = selectedServices[section].indexOf(id);
    
    if (index === -1) {
      // Add to selected services
      selectedServices[section] = [...selectedServices[section], id];
    } else {
      // Remove from selected services
      selectedServices[section] = selectedServices[section].filter(i => i !== id);
    }
    
    // Update the data model
    if (data.serviceOfferings && data.serviceOfferings[section]) {
      data.serviceOfferings[section] = data.serviceOfferings[section].map(service => {
        if (service.id === id) {
          return { ...service, includedInPackage: index === -1 };
        }
        return service;
      });
    }
    
    // Recalculate section totals
    recalculateSectionTotals();
  }
  
  // Recalculate section totals
  function recalculateSectionTotals() {
    if (!data.sectionTotals) {
      data.sectionTotals = {
        baseContract: 0,
        dealerAddOns: 0,
        supportServices: 0,
        retirementPlans: 0,
        total: 0
      };
    }
    
    // Calculate totals for each section
    ['baseContract', 'dealerAddOns', 'supportServices', 'retirementPlans'].forEach(section => {
      if (data.serviceOfferings && data.serviceOfferings[section]) {
        data.sectionTotals[section] = data.serviceOfferings[section]
          .filter(service => selectedServices[section].includes(service.id))
          .reduce((total, service) => total + (parseFloat(service.cost) || 0), 0);
      }
    });
    
    // Calculate overall total
    data.sectionTotals.total = 
      data.sectionTotals.baseContract + 
      data.sectionTotals.dealerAddOns + 
      data.sectionTotals.supportServices + 
      data.sectionTotals.retirementPlans;
  }
  
  // Handle service editing
  async function handleServiceEdit(section, event) {
    const { service } = event.detail;
    
    try {
      // Special handling for Dealer Add-Ons to ensure unique identification
      if (section === 'dealerAddOns' && !service.uniqueId) {
        service.uniqueId = `addon_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
      }
      
      // Prepare data for submission
      const rowData = {
        rowId: service.rowId || service.id,
        quotationId: data.quotationHeader?._id,
        section: section,
        serviceId: service.serviceId || '',
        service: service.service,
        level: service.level,
        cost: service.cost || 0,
        fixed: service.fixed !== undefined ? service.fixed : true,
        variableType: service.variableType || 'hr',
        oemImporter: service.oemImporter !== undefined ? service.oemImporter : false,
        fleetOwner: service.fleetOwner !== undefined ? service.fleetOwner : false,
        scos: service.scos || 0,
        rrp: service.rrp || 0,
        uniqueId: service.uniqueId || null // Include uniqueId for proper identification
      };
      
      console.log('Updating service:', rowData);
      
      // Create form data
      const formData = new FormData();
      formData.append('rowData', JSON.stringify(rowData));
      
      // Send request
      const response = await fetch(`?/updateQuotationRow`, {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error(`Server returned ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        // Update the data model - only update the specific service
        if (data.serviceOfferings && data.serviceOfferings[section]) {
          data.serviceOfferings[section] = data.serviceOfferings[section].map(item => {
            // Match by uniqueId for Dealer Add-Ons, otherwise by id
            if ((section === 'dealerAddOns' && item.uniqueId === service.uniqueId) || 
                (section !== 'dealerAddOns' && item.id === service.id)) {
              return { ...service };
            }
            return item;
          });
        }
        
        // Recalculate section totals
        recalculateSectionTotals();
        
        showSuccessMessage = true;
        successMessage = 'Service updated successfully';
        setTimeout(() => {
          showSuccessMessage = false;
        }, 3000);
      } else {
        throw new Error(result.error || 'Failed to update service');
      }
    } catch (error) {
      console.error('Error updating service:', error);
      showErrorMessage = true;
      errorMessage = error.message || 'An error occurred while updating the service';
      setTimeout(() => {
        showErrorMessage = false;
      }, 3000);
    }
  }
  
  // Handle service addition
  async function handleServiceAdd(section, event) {
    const { service } = event.detail;
    
    try {
      // If no quotation header exists, create one first
      if (!data.quotationHeader) {
        console.log('No quotation header found, creating one first...');
        
        // Prepare data for a new quotation
        const preparedData = {
          computerId: $page.params.id,
          contractLength: data.computer?.contractLength || 12,
          contractStartDate: data.computer?.desiredContractStartDate || new Date(),
          selectedServices: []
        };
        
        // Create form data
        const formData = new FormData();
        formData.append('quoteData', JSON.stringify(preparedData));
        
        // Send request to create a new quotation header
        const response = await fetch(`?/saveQuote`, {
          method: 'POST',
          body: formData
        });
        
        if (!response.ok) {
          throw new Error(`Server returned ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'Failed to create quotation header');
        }
        
        // Reload the page to get the updated data with the new quotation header
        window.location.reload();
        return; // Stop execution here, the page will reload
      }
      
      // For dealer add-ons, ensure we have a unique ID
      if (section === 'dealerAddOns' && !service.uniqueId) {
        service.uniqueId = `addon_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
      }
      
      // Prepare data for submission
      const rowData = {
        quotationId: data.quotationHeader._id,
        section: section,
        serviceId: service.serviceId || '',
        service: service.service,
        level: service.level,
        cost: service.cost || 0,
        fixed: service.fixed !== undefined ? service.fixed : true,
        variableType: service.variableType || 'hr',
        oemImporter: service.oemImporter !== undefined ? service.oemImporter : false,
        fleetOwner: service.fleetOwner !== undefined ? service.fleetOwner : false,
        scos: service.scos || 0,
        rrp: service.rrp || 0,
        uniqueId: service.uniqueId || null // Include uniqueId for Dealer Add-Ons
      };
      
      console.log('Adding new service:', rowData);
      
      // Create form data
      const formData = new FormData();
      formData.append('rowData', JSON.stringify(rowData));
      
      // Send request
      const response = await fetch(`?/updateQuotationRow`, {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error(`Server returned ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        // Update the service with the new row ID
        service.rowId = result.rowId;
        service.id = result.rowId;
        service.includedInPackage = true;
        
        // Add to selected services
        selectedServices[section] = [...selectedServices[section], service.id];
        
        // Update the data model - the service is already in the local array from QuoteGrid
        // We just need to update it with the returned ID
        if (data.serviceOfferings && data.serviceOfferings[section]) {
          data.serviceOfferings[section] = data.serviceOfferings[section].map(s => {
            if (s.uniqueId === service.uniqueId) {
              return { ...s, id: service.id, rowId: service.rowId };
            }
            return s;
          });
        }
        
        // Recalculate section totals
        recalculateSectionTotals();
        
        showSuccessMessage = true;
        successMessage = 'Service added successfully';
        setTimeout(() => {
          showSuccessMessage = false;
        }, 3000);
      } else {
        throw new Error(result.error || 'Failed to add service');
      }
    } catch (error) {
      console.error('Error adding service:', error);
      showErrorMessage = true;
      errorMessage = error.message || 'An error occurred while adding the service';
      setTimeout(() => {
        showErrorMessage = false;
      }, 3000);
    }
  }
  
  // Handle service removal
  async function handleServiceRemove(section, event) {
    const { id } = event.detail;
    
    try {
      // Find the service
      const service = data.serviceOfferings[section].find(s => s.id === id);
      
      if (!service || !service.rowId || !data.quotationHeader?._id) {
        throw new Error('Invalid service or quotation data');
      }
      
      // Create form data
      const formData = new FormData();
      formData.append('rowId', service.rowId);
      formData.append('quotationId', data.quotationHeader._id);
      
      // Send request
      const response = await fetch(`?/deleteQuotationRow`, {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error(`Server returned ${response.status}: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (result.success) {
        // Remove from selected services
        selectedServices[section] = selectedServices[section].filter(i => i !== id);
        
        // Update the data model
        if (data.serviceOfferings && data.serviceOfferings[section]) {
          data.serviceOfferings[section] = data.serviceOfferings[section].filter(item => item.id !== id);
        }
        
        // Recalculate section totals
        recalculateSectionTotals();
        
        showSuccessMessage = true;
        successMessage = 'Service removed successfully';
        setTimeout(() => {
          showSuccessMessage = false;
        }, 3000);
      } else {
        throw new Error(result.error || 'Failed to remove service');
      }
    } catch (error) {
      console.error('Error removing service:', error);
      showErrorMessage = true;
      errorMessage = error.message || 'An error occurred while removing the service';
      setTimeout(() => {
        showErrorMessage = false;
      }, 3000);
    }
  }
  
  // Save the quote
  async function saveQuote() {
    try {
      console.log('Starting quote save process');
      
      // Prepare data for submission
      const preparedData = {
        computerId: $page.params.id,
        quotationId: data.quotationHeader?._id,
        contractLength: data.computer?.contractLength || 12,
        contractStartDate: data.computer?.desiredContractStartDate || new Date(),
        status: 'Draft',
        notes: '',
        selectedServices: []
      };
      
      console.log('Initial data preparation:', preparedData);
      
      // Add selected services from all sections
      ['baseContract', 'dealerAddOns', 'supportServices', 'retirementPlans'].forEach(section => {
        if (data.serviceOfferings && data.serviceOfferings[section]) {
          data.serviceOfferings[section].forEach(service => {
            if (selectedServices[section].includes(service.id)) {
              // Ensure all required fields are present and correctly formatted
              preparedData.selectedServices.push({
                id: service.id,
                section: section,
                name: service.name || '',
                service: service.service || service.name || '',  // Ensure service name is always populated
                serviceId: service.serviceId || '',
                level: parseInt(service.level) || 0,
                cost: parseFloat(service.cost) || 0,
                fixed: service.fixed !== undefined ? service.fixed : true,
                variableType: service.variableType || 'hr',
                oemImporter: parseFloat(service.oemImporter) || 0,
                fleetOwner: parseFloat(service.fleetOwner) || 0,
                scos: parseFloat(service.scos) || 0,
                rrp: parseFloat(service.rrp) || 0
              });
            }
          });
        }
      });
      
      console.log(`Prepared ${preparedData.selectedServices.length} services for saving`);
      
      // Create form data
      const formData = new FormData();
      formData.append('quoteData', JSON.stringify(preparedData));
      
      // Send request
      console.log('Sending save request to server...');
      const response = await fetch(`?/saveQuote`, {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Server returned ${response.status}: ${response.statusText}`, errorText);
        throw new Error(`Server error: ${response.status} ${response.statusText}`);
      }
      
      const result = await response.json();
      console.log('Save result:', result);
      
      if (result.success) {
        // Update quotation header if it was a new quote
        if (!data.quotationHeader) {
          // Reload the page to get the updated data
          console.log('New quote created, reloading page...');
          window.location.reload();
        } else {
          showSuccessMessage = true;
          successMessage = result.message || 'Quote saved successfully';
          setTimeout(() => {
            showSuccessMessage = false;
          }, 3000);
        }
        
        return result;
      } else {
        console.error('Save failed:', result.error);
        throw new Error(result.error || 'Failed to save quote');
      }
      
    } catch (error) {
      console.error('Error saving quote:', error);
      showErrorMessage = true;
      errorMessage = error.message || 'An error occurred while saving the quote';
      setTimeout(() => {
        showErrorMessage = false;
      }, 3000);
    }
  }
  
  // Go back to computer details
  function goBack() {
    goto(`/computer-id/${$page.params.id}`);
  }
</script>

<QuotePageGrid>
  <!-- Header Section -->
  <div slot="header">
    <div class="header-actions">
      <button class="back-button" on:click={goBack}>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M19 12H5M12 19l-7-7 7-7"/>
        </svg>
        Back to Computer
      </button>
      
      <h1>Service Quote</h1>
      
      <button class="save-button" on:click={saveQuote}>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M19 21H5a2 2 0 01-2-2V5a2 2 0 012-2h11l5 5v11a2 2 0 01-2 2z"/>
          <polyline points="17 21 17 13 7 13 7 21"/>
          <polyline points="7 3 7 8 15 8"/>
        </svg>
        Save Quote
      </button>
    </div>
    
    {#if showSuccessMessage}
      <div class="success-message">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
          <polyline points="22 4 12 14.01 9 11.01"/>
        </svg>
        <span>{successMessage}</span>
      </div>
    {/if}
    
    {#if showErrorMessage}
      <div class="error-message">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"/>
          <line x1="12" y1="8" x2="12" y2="12"/>
          <line x1="12" y1="16" x2="12.01" y2="16"/>
        </svg>
        <span>{errorMessage}</span>
      </div>
    {/if}
  </div>
  
  <!-- Info Section -->
  <div slot="info">
    {#if !data.computer}
      <div class="error-container">
        <h2>Error Loading Computer Data</h2>
        <p>{data.error || 'Failed to load computer data'}</p>
        <button on:click={goBack}>Return to Computer List</button>
      </div>
    {:else}
      <div class="section-header">
        <div class="header-left">
          <h2>Computer and Contract Information</h2>
        </div>
        <div class="header-right">
          {#if data.quotationHeader}
            <div class="quotation-info">
              <div class="quotation-number">Quotation #: {data.quotationHeader.quotationNumber}</div>
              <div class="quotation-status">Status: {data.quotationHeader.status}</div>
              <div class="quotation-date">Created: {formatDate(data.quotationHeader.createdAt)}</div>
            </div>
          {/if}
        </div>
      </div>
      
      <QuoteInfoGrid>
        <div class="info-item">
          <div class="info-label">Computer Name</div>
          <div class="info-value">{data.computer.name}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Serial Number</div>
          <div class="info-value">{data.computer.serialNumber}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Product Designation</div>
          <div class="info-value">{data.computer.productDesignation}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Product Part Number</div>
          <div class="info-value">{data.computer.productPartNumber}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Category</div>
          <div class="info-value">{data.computer.category}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Product Validity Group</div>
          <div class="info-value">{data.computer.productValidityGroup}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Contract Length</div>
          <div class="info-value">{data.computer.contractLength} months</div>
        </div>
        <div class="info-item">
          <div class="info-label">Desired Start Date</div>
          <div class="info-value">{formatDate(data.computer.desiredContractStartDate)}</div>
        </div>
      </QuoteInfoGrid>
    {/if}
  </div>
  
  <!-- Content Section -->
  <div slot="content">
    {#if data.computer}
      <div class="packages-section">
        <h2>Service Packages</h2>
        
        <TotalsSummaryGrid>
          <div class="section-total-item">
            <div class="section-total-label">Base Contract:</div>
            <div class="section-total-value">{formatCurrency(data.sectionTotals?.baseContract || 0)}</div>
          </div>
          <div class="section-total-item">
            <div class="section-total-label">Dealer Add-Ons:</div>
            <div class="section-total-value">{formatCurrency(data.sectionTotals?.dealerAddOns || 0)}</div>
          </div>
          <div class="section-total-item">
            <div class="section-total-label">Support Services:</div>
            <div class="section-total-value">{formatCurrency(data.sectionTotals?.supportServices || 0)}</div>
          </div>
          <div class="section-total-item">
            <div class="section-total-label">Retirement Plans:</div>
            <div class="section-total-value">{formatCurrency(data.sectionTotals?.retirementPlans || 0)}</div>
          </div>
          <div class="section-total-item total">
            <div class="section-total-label">Total Quote Amount:</div>
            <div class="section-total-value">{formatCurrency(data.sectionTotals?.total || 0)}</div>
          </div>
        </TotalsSummaryGrid>
        
        <div class="service-sections">
          <ServiceSectionGrid title="Base Contract">
            <QuoteGrid 
              services={data.serviceOfferings.baseContract} 
              selectedIds={selectedServices.baseContract}
              section="baseContract"
              editable={true}
              allowAddRemove={true}
              on:toggle={(e) => toggleService('baseContract', e)}
              on:edit={(e) => handleServiceEdit('baseContract', e)}
              on:add={(e) => handleServiceAdd('baseContract', e)}
              on:remove={(e) => handleServiceRemove('baseContract', e)}
            />
          </ServiceSectionGrid>
          
          <ServiceSectionGrid title="Dealer Add-Ons">
            <QuoteGrid 
              services={data.serviceOfferings.dealerAddOns} 
              selectedIds={selectedServices.dealerAddOns}
              section="dealerAddOns"
              editable={true}
              allowAddRemove={true}
              on:toggle={(e) => toggleService('dealerAddOns', e)}
              on:edit={(e) => handleServiceEdit('dealerAddOns', e)}
              on:add={(e) => handleServiceAdd('dealerAddOns', e)}
              on:remove={(e) => handleServiceRemove('dealerAddOns', e)}
            />
          </ServiceSectionGrid>
          
          <ServiceSectionGrid title="Support Services">
            <QuoteGrid 
              services={data.serviceOfferings.supportServices} 
              selectedIds={selectedServices.supportServices}
              section="supportServices"
              editable={true}
              allowAddRemove={true}
              on:toggle={(e) => toggleService('supportServices', e)}
              on:edit={(e) => handleServiceEdit('supportServices', e)}
              on:add={(e) => handleServiceAdd('supportServices', e)}
              on:remove={(e) => handleServiceRemove('supportServices', e)}
            />
          </ServiceSectionGrid>
          
          <ServiceSectionGrid title="Retirement Plans">
            <QuoteGrid 
              services={data.serviceOfferings.retirementPlans} 
              selectedIds={selectedServices.retirementPlans}
              section="retirementPlans"
              editable={true}
              allowAddRemove={true}
              on:toggle={(e) => toggleService('retirementPlans', e)}
              on:edit={(e) => handleServiceEdit('retirementPlans', e)}
              on:add={(e) => handleServiceAdd('retirementPlans', e)}
              on:remove={(e) => handleServiceRemove('retirementPlans', e)}
            />
          </ServiceSectionGrid>
        </div>
      </div>
    {/if}
  </div>
</QuotePageGrid>

<style>
  .header-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  
  .back-button, .save-button {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
  }
  
  .back-button:hover, .save-button:hover {
    background-color: #e9ecef;
  }
  
  .back-button svg, .save-button svg {
    margin-right: 0.5rem;
    width: 20px;
    height: 20px;
  }
  
  .save-button {
    background-color: #28a745;
    color: white;
    border-color: #28a745;
  }
  
  .save-button:hover {
    background-color: #218838;
    border-color: #1e7e34;
  }
  
  h1 {
    margin: 0;
    font-size: 1.5rem;
  }
  
  .success-message, .error-message {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    margin-top: 1rem;
    border-radius: 4px;
    font-size: 0.9rem;
  }
  
  .success-message {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }
  
  .error-message {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }
  
  .success-message svg, .error-message svg {
    margin-right: 0.5rem;
    width: 20px;
    height: 20px;
  }
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }
  
  .header-left h2 {
    margin: 0;
    font-size: 1.2rem;
  }
  
  .quotation-info {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
  }
  
  .info-item {
    margin-bottom: 0.5rem;
  }
  
  .info-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
  }
  
  .info-value {
    font-size: 1rem;
  }
  
  .packages-section h2 {
    margin-top: 0;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
  }
  
  .section-total-item {
    display: flex;
    flex-direction: column;
    padding: 0.5rem;
  }
  
  .section-total-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
  }
  
  .section-total-value {
    font-size: 1.1rem;
    font-weight: 500;
  }
  
  .total {
    grid-column: 1 / -1;
    background-color: #e9ecef;
    padding: 0.75rem;
    border-radius: 4px;
    margin-top: 0.5rem;
  }
  
  .total .section-total-label {
    font-size: 0.9rem;
    font-weight: 500;
  }
  
  .total .section-total-value {
    font-size: 1.3rem;
    font-weight: 700;
    color: #28a745;
  }
  
  .service-sections {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .error-container {
    text-align: center;
    padding: 2rem;
  }
  
  .error-container button {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }
  
  @media (max-width: 768px) {
    .header-actions {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
    
    .quotation-info {
      flex-direction: column;
      gap: 0.5rem;
    }
  }
</style>
