import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/**
 * DELETE handler for removing a quote row
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function DELETE({ params }) {
  try {
    const db = client.db('ServiceContracts');
    const rowId = params.id;
    
    // Validate ObjectId format
    if (!ObjectId.isValid(rowId)) {
      return json({ success: false, message: 'Invalid row ID format' }, { status: 400 });
    }
    
    console.log(`Deleting quote row with ID: ${rowId}`);
    
    // Delete from the QuotationRows collection (using PascalCase per convention)
    const result = await db.collection('QuotationRows').deleteOne({ 
      _id: new ObjectId(rowId) 
    });
    
    if (result.deletedCount === 0) {
      return json({ success: false, message: 'Quote row not found or already deleted' }, { status: 404 });
    }
    
    return json({ 
      success: true, 
      message: 'Quote row deleted successfully',
      deletedCount: result.deletedCount
    });
  } catch (error) {
    console.error('Error deleting quote row:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return json({ success: false, message: errorMessage }, { status: 500 });
  }
}
