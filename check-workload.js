import { MongoClient, ObjectId } from 'mongodb';

async function main() {
  try {
    const uri = 'mongodb://localhost:27017';
    const client = new MongoClient(uri);
    await client.connect();
    
    const db = client.db('ServiceContracts');
    const collection = db.collection('Workload');
    
    const count = await collection.countDocuments();
    console.log(`Workload collection contains ${count} documents`);
    
    if (count > 0) {
      const sample = await collection.find().limit(3).toArray();
      console.log('Sample documents:');
      console.log(JSON.stringify(sample, null, 2));
    }
    
    await client.close();
  } catch (err) {
    console.error('Error:', err);
  }
}

main();
