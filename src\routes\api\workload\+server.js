import { json } from '@sveltejs/kit';
import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';

// Function to recalculate AccHours for a specific computer
async function recalculateAccHours(db, computerId) {
    console.log(`🔄 Recalculating AccHours for computer: ${computerId}`);

    // Get all workload entries for this computer, sorted chronologically
    const workloadEntries = await db.collection('Workload')
        .find({ computerId: new ObjectId(computerId) })
        .sort({ year: 1, month: 1 })
        .toArray();

    let accumulatedHours = 0;
    const updates = [];

    // Calculate accumulated hours for each entry
    for (const entry of workloadEntries) {
        accumulatedHours += entry.hours || 0;

        // Prepare update operation
        updates.push({
            updateOne: {
                filter: { _id: entry._id },
                update: {
                    $set: {
                        accHours: accumulatedHours,
                        updatedAt: new Date()
                    }
                }
            }
        });
    }

    // Execute bulk update if we have updates
    if (updates.length > 0) {
        await db.collection('Workload').bulkWrite(updates);
        console.log(`✅ Updated AccHours for ${updates.length} workload entries`);
    }

    return accumulatedHours;
}

/** @typedef {import('@sveltejs/kit').RequestEvent} RequestEvent */


/**
 * Handles GET requests to fetch workload data for a computer
 * @param {RequestEvent} event - The request event
 * @returns {Promise<Response>} JSON response with workload data
 */
export async function GET(event) {
    const { url } = event;
    const computerId = url.searchParams.get('computerId');
    const workloadAreaId = url.searchParams.get('workloadAreaId');

    if (!computerId || !ObjectId.isValid(computerId)) {
        return json({ success: false, error: 'Invalid computer ID' }, { status: 400 });
    }

    try {
        const client = new MongoClient(uri);
        await client.connect();
        const db = client.db('ServiceContracts');
        const workloadCollection = db.collection('Workload');

        // Build query based on parameters
        const query = {
            computerId: new ObjectId(computerId)
        };

        // If workloadAreaId is provided, filter by it
        if (workloadAreaId && ObjectId.isValid(workloadAreaId)) {
            query.workloadAreaId = new ObjectId(workloadAreaId);
        }

        // Get all workload entries for this computer
        const workloadData = await workloadCollection.find(query)
            .sort({ year: -1, month: -1 }).toArray();

        // Transform data for the grid
        const transformedData = workloadData.map(entry => ({
            id: entry._id.toString(),
            computerId: entry.computerId.toString(),
            workloadAreaId: entry.workloadAreaId ? entry.workloadAreaId.toString() : null,
            year: entry.year,
            month: entry.month,
            hours: entry.hours,
            accHours: entry.accHours || 0,
            activity: entry.activity,
            type: entry.type,
            description: entry.description || '',
            createdAt: entry.createdAt,
            updatedAt: entry.updatedAt
        }));

        return json({ success: true, data: transformedData });
    } catch (error) {
        console.error('Error fetching workload data:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return json({ success: false, error: errorMessage }, { status: 500 });
    }
}


/**
 * Handles POST requests to manage workload entries
 * @param {RequestEvent} event - The request event
 * @returns {Promise<Response>} JSON response
 */
export async function POST(event) {
    const { request } = event;
    const client = new MongoClient(uri);

    try {
        await client.connect();
        const db = client.db('ServiceContracts');
        const workloadCollection = db.collection('Workload');

        // Parse and validate request body
        let data;
        try {
            data = await request.json();
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return json({
                success: false,
                error: 'Invalid JSON payload',
                details: errorMessage
            }, { status: 400 });
        }

        // Extract and validate required fields
        const {
            computerId,
            year,
            month,
            hours,
            activity,
            description,
            type,
            isFixed = false,
            workloadAreaId = null
        } = data;

        // Validate required fields with specific error messages
        const validationErrors = [];
        if (!computerId || !ObjectId.isValid(computerId)) {
            validationErrors.push('Invalid computer ID');
        }

        const numericYear = parseInt(year);
        const numericMonth = parseInt(month);
        const numericHours = parseFloat(hours);

        if (isNaN(numericYear) || numericYear < 2000 || numericYear > 2100) {
            validationErrors.push('Invalid year. Must be between 2000 and 2100');
        }

        if (isNaN(numericMonth) || numericMonth < 1 || numericMonth > 12) {
            validationErrors.push('Invalid month. Must be between 1 and 12');
        }

        if (isNaN(numericHours) || numericHours < 0) {
            validationErrors.push('Hours must be a positive number');
        }

        if (validationErrors.length > 0) {
            return json({
                success: false,
                error: 'Validation failed',
                details: validationErrors
            }, { status: 400 });
        }

        // Check for existing entry first
        const existingEntry = await workloadCollection.findOne({
            computerId: new ObjectId(computerId),
            year: numericYear,
            month: numericMonth
        });

        // Prepare workload data with proper types
        const baseWorkloadData = {
            computerId: new ObjectId(computerId),
            year: numericYear,
            month: numericMonth,
            hours: numericHours,
            activity: activity || 'Contract Service',
            type: type || (isFixed ? 'Fixed' : 'Soft'),
            description: description || '',
            updatedAt: new Date()
        };

        // Add workloadAreaId if provided and valid
        if (workloadAreaId && ObjectId.isValid(workloadAreaId)) {
            baseWorkloadData.workloadAreaId = new ObjectId(workloadAreaId);
        }

        // Create the final workload data object with type assertion
        const workloadData = /** @type {any} */ ({
            ...baseWorkloadData,
            updatedAt: new Date(),
            ...(!existingEntry && { createdAt: new Date() })
        });

        let result;

        if (existingEntry) {
            // Update existing entry
            await workloadCollection.updateOne(
                { _id: existingEntry._id },
                { $set: workloadData }
            );

            // Fetch the updated document
            const updatedDoc = await workloadCollection.findOne({ _id: existingEntry._id });

            if (!updatedDoc) {
                return json({
                    success: false,
                    error: 'Failed to update workload',
                    details: ['Failed to fetch updated workload']
                });
            }

            // Recalculate AccHours for this computer
            await recalculateAccHours(db, computerId);

            // Fetch the updated document again to get the new AccHours
            const finalUpdatedDoc = await workloadCollection.findOne({ _id: existingEntry._id });

            result = {
                success: true,
                action: 'updated',
                data: {
                    id: finalUpdatedDoc._id.toString(),
                    computerId: finalUpdatedDoc.computerId.toString(),
                    workloadAreaId: finalUpdatedDoc.workloadAreaId ? finalUpdatedDoc.workloadAreaId.toString() : null,
                    year: finalUpdatedDoc.year,
                    month: finalUpdatedDoc.month,
                    hours: finalUpdatedDoc.hours,
                    accHours: finalUpdatedDoc.accHours || 0,
                    activity: finalUpdatedDoc.activity,
                    type: finalUpdatedDoc.type,
                    description: finalUpdatedDoc.description || '',
                    updatedAt: finalUpdatedDoc.updatedAt,
                    createdAt: finalUpdatedDoc.createdAt
                }
            };
        } else {
            // Create new entry
            const insertResult = await workloadCollection.insertOne(workloadData);

            // Recalculate AccHours for this computer
            await recalculateAccHours(db, computerId);

            // Fetch the created document with updated AccHours
            const newDoc = await workloadCollection.findOne({ _id: insertResult.insertedId });

            if (!newDoc) {
                return json({
                    success: false,
                    error: 'Failed to create workload',
                    details: ['Failed to fetch created workload']
                });
            }

            result = {
                success: true,
                action: 'created',
                data: {
                    id: newDoc._id.toString(),
                    computerId: newDoc.computerId.toString(),
                    workloadAreaId: newDoc.workloadAreaId ? newDoc.workloadAreaId.toString() : null,
                    year: newDoc.year,
                    month: newDoc.month,
                    hours: newDoc.hours,
                    accHours: newDoc.accHours || 0,
                    activity: newDoc.activity,
                    type: newDoc.type,
                    description: newDoc.description || '',
                    updatedAt: newDoc.updatedAt,
                    createdAt: newDoc.createdAt
                }
            };
        }

        return json(result);
    } catch (error) {
        console.error('Error handling workload operation:', error);
        // Fix the error type issue
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return json({ success: false, error: errorMessage }, { status: 500 });
    } finally {
        await client.close();
    }
}

/**
 * Handles PUT requests to update workload entries
 * @param {RequestEvent} event - The request event
 * @returns {Promise<Response>} JSON response
 */
export async function PUT(event) {
    console.log('PUT request received');
    const { request, params } = event;
    const client = new MongoClient(uri);

    try {
        console.log('Connecting to MongoDB...');
        await client.connect();
        const db = client.db('ServiceContracts');
        const workloadCollection = db.collection('Workload');

        // Log request URL and method
        const url = new URL(request.url);
        console.log('Request URL:', url.toString());
        console.log('Request method:', request.method);

        // Parse request body
        let data;
        try {
            const rawBody = await request.text();
            console.log('Raw request body:', rawBody);
            data = JSON.parse(rawBody);
            console.log('Parsed request data:', JSON.stringify(data, null, 2));
        } catch (error) {
            const parseError = error instanceof Error ? error : new Error(String(error));
            console.error('Error parsing request body:', parseError);
            return json({
                success: false,
                error: 'Invalid request body',
                details: parseError.message
            }, { status: 400 });
        }

        const {
            computerId,
            year,
            month,
            hours,
            activity,
            type,
            description,
            workloadAreaId = null
        } = data;

        // Extract ID from URL if not in params
        const id = params.id || (new URL(request.url).pathname.split('/').pop());
        console.log('Workload ID:', id);

        // Validate required fields
        if (!id || !ObjectId.isValid(id)) {
            const error = `Invalid workload ID: ${id}`;
            console.error(error);
            return json({ success: false, error }, { status: 400 });
        }

        if (!computerId || !ObjectId.isValid(computerId)) {
            const error = `Invalid computer ID: ${computerId}`;
            console.error(error);
            return json({ success: false, error }, { status: 400 });
        }

        if (typeof hours !== 'number') {
            const error = `Hours must be a number, got: ${typeof hours} (${hours})`;
            console.error(error);
            return json({ success: false, error }, { status: 400 });
        }

        // Prepare update data
        const updateData = {
            computerId: new ObjectId(computerId),
            year: parseInt(year),
            month: parseInt(month),
            hours: hours,
            activity: activity || 'Contract Service',
            type: type || 'Soft',
            description: description || '',
            updatedAt: new Date()
        };

        // Add workloadAreaId if provided and valid
        if (workloadAreaId && ObjectId.isValid(workloadAreaId)) {
            updateData.workloadAreaId = new ObjectId(workloadAreaId);
        } else if (workloadAreaId === null) {
            // If explicitly set to null, remove the workloadAreaId
            updateData.$unset = { workloadAreaId: "" };
        }

        console.log('Updating workload with data:', updateData);

        // Update the workload entry
        const updateResult = await workloadCollection.updateOne(
            { _id: new ObjectId(id) },
            { $set: updateData }
        );

        console.log('Update result:', updateResult);

        if (updateResult.matchedCount === 0) {
            const error = `Workload not found with ID: ${id}`;
            console.error(error);
            return json({ success: false, error }, { status: 404 });
        }

        // Recalculate AccHours for this computer
        await recalculateAccHours(db, computerId);

        const responseData = {
            success: true,
            updated: updateResult.modifiedCount,
            id: id
        };

        console.log('Update successful:', responseData);
        return json(responseData);

    } catch (error) {
        console.error('Error updating workload:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return json({ success: false, error: errorMessage }, { status: 500 });
    } finally {
        await client.close();
    }
}
