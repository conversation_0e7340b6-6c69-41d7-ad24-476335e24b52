import { MongoClient } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);
const dbName = 'ServiceContracts';

/**
 * @param {Object} options
 * @param {Object} options.params
 * @param {string} options.params.group - The ProductValidityGroup value to filter by
 */
export async function GET({ params }) {
    try {
        await client.connect();
        const db = client.db(dbName);
        
        // First, find all BaseServices with the given ProductValidityGroup
        const baseServicesCollection = db.collection('BaseServices');
        const baseServices = await baseServicesCollection.find({
            ProductValidityGroup: params.group
        })
        .project({ ServiceCode: 1 })
        .toArray();
        
        // Extract ServiceCode values
        const serviceCodes = baseServices.map(service => service.ServiceCode);
        
        if (serviceCodes.length === 0) {
            return json([]);
        }
        
        // Then, find all ServiceCodes that match these ServiceCodes
        const serviceCodesCollection = db.collection('ServiceCodes');
        const documents = await serviceCodesCollection.find({
            servicecode: { $in: serviceCodes }
        })
        .sort({ servicecode: 1 })
        .toArray();
            
        return json(documents);
    } catch (error) {
        console.error('Error fetching service codes by product validity group:', error);
        return new Response(JSON.stringify({ error: error instanceof Error ? error.message : 'Unknown error' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    } finally {
        await client.close();
    }
}
