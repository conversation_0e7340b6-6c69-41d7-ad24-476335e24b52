import { json } from '@sveltejs/kit';
import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/** @type {import('./$types').RequestHandler} */
export async function GET({ params }) {
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('PartNumbersServiceCodeAction');
    
    // Validate ObjectId format
    if (!ObjectId.isValid(params.id)) {
      return json({ error: 'Invalid ID format' }, { status: 400 });
    }
    
    const item = await collection.findOne({ _id: new ObjectId(params.id) });
    
    if (!item) {
      return json({ error: 'Item not found' }, { status: 404 });
    }
    
    // Convert ObjectId to string for serialization
    const serializedItem = {
      ...item,
      _id: item._id.toString()
    };
    
    return json(serializedItem);
  } catch (error) {
    console.error('Error fetching item:', error);
    return json({ error: 'Failed to fetch item' }, { status: 500 });
  } finally {
    await client.close();
  }
}

/** @type {import('./$types').RequestHandler} */
export async function PUT({ params, request }) {
  try {
    const data = await request.json();
    
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('PartNumbersServiceCodeAction');
    
    // Validate ObjectId format
    if (!ObjectId.isValid(params.id)) {
      return json({ error: 'Invalid ID format' }, { status: 400 });
    }
    
    // Remove _id from the update data if present
    const { _id, ...updateData } = data;
    
    // Add updated timestamp
    updateData.updatedAt = new Date();
    
    const result = await collection.updateOne(
      { _id: new ObjectId(params.id) },
      { $set: updateData }
    );
    
    if (result.matchedCount === 0) {
      return json({ error: 'Item not found' }, { status: 404 });
    }
    
    return json({ 
      success: true, 
      message: 'Item updated successfully' 
    });
  } catch (error) {
    console.error('Error updating item:', error);
    return json({ error: 'Failed to update item' }, { status: 500 });
  } finally {
    await client.close();
  }
}

/** @type {import('./$types').RequestHandler} */
export async function DELETE({ params }) {
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('PartNumbersServiceCodeAction');
    
    // Validate ObjectId format
    if (!ObjectId.isValid(params.id)) {
      return json({ error: 'Invalid ID format' }, { status: 400 });
    }
    
    const result = await collection.deleteOne({ _id: new ObjectId(params.id) });
    
    if (result.deletedCount === 0) {
      return json({ error: 'Item not found' }, { status: 404 });
    }
    
    return json({ 
      success: true, 
      message: 'Item deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting item:', error);
    return json({ error: 'Failed to delete item' }, { status: 500 });
  } finally {
    await client.close();
  }
}
