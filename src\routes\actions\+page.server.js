import { init } from '$lib/db';

/** @type {import('./$types').PageServerLoad} */
export async function load() {
    try {
        const db = await init();
        const collection = db.collection('ServiceCodeAndActionType');
        
        const items = await collection.find({})
            .sort({ ProductValidityGroup: 1 })
            .limit(100)  // Limit initial load to 100 items
            .toArray();

        const safeItems = items.map(item => ({
            _id: item._id.toString(),
            ProductValidityGroup: item.ProductValidityGroup || '',
            ActivityPurpose: item.ActivityPurpose || '',
            ServiceActivityLabel: item.ServiceActivityLabel || '',
            ServiceCode: item.ServiceCode || '',
            ActionType: item.ActionType || '',
            PartNumber: item.PartNumber || 0,
            UnitOfMeasure: item.UnitOfMeasure || '',
            Quantity: item.Quantity || 0,
            InternalNoOfHours: item.InternalNoOfHours || 0,
            InternalNoOfMonths: item.InternalNoOfMonths
        }));

        return {
            items: safeItems,
            formData: {
                productDesignation: '',
                productPartNumber: ''
            }
        };
    } catch (error) {
        console.error('Error in load function:', error);
        return {
            items: [],
            formData: {
                productDesignation: '',
                productPartNumber: ''
            }
        };
    }
}

/** @type {import('./$types').Actions} */
export const actions = {
    default: async ({ request }) => {
        const formData = await request.formData();
        const productDesignation = formData.get('productDesignation')?.toString() || '';
        const productPartNumber = formData.get('productPartNumber')?.toString() || '';

        try {
            const db = await init();
            const pvgCollection = db.collection('ProductValidityGroupPartNumber');
            
            console.log('Searching for:', { productDesignation, productPartNumber });
            
            const pvgDoc = await pvgCollection.findOne({
                ProductDesignation: productDesignation,
                ProductPartNumber: productPartNumber ? parseInt(productPartNumber) : 0
            });

            console.log('Found PVG doc:', pvgDoc);

            if (!pvgDoc) {
                console.log('No PVG doc found');
                return {
                    status: 200,
                    data: {
                        items: [],
                        formData: { productDesignation, productPartNumber }
                    }
                };
            }

            const validityGroup = pvgDoc.ProductValidityGroup;
            const collection = db.collection('ServiceCodeAndActionType');
            
            console.log('Searching in ServiceCodeAndActionType with:', { ProductValidityGroup: validityGroup });
            
            const items = await collection.find({
                ProductValidityGroup: validityGroup
            }).sort({ ProductValidityGroup: 1 }).toArray();

            console.log('Found items:', items.length);

            const safeItems = items.map(item => ({
                _id: item._id.toString(),
                ProductValidityGroup: item.ProductValidityGroup || '',
                ActivityPurpose: item.ActivityPurpose || '',
                ServiceActivityLabel: item.ServiceActivityLabel || '',
                ServiceCode: item.ServiceCode || '',
                ActionType: item.ActionType || '',
                PartNumber: item.PartNumber || 0,
                UnitOfMeasure: item.UnitOfMeasure || '',
                Quantity: item.Quantity || 0,
                InternalNoOfHours: item.InternalNoOfHours || 0,
                InternalNoOfMonths: item.InternalNoOfMonths
            }));

            console.log('Returning items:', safeItems.length);

            return {
                status: 200,
                data: {
                    items: safeItems,
                    formData: { productDesignation, productPartNumber }
                }
            };
        } catch (error) {
            console.error('Error:', error);
            return {
                status: 500,
                data: {
                    items: [],
                    formData: { productDesignation, productPartNumber },
                    error: 'Internal server error'
                }
            };
        }
    }
};
