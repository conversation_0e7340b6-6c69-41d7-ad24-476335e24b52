// Script to rename ProductDesignationFull to ProductDesignationShort in ProductValidityGroup collection
// This script renames the field while preserving all data

import { MongoClient } from 'mongodb';

// Connection URL and Database name
const url = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';
const collectionName = 'ProductValidityGroup';

async function renameProductDesignationField() {
  let client;

  try {
    // Connect to MongoDB
    client = new MongoClient(url);
    await client.connect();
    console.log('Connected to MongoDB server');

    // Get database and collection
    const db = client.db(dbName);
    const collection = db.collection(collectionName);

    // Count documents before update
    const totalDocuments = await collection.countDocuments();
    console.log(`Total documents in ${collectionName}: ${totalDocuments}`);

    // Rename field from ProductDesignationFull to ProductDesignationShort
    const updateResult = await collection.updateMany(
      {}, // Match all documents
      { $rename: { "ProductDesignationFull": "ProductDesignationShort" } }
    );

    console.log(`Renamed field in ${updateResult.modifiedCount} documents`);

    // Verify update by checking a few documents
    const sampleDocs = await collection.find({}).limit(5).toArray();
    console.log('Sample documents after update:');
    sampleDocs.forEach(doc => {
      console.log(`ID: ${doc._id}, ProductDesignation: ${doc.ProductDesignation}, ProductDesignationShort: ${doc.ProductDesignationShort}`);
    });

  } catch (err) {
    console.error('Error updating documents:', err);
  } finally {
    // Close the connection
    if (client) {
      await client.close();
      console.log('MongoDB connection closed');
    }
  }
}

// Run the update function
renameProductDesignationField().catch(console.error);
