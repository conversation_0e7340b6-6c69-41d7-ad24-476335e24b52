<script lang="ts">
  // Props
  export let value: number = 0;
  export let activity: string = '';
  export let isFixed: boolean = false;
  export let isSelected: boolean = false;
  export let hasFaultCode: boolean = false;
  export let isMonth: boolean = true;
  
  // Computed values
  $: hasActivity = !!activity && activity.trim() !== '';
</script>

<div 
  class="workload-cell"
  class:has-activity={hasActivity}
  class:is-fixed={isFixed}
  class:is-selected={isSelected}
  class:has-faultcode={hasFaultCode}
  class:is-month={isMonth}
  on:click
  on:keydown
>
  <div class="cell-content">
    <span class="value">{value}</span>
    {#if hasActivity}
      <span class="activity-indicator" title={activity}>
        {activity.substring(0, 8)}{activity.length > 8 ? '...' : ''}
      </span>
    {/if}
    {#if isFixed}
      <span class="fixed-indicator" title="Fixed schedule">*</span>
    {/if}
    {#if hasFaultCode}
      <span class="faultcode-indicator" title="Has fault codes">!</span>
    {/if}
  </div>
</div>

<style>
  .workload-cell {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #1e293b;
    border-radius: 6px;
    padding: 0.5rem;
    width: 100%;
    height: 100%;
    min-height: 50px;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
  }
  
  .workload-cell:hover {
    background-color: #334155;
  }
  
  .is-selected {
    outline: 2px solid #60a5fa;
    z-index: 1;
  }
  
  .cell-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
  }
  
  .value {
    font-size: 1rem;
    font-weight: 500;
  }
  
  .has-activity {
    background-color: rgba(59, 130, 246, 0.1);
  }
  
  .is-fixed {
    background-color: rgba(59, 130, 246, 0.2);
  }
  
  .has-faultcode::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 8px 8px 0;
    border-color: transparent #ef4444 transparent transparent;
  }
  
  .activity-indicator {
    font-size: 0.75rem;
    color: #94a3b8;
    max-width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  
  .fixed-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    font-size: 0.875rem;
    color: #60a5fa;
  }
  
  .faultcode-indicator {
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 0.75rem;
    color: #ef4444;
    font-weight: bold;
  }
  
  .is-month {
    min-width: 60px;
    height: 60px;
  }
</style>
