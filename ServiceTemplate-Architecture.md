# Service Template Application Architecture

This document provides a comprehensive overview of the Service Template application architecture, including all functions, their relationships, parameters, and call patterns.

## Application Overview

The Service Template application is a SvelteKit-based web application that manages customer computers. It provides CRUD (Create, Read, Update, Delete) functionality for computer records associated with customers. The application uses MongoDB for data storage and follows a modern web architecture with server-side rendering and progressive enhancement.

## System Architecture

```plantuml
@startuml
!theme plain
skinparam linetype ortho

package "Client" {
  [<PERSON>rows<PERSON>] as browser
}

package "SvelteKit Application" {
  [Routes] as routes
  [Components] as components
  [API Endpoints] as api
  [Database Utils] as dbUtils
}

database "MongoDB" {
  [CustomerComputers] as computers
  [Customers] as customers
}

browser --> routes : HTTP Requests
routes --> components : Uses
routes --> dbUtils : Uses
api --> dbUtils : Uses
dbUtils --> computers : Queries/Updates
dbUtils --> customers : Queries/Updates

@enduml
```

## Database Layer

### Connection Management

```plantuml
@startuml
!theme plain

class "db.ts" as db {
  +uri: string
  +client: MongoClient
  +dbName: string
  +init(): Promise<Db>
  +close(): Promise<void>
}

class "mongo.js" as mongo {
  +uri: string
  +dbName: string
  +client: MongoClient
  +db: Db | null
  +connectToDatabase(): Promise<Db>
  +getCollection(collectionName: string): Promise<Collection>
  +serializeDocument(doc: Object): Object
  +serializeDocuments(docs: Array<Object>): Array<Object>
  +isValidObjectId(id: string): boolean
  +toObjectId(id: string): ObjectId
  +toClientDocument(doc: T): T
  +toDatabaseDocument(doc: T): T
  +closeConnection(): Promise<void>
}

db --> mongo : Uses

@enduml
```

### Database Functions

| Function | Parameters | Returns | Description | Called By |
|----------|------------|---------|-------------|-----------|
| `init()` | None | Promise\<Db\> | Initializes MongoDB connection | Application startup |
| `close()` | None | Promise\<void\> | Closes MongoDB connection | Application shutdown |
| `connectToDatabase()` | None | Promise\<Db\> | Connects to MongoDB with caching | `getCollection()` |
| `getCollection(collectionName)` | collectionName: string | Promise\<Collection\> | Gets MongoDB collection | Server load functions, API endpoints |
| `serializeDocument(doc)` | doc: Object | Object | Converts ObjectIds to strings | Server load functions, API endpoints |
| `serializeDocuments(docs)` | docs: Array\<Object\> | Array\<Object\> | Converts ObjectIds in array | Server load functions, API endpoints |
| `isValidObjectId(id)` | id: string | boolean | Validates ObjectId format | Server load functions, API endpoints |
| `toObjectId(id)` | id: string | ObjectId | Converts string to ObjectId | Server load functions, API endpoints |

## Customer Computers Module

```plantuml
@startuml
!theme plain

package "Customer Computers" {
  [List View] as list
  [Detail View] as detail
  [New Computer] as new
  [Computer Form] as form
}

package "API" {
  [Customer Computers API] as api
}

package "Database" {
  [MongoDB] as db
}

list --> form : Uses
detail --> form : Uses
new --> form : Uses
list --> db : Queries/Updates
detail --> db : Queries/Updates
new --> db : Creates
api --> db : Queries

@enduml
```

### Component Structure

```plantuml
@startuml
!theme plain

class "List View" as list {
  +load({ url }): Promise<Object>
  +formatDate(date): string
  +getCustomerName(customerId): string
  +getCustomerType(customerId): string
  +handleCustomerChange(): void
  +selectComputer(computerId): void
  +handleFormResult(result): void
  +actions.addComputer({ request }): Promise<Object>
  +actions.deleteComputer({ request }): Promise<Object>
}

class "Detail View" as detail {
  +load({ params }): Promise<Object>
  +formatDate(date): string
  +formatDateForInput(date): string
  +handleFormResult(result): void
  +toggleEditMode(): void
  +confirmDelete(): void
  +actions.updateComputer({ request, params }): Promise<Object>
  +actions.deleteComputer({ params }): Promise<Object>
}

class "New Computer" as new {
  +load({ url }): Promise<Object>
  +handleFormResult(result): void
  +handleCustomerChange(): void
  +actions.addComputer({ request }): Promise<Object>
}

class "Computer Form" as form {
  +customerId: string
  +editingComputerId: string
  +formData: Object
  +handleCancel(): void
  +dispatch('cancel'): void
}

list --> form : Uses
detail --> form : Uses
new --> form : Uses

@enduml
```

### List View Functions

| Function | Parameters | Returns | Description | Calls | Called By |
|----------|------------|---------|-------------|-------|-----------|
| `load({ url })` | url: URL | Promise\<Object\> | Loads computers and customers | `getCollection()`, `serializeDocument()` | SvelteKit routing |
| `formatDate(date)` | date: Date \| null | string | Formats date for display | None | Template rendering |
| `getCustomerName(customerId)` | customerId: string | string | Gets customer name by ID | None | Template rendering |
| `getCustomerType(customerId)` | customerId: string | string | Gets customer type by ID | None | Template rendering |
| `handleCustomerChange()` | None | void | Updates URL with customer ID | None | Customer dropdown change |
| `selectComputer(computerId)` | computerId: string | void | Navigates to detail page | `goto()` | Computer row click |
| `handleFormResult(result)` | result: Object | void | Processes form result | `goto()` | Form submission callback |
| `actions.addComputer({ request })` | request: Request | Promise\<Object\> | Adds new computer | `getCollection()`, `insertOne()` | Form submission |
| `actions.deleteComputer({ request })` | request: Request | Promise\<Object\> | Deletes computer | `getCollection()`, `deleteOne()` | Delete button click |

### Detail View Functions

| Function | Parameters | Returns | Description | Calls | Called By |
|----------|------------|---------|-------------|-------|-----------|
| `load({ params })` | params: Object | Promise\<Object\> | Loads computer details | `getCollection()`, `aggregate()` | SvelteKit routing |
| `formatDateForInput(date)` | date: Date \| null | string | Formats date for input | None | Form rendering |
| `toggleEditMode()` | None | void | Switches edit/view mode | None | Edit button click |
| `confirmDelete()` | None | void | Shows delete confirmation | None | Delete button click |
| `actions.updateComputer({ request, params })` | request: Request, params: Object | Promise\<Object\> | Updates computer | `getCollection()`, `updateOne()` | Form submission |
| `actions.deleteComputer({ params })` | params: Object | Promise\<Object\> | Deletes computer | `getCollection()`, `deleteOne()` | Delete form submission |

### New Computer Functions

| Function | Parameters | Returns | Description | Calls | Called By |
|----------|------------|---------|-------------|-------|-----------|
| `load({ url })` | url: URL | Promise\<Object\> | Loads customers data | `getCollection()`, `find()` | SvelteKit routing |
| `handleFormResult(result)` | result: Object | void | Processes form result | None | Form submission callback |
| `handleCustomerChange()` | None | void | Updates URL with customer | None | Customer dropdown change |
| `actions.addComputer({ request })` | request: Request | Promise\<Object\> | Adds new computer | `getCollection()`, `insertOne()` | Form submission |

### Computer Form Component

| Function | Parameters | Returns | Description | Calls | Called By |
|----------|------------|---------|-------------|-------|-----------|
| `handleCancel()` | None | void | Handles cancel button | `dispatch('cancel')` | Cancel button click |

## API Endpoints

```plantuml
@startuml
!theme plain

package "API Endpoints" {
  class "GET /api/customer-computers" as getComputers {
    +GET(): Promise<Response>
  }

  class "POST /api/customer-computers/update-schema" as updateSchema {
    +POST(): Promise<Response>
  }
}

package "Database" {
  [MongoDB] as db
}

getComputers --> db : Queries
updateSchema --> db : Updates

@enduml
```

### API Functions

| Endpoint | Method | Parameters | Returns | Description | Calls | Called By |
|----------|--------|------------|---------|-------------|-------|-----------|
| `/api/customer-computers` | GET | None | Promise\<Response\> | Returns all computers | `getCollection()`, `aggregate()` | Client-side fetch |
| `/api/customer-computers/update-schema` | POST | None | Promise\<Response\> | Updates schema | `getCollection()`, `updateOne()` | Admin tools |

## Data Flow

```plantuml
@startuml
!theme plain

actor User
boundary "Browser UI" as ui
control "SvelteKit Server" as server
entity "MongoDB" as db

== Create Computer ==
User -> ui : Fill form and submit
ui -> server : POST /customer-computers/new
server -> db : insertOne(computerData)
db -> server : insertedId
server -> ui : Redirect to detail page
ui -> User : Show success

== Read Computers ==
User -> ui : Navigate to list view
ui -> server : GET /customer-computers
server -> db : aggregate(pipeline)
db -> server : computers[]
server -> ui : Render with data
ui -> User : Display computers

== Update Computer ==
User -> ui : Edit form and submit
ui -> server : POST /customer-computers/[id]
server -> db : updateOne(query, update)
db -> server : updateResult
server -> ui : Success response
ui -> User : Show updated data

== Delete Computer ==
User -> ui : Click delete and confirm
ui -> server : POST /customer-computers/[id]?/deleteComputer
server -> db : deleteOne(query)
db -> server : deleteResult
server -> ui : Redirect to list
ui -> User : Show list view

@enduml
```

## Database Schema

```plantuml
@startuml
!theme plain

entity "Customers" as customers {
  * _id : ObjectId
  --
  * companyName : string
  * name : string
  * type : string
  * city : string
  * country : string
  * email : string
  * phone : string
}

entity "CustomerComputers" as computers {
  * _id : ObjectId
  --
  * customerId : ObjectId <<FK>>
  * name : string
  * serialNumber : string
  * model : string
  * productType : string
  * productDesignation : string
  * engineType : string
  * operatingHours : number
  * installationDate : Date
  * manufactureDate : Date
  * isActive : boolean
  * notes : string
  * createdAt : Date
  * updatedAt : Date
}

customers ||--o{ computers : has

@enduml
```

## Function Call Examples

### Creating a New Computer

```javascript
// 1. Client: User selects customer and fills form
// 2. Client: Form is submitted with enhance
<form method="POST" action="?/addComputer" use:enhance={...}>

// 3. Server: Form data is processed in action
export const actions = {
  addComputer: async ({ request }) => {
    // Get form data
    const formData = await request.formData();
    const customerId = formData.get('customerId')?.toString();
    const name = formData.get('name')?.toString();
    // ... other fields ...

    // Validate data
    if (!customerId || !name) {
      return { success: false, error: 'Missing required fields' };
    }

    // Get collection and insert document
    const collection = await getCollection('CustomerComputers');
    const result = await collection.insertOne({
      customerId: toObjectId(customerId),
      name,
      // ... other fields ...
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Redirect to detail page
    throw redirect(303, `/customer-computers/${result.insertedId.toString()}`);
  }
};

// 4. Client: Redirected to detail page
```

### Updating a Computer

```javascript
// 1. Client: User clicks Edit button
function toggleEditMode() {
  isEditing = !isEditing;

  if (isEditing) {
    // Reset form data to current computer values
    formData = {
      name: computer.name || '',
      serialNumber: computer.serialNumber || '',
      // ... other fields ...
    };
  }
}

// 2. Client: User edits form and submits
<form method="POST" action="?/updateComputer" use:enhance={() => {
  isSubmitting = true;
  return async ({ result }) => {
    handleFormResult(result.data);
  };
}}>

// 3. Server: Form data is processed in action
export const actions = {
  updateComputer: async ({ request, params }) => {
    const { id } = params;
    const formData = await request.formData();

    // Create update data object
    const updateData = {
      name: formData.get('name')?.toString(),
      serialNumber: formData.get('serialNumber')?.toString(),
      // ... other fields ...
      updatedAt: new Date()
    };

    // Update the computer
    const collection = await getCollection('CustomerComputers');
    const result = await collection.updateOne(
      { _id: toObjectId(id) },
      { $set: updateData }
    );

    return {
      success: true,
      message: 'Computer updated successfully'
    };
  }
};

// 4. Client: Process result and update UI
function handleFormResult(result) {
  isSubmitting = false;

  if (result.success) {
    successMessage = result.message || 'Operation completed successfully';
    isEditing = false;

    // Reload the page to refresh the data
    window.location.reload();
  } else {
    formError = result.error || 'An error occurred';
  }
}
```

### Deleting a Computer

```javascript
// 1. Client: User clicks Delete button
function confirmDelete() {
  if (confirm('Are you sure you want to delete this computer? This action cannot be undone.')) {
    document.getElementById('delete-form')?.requestSubmit();
  }
}

// 2. Client: Hidden form is submitted
<form id="delete-form" method="POST" action="?/deleteComputer" use:enhance={...}>
  <!-- No form fields needed -->
</form>

// 3. Server: Process deletion
export const actions = {
  deleteComputer: async ({ params }) => {
    const { id } = params;

    // Delete the computer
    const collection = await getCollection('CustomerComputers');
    const result = await collection.deleteOne({
      _id: toObjectId(id)
    });

    if (result.deletedCount === 0) {
      return {
        success: false,
        error: 'Computer not found or could not be deleted'
      };
    }

    return {
      success: true,
      message: 'Computer deleted successfully',
      redirect: '/customer-computers'
    };
  }
};

// 4. Client: Redirect to list view
// handleFormResult processes the response and redirects
```

## Conclusion

The Service Template application follows a clean architecture with clear separation of concerns:

1. **Client-side components** handle user interactions and UI state
2. **Server-side load functions** retrieve and prepare data for rendering
3. **Server-side actions** process form submissions and update the database
4. **API endpoints** provide data access for other parts of the application
5. **Database utilities** abstract database operations and handle data transformation

This architecture makes the application maintainable, scalable, and easy to understand. The use of SvelteKit's form actions with progressive enhancement ensures good user experience even with slower connections.
