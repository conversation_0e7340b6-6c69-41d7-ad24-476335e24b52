import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function populatePartNumbersStandard() {
    try {
        await client.connect();
        console.log('Connected to MongoDB');

        const db = client.db('ServiceContracts');
        const sourceCollection = db.collection('PartNumbersServiceCodeAction');
        const targetCollection = db.collection('PartNumbersStandard');

        // Get all unique part numbers with their details
        const uniquePartNumbers = await sourceCollection.aggregate([
            {
                $match: {
                    PartNumber: { $ne: null }  // Only get records with part numbers
                }
            },
            {
                $group: {
                    _id: '$PartNumber',
                    details: {
                        $first: {
                            PartNumber: '$PartNumber',
                            ProductValidityGroup: '$ProductValidityGroup',
                            UnitOfMeasure: '$UnitOfMeasure'
                        }
                    },
                    usageCount: { $sum: 1 }  // Count how many times this part number is used
                }
            }
        ]).toArray();

        console.log(`Found ${uniquePartNumbers.length} unique part numbers`);

        // Clear existing data in target collection
        await targetCollection.deleteMany({});
        console.log('Cleared existing data in PartNumbersStandard collection');

        if (uniquePartNumbers.length > 0) {
            // Insert the unique part numbers with their details
            const documents = uniquePartNumbers.map(item => ({
                ...item.details,
                usageCount: item.usageCount,
                createdAt: new Date(),
                updatedAt: new Date()
            }));

            const result = await targetCollection.insertMany(documents);
            console.log(`Successfully inserted ${result.insertedCount} documents`);
        }

        // Create indexes for better query performance
        await targetCollection.createIndex({ PartNumber: 1 }, { unique: true });
        await targetCollection.createIndex({ ProductValidityGroup: 1 });
        console.log('Created indexes on PartNumbersStandard collection');

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await client.close();
        console.log('Disconnected from MongoDB');
    }
}

// Run the population script
populatePartNumbersStandard();
