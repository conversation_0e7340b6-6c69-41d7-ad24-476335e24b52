<!-- FormGrid.svelte -->
<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  
  // Props for form layout configuration
  export let labelWidth: string = '200px';
  export let inputWidth: string = '1fr';
  export let rowGap: string = '1rem';
</script>

<BaseGrid 
  columns="{labelWidth} {inputWidth}"
  gap={rowGap}
  padding="1rem"
  className="form-grid"
>
  <slot />
</BaseGrid>

<style>
  :global(.form-grid) {
    max-width: 800px;
    margin: 0 auto;
  }

  :global(.form-grid label) {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 1rem;
  }

  :global(.form-grid input, .form-grid select, .form-grid textarea) {
    width: 100%;
  }
</style>
