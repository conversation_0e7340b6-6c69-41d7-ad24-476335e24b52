import { MongoClient, ObjectId } from 'mongodb';
import { error } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017/';
const dbName = 'ServiceContracts';

// Valid customer types based on memory requirement
const CUSTOMER_TYPES = ['OEM Importer', 'Fleet Owner', 'Retail'];
const DEFAULT_CUSTOMER_TYPE = 'Retail';

/** @type {import('mongodb').MongoClient | null} */
let mongoClient = null;

/** @returns {Promise<import('mongodb').MongoClient>} */
async function getMongoClient() {
  if (!mongoClient) {
    mongoClient = new MongoClient(uri);
    await mongoClient.connect();
  }
  return mongoClient;
}

/**
 * Helper function to convert MongoDB documents to serializable format
 * @param {any} doc - Document to convert
 * @returns {any} Serialized document
 */
function convertToSerializable(doc) {
  if (!doc) return null;
  
  if (doc instanceof ObjectId) {
    return doc.toString();
  } else if (doc instanceof Date) {
    return doc.toISOString();
  } else if (Array.isArray(doc)) {
    return doc.map(item => convertToSerializable(item));
  } else if (doc && typeof doc === 'object') {
    return Object.fromEntries(
      Object.entries(doc).map(([key, value]) => [key, convertToSerializable(value)])
    );
  }
  return doc;
}

/** @type {import('@sveltejs/kit').PageServerLoad} */
export async function load({ params }) {
  let client;
  
  try {
    const { id } = params;

    // Validate ObjectId format as per memory requirement
    if (!id || !ObjectId.isValid(id)) {
      throw error(404, {
        message: 'Invalid customer ID format'
      });
    }

    client = await getMongoClient();
    const db = client.db(dbName);
    const customerId = new ObjectId(id);  // Convert to ObjectId for querying

    // Get customer details
    const customer = await db.collection('Customers').findOne({
      _id: customerId
    });

    if (!customer) {
      throw error(404, {
        message: 'Customer not found'
      });
    }

    // Get customer computers with populated data
    const customerComputers = await db.collection('CustomerComputers')
      .find({ customerId: new ObjectId(id) })
      .toArray();

    // Get labour times for each computer category
    const labourTimes = await db.collection('LabourTime')
      .find({
        ComputerCategory: {
          $in: customerComputers.map(/** @param {any} comp */ comp => comp.type)
        }
      })
      .toArray();

    return {
      customer: convertToSerializable(customer),
      customerComputers: convertToSerializable(customerComputers),
      customerTypes: CUSTOMER_TYPES,
      labourTimes: convertToSerializable(labourTimes)
    };
  } catch (err) {
    console.error('Error loading customer computers:', err);
    throw error(500, {
      message: 'Failed to load customer computers'
    });
  }
}

/**
 * Actions for customer computers
 * @type {import('@sveltejs/kit').Actions}
 */
export const actions = {
  /**
   * Add a new customer computer
   * @param {import('@sveltejs/kit').ActionData} data
   * @returns {Promise<{ success: boolean, error?: string, computer?: any }>}
   */
  add: async ({ request, params }) => {
    let client;
    
    try {
      const { id } = params;
      // Validate customer ObjectId format as per memory requirement
      if (!id || !ObjectId.isValid(id)) {
        return {
          success: false,
          error: 'Invalid customer ID format'
        };
      }

      const formData = await request.formData();
      const customerId = new ObjectId(id);  // Convert to native ObjectId for storage

      // Validate required fields
      const name = formData.get('name')?.trim();
      const type = formData.get('type')?.trim();
      
      if (!name) {
        return {
          success: false,
          error: 'Computer name is required'
        };
      }
      
      if (!type) {
        return {
          success: false,
          error: 'Computer type is required'
        };
      }

      // Prepare computer data with proper ObjectId handling
      const computerData = {
        customerId, // Store as native ObjectId as per memory requirement
        name,
        type,
        serialNumber: formData.get('serialNumber')?.trim() || '',
        model: formData.get('model')?.trim() || '',
        manufacturer: formData.get('manufacturer')?.trim() || '',
        operatingSystem: formData.get('operatingSystem')?.trim() || '',
        notes: formData.get('notes')?.trim() || '',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Handle date fields with proper validation
      const purchaseDate = formData.get('purchaseDate');
      if (purchaseDate) {
        const date = new Date(purchaseDate);
        if (isNaN(date.getTime())) {
          return {
            success: false,
            error: 'Invalid purchase date format'
          };
        }
        computerData.purchaseDate = date;
      }

      const warrantyEndDate = formData.get('warrantyEndDate');
      if (warrantyEndDate) {
        const date = new Date(warrantyEndDate);
        if (isNaN(date.getTime())) {
          return {
            success: false,
            error: 'Invalid warranty end date format'
          };
        }
        computerData.warrantyEndDate = date;
      }

      client = await getMongoClient();
      const db = client.db(dbName);

      // Verify customer exists
      const customerExists = await db.collection('Customers').findOne({
        _id: customerId
      });

      if (!customerExists) {
        return {
          success: false,
          error: 'Customer not found'
        };
      }
      
      // Insert document with native ObjectId
      const result = await db.collection('CustomerComputers').insertOne(computerData);
      
      if (!result.acknowledged) {
        return {
          success: false,
          error: 'Failed to create computer in database'
        };
      }

      // Convert ObjectIds to strings for client-side use
      const newComputer = {
        ...computerData,
        _id: result.insertedId.toString(),
        customerId: customerId.toString()
      };

      return {
        success: true,
        computer: newComputer
      };
    } catch (err) {
      console.error('Error creating customer computer:', err);
      return {
        success: false,
        error: err.message || 'Failed to create computer. Please try again.'
      };
    }
  },

  /**
   * Delete a customer computer
   * @param {import('@sveltejs/kit').ActionData} data
   * @returns {Promise<{ success: boolean, error?: string }>}
   */
  delete: async ({ request, params }) => {
    let client;
    
    try {
      const { id } = params;
      const formData = await request.formData();
      const computerId = formData.get('computerId');

      // Validate ObjectIds as per memory requirement
      if (!id || !ObjectId.isValid(id) || !computerId || !ObjectId.isValid(computerId)) {
        return {
          success: false,
          error: 'Invalid ID format'
        };
      }

      client = await getMongoClient();
      const db = client.db(dbName);
      
      // Use native ObjectIds for querying
      const result = await db.collection('CustomerComputers').deleteOne({
        _id: new ObjectId(computerId),
        customerId: new ObjectId(id)
      });

      if (!result.deletedCount) {
        return {
          success: false,
          error: 'Computer not found or already deleted'
        };
      }

      return {
        success: true
      };
    } catch (err) {
      console.error('Error deleting customer computer:', err);
      return {
        success: false,
        error: err.message || 'Failed to delete computer'
      };
    }
  }
};
