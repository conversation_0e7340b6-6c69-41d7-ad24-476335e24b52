const { MongoClient } = require('mongodb');

async function updateFieldName() {
    const uri = 'mongodb://localhost:27017';
    const client = new MongoClient(uri);

    try {
        await client.connect();
        console.log('Connected to MongoDB');
        
        const db = client.db('ServiceContracts');
        const collection = db.collection('ProductValidityGroup');

        // First, let's check what documents we have
        const beforeCount = await collection.countDocuments();
        console.log(`Total documents before update: ${beforeCount}`);

        // Check for documents with the old field name
        const oldFieldDocs = await collection.find({ "Product Validity GRoup": { $exists: true } }).toArray();
        console.log(`Documents with old field name: ${oldFieldDocs.length}`);

        if (oldFieldDocs.length > 0) {
            // Update documents one by one to ensure proper conversion
            for (const doc of oldFieldDocs) {
                const updateResult = await collection.updateOne(
                    { _id: doc._id },
                    {
                        $set: { "ProductValidityGroup": doc["Product Validity GRoup"] },
                        $unset: { "Product Validity GRoup": "" }
                    }
                );
                console.log(`Updated document ${doc._id}: ${updateResult.modifiedCount} modified`);
            }
        }

        // Verify the changes
        const afterDocs = await collection.find().limit(5).toArray();
        console.log('\nSample documents after update:');
        afterDocs.forEach(doc => {
            console.log(JSON.stringify(doc, null, 2));
        });

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await client.close();
        console.log('Disconnected from MongoDB');
    }
}

updateFieldName().catch(console.error);
