// Migration script: Rename StartDateTime to ReportDateTime in ActualComputerHours
// Usage: node src/lib/scripts/migrate-actual-computer-hours-report-date.js

import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function migrate() {
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('ActualComputerHours');

    // Copy StartDateTime to ReportDateTime and remove StartDateTime
    const result = await collection.updateMany(
      { StartDateTime: { $exists: true } },
      [
        { $set: { ReportDateTime: "$StartDateTime" } },
        { $unset: "StartDateTime" }
      ]
    );
    console.log(`Migrated ${result.modifiedCount} documents.`);
  } catch (err) {
    console.error('Migration error:', err);
  } finally {
    await client.close();
  }
}

migrate();
