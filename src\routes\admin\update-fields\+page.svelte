<script>
  import { onMount } from 'svelte';
  
  let updateStatus = '';
  let isUpdating = false;
  let results = null;
  
  async function updateFields() {
    try {
      isUpdating = true;
      updateStatus = 'Updating fields...';
      
      const response = await fetch('/api/update-quotation-fields');
      results = await response.json();
      
      if (results.success) {
        updateStatus = 'Update completed successfully!';
      } else {
        updateStatus = `Error: ${results.message || 'Unknown error'}`;
      }
    } catch (error) {
      updateStatus = `Error: ${error.message || 'Unknown error'}`;
      console.error('Error updating fields:', error);
    } finally {
      isUpdating = false;
    }
  }
</script>

<div class="container">
  <h1>Database Field Update Tool</h1>
  
  <div class="card">
    <div class="card-header">
      <h2>Update QuotationLines Collection</h2>
    </div>
    
    <div class="card-body">
      <p>
        This tool will update the QuotationLines collection to change all instances of:
      </p>
      
      <ul>
        <li><code>activity</code> → <code>ServiceActivity</code></li>
        <li><code>Activity</code> → <code>ServiceActivity</code></li>
        <li><code>serviceActivity</code> → <code>ServiceActivity</code></li>
      </ul>
      
      <div class="button-container">
        <button 
          class="update-button" 
          on:click={updateFields} 
          disabled={isUpdating}
        >
          {isUpdating ? 'Updating...' : 'Update Fields'}
        </button>
      </div>
      
      {#if updateStatus}
        <div class="status-message {results?.success ? 'success' : 'error'}">
          <p>{updateStatus}</p>
        </div>
      {/if}
      
      {#if results && results.success}
        <div class="results">
          <h3>Update Results:</h3>
          <ul>
            <li>Documents with <code>activity</code> updated: {results.activityUpdated}</li>
            <li>Documents with <code>Activity</code> updated: {results.ActivityUpdated}</li>
            <li>Documents with <code>serviceActivity</code> updated: {results.serviceActivityUpdated}</li>
          </ul>
        </div>
      {/if}
    </div>
  </div>
</div>

<style>
  /* Using CSS Grid for layout as per user preference */
  .container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    max-width: 800px;
    margin: 2rem auto;
    padding: 0 1rem;
  }
  
  .card {
    display: grid;
    grid-template-rows: auto 1fr;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .card-header {
    background-color: #f5f5f5;
    padding: 1rem;
    border-bottom: 1px solid #ddd;
  }
  
  .card-body {
    padding: 1.5rem;
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  h1 {
    margin: 0;
    font-size: 2rem;
    color: #333;
  }
  
  h2 {
    margin: 0;
    font-size: 1.5rem;
    color: #444;
  }
  
  .button-container {
    display: grid;
    justify-content: center;
  }
  
  .update-button {
    background-color: #4a76a8;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .update-button:hover:not(:disabled) {
    background-color: #3a5f8a;
  }
  
  .update-button:disabled {
    background-color: #a0a0a0;
    cursor: not-allowed;
  }
  
  .status-message {
    padding: 1rem;
    border-radius: 4px;
    text-align: center;
  }
  
  .status-message.success {
    background-color: #e8f5e9;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
  }
  
  .status-message.error {
    background-color: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
  }
  
  .results {
    background-color: #f5f5f5;
    padding: 1rem;
    border-radius: 4px;
  }
  
  ul {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
  }
  
  code {
    background-color: #f1f1f1;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-family: monospace;
  }
</style>
