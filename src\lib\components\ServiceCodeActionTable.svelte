<script>
  export let productValidityGroup = '';
  export let serviceItems = {};
  export let totals = {};
  export let dataSource = {};

  // Calculate overall totals
  $: totalServices = Object.values(serviceItems).reduce((total, services) => total + (services?.length || 0), 0);
  $: totalCost = Object.values(serviceItems).reduce((total, services) => {
    return total + (services?.reduce((sum, service) => sum + (service.cost || 0), 0) || 0);
  }, 0);
  $: totalHours = Object.values(serviceItems).reduce((total, services) => {
    return total + (services?.reduce((sum, service) => sum + (service.hours || 0), 0) || 0);
  }, 0);

  // Format currency
  function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount || 0);
  }

  // Format hours
  function formatHours(hours) {
    return `${hours || 0}`;
  }
</script>

<div class="service-code-action-container">
  <h2>Service Codes and Action Types</h2>
  <p class="product-validity-info">
    Based on Computer's Product Validity Group: <strong>{productValidityGroup}</strong>
  </p>

  <!-- Service Items Table -->
  <div class="table-container">
    <table class="service-table">
      <thead>
        <tr>
          <th>Service Code</th>
          <th>Action Type</th>
          <th>Activity Purpose</th>
          <th>Service Activity Label</th>
          <th>Part Number</th>
          <th>Hours</th>
          <th>Cost</th>
        </tr>
      </thead>
      <tbody>
        {#each Object.entries(serviceItems) as [label, items]}
          {#each items as item}
            <tr class="service-row" class:level-s={label === 'S'} class:level-a={label === 'A'} class:level-b={label === 'B'} class:level-c={label === 'C'} class:level-d={label === 'D'} class:level-e={label === 'E'}>
              <td class="service-code">{item.serviceCode}</td>
              <td class="action-type">{item.actionType}</td>
              <td class="activity-purpose">{item.activityPurpose}</td>
              <td class="service-label">{item.serviceActivityLabel}</td>
              <td class="part-number">{item.partNumber || ''}</td>
              <td class="hours">{formatHours(item.hours)}</td>
              <td class="cost">{formatCurrency(item.cost)}</td>
            </tr>
          {/each}
        {/each}
      </tbody>
    </table>
  </div>

  <!-- Summary Section -->
  <div class="summary-section">
    <h3>Summary by Service Activity Label</h3>
    <div class="summary-grid">
      {#each Object.entries(totals) as [levelKey, levelData]}
        <div class="summary-card" class:has-items={levelData.count > 0}>
          <div class="summary-header">Level {levelKey.replace('level', '')}</div>
          <div class="summary-stats">
            <div class="stat">
              <span class="stat-label">Items:</span>
              <span class="stat-value">{levelData.count}</span>
            </div>
            <div class="stat">
              <span class="stat-label">Hours:</span>
              <span class="stat-value">{formatHours(levelData.totalHours)}</span>
            </div>
            <div class="stat">
              <span class="stat-label">Cost:</span>
              <span class="stat-value">{formatCurrency(levelData.totalCost)}</span>
            </div>
          </div>
        </div>
      {/each}
    </div>
  </div>

  <!-- Overall Totals -->
  <div class="totals-section">
    <h3>Overall Totals</h3>
    <div class="totals-grid">
      <div class="total-item">
        <span class="total-label">Total Services:</span>
        <span class="total-value">{totalServices}</span>
      </div>
      <div class="total-item">
        <span class="total-label">Total Hours:</span>
        <span class="total-value">{formatHours(totalHours)}</span>
      </div>
      <div class="total-item">
        <span class="total-label">Total Cost:</span>
        <span class="total-value">{formatCurrency(totalCost)}</span>
      </div>
    </div>
  </div>

  <!-- Data Source Information -->
  <div class="data-source-section">
    <h3>Data Sources</h3>
    <div class="data-source-info">
      <div class="source-item">
        <span class="source-label">ServiceCodeAndActionType items:</span>
        <span class="source-value">{dataSource.serviceCodeAndActionType || 0}</span>
      </div>
      <div class="source-item">
        <span class="source-label">ServiceID prices:</span>
        <span class="source-value">{dataSource.serviceIDPrices || 0}</span>
      </div>
      <div class="source-item">
        <span class="source-label">PriceList prices:</span>
        <span class="source-value">{dataSource.priceListPrices || 0}</span>
      </div>
    </div>
  </div>
</div>

<style>
  .service-code-action-container {
    padding: 1rem;
    max-width: 1400px;
    margin: 0 auto;
  }

  .product-validity-info {
    margin-bottom: 1rem;
    padding: 0.5rem;
    background-color: #f0f8ff;
    border-left: 4px solid #007bff;
    font-size: 0.9rem;
  }

  .table-container {
    overflow-x: auto;
    margin-bottom: 2rem;
  }

  .service-table {
    width: 100%;
    border-collapse: collapse;
    border: 2px solid #333;
    font-size: 0.9rem;
  }

  .service-table th {
    background-color: #f5f5f5;
    padding: 0.75rem 0.5rem;
    border: 1px solid #333;
    text-align: left;
    font-weight: bold;
    white-space: nowrap;
  }

  .service-table td {
    padding: 0.5rem;
    border: 1px solid #333;
    text-align: left;
  }

  .service-row:hover {
    background-color: #f9f9f9;
  }

  /* Color coding for different service activity labels */
  .level-s { background-color: #fff3cd; }
  .level-a { background-color: #d4edda; }
  .level-b { background-color: #cce5ff; }
  .level-c { background-color: #f8d7da; }
  .level-d { background-color: #e2e3e5; }
  .level-e { background-color: #fff0f5; }

  .service-code {
    font-weight: bold;
    color: #007bff;
  }

  .service-label {
    font-weight: bold;
    text-align: center;
  }

  .cost {
    text-align: right;
    font-weight: bold;
  }

  .hours {
    text-align: right;
  }

  .summary-section {
    margin-bottom: 2rem;
  }

  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
  }

  .summary-card {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 1rem;
    background-color: #f9f9f9;
  }

  .summary-card.has-items {
    background-color: #e8f5e8;
    border-color: #28a745;
  }

  .summary-header {
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    text-align: center;
  }

  .summary-stats {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .stat {
    display: flex;
    justify-content: space-between;
  }

  .stat-label {
    font-weight: normal;
  }

  .stat-value {
    font-weight: bold;
  }

  .totals-section {
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 4px;
  }

  .totals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
  }

  .total-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem;
    background-color: white;
    border-radius: 4px;
    border: 1px solid #ddd;
  }

  .total-label {
    font-weight: normal;
  }

  .total-value {
    font-weight: bold;
    color: #007bff;
  }

  .data-source-section {
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #ddd;
  }

  .data-source-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.5rem;
    margin-top: 1rem;
  }

  .source-item {
    display: flex;
    justify-content: space-between;
    padding: 0.25rem 0;
  }

  .source-label {
    font-weight: normal;
  }

  .source-value {
    font-weight: bold;
    color: #28a745;
  }

  @media (max-width: 768px) {
    .service-code-action-container {
      padding: 0.5rem;
    }
    
    .service-table {
      font-size: 0.8rem;
    }
    
    .service-table th,
    .service-table td {
      padding: 0.25rem;
    }
  }
</style>
