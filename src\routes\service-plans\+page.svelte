<script>
  /** @type {import('./$types').PageData} */
  export let data;
  
  // Format date for display
  function formatDate(dateStr) {
    return new Date(dateStr).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
  
  // View service plan
  function viewServicePlan(computerId, productDesignation) {
    window.open(`/service-plan-report/${computerId}?productDesignation=${productDesignation}`, '_blank');
  }
  
  // Delete service plan
  async function deleteServicePlan(planId, computerName) {
    if (!confirm(`Are you sure you want to delete the service plan for ${computerName}?`)) {
      return;
    }
    
    try {
      const response = await fetch(`/api/computer-service-plan?planId=${planId}`, {
        method: 'DELETE'
      });
      
      const result = await response.json();
      
      if (result.success) {
        alert('Service plan deleted successfully!');
        window.location.reload();
      } else {
        alert(`Failed to delete service plan: ${result.error}`);
      }
    } catch (error) {
      console.error('Error deleting service plan:', error);
      alert('Failed to delete service plan. Please try again.');
    }
  }
</script>

<svelte:head>
  <title>Saved Service Plans</title>
</svelte:head>

<div class="service-plans-container">
  <div class="header">
    <button class="back-button" on:click={() => history.back()}>
      ← Back
    </button>
    <h1>Saved Service Plans</h1>
    <div class="header-stats">
      <span class="stat">{data.servicePlans.length} Plans</span>
    </div>
  </div>

  {#if data.error}
    <div class="error-message">
      <h2>❌ Error</h2>
      <p>{data.error}</p>
    </div>
  {:else if data.servicePlans.length === 0}
    <div class="no-plans">
      <div class="no-plans-content">
        <h2>📋 No Service Plans Found</h2>
        <p>No service plans have been saved yet.</p>
        <p>Generate a service plan from the workload display page to see it here.</p>
      </div>
    </div>
  {:else}
    <div class="plans-grid">
      {#each data.servicePlans as plan}
        <div class="plan-card">
          <div class="plan-header">
            <h3>{plan.computerName}</h3>
            <div class="plan-id">ID: {plan.computerId}</div>
          </div>
          
          <div class="plan-details">
            <div class="detail-row">
              <span class="label">Product:</span>
              <span class="value">{plan.productDesignation}</span>
            </div>
            <div class="detail-row">
              <span class="label">Services:</span>
              <span class="value">{plan.totalServices.toLocaleString()}</span>
            </div>
            <div class="detail-row">
              <span class="label">Total Hours:</span>
              <span class="value">{plan.totalHours.toLocaleString()}</span>
            </div>
            <div class="detail-row">
              <span class="label">Generated:</span>
              <span class="value">{formatDate(plan.generatedAt)}</span>
            </div>
            <div class="detail-row">
              <span class="label">Saved:</span>
              <span class="value">{formatDate(plan.savedAt)}</span>
            </div>
            <div class="detail-row">
              <span class="label">Source:</span>
              <span class="value">{plan.dataSource}</span>
            </div>
          </div>
          
          <div class="plan-actions">
            <button 
              class="action-btn view-btn"
              on:click={() => viewServicePlan(plan.computerId, plan.productDesignation)}
            >
              👁️ View Plan
            </button>
            <button 
              class="action-btn delete-btn"
              on:click={() => deleteServicePlan(plan._id, plan.computerName)}
            >
              🗑️ Delete
            </button>
          </div>
        </div>
      {/each}
    </div>
  {/if}
</div>

<style>
  .service-plans-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e2e8f0;
  }

  .header h1 {
    margin: 0;
    color: #1e293b;
    font-size: 2rem;
    font-weight: 700;
  }

  .back-button {
    padding: 0.5rem 1rem;
    background: #1e40af;
    color: white;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
  }

  .back-button:hover {
    background: #1e3a8a;
  }

  .header-stats {
    display: flex;
    gap: 1rem;
  }

  .stat {
    background: #f1f5f9;
    color: #475569;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    font-size: 0.875rem;
  }

  .error-message {
    background: #fef2f2;
    border: 1px solid #fca5a5;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .error-message h2 {
    color: #dc2626;
    margin: 0 0 1rem 0;
  }

  .no-plans {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }

  .no-plans-content {
    text-align: center;
    color: #64748b;
  }

  .no-plans-content h2 {
    margin: 0 0 1rem 0;
    font-size: 1.5rem;
  }

  .plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 1.5rem;
  }

  .plan-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s;
  }

  .plan-card:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  .plan-header {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f1f5f9;
  }

  .plan-header h3 {
    margin: 0 0 0.5rem 0;
    color: #1e293b;
    font-size: 1.25rem;
    font-weight: 600;
  }

  .plan-id {
    color: #64748b;
    font-size: 0.875rem;
    font-family: monospace;
  }

  .plan-details {
    margin-bottom: 1.5rem;
  }

  .detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8fafc;
  }

  .detail-row:last-child {
    border-bottom: none;
  }

  .label {
    font-weight: 500;
    color: #64748b;
    font-size: 0.875rem;
  }

  .value {
    font-weight: 600;
    color: #1e293b;
    font-size: 0.875rem;
    text-align: right;
  }

  .plan-actions {
    display: flex;
    gap: 0.75rem;
  }

  .action-btn {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .view-btn {
    background: #1e40af;
    color: white;
  }

  .view-btn:hover {
    background: #1e3a8a;
  }

  .delete-btn {
    background: #dc2626;
    color: white;
  }

  .delete-btn:hover {
    background: #b91c1c;
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .service-plans-container {
      padding: 1rem;
    }
    
    .header {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;
    }
    
    .plans-grid {
      grid-template-columns: 1fr;
    }
    
    .plan-actions {
      flex-direction: column;
    }
  }
</style>
