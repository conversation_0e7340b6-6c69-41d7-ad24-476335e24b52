// MongoDB script to update all ActualComputerHours documents to make hours an integer
db = db.getSiblingDB('ServiceContracts');

// Find all documents in ActualComputerHours collection
let result = db.ActualComputerHours.find().toArray();
console.log(`Found ${result.length} documents in ActualComputerHours collection`);

// Update each document to convert hours to integer
let updateCount = 0;
result.forEach(doc => {
  // Convert hours to integer if it's not already
  if (doc.hours !== undefined) {
    let hoursInt = parseInt(doc.hours);
    if (!isNaN(hoursInt)) {
      // Only update if the value would actually change
      if (typeof doc.hours !== 'number' || doc.hours !== hoursInt) {
        db.ActualComputerHours.updateOne(
          { _id: doc._id },
          { $set: { hours: hoursInt } }
        );
        updateCount++;
      }
    }
  }
});

console.log(`Updated hours field to integer in ${updateCount} documents`);

// Verify the schema now
let verifyResult = db.ActualComputerHours.find().toArray();
console.log("Sample documents after update:");
verifyResult.slice(0, 3).forEach(doc => {
  console.log(`ID: ${doc._id}, Hours: ${doc.hours} (type: ${typeof doc.hours})`);
});
