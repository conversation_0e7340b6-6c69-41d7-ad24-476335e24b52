<script>
  import { cn } from "$lib/utils";
  import { fade, scale } from "svelte/transition";

  export let open = false;

  /** @param {KeyboardEvent} event */
  function handleKeydown(event) {
    if (event.key === 'Escape') {
      open = false;
    }
  }
</script>

{#if open}
  <!-- svelte-ignore a11y-click-events-have-key-events -->
  <div
    role="dialog"
    aria-modal="true"
    tabindex="-1"
    class="fixed inset-0 z-50 bg-black/50"
    on:click={() => (open = false)}
    on:keydown={handleKeydown}
    transition:fade={{ duration: 100 }}
  >
    <button
      class="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg"
      on:click|stopPropagation
      transition:scale={{ duration: 200 }}
    >
      <slot />
    </button>
  </div>
{/if}
