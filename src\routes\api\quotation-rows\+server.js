import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/** @type {import('@sveltejs/kit').RequestHandler} */
export async function GET({ url }) {
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    
    const quoteId = url.searchParams.get('quoteId');
    if (!quoteId || !ObjectId.isValid(quoteId)) {
      return json({ success: false, error: 'Invalid quote ID' }, { status: 400 });
    }

    // Get rows from QuotationRows collection
    const rowsCollection = db.collection('QuotationRows');
    const rows = await rowsCollection
      .find({
        $or: [
          { QuoteId: new ObjectId(quoteId) },
          { QuotationId: new ObjectId(quoteId) }
        ]
      })
      .sort({ RowOrder: 1 })
      .toArray();

    // Convert ObjectIds to strings
    const rowsData = rows.map(row => ({
      ...row,
      _id: row._id.toString(),
      QuoteId: (row.QuoteId || row.QuotationId)?.toString(),
      QuotationId: (row.QuoteId || row.QuotationId)?.toString() // For backward compatibility
    }));

    return json({ success: true, rows: rowsData });
  } catch (error) {
    console.error('Error fetching quotation rows:', error);
    return json({ success: false, error: 'Failed to fetch quotation rows' }, { status: 500 });
  } finally {
    await client.close();
  }
}

/** @type {import('@sveltejs/kit').RequestHandler} */
export async function POST({ request }) {
  try {
    const row = await request.json();
    
    // Validate required fields
    if (!row) {
      return json({ success: false, error: 'No data provided' }, { status: 400 });
    }
    if (!row.ServiceActivity?.trim()) {
      return json({ success: false, error: 'Service activity is required' }, { status: 400 });
    }
    if (!row.QuotationId && !row.QuoteId) {
      return json({ success: false, error: 'Quote ID is required' }, { status: 400 });
    }

    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('QuotationRows');

    // Remove _id if it exists to let MongoDB generate it
    const { _id, ...rowData } = row;

    // Convert QuoteId and QuotationId to ObjectId
    const quoteId = row.QuoteId || row.QuotationId;
    const now = new Date();
    const quotationObjectId = new ObjectId(quoteId);

    const insertDoc = {
      ...rowData,
      QuoteId: quotationObjectId,
      QuotationId: quotationObjectId, // Ensure QuotationId is set to the same ObjectId
      RowType: rowData.RowType || 'BaseContract',
      RowOrder: parseInt(rowData.RowOrder) || 1,
      ServiceActivity: rowData.ServiceActivity.trim(),
      Cost: parseFloat(rowData.Cost) || 0,
      OemImporter: parseFloat(rowData.OemImporter) || 0,
      FleetOwner: parseFloat(rowData.FleetOwner) || 0,
      SSP: parseFloat(rowData.SSP) || 0,
      CustomerSpecific: !!rowData.CustomerSpecific,
      Required: !!rowData.Required,
      IncludeInOffer: rowData.IncludeInOffer !== false,
      CreatedAt: now,
      UpdatedAt: now
    };

    const result = await collection.insertOne(insertDoc);

    if (!result.acknowledged) {
      return json({ success: false, error: 'Failed to insert document' }, { status: 500 });
    }

    const insertedRow = await collection.findOne({ _id: result.insertedId });
    if (!insertedRow) {
      return json({ success: false, error: 'Failed to retrieve inserted row' }, { status: 500 });
    }

    return json({ 
      success: true, 
      row: {
        ...insertedRow,
        _id: insertedRow._id.toString(),
        QuoteId: insertedRow.QuoteId.toString(),
        QuotationId: insertedRow.QuotationId.toString() // Both IDs will be the same string
      }
    });
  } catch (error) {
    console.error('Error creating quotation row:', error);
    return json({ success: false, error: 'Failed to create quotation row' }, { status: 500 });
  } finally {
    await client.close();
  }
}

/** @type {import('@sveltejs/kit').RequestHandler} */
export async function PUT({ request }) {
  try {
    const row = await request.json();
    if (!row || !row._id || (!row.QuoteId && !row.QuotationId)) {
      return json({ success: false, error: 'Invalid request data' }, { status: 400 });
    }

    if (!ObjectId.isValid(row._id)) {
      return json({ success: false, error: 'Invalid row ID' }, { status: 400 });
    }

    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('QuotationRows');

    // Keep _id and QuoteId separate from other update data
    const { _id, QuoteId, QuotationId, CreatedAt, ...updateData } = row;
    const quoteObjectId = new ObjectId(QuoteId || QuotationId);

    const result = await collection.findOneAndUpdate(
      { _id: new ObjectId(_id) },
      { 
        $set: {
          ...updateData,
          QuoteId: quoteObjectId,
          QuotationId: quoteObjectId, // Update both for backward compatibility
          UpdatedAt: new Date()
        }
      },
      { returnDocument: 'after' }
    );

    if (!result || !result.value) {
      return json({ success: false, error: 'Row not found' }, { status: 404 });
    }

    return json({ 
      success: true, 
      row: {
        ...result.value,
        _id: result.value._id.toString(),
        QuoteId: result.value.QuoteId.toString(),
        QuotationId: result.value.QuoteId.toString() // For backward compatibility
      }
    });
  } catch (error) {
    console.error('Error updating quotation row:', error);
    return json({ success: false, error: 'Failed to update quotation row' }, { status: 500 });
  } finally {
    await client.close();
  }
}

/** @type {import('@sveltejs/kit').RequestHandler} */
export async function DELETE({ request }) {
  try {
    const { id } = await request.json();
    if (!id || !ObjectId.isValid(id)) {
      return json({ success: false, error: 'Invalid row ID' }, { status: 400 });
    }

    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('QuotationRows');

    const result = await collection.deleteOne({ _id: new ObjectId(id) });
    if (result.deletedCount === 0) {
      return json({ success: false, error: 'Row not found' }, { status: 404 });
    }

    return json({ success: true });
  } catch (error) {
    console.error('Error deleting quotation row:', error);
    return json({ success: false, error: 'Failed to delete quotation row' }, { status: 500 });
  } finally {
    await client.close();
  }
}
