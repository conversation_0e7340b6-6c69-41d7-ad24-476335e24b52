import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function updateLabourTimeFields() {
    try {
        await client.connect();
        const db = client.db('ServiceContracts');
        const collection = db.collection('LabourTime');

        // Get all documents
        const documents = await collection.find({}).toArray();

        // Update each document
        for (const doc of documents) {
            const serviceCode = doc['Service Code'];
            if (serviceCode) {
                const [servicePhase, computerCategory] = serviceCode.split('-');
                await collection.updateOne(
                    { _id: doc._id },
                    {
                        $set: {
                            ServicePhase: servicePhase,
                            ComputerCategory: computerCategory
                        }
                    }
                );
            }
        }

        console.log('Successfully updated ServicePhase and ComputerCategory fields');

    } catch (error) {
        console.error('Error updating fields:', error);
    } finally {
        await client.close();
    }
}

updateLabourTimeFields();
