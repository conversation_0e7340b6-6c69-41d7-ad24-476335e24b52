<script>
  import BaseGrid from './BaseGrid.svelte';
  
  /**
   * ContentGrid.svelte - Grid component for content-focused pages
   * 
   * This component provides a responsive CSS grid layout optimized for displaying content.
   * It creates a clean, centered layout with appropriate spacing and margins.
   */
  
  // Optional props for customization
  export let maxWidth = "100%";
  export let padding = "1rem";
</script>

<style>
  .content-grid {
    display: grid;
    grid-template-columns: 1fr minmax(0, var(--max-width)) 1fr;
    gap: var(--padding);
    width: 100%;
  }
  
  .content-area {
    grid-column: 2;
    width: 100%;
    padding: var(--padding);
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .content-grid {
      grid-template-columns: var(--padding) 1fr var(--padding);
    }
    
    .content-area {
      padding: calc(var(--padding) / 2);
    }
  }
</style>

<BaseGrid>
  <div class="content-grid" style="--max-width: {maxWidth}; --padding: {padding};">
    <div class="content-area">
      <slot />
    </div>
  </div>
</BaseGrid>
