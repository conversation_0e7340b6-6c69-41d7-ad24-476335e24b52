import { json } from '@sveltejs/kit';
import { getCollection } from '$lib/db/mongo';
import { ObjectId } from 'mongodb';

/**
 * GET handler for fetching a single customer computer by ID
 * @param {import('@sveltejs/kit').RequestEvent} event
 */
export async function GET({ params }) {
  try {
    // Get computer ID from params
    const computerId = params.id;
    
    // Validate computer ID format
    if (!computerId || !ObjectId.isValid(computerId)) {
      return new Response(
        JSON.stringify({ error: 'Invalid computer ID' }), 
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get the CustomerComputers collection
    const collection = await getCollection('CustomerComputers');
    
    // Find computer by ID with customer information
    const pipeline = [
      { 
        $match: { 
          _id: new ObjectId(computerId) 
        } 
      },
      {
        $lookup: {
          from: 'Customers',
          localField: 'customerId',
          foreignField: '_id',
          as: 'customer'
        }
      },
      {
        $unwind: {
          path: '$customer',
          preserveNullAndEmptyArrays: true
        }
      }
    ];
    
    const computers = await collection.aggregate(pipeline).toArray();
    
    if (computers.length === 0) {
      return new Response(
        JSON.stringify({ error: 'Computer not found' }), 
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    const computer = computers[0];
    
    // Convert ObjectIds to strings for client-side use
    if (computer._id) computer._id = computer._id.toString();
    if (computer.customerId) computer.customerId = computer.customerId.toString();
    if (computer.customer && computer.customer._id) {
      computer.customer._id = computer.customer._id.toString();
    }
    
    return json(computer);
  } catch (err) {
    console.error('Error fetching computer:', err);
    return new Response(
      JSON.stringify({ 
        error: 'Internal Server Error', 
        details: err instanceof Error ? err.message : 'Unknown error'
      }), 
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}

/**
 * PUT handler for updating a customer computer
 * @param {import('@sveltejs/kit').RequestEvent} event
 */
export async function PUT({ request, params }) {
  try {
    // Get computer ID from params
    const computerId = params.id;
    
    // Validate computer ID format
    if (!computerId || !ObjectId.isValid(computerId)) {
      return new Response(
        JSON.stringify({ error: 'Invalid computer ID' }), 
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    // Get update data from request body
    const updateData = await request.json();
    
    // Remove _id from update data if present
    delete updateData._id;
    
    // Convert string IDs to ObjectIds
    if (updateData.customerId && ObjectId.isValid(updateData.customerId)) {
      updateData.customerId = new ObjectId(updateData.customerId);
    }
    
    // Add updatedAt timestamp
    updateData.updatedAt = new Date();
    
    // Get the CustomerComputers collection
    const collection = await getCollection('CustomerComputers');
    
    // Update the computer
    const result = await collection.updateOne(
      { _id: new ObjectId(computerId) },
      { $set: updateData }
    );
    
    if (result.matchedCount === 0) {
      return new Response(
        JSON.stringify({ error: 'Computer not found' }), 
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    return json({ 
      success: true, 
      message: 'Computer updated successfully' 
    });
  } catch (err) {
    console.error('Error updating computer:', err);
    return new Response(
      JSON.stringify({ 
        error: 'Internal Server Error', 
        details: err instanceof Error ? err.message : 'Unknown error'
      }), 
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}

/**
 * DELETE handler for deleting a customer computer
 * @param {import('@sveltejs/kit').RequestEvent} event
 */
export async function DELETE({ params }) {
  try {
    // Get computer ID from params
    const computerId = params.id;
    
    // Validate computer ID format
    if (!computerId || !ObjectId.isValid(computerId)) {
      return new Response(
        JSON.stringify({ error: 'Invalid computer ID' }), 
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    // Get the CustomerComputers collection
    const collection = await getCollection('CustomerComputers');
    
    // Delete the computer
    const result = await collection.deleteOne({ _id: new ObjectId(computerId) });
    
    if (result.deletedCount === 0) {
      return new Response(
        JSON.stringify({ error: 'Computer not found or already deleted' }), 
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    return json({ 
      success: true, 
      message: 'Computer deleted successfully' 
    });
  } catch (err) {
    console.error('Error deleting computer:', err);
    return new Response(
      JSON.stringify({ 
        error: 'Internal Server Error', 
        details: err instanceof Error ? err.message : 'Unknown error'
      }), 
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
