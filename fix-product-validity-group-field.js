const { MongoClient } = require('mongodb');

async function fixFieldName() {
    const uri = 'mongodb://localhost:27017';
    const client = new MongoClient(uri);

    try {
        await client.connect();
        const db = client.db('ServiceContracts');
        const collection = db.collection('ProductValidityGroup');

        // Update all documents to rename the field
        const result = await collection.updateMany(
            { "Product Validity GRoup": { $exists: true } },
            [{
                $set: {
                    "ProductValidityGroup": "$Product Validity GRoup",
                }
            },
            {
                $unset: ["Product Validity GRoup"]
            }]
        );

        console.log(`Updated ${result.modifiedCount} documents`);

        // Verify the changes
        const sampleDoc = await collection.findOne({});
        console.log('\nSample document after update:', JSON.stringify(sampleDoc, null, 2));

    } catch (error) {
        console.error('Error updating field name:', error);
    } finally {
        await client.close();
    }
}

fixFieldName();
