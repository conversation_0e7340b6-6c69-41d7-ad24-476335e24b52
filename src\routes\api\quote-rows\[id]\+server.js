import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

// MongoDB connection URI
const uri = 'mongodb://localhost:27017';

// GET: Fetch quote rows for a quotation ID
export async function GET({ params }) {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    
    // Get the QuoteRows collection (Using PascalCase per project convention)
    const quoteRows = db.collection('QuoteRows');
    
    // Convert string ID to ObjectId
    const quotationId = new ObjectId(params.id);
    
    // Fetch all quote rows for this quotation ID
    // Using quotationId as the foreign key field
    const rows = await quoteRows.find({ quotationId: quotationId }).toArray();
    
    // Convert ObjectId to string for client-side use
    const formattedRows = rows.map(row => ({
      ...row,
      _id: row._id.toString(),
      quotationId: row.quotationId.toString()
    }));
    
    return json(formattedRows);
  } catch (error) {
    console.error('Error fetching quote rows:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  } finally {
    await client.close();
  }
}

// DELETE: Delete a quote row by ID
export async function DELETE({ params }) {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    
    // Get the QuoteRows collection (Using PascalCase per project convention)
    const quoteRows = db.collection('QuoteRows');
    
    // Convert string ID to ObjectId
    const rowId = new ObjectId(params.id);
    
    // Delete the quote row
    const result = await quoteRows.deleteOne({ _id: rowId });
    
    if (result.deletedCount === 1) {
      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    } else {
      return new Response(JSON.stringify({ error: 'Quote row not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  } catch (error) {
    console.error('Error deleting quote row:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  } finally {
    await client.close();
  }
}
