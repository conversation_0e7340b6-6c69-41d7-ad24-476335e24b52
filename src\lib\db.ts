import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);
const dbName = 'ServiceContracts';

// Create a new MongoClient
// let db: Db | undefined;

// Initialize function to connect to the database
export async function init() {
    try {
        console.log('Connecting to MongoDB...');
        await client.connect();
        console.log('Successfully connected to MongoDB');
        // Test the connection by listing collections
        const db = client.db(dbName);
        const collections = await db.listCollections().toArray();
        console.log('Available collections:', collections.map(c => c.name));
        return db;
    } catch (err) {
        console.error('Failed to connect to MongoDB:', err);
        throw err;
    }
}

// Handle process termination
process.on('SIGINT', async () => {
    if (client) {
        await client.close();
    }
    process.exit();
});

process.on('SIGTERM', async () => {
    if (client) {
        await client.close();
    }
    process.exit();
});

export async function close() {
    await client.close();
}

export { client };
