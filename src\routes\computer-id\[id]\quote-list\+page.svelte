<!-- @component
Displays a list of quotes for a computer with links to view details and quotation rows
-->
<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  
  interface ComputerInfo {
    computerId: string;
    serialNumber: string;
    model: string;
    customerName: string;
  }

  interface Quote {
    _id: string;
    quotationNumber: string;
    createdAt: string;
    updatedAt: string;
    status: string;
    contractLength: number;
    totalAmount: number;
  }

  interface PageData {
    quotes: Quote[];
    computerInfo: ComputerInfo;
    error?: string;
  }

  export let data: PageData;
  
  let quotes: Quote[] = [];
  let loading = true;
  let error: string | null = null;
  let computerInfo: ComputerInfo | null = null;
  
  onMount(async () => {
    try {
      // Quotes are loaded from the server
      quotes = data.quotes || [];
      computerInfo = data.computerInfo;
      error = data.error || null;
      loading = false;
    } catch (err: unknown) {
      error = err instanceof Error ? err.message : 'Failed to load quotes';
      loading = false;
    }
  });
  
  function formatDate(dateString: string): string {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  }
  
  function formatCurrency(amount: number | null | undefined): string {
    if (amount === undefined || amount === null) return '£ -';
    return `£ ${parseFloat(amount.toString()).toFixed(2)}`;
  }
  
  function viewQuote(quoteId: string): void {
    goto(`/computer-id/${$page.params.id}/quote/${quoteId}`);
  }
</script>

<div class="container">
  <div class="header">
    <div class="back-button-container">
      <a href="/computer-id/{$page.params.id}" class="back-button">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="19" y1="12" x2="5" y2="12"></line>
          <polyline points="12 19 5 12 12 5"></polyline>
        </svg>
        Back to Computer
      </a>
    </div>
    <h1 class="title">Service Quotations</h1>
    <div class="actions">
      <form method="POST" action="?/createNewQuote">
        <input type="hidden" name="computerId" value={$page.params.id} />
        <button type="submit" class="add-button">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="16"></line>
            <line x1="8" y1="12" x2="16" y2="12"></line>
          </svg>
          Create New Quote
        </button>
      </form>
    </div>
  </div>
  
  {#if loading}
    <div class="loading">
      <div class="spinner"></div>
      <p>Loading quotes...</p>
    </div>
  {:else if error}
    <div class="error-container">
      <p class="error-message">{error}</p>
    </div>
  {:else if quotes.length === 0}
    <div class="empty-state">
      <p>No quotations found for this computer.</p>
      <form method="POST" action="?/createNewQuote">
        <input type="hidden" name="computerId" value={$page.params.id} />
        <button type="submit" class="add-button">Create New Quote</button>
      </form>
    </div>
  {:else}
    <div class="computer-info">
      <h2>Computer Information</h2>
      <div class="info-grid">
        <div class="info-item">
          <div class="info-label">Computer ID</div>
          <div class="info-value">{computerInfo?.computerId || 'N/A'}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Serial Number</div>
          <div class="info-value">{computerInfo?.serialNumber || 'N/A'}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Model</div>
          <div class="info-value">{computerInfo?.model || 'N/A'}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Customer</div>
          <div class="info-value">{computerInfo?.customerName || 'N/A'}</div>
        </div>
      </div>
    </div>
    
    <div class="quote-list">
      <h2>Available Quotations</h2>
      <div class="quote-grid">
        <div class="grid-header">
          <div class="header-number">Quote #</div>
          <div class="header-date">Created</div>
          <div class="header-date">Updated</div>
          <div class="header-status">Status</div>
          <div class="header-contract">Contract Length</div>
          <div class="header-amount">Total Amount</div>
          <div class="header-actions">Actions</div>
        </div>
        
        {#each quotes as quote}
          <div class="grid-row">
            <div class="cell-number">{quote.quotationNumber || 'Draft'}</div>
            <div class="cell-date">{formatDate(quote.createdAt)}</div>
            <div class="cell-date">{formatDate(quote.updatedAt)}</div>
            <div class="cell-status">
              <span class="status-badge {quote.status.toLowerCase()}">{quote.status}</span>
            </div>
            <div class="cell-contract">{quote.contractLength || 12} months</div>
            <div class="cell-amount">{formatCurrency(quote.totalAmount)}</div>
            <div class="cell-actions">
              <a href="/computer-id/{$page.params.id}/computer-quote/{quote._id}" class="action-button computer-quote" title="Computer Quote">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                  <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                  <line x1="12" y1="22.08" x2="12" y2="12"></line>
                </svg>
                Computer Quote
              </a>
            </div>
          </div>
        {/each}
      </div>
    </div>
  {/if}
</div>

<style>
  .container {
    padding: 1rem;
    max-width: 95%;
    width: 95%;
    margin: 0 auto;
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }
  
  .back-button-container {
    flex: 0 0 auto;
  }
  
  .back-button {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
    text-decoration: none;
    color: inherit;
  }
  
  .back-button:hover {
    background-color: #e9ecef;
  }
  
  .back-button svg {
    margin-right: 0.5rem;
  }
  
  .title {
    flex: 1 1 auto;
    text-align: center;
    margin: 0;
    font-size: 1.5rem;
  }
  
  .actions {
    display: flex;
    gap: 1rem;
    align-items: center;
  }

  .add-button {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
  }
  
  .add-button:hover {
    background-color: #0069d9;
  }
  
  .add-button svg {
    margin-right: 0.5rem;
  }
  
  .loading {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 200px;
  }
  
  .spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid #007bff;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .error-container {
    padding: 2rem;
    text-align: center;
    background-color: #f8d7da;
    border-radius: 4px;
    color: #721c24;
  }
  
  .empty-state {
    padding: 3rem;
    text-align: center;
    background-color: #f8f9fa;
    border-radius: 8px;
  }
  
  .empty-state p {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    color: #6c757d;
  }
  
  .computer-info {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .computer-info h2 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.2rem;
  }
  
  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }
  
  .info-item {
    margin-bottom: 0.5rem;
  }
  
  .info-label {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
  }
  
  .info-value {
    font-size: 1rem;
  }
  
  .quote-list {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
  }
  
  .quote-list h2 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.2rem;
  }
  
  .quote-grid {
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .grid-header {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
    background-color: #f8f9fa;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
  }
  
  .grid-header > div {
    padding: 12px 8px;
    text-align: center;
    border-right: 1px solid #eee;
  }
  
  .grid-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr 1fr;
    border-bottom: 1px solid #ddd;
  }
  
  .grid-row > div {
    padding: 12px 8px;
    border-right: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
  }
  
  .status-badge.draft {
    background-color: #e2e3e5;
    color: #383d41;
  }
  
  .status-badge.pending {
    background-color: #fff3cd;
    color: #856404;
  }
  
  .status-badge.approved {
    background-color: #d4edda;
    color: #155724;
  }
  
  .status-badge.rejected {
    background-color: #f8d7da;
    color: #721c24;
  }
  
  .action-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    text-decoration: none;
    color: white;
    transition: background-color 0.2s;
  }

  .action-button.computer-quote {
    background-color: #6610f2;
  }

  .action-button.computer-quote:hover {
    background-color: #5c0bff;
  }

  .cell-actions {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
  
  /* Responsive adjustments */
  @media (max-width: 992px) {
    .grid-header, .grid-row {
      grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
    }
    
    .header-date:nth-child(3), .cell-date:nth-child(3) {
      display: none;
    }
    
    .header-contract, .cell-contract {
      display: none;
    }
  }
  
  @media (max-width: 768px) {
    .header {
      flex-direction: column;
      gap: 1rem;
    }
    
    .title {
      order: -1;
    }
    
    .grid-header, .grid-row {
      grid-template-columns: 1fr 1fr 1fr 1fr;
    }
    
    .header-date:nth-child(2), .cell-date:nth-child(2) {
      display: none;
    }
  }
</style>
