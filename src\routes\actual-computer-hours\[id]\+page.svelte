<script lang="ts">
  import { goto } from '$app/navigation';
  import FormattedDateTimeInput from '$lib/components/FormattedDateTimeInput.svelte';
  export let data: { actualComputerHour: any };
  const originalDoc = data.actualComputerHour || {};
  let doc = { ...originalDoc };
  let editing = false;
  let saving = false;
  let errorMsg = '';
  let successMsg = '';

  function fmt(key: string, val: any) {
    if (!val) return '';
    if (typeof val === 'boolean') return val ? 'Yes' : 'No';
    if (key === 'CalculatedHoursPerMonth') return typeof val === 'number' ? val : parseInt(val) || 0;
    if (key === 'hours') return typeof val === 'number' ? val : parseInt(val) || 0;
    if (!isNaN(Date.parse(val))) return new Date(val).toLocaleString();
    return val;
  }

  function startEdit() {
    editing = true;
    doc = { ...originalDoc };
    initializeFields();
    errorMsg = '';
    successMsg = '';
  }

  function initializeFields() {
    // Check for ReportDate and ReportTime fields
    if (!doc.ReportDate) {
      doc.ReportDate = new Date().toISOString().split('T')[0];
    }
    if (!doc.ReportTime) {
      doc.ReportTime = new Date().toTimeString().substr(0, 5);
    }
  }

  function cancelEdit() {
    editing = false;
    doc = { ...originalDoc };
    errorMsg = '';
    successMsg = '';
  }

  async function saveEdit() {
    saving = true;
    errorMsg = '';
    successMsg = '';
    try {
      // Ensure hours and CalculatedHoursPerMonth are properly converted to integers
      if (doc.hours !== undefined) {
        doc.hours = parseInt(doc.hours);
        if (isNaN(doc.hours)) doc.hours = 0;
      }
      
      if (doc.CalculatedHoursPerMonth !== undefined) {
        // Remove old cap logic: just parse as number, don't limit
        doc.CalculatedHoursPerMonth = parseFloat(doc.CalculatedHoursPerMonth) || 0;
      }
      
      // Verify date and time formats are correct before saving
      const datePattern = /^\d{4}-\d{2}-\d{2}$/;
      const timePattern = /^([01]\d|2[0-3]):([0-5]\d)$/;
      
      if (!datePattern.test(doc.ReportDate)) {
        errorMsg = 'Report Date must be in YYYY-MM-DD format';
        saving = false;
        return;
      }
      
      if (!timePattern.test(doc.ReportTime)) {
        errorMsg = 'Report Time must be in 24-hour format (HH:MM)';
        saving = false;
        return;
      }
      
      // Format ReportDate in YYYY-MM-DD format
      const date = new Date(doc.ReportDate || new Date());
      doc.ReportDate = date.toISOString().split('T')[0]; // YYYY-MM-DD
      
      // Format ReportTime in 24-hour format (HH:MM)
      const timeStr = doc.ReportTime || '';
      const timeParts = timeStr.split(':');
      let hours = parseInt(timeParts[0]) || 0;
      const minutes = parseInt(timeParts[1]) || 0;
      
      // Convert to 24-hour format if needed
      if (timeStr.toLowerCase().includes('pm') && hours < 12) {
        hours += 12;
      } else if (timeStr.toLowerCase().includes('am') && hours === 12) {
        hours = 0;
      }
      
      doc.ReportTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
      
      const res = await fetch(`/api/actual-computer-hours/${doc._id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(doc)
      });
      
      if (!res.ok) {
        const data = await res.json().catch(() => ({}));
        throw new Error(data.error || 'Failed to update.');
      }
      
      const result = await res.json();
      
      if (!result.success) {
        errorMsg = result.error || 'Failed to update.';
      } else {
        successMsg = 'Saved successfully!';
        editing = false;
        setTimeout(() => window.location.reload(), 800);
      }
    } catch (e) {
      errorMsg = e?.message || 'Error saving.';
    }
    saving = false;
  }

  async function deleteDoc() {
    if (!confirm('Delete this Actual Computer Hour?')) return;
    saving = true;
    errorMsg = '';
    successMsg = '';
    try {
      const res = await fetch(window.location.pathname, { method: 'DELETE' });
      if (res.ok) {
        successMsg = 'Deleted! Redirecting...';
        setTimeout(() => goto('/actual-computer-hours'), 1000);
      } else {
        errorMsg = 'Delete failed.';
      }
    } catch (e) {
      errorMsg = e.message || 'Error deleting.';
    }
    saving = false;
  }
</script>

<div class="ach-card">
  <button class="back-btn" on:click={() => history.length > 1 ? history.back() : goto('/actual-computer-hours')}>Back</button>
  <h2>Actual Computer Hour Details</h2>
  {#if !editing}
    <div class="ach-fields">
      {#each Object.entries(doc) as [key, value]}
        {#if key !== 'StopDateTime' && key !== 'month' && key !== 'year'}
          {#if key === 'HoursPerMonth'}
            <div class="ach-row"><span class="ach-label">HoursPerMonth:</span> <span class="ach-value">{Math.trunc(doc.HoursPerMonth)}</span></div>
          {:else}
            <div class="ach-row"><span class="ach-label">{key}:</span> <span class="ach-value">{fmt(key, value)}</span></div>
          {/if}
        {/if}
      {/each}
    </div>
    <div class="ach-actions">
      <button class="edit-btn" on:click={startEdit}>Edit</button>
      <button class="delete-btn" on:click={deleteDoc}>Delete</button>
    </div>
    {#if errorMsg}<div class="error-msg">{errorMsg}</div>{/if}
    {#if successMsg}<div class="success-msg">{successMsg}</div>{/if}
  {:else}
    <form class="ach-edit-form" on:submit|preventDefault={saveEdit}>
      {#each Object.entries(doc) as [key, value]}
        {#if key !== 'StopDateTime' && key !== 'month' && key !== 'year'}
          <div class="ach-row">
            <span class="ach-label">{key}:</span>
            {#if key === '_id'}
              <input type="text" value={value} disabled />
            {:else if key === 'hours' || key === 'CalculatedHoursPerMonth'}
              {#if key === 'CalculatedHoursPerMonth'}
                <input type="number" bind:value={doc[key]} readonly />
              {:else}
                <input type="number" min="0" bind:value={doc[key]} />
              {/if}
            {:else if key === 'HoursPerMonth'}
              <input type="number" bind:value={doc[key]} min="0" step="1" />
            {:else if key === 'ReportDate'}
              <div>
                <label for="report-date-edit">Date</label>
                <input id="report-date-edit" type="date" bind:value={doc[key]} />
              </div>
            {:else if key === 'ReportTime'}
              <div>
                <label for="report-time-edit">Time</label>
                <input 
                  id="report-time-edit"
                  type="text" 
                  bind:value={doc[key]}
                  placeholder="HH:MM (24-hour)"
                />
              </div>
            {:else if typeof value === 'boolean'}
              <select bind:value={doc[key]}>
                <option value={true}>Yes</option>
                <option value={false}>No</option>
              </select>
            {:else if !isNaN(Date.parse(value))}
              <input type="datetime-local" bind:value={doc[key]} />
            {:else}
              <input type="text" bind:value={doc[key]} />
            {/if}
          </div>
        {/if}
      {/each}
      <div class="ach-actions">
        <button class="save-btn" type="submit" disabled={saving}>Save</button>
        <button class="cancel-btn" type="button" on:click={cancelEdit} disabled={saving}>Cancel</button>
      </div>
      {#if errorMsg}<div class="error-msg">{errorMsg}</div>{/if}
      {#if successMsg}<div class="success-msg">{successMsg}</div>{/if}
    </form>
  {/if}
</div>

<style>
.ach-card {
  max-width: 480px;
  margin: 3em auto;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 18px rgba(0,0,0,0.08);
  padding: 2.5em 2em 2em 2em;
}
.ach-fields {
  display: flex;
  flex-direction: column;
  gap: 0.7em;
}
.ach-row {
  display: flex;
  gap: 1em;
  font-size: 1.08em;
  align-items: center;
}
.ach-label {
  font-weight: 600;
  color: #2d3a4a;
  min-width: 140px;
}
.ach-value {
  color: #444;
}
.ach-actions {
  margin-top: 1.5em;
  display: flex;
  gap: 1em;
}
.edit-btn, .save-btn {
  background: #2563eb;
  color: #fff;
  border: none;
  border-radius: 5px;
  font-weight: 600;
  padding: 0.55em 1.3em;
  font-size: 1.06em;
  cursor: pointer;
  transition: background 0.15s;
}
.edit-btn:hover, .save-btn:hover { background: #1747b1; }
.cancel-btn {
  background: #f3f3f3;
  color: #333;
  border: 1px solid #e3e7ee;
  border-radius: 5px;
  padding: 0.55em 1.3em;
  font-weight: 600;
  cursor: pointer;
}
.delete-btn {
  background: #e53935;
  color: #fff;
  border: none;
  border-radius: 5px;
  font-weight: 600;
  padding: 0.55em 1.3em;
  font-size: 1.06em;
  cursor: pointer;
  transition: background 0.15s;
}
.delete-btn:hover { background: #c62828; }
.add-btn {
  background: #059669;
  color: #fff;
  border: none;
  border-radius: 5px;
  font-weight: 600;
  padding: 0.55em 1.3em;
  font-size: 1.06em;
  cursor: pointer;
  transition: background 0.15s;
  margin-right: 1em;
}
.add-btn:hover { background: #047857; }
.adapt-btn {
  background: #9333ea;
  color: #fff;
  border: none;
  border-radius: 5px;
  font-weight: 600;
  padding: 0.55em 1.3em;
  font-size: 1.06em;
  cursor: pointer;
  transition: background 0.15s;
  margin-left: 1em;
}
.adapt-btn:hover { background: #6d28d9; }
.error-msg { color: #d32f2f; margin-top: 1em; }
.success-msg { color: #059669; margin-top: 1em; }
.ach-edit-form input, .ach-edit-form select {
  font-size: 1em;
  padding: 0.45em 0.7em;
  border: 1px solid #d2d8e0;
  border-radius: 5px;
  background: #f9f9fb;
}
.back-btn {
  background: #f3f3f3;
  color: #333;
  border: 1px solid #e3e7ee;
  border-radius: 6px;
  padding: 0.4em 1.2em;
  font-size: 1em;
  cursor: pointer;
  margin-bottom: 1em;
  transition: background 0.15s;
}
.back-btn:hover {
  background: #e8e8e8;
}
.format-note {
  font-size: 0.9em;
  color: #666;
  margin-top: 0.2em;
}
.input-group {
  display: flex;
  flex-direction: column;
  gap: 0.2em;
}
.input-label {
  font-size: 0.9em;
  color: #666;
}
</style>
