<script lang="ts">
  import { page } from '$app/stores';
  import { browser } from '$app/environment';
  import { goto } from '$app/navigation';
  import FormGrid from '$lib/components/grids/FormGrid.svelte';

  let formData = {
    computerId: $page.params.id || '',
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
    activity: 'Running',
    hasFixed: false,
    hours: 0,
    isIdle: false
  };

  let loading = false;
  let error = '';
  let success = false;

  async function handleSave() {
    loading = true;
    error = '';
    success = false;
    
    try {
      // Create a new document in ActualComputerHours
      const res = await fetch('/api/actual-computer-hours', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });
      
      if (!res.ok) {
        const data = await res.json().catch(() => ({}));
        throw new Error(data.error || 'Failed to save');
      }
      
      success = true;
      // Reset hours only, keep other fields the same
      formData.hours = 0;
    } catch (e: any) {
      console.error('Save error:', e);
      error = e?.message || 'Unknown error';
    } finally {
      loading = false;
    }
  }

  function handleBack() {
    if (browser) history.back();
  }
  
  function handleAddNew() {
    // Use the exact computerId from the document
    goto(`/actual-computer-hours/add?computerId=${formData.computerId}`);
  }
</script>

<main>
  <h2>Actual Computer Hours</h2>
  
  {#if error}
    <div class="error">{error}</div>
  {/if}
  
  <FormGrid>
    <label for="computerId">Computer ID (preset)</label>
    <input id="computerId" type="text" bind:value={formData.computerId} readonly />
    
    <label for="activity">Activity</label>
    <select id="activity" bind:value={formData.activity}>
      <option value="Running">Running</option>
      <option value="Idle">Idle</option>
      <option value="Maintenance">Maintenance</option>
      <option value="Offline">Offline</option>
    </select>
    
    <label for="month">Month</label>
    <input id="month" type="number" min="1" max="12" bind:value={formData.month} />
    
    <label for="year">Year</label>
    <input id="year" type="number" min="2020" max="2030" bind:value={formData.year} />
    
    <label for="hours">Hours</label>
    <input id="hours" type="number" min="0" bind:value={formData.hours} />
    
    <label for="hasFixed">Has Fixed</label>
    <input id="hasFixed" type="checkbox" bind:checked={formData.hasFixed} />
    
    <label for="isIdle">Is Idle</label>
    <input id="isIdle" type="checkbox" bind:checked={formData.isIdle} />
    
    <label for="hoursPerMonth">HoursPerMonth (Int32)</label>
    <input id="hoursPerMonth" type="number" value={formData.HoursPerMonth !== undefined && formData.HoursPerMonth !== null ? Math.trunc(formData.HoursPerMonth) : ''} readonly />
    
    {#each Object.entries(formData) as [key, value]}
      {#if key !== 'HoursPerMonth' && key !== 'CalculatedHoursPerMonth' && key !== 'ReportDateTime' && key !== 'computerId' && key !== 'activity' && key !== 'month' && key !== 'year' && key !== 'hours' && key !== 'hasFixed' && key !== 'isIdle'}
        <label for={key}>{key}</label>
        <input id={key} type="text" bind:value={value} />
      {/if}
    {/each}
    
    {#if formData.CalculatedHoursPerMonth !== undefined}
      <label for="calculatedHoursPerMonth">Calculated Hours Per Month</label>
      <input id="calculatedHoursPerMonth" type="number" value={formData.CalculatedHoursPerMonth !== undefined && formData.CalculatedHoursPerMonth !== null ? Math.trunc(formData.CalculatedHoursPerMonth) : ''} readonly />
    {/if}
  </FormGrid>
  
  <div class="button-row">
    <button on:click={handleBack} type="button">Back</button>
    <button on:click={handleAddNew} type="button">Add New</button>
    <button on:click={handleSave} type="button" disabled={loading}>
      {loading ? 'Saving...' : 'Save'}
    </button>
  </div>
</main>

<style>
  main {
    padding: 2rem 0 3rem 0;
  }
  h2 {
    text-align: center;
    margin-bottom: 2rem;
  }
  .button-row {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
  }
  .error {
    color: #b00020;
    padding: 0.5rem;
    margin-bottom: 1rem;
    background-color: #ffebee;
    border-radius: 4px;
  }
  .success {
    color: #388e3c;
    padding: 0.5rem;
    margin-bottom: 1rem;
    background-color: #e8f5e9;
    border-radius: 4px;
  }
  label {
    font-weight: 500;
    color: #222;
  }
  input[readonly] {
    background: #f5f5f5;
    color: #666;
    border: 1px solid #e0e0e0;
    cursor: not-allowed;
  }
</style>