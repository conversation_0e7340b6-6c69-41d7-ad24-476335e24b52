<script>
    import { onMount } from 'svelte';

    export let fullScreen = true;

    onMount(() => {
        const adjustHeight = () => {
            const vh = window.innerHeight;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        };

        adjustHeight();
        window.addEventListener('resize', adjustHeight);

        return () => {
            window.removeEventListener('resize', adjustHeight);
        };
    });
</script>

<div class="matrix-container" class:full-screen={fullScreen}>
    <slot />
</div>

<style>
    .matrix-container {
        display: grid;
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
        background-color: var(--background-color, #0f172a);
        border-radius: 8px;
        overflow: auto;
    }
    
    .full-screen {
        width: 100vw;
        height: 100vh;
        height: var(--vh, 100vh);
        position: fixed;
        top: 0;
        left: 0;
        z-index: 50;
        border-radius: 0;
    }

    /* Custom scrollbar styles */
    .matrix-container::-webkit-scrollbar {
        width: 10px;
        height: 10px;
    }

    .matrix-container::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.2);
    }

    .matrix-container::-webkit-scrollbar-thumb {
        background: rgba(59, 130, 246, 0.5);
        border-radius: 5px;
    }
    
    .matrix-container::-webkit-scrollbar-thumb:hover {
        background: rgba(59, 130, 246, 0.7);
    }
</style>
