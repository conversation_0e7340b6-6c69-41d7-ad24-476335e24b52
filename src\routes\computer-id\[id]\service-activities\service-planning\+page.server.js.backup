import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/** @type {import('./$types').PageServerLoad} */
export async function load({ params, url }) {
    try {
        const db = client.db('ServiceContracts');
        const computerId = params.id;
        
        // Get URL parameters
        const productValidityGroupParam = url.searchParams.get('productValidityGroup');
        const categoryParam = url.searchParams.get('category');
        const hoursAtContractStartParam = url.searchParams.get('hoursAtContractStart');
        
        console.log('\n=== URL Parameters ===');
        console.log('ProductValidityGroup from URL:', productValidityGroupParam);
        console.log('Category from URL:', categoryParam);
        console.log('Hours At Contract Start from URL:', hoursAtContractStartParam);
        
        // Step 1: Get computer info
        console.log('\n=== Step 1: Getting Computer Info ===');
        const computer = await db.collection('CustomerComputers')
            .findOne({ _id: new ObjectId(computerId) });

        if (!computer) {
            throw new Error('Computer not found');
        }
        
        // Debug: Check all fields of the computer object to identify potential field names for hours
        const allFields = Object.keys(computer);
        console.log('All fields in computer object:', allFields);
        
        // Check for various possible field name formats
        const hoursPossibilities = [
            'HoursAtContractStart',
            'hoursAtContractStart',
            'Hours_At_Contract_Start', 
            'hours_at_contract_start', 
            'HoursContractStart', 
            'hoursContractStart',
            'Hours Contract Start',
            'hours contract start',
            'HourStart',
            'hourStart',
            'ContractHours',
            'contractHours'
        ];
        
        console.log('Checking for hours fields:');
        hoursPossibilities.forEach(field => {
            if (computer[field] !== undefined) {
                console.log(`Found field "${field}" with value:`, computer[field]);
            }
        });

        console.log('Computer found:', {
            id: computerId,
            productDesignation: computer.productDesignation,
            productPartNumber: computer.productPartNumber
        });

        // Step 2: Query ProductValidityGroupPartNumber collection
        console.log('\n=== Step 2: Querying ProductValidityGroupPartNumber ===');
        
        // Use productPartNumber as the primary filter if available
        const filter = [];
        
        if (computer.productPartNumber) {
            // Convert to Number for proper matching
            const partNumber = Number(computer.productPartNumber);
            filter.push({ ProductPartNumber: partNumber });
        }
        
        if (computer.productDesignation) {
            filter.push({ ProductDesignation: computer.productDesignation });
        }
        
        // Use $or to match either criteria
        const query = filter.length > 0 ? { $or: filter } : {};
        
        console.log('Query:', JSON.stringify(query, null, 2));
        
        const productDetails = await db.collection('ProductValidityGroupPartNumber')
            .find(query)
            .sort({
                ProductGroupId: 1,    // Primary sort
                Id: 1,                // Secondary sort
                ProductPartNumber: 1   // Final sort - unique per product
            })
            .toArray();
        
        console.log(`Found ${productDetails.length} matching products`);
        
        if (productDetails.length > 0) {
            console.log('Sample product:', {
                ProductName: productDetails[0].ProductName,
                ProductPartNumber: productDetails[0].ProductPartNumber,
                ProductDesignation: productDetails[0].ProductDesignation,
                ProductValidityGroup: productDetails[0].ProductValidityGroup
            });
        }

        // Find the hours field by checking all possibilities or use URL parameter
        let hoursValue = null;
        
        // First priority: Use hours from URL parameter if available
        if (hoursAtContractStartParam && !isNaN(Number(hoursAtContractStartParam))) {
            hoursValue = Number(hoursAtContractStartParam);
            console.log(`Using hours from URL parameter: ${hoursValue}`);
        } 
        // Second priority: Find hours from computer object
        else {
            for (const field of hoursPossibilities) {
                if (computer[field] !== undefined) {
                    hoursValue = computer[field];
                    console.log(`Using hours from field "${field}": ${hoursValue}`);
                    break;
                }
            }
        }
        
        // If no hours found, default to 0
        if (hoursValue === null) {
            hoursValue = 0;
            console.log(`No hours found, defaulting to: ${hoursValue}`);
        }
        
        // Use ProductValidityGroup from URL parameter if available
        let productValidityGroup = null;
        
        if (productValidityGroupParam && productValidityGroupParam.trim() !== '') {
            productValidityGroup = productValidityGroupParam;
            console.log(`Using ProductValidityGroup from URL: ${productValidityGroup}`);
        } 
        // Otherwise, try to get it from product details
        else if (productDetails.length > 0 && productDetails[0].ProductValidityGroup) {
            productValidityGroup = productDetails[0].ProductValidityGroup;
            console.log(`Using ProductValidityGroup from product details: ${productValidityGroup}`);
        } 
        // Finally, use from computer object if available
        else if (computer.ProductValidityGroup) {
            productValidityGroup = computer.ProductValidityGroup;
            console.log(`Using ProductValidityGroup from computer object: ${productValidityGroup}`);
        }
        
        // Use Category from URL parameter if available
        let category = null;
        
        if (categoryParam && categoryParam.trim() !== '') {
            category = categoryParam;
            console.log(`Using Category from URL: ${category}`);
        } 
        // Otherwise, try to get Category from computer object
        else if (computer.Category) {
            category = computer.Category;
            console.log(`Using Category from computer object: ${category}`);
        }
        
        // Transform data for client
        const transformedComputer = {
            ...computer,
            _id: String(computer._id),
            customerId: String(computer.customerId),
            siteId: computer.siteId ? String(computer.siteId) : null,
            // Keep the existing Hours Contract Start field
            'Hours Contract Start': hoursValue,
            // Add a direct reference to HoursAtContractStart
            HoursAtContractStart: hoursValue,
            ProductValidityGroup: productValidityGroup,
            Category: category
        };
        
        console.log('Final Hours Contract Start value:', transformedComputer['Hours Contract Start']);
        console.log('Direct HoursAtContractStart value:', transformedComputer.HoursAtContractStart);

        // Remove duplicates based on ProductPartNumber
        const uniqueProductsMap = new Map();
        
        productDetails.forEach(item => {
            const key = item.ProductPartNumber.toString();
            // Only add if not already in the map, or replace if this entry has more complete data
            if (!uniqueProductsMap.has(key) || 
                (!uniqueProductsMap.get(key).ProductValidityGroup && item.ProductValidityGroup)) {
                uniqueProductsMap.set(key, item);
            }
        });
        
        const transformedProductDetails = Array.from(uniqueProductsMap.values()).map(item => ({
            _id: String(item._id),
            ProductName: item.ProductName || '',
            ProductPartNumber: Number(item.ProductPartNumber) || 0,
            ProductDesignation: item.ProductDesignation || '',
            ProductGroupId: item.ProductGroupId || 0,
            Id: item.Id || 0,
            ProductValidityGroup: item.ProductValidityGroup || ''
        }));

        console.log('\n=== Step 3: Loading Workload Data ===');
        
        const currentYear = new Date().getFullYear();
        const startYear = currentYear - 2; // Include previous 2 years
        const endYear = currentYear + 3;   // Include next 3 years
        
        console.log(`Loading workload data for years ${startYear} to ${endYear}`);
        
        // Query for workload data related to this computer
        let workloadData = [];
        try {
            workloadData = await db.collection('Workload')
                .find({ 
                    computerId: new ObjectId(computerId),
                    year: { $gte: startYear, $lte: endYear }
                })
                .sort({ 
                    year: 1,
                    month: 1
                })
                .toArray();
                
            console.log(`Found ${workloadData.length} workload records`);
            
            // If no data found, try with string ID
            if (workloadData.length === 0) {
                console.log('No workload found with ObjectId, trying with string ID');
                workloadData = await db.collection('Workload')
                    .find({ 
                        computerId: computerId,
                        year: { $gte: startYear, $lte: endYear }
                    })
                    .sort({ 
                        year: 1,
                        month: 1
                    })
                    .toArray();
                    
                console.log(`Found ${workloadData.length} workload records with string ID`);
            }
            
            // If still no data, create sample data for the current year
            if (workloadData.length === 0) {
                console.log('No workload found, creating sample data');
                
                // Generate sample data for the current year
                workloadData = [];
                
                for (let month = 1; month <= 12; month++) {
                    workloadData.push({
                        _id: `sample_${currentYear}_${month}`,
                        computerId: computerId,
                        year: currentYear,
                        month: month,
                        hours: 0,
                        activity: month === 1 ? 'Contract' : '',
                        hasFixed: false,
                        isIdle: true,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                }
                
                console.log(`Created ${workloadData.length} sample workload records`);
            }
        } catch (error) {
            console.error('Error loading workload data:', error);
            workloadData = [];
        }
        
        // Transform workload data
        const transformedWorkload = workloadData.map(item => ({
            _id: String(item._id),
            computerId: String(item.computerId),
            year: item.year,
            month: item.month,
            hours: item.hours || 0,
            activity: item.activity || '',
            hasFixed: item.hasFixed || false,
            isIdle: item.isIdle || false,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt
        }));

        console.log('\n=== Data Load Complete ===');
        
        return {
            computer: transformedComputer,
            productValidityGroup: transformedProductDetails,
            workload: transformedWorkload,
            success: true
        };

    } catch (error) {
        console.error('\n=== Error ===');
        console.error('Details:', error);
        return {
            computer: null,
            productValidityGroup: [],
            workload: [],
            success: false,
            error: error.message || 'Failed to load data'
        };
    }
}

/** @type {import('./$types').Actions} */
export const actions = {
    updateWorkload: async ({ request }) => {
        try {
            const formData = await request.formData();
            const data = formData.get('data');
            
            if (!data || typeof data !== 'string') {
                throw new Error('Invalid form data');
            }
            
            const itemData = JSON.parse(data);
            
            const db = client.db('ServiceContracts');
            
            await db.collection('Workload').updateOne(
                { _id: new ObjectId(itemData._id) },
                { 
                    $set: {
                        activity: itemData.activity,
                        hours: Number(itemData.hours),
                        updatedAt: new Date()
                    }
                }
            );

            return { success: true };
        } catch (error) {
            return { success: false };
        }
    }
};
