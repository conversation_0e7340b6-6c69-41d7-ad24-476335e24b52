import { init } from '$lib/db';
import { MongoClient } from 'mongodb';

/**
 * Safely fetch count from a collection with error handling
 * @param {import('mongodb').Db} db - MongoDB database connection
 * @param {string} collectionName - Name of the collection
 * @returns {Promise<number>} - Count of documents or 0 if error
 */
async function safeCountDocuments(db, collectionName) {
    try {
        return await db.collection(collectionName).countDocuments();
    } catch (error) {
        console.error(`Error counting documents in ${collectionName}:`, error);
        return 0;
    }
}

/** @type {import('./$types').PageServerLoad} */
export const load = async () => {
    try {
        // Get database connection
        const db = await init();
        
        // Use Promise.all to fetch all counts in parallel with error handling for each
        const [
            customerCount,
            sitesCount,
            regionsCount,
            customerComputersCount,
            calculationRulesCount,
            engineVariantsCount,
            calculationsCount,
            serviceCodeActionsCount,
            productValidityGroupsCount,
            serviceCodesCount,
            serviceCodeHeaderCount,
            baseServicesCount,
            labourCostsCount,
            productDesignationsCount,
            pdpValidityCount,
            serviceCodeAndActionTypeCount,
            partNumbersStandardCount,
            labourTimeCount,
            pricelistCount,
            priceFileCount,
            serviceIdCount
        ] = await Promise.all([
            safeCountDocuments(db, 'Customers'),
            safeCountDocuments(db, 'Sites'),
            safeCountDocuments(db, 'Regions'),
            safeCountDocuments(db, 'CustomerComputers'),
            safeCountDocuments(db, 'CalculationRules'),
            safeCountDocuments(db, 'EngineVariants'),
            safeCountDocuments(db, 'Calculations'),
            safeCountDocuments(db, 'ServiceCodeActions'),
            safeCountDocuments(db, 'ProductValidityGroup'), // This is the correct collection for product designations
            safeCountDocuments(db, 'ServiceCodes'),
            safeCountDocuments(db, 'ServiceCodeHeader'),
            safeCountDocuments(db, 'BaseServices'),
            safeCountDocuments(db, 'LabourCosts'),
            safeCountDocuments(db, 'ProductValidityGroup'), // Use ProductValidityGroup instead of ProductDesignation
            safeCountDocuments(db, 'PDPValidity'),
            safeCountDocuments(db, 'ServiceCodeAndActionType'),
            safeCountDocuments(db, 'PartNumbersStandard'),
            safeCountDocuments(db, 'LabourTime'),
            safeCountDocuments(db, 'PriceList'), // Correct PascalCase name
            safeCountDocuments(db, 'PriceFile'),
            safeCountDocuments(db, 'ServiceId') // Correct casing
        ]);

        return {
            customerCount,
            sitesCount,
            regionsCount,
            customerComputersCount,
            calculationRulesCount,
            engineVariantsCount,
            calculationsCount,
            serviceCodeActionsCount,
            productValidityGroupsCount,
            serviceCodesCount,
            serviceCodeHeaderCount,
            baseServicesCount,
            labourCostsCount,
            productDesignationsCount,
            pdpValidityCount,
            serviceCodeAndActionTypeCount,
            partNumbersStandardCount,
            labourTimeCount,
            pricelistCount,
            priceFileCount,
            serviceIdCount
        };
    } catch (error) {
        console.error('Error loading counts:', error);
        // Return default values in case of error
        return {
            customerCount: 0,
            sitesCount: 0,
            regionsCount: 0,
            customerComputersCount: 0,
            calculationRulesCount: 0,
            engineVariantsCount: 0,
            calculationsCount: 0,
            serviceCodeActionsCount: 0,
            productValidityGroupsCount: 0,
            serviceCodesCount: 0,
            serviceCodeHeaderCount: 0,
            baseServicesCount: 0,
            labourCostsCount: 0,
            productDesignationsCount: 0,
            pdpValidityCount: 0,
            serviceCodeAndActionTypeCount: 0,
            partNumbersStandardCount: 0,
            labourTimeCount: 0,
            pricelistCount: 0,
            priceFileCount: 0,
            serviceIdCount: 0
        };
    }
}
