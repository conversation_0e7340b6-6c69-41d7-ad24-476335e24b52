// API endpoint for handling individual service action operations
import { json } from '@sveltejs/kit';
import { ObjectId } from 'mongodb';
import { client } from '$lib/db';

const dbName = 'ServiceContracts';

// Get a specific service action
export async function GET({ params }) {
  try {
    const db = client.db(dbName);
    const id = params.id;
    
    // Validate ObjectId
    if (!ObjectId.isValid(id)) {
      return json({ error: 'Invalid ID format' }, { status: 400 });
    }
    
    const serviceAction = await db.collection('ServiceActions').findOne({ 
      _id: new ObjectId(id) 
    });
    
    if (!serviceAction) {
      return json({ error: 'Service action not found' }, { status: 404 });
    }
    
    return json(serviceAction);
  } catch (error) {
    console.error('Error fetching service action:', error);
    return json({ error: String(error) }, { status: 500 });
  }
}

// Update a service action
export async function PUT({ request, params }) {
  try {
    const db = client.db(dbName);
    const id = params.id;
    const data = await request.json();
    
    // Validate ObjectId
    if (!ObjectId.isValid(id)) {
      return json({ error: 'Invalid ID format' }, { status: 400 });
    }
    
    // Validate required fields
    if (!data.title || !data.demarcation) {
      return json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    // Update timestamp
    data.updatedAt = new Date();
    
    // Remove _id from data to prevent MongoDB error
    if (data._id) {
      delete data._id;
    }
    
    // Update record
    const result = await db.collection('ServiceActions').updateOne(
      { _id: new ObjectId(id) },
      { $set: data }
    );
    
    if (result.matchedCount === 0) {
      return json({ error: 'Service action not found' }, { status: 404 });
    }
    
    return json({ 
      _id: id,
      ...data
    });
  } catch (error) {
    console.error('Error updating service action:', error);
    return json({ error: String(error) }, { status: 500 });
  }
}

// Delete a service action
export async function DELETE({ params }) {
  try {
    const db = client.db(dbName);
    const id = params.id;
    
    // Validate ObjectId
    if (!ObjectId.isValid(id)) {
      return json({ error: 'Invalid ID format' }, { status: 400 });
    }
    
    const result = await db.collection('ServiceActions').deleteOne({ 
      _id: new ObjectId(id) 
    });
    
    if (result.deletedCount === 0) {
      return json({ error: 'Service action not found' }, { status: 404 });
    }
    
    return json({ success: true });
  } catch (error) {
    console.error('Error deleting service action:', error);
    return json({ error: String(error) }, { status: 500 });
  }
}
