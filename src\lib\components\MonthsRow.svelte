<script lang="ts">
  // Props
  export let months: string[] = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  export let shortMonths: boolean = false;
  export let selectedMonth: number | null = null;
  export let highlightCurrentMonth: boolean = true;
  
  // Computed
  $: displayMonths = shortMonths ? 
    months.map(m => m.substring(0, 3)) : 
    months;
    
  $: currentMonth = new Date().getMonth();
</script>

<div class="months-row-grid">
  {#each displayMonths as month, index}
    <div 
      class="month-cell"
      class:selected={selectedMonth === index}
      class:current={highlightCurrentMonth && index === currentMonth}
      on:click={() => {
        if (typeof onSelect === 'function') {
          onSelect(index);
        }
      }}
      on:keydown={(e) => {
        if (e.key === 'Enter' && typeof onSelect === 'function') {
          onSelect(index);
        }
      }}
      role="button"
      tabindex="0"
      data-month={index + 1}
    >
      <span class="month-name">{month}</span>
      <slot name="month-content" month={index + 1}></slot>
    </div>
  {/each}
</div>

<style>
  .months-row-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 0.5rem;
    width: 100%;
  }
  
  .month-cell {
    background: #1e293b;
    border-radius: 6px;
    padding: 0.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 2.5rem;
  }
  
  .month-cell:hover {
    background: #334155;
  }
  
  .month-cell.selected {
    background: #3b82f6;
    color: white;
  }
  
  .month-cell.current {
    border: 1px solid #60a5fa;
  }
  
  .month-name {
    font-weight: 500;
    font-size: 0.875rem;
  }
  
  @media (max-width: 768px) {
    .months-row-grid {
      grid-template-columns: repeat(6, 1fr);
      grid-template-rows: repeat(2, 1fr);
    }
  }
  
  @media (max-width: 480px) {
    .months-row-grid {
      grid-template-columns: repeat(4, 1fr);
      grid-template-rows: repeat(3, 1fr);
    }
  }
</style>
