const { MongoClient } = require('mongodb');

async function updateWorkloadActivity() {
    const uri = "mongodb://localhost:27017";
    const client = new MongoClient(uri);

    try {
        await client.connect();
        console.log('Connected to MongoDB');

        const db = client.db("ServiceContracts");
        const Workload = db.collection("Workload");

        // Update all documents in Workload collection
        const result = await Workload.updateMany(
            {}, // match all documents
            { 
                $set: { 
                    activity: 'idle'
                } 
            }
        );

        console.log(`Matched ${result.matchedCount} documents`);
        console.log(`Modified ${result.modifiedCount} documents`);

    } catch (error) {
        console.error('Error updating workload activity:', error);
    } finally {
        await client.close();
        console.log('Disconnected from MongoDB');
    }
}

// Run the update
updateWorkloadActivity().catch(console.error);
