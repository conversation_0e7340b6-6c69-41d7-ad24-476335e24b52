<script>
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import ContentGrid from '$lib/components/grids/ContentGrid.svelte';

  export let data;

  function goBack() {
    goto(`/computer-id/${$page.params.id}/service-activities/service-planning`);
  }
  
  // Structure the data for better viewing
  const jsonSections = [
    { title: "Computer Information", data: data.computer },
    { title: "Contract Details", data: data.contract },
    { title: "Service Workload", data: data.workload },
    { title: "Active Fault Codes", data: data.activeFaultCodes },
    { title: "Actual Computer Hours", data: data.actualComputerHours },
    { title: "Product Validity Group", data: data.productValidityGroup }
  ];
</script>

<ContentGrid>
  <div class="json-view-container">
    <div class="header">
      <h1>Service Planning Data</h1>
      <button class="back-btn" on:click={goBack}>← Back to Service Planning</button>
    </div>
    
    {#each jsonSections as section}
      {#if section.data}
        <div class="json-section">
          <h2>{section.title}</h2>
          <div class="json-content">
            <pre>{JSON.stringify(section.data, null, 2)}</pre>
          </div>
        </div>
      {/if}
    {/each}
  </div>
</ContentGrid>

<style>
  .json-view-container {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 1rem;
  }
  
  h1 {
    font-size: 1.875rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
  }

  .back-btn {
    background-color: #3b82f6;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 0.875rem;
  }

  .back-btn:hover {
    background-color: #2563eb;
  }
  
  .json-section {
    margin-bottom: 2rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    overflow: hidden;
  }
  
  .json-section h2 {
    margin: 0;
    padding: 0.75rem 1rem;
    background-color: #f1f5f9;
    border-bottom: 1px solid #e2e8f0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #334155;
  }

  .json-content {
    background-color: #f8fafc;
    padding: 1rem;
    overflow-x: auto;
  }
  
  pre {
    margin: 0;
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    color: #334155;
    white-space: pre-wrap;
  }
</style>
