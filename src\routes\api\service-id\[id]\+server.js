import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

// MongoDB connection URI
const uri = 'mongodb://localhost:27017';

// GET: Fetch a specific service ID item by ID
export async function GET({ params }) {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    const serviceIdCollection = db.collection('ServiceID');
    
    // Validate ObjectId format
    if (!ObjectId.isValid(params.id)) {
      return new Response(JSON.stringify({ error: 'Invalid ID format' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Find the service ID item by its ObjectId
    const item = await serviceIdCollection.findOne({ _id: new ObjectId(params.id) });
    
    if (!item) {
      return new Response(JSON.stringify({ error: 'Service ID item not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    return json(item);
    
  } catch (error) {
    console.error('Error fetching service ID item:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  } finally {
    await client.close();
  }
}

// DELETE: Remove a service ID item by ID
export async function DELETE({ params }) {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    const serviceIdCollection = db.collection('ServiceID');
    
    // Validate ObjectId format
    if (!ObjectId.isValid(params.id)) {
      return new Response(JSON.stringify({ error: 'Invalid ID format' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Delete the service ID item
    const result = await serviceIdCollection.deleteOne({ _id: new ObjectId(params.id) });
    
    if (result.deletedCount === 0) {
      return new Response(JSON.stringify({ error: 'Service ID item not found or could not be deleted' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    return json({ success: true, message: 'Service ID item deleted successfully' });
    
  } catch (error) {
    console.error('Error deleting service ID item:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  } finally {
    await client.close();
  }
}
