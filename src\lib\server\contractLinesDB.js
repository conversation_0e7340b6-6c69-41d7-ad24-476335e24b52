import { COLLECTIONS, serializeDocument, createObjectId, executeDbOperation } from './serviceContractDB.js';
import serviceContractDB from './serviceContractDB.js';

const db = serviceContractDB.db;
const collection = COLLECTIONS.CONTRACT_LINES;

/**
 * Create a new contract line
 * @param {Object} contractLine - Contract line data
 * @returns {Promise<Object>} Inserted document with ID
 */
export async function createContractLine(contractLine) {
  if (!contractLine.contractHeaderId) {
    throw new Error('Contract header ID is required');
  }
  
  // Add timestamps
  const now = new Date();
  const documentToInsert = {
    ...contractLine,
    contractHeaderId: createObjectId(contractLine.contractHeaderId),
    createdAt: now,
    updatedAt: now
  };
  
  return executeDbOperation(async () => {
    const result = await db.collection(collection).insertOne(documentToInsert);
    if (!result.acknowledged) {
      throw new Error('Failed to create contract line');
    }
    return {
      ...serializeDocument(documentToInsert),
      _id: result.insertedId.toString()
    };
  }, 'Error creating contract line');
}

/**
 * Create multiple contract lines in a single operation
 * @param {Array} contractLines - Array of contract line data
 * @param {string} contractHeaderId - Contract header ID
 * @returns {Promise<Array>} Array of inserted documents with IDs
 */
export async function createContractLines(contractLines, contractHeaderId) {
  if (!contractHeaderId) {
    throw new Error('Contract header ID is required');
  }
  
  if (!Array.isArray(contractLines) || contractLines.length === 0) {
    return [];
  }
  
  // Add header ID and timestamps to all lines
  const now = new Date();
  const contractHeaderObjectId = createObjectId(contractHeaderId);
  
  const documentsToInsert = contractLines.map(line => ({
    ...line,
    contractHeaderId: contractHeaderObjectId,
    createdAt: now,
    updatedAt: now
  }));
  
  return executeDbOperation(async () => {
    const result = await db.collection(collection).insertMany(documentsToInsert);
    if (!result.acknowledged) {
      throw new Error('Failed to create contract lines');
    }
    
    // Return the inserted documents with IDs
    return documentsToInsert.map((doc, index) => ({
      ...serializeDocument(doc),
      _id: result.insertedIds[index].toString()
    }));
  }, 'Error creating contract lines');
}

/**
 * Get all contract lines for a contract header
 * @param {string} contractHeaderId - Contract header ID
 * @param {Object} options - MongoDB options (sort, limit, etc.)
 * @returns {Promise<Array>} Array of contract lines
 */
export async function getContractLinesByHeaderId(contractHeaderId, options = {}) {
  return executeDbOperation(async () => {
    const headerObjectId = createObjectId(contractHeaderId);
    if (!headerObjectId) return [];
    
    const defaultOptions = {
      sort: { lineNumber: 1 },
      ...options
    };
    
    const cursor = db.collection(collection).find(
      { contractHeaderId: headerObjectId }, 
      defaultOptions
    );
    
    const documents = await cursor.toArray();
    return documents.map(doc => serializeDocument(doc));
  }, 'Error retrieving contract lines');
}

/**
 * Get a single contract line by ID
 * @param {string} id - Contract line ID
 * @returns {Promise<Object|null>} Contract line or null if not found
 */
export async function getContractLineById(id) {
  return executeDbOperation(async () => {
    const objectId = createObjectId(id);
    if (!objectId) return null;
    
    const document = await db.collection(collection).findOne({ _id: objectId });
    return document ? serializeDocument(document) : null;
  }, 'Error retrieving contract line');
}

/**
 * Update a contract line
 * @param {string} id - Contract line ID
 * @param {Object} updates - Fields to update
 * @returns {Promise<Object|null>} Updated contract line or null if not found
 */
export async function updateContractLine(id, updates) {
  return executeDbOperation(async () => {
    const objectId = createObjectId(id);
    if (!objectId) return null;
    
    // Add updated timestamp
    const updateData = {
      ...updates,
      updatedAt: new Date()
    };
    
    // Don't allow updating the _id or contractHeaderId fields
    if (updateData._id) delete updateData._id;
    if (updateData.contractHeaderId) delete updateData.contractHeaderId;
    
    const result = await db.collection(collection).findOneAndUpdate(
      { _id: objectId },
      { $set: updateData },
      { returnDocument: 'after' }
    );
    
    return result.value ? serializeDocument(result.value) : null;
  }, 'Error updating contract line');
}

/**
 * Delete a contract line
 * @param {string} id - Contract line ID
 * @returns {Promise<boolean>} True if deleted, false if not found
 */
export async function deleteContractLine(id) {
  return executeDbOperation(async () => {
    const objectId = createObjectId(id);
    if (!objectId) return false;
    
    const result = await db.collection(collection).deleteOne({ _id: objectId });
    return result.deletedCount > 0;
  }, 'Error deleting contract line');
}

/**
 * Delete all contract lines for a contract header
 * @param {string} contractHeaderId - Contract header ID
 * @returns {Promise<number>} Number of deleted lines
 */
export async function deleteContractLinesByHeaderId(contractHeaderId) {
  return executeDbOperation(async () => {
    const headerObjectId = createObjectId(contractHeaderId);
    if (!headerObjectId) return 0;
    
    const result = await db.collection(collection).deleteMany({ 
      contractHeaderId: headerObjectId 
    });
    
    return result.deletedCount;
  }, 'Error deleting contract lines by header ID');
}

export default {
  createContractLine,
  createContractLines,
  getContractLinesByHeaderId,
  getContractLineById,
  updateContractLine,
  deleteContractLine,
  deleteContractLinesByHeaderId
};
