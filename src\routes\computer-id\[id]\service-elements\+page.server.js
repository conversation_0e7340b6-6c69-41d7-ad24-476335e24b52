import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/** @type {import('./$types').PageServerLoad} */
export async function load({ params, url }) {
    try {
        const db = client.db('ServiceContracts');
        const computerId = params.id;
        
        // Get URL parameters
        const productValidityGroupParam = url.searchParams.get('productValidityGroup');
        const computerCategoryParam = url.searchParams.get('computerCategory');
        
        console.log('ProductValidityGroup from URL:', productValidityGroupParam);
        console.log('Computer Category from URL:', computerCategoryParam);
        
        // Step 1: Get computer info
        console.log('\n=== Step 1: Getting Computer Info ===');
        const computer = await db.collection('CustomerComputers')
            .findOne({ _id: new ObjectId(computerId) });

        if (!computer) {
            throw new Error('Computer not found');
        }

        // Extract or default Category - using the correct field name
        const computerCategory = computerCategoryParam || computer.Category || '';

        console.log('Computer found:', {
            id: computerId,
            productDesignation: computer.productDesignation,
            productValidityGroup: computer.ProductValidityGroup,
            category: computerCategory
        });

        // Step 2: Get product validity group info
        let productValidityGroup = '';
        
        // First try to use the query parameter if available and not empty
        if (productValidityGroupParam && productValidityGroupParam.trim() !== '') {
            productValidityGroup = productValidityGroupParam;
            console.log('Using ProductValidityGroup from URL parameter:', productValidityGroup);
        }
        // Then try to get from direct field if available
        else if (computer.ProductValidityGroup && computer.ProductValidityGroup.trim() !== '') {
            productValidityGroup = computer.ProductValidityGroup;
            console.log('Using ProductValidityGroup from computer document:', productValidityGroup);
        } 
        // Otherwise, query ProductValidityGroup collection to find matching product
        else if (computer.productDesignation || computer.productPartNumber) {
            const filter = [];
            
            if (computer.productPartNumber) {
                // Convert to Number for proper matching
                const partNumber = Number(computer.productPartNumber);
                filter.push({ ProductPartNumber: partNumber });
            }
            
            if (computer.productDesignation) {
                filter.push({ ProductDesignation: computer.productDesignation });
            }
            
            // Use $or to match either criteria
            const query = filter.length > 0 ? { $or: filter } : {};
            
            const productDetails = await db.collection('ProductValidityGroup')
                .findOne(query);
                
            if (productDetails && productDetails.ProductValidityGroup) {
                productValidityGroup = productDetails.ProductValidityGroup;
                console.log('Using ProductValidityGroup from ProductValidityGroup collection:', productValidityGroup);
            }
        }
        
        // If still no ProductValidityGroup, try to get any available one from PartNumbersServiceCodeAction
        if (!productValidityGroup || productValidityGroup.trim() === '') {
            console.log('No ProductValidityGroup found so far, checking PartNumbersServiceCodeAction collection');
            
            // Get the first available ProductValidityGroup from the collection
            const anyServiceElement = await db.collection('PartNumbersServiceCodeAction')
                .findOne({}, { projection: { ProductValidityGroup: 1 } });
                
            if (anyServiceElement && anyServiceElement.ProductValidityGroup) {
                productValidityGroup = anyServiceElement.ProductValidityGroup;
                console.log('Using default ProductValidityGroup from PartNumbersServiceCodeAction:', productValidityGroup);
            } else {
                // Absolute fallback - use a hardcoded value
                productValidityGroup = 'D13';  // Default value if nothing else works
                console.log('Using hardcoded default ProductValidityGroup:', productValidityGroup);
            }
        }
        
        console.log('Final ProductValidityGroup used for querying:', productValidityGroup);

        // Step 3: Get all service elements for this product validity group
        console.log('\n=== Step 3: Querying Service Elements ===');
        
        // Query for service elements based on product validity group
        const serviceElements = await db.collection('PartNumbersServiceCodeAction')
            .find({ 
                ProductValidityGroup: productValidityGroup 
            })
            .sort({ 
                ServiceCode: 1,
                PartNumber: 1
            })
            .toArray();
            
        console.log(`Found ${serviceElements.length} service elements`);
        
        // Step 4: Get additional service data from BaseServices collection
        console.log('\n=== Step 4: Getting Service Hours and Months Data ===');
        
        // Get all service codes from the elements
        const serviceCodes = [...new Set(serviceElements.map(item => item.ServiceCode))];
        console.log(`Found ${serviceCodes.length} unique service codes to look up`);
        
        // Query BaseServices for additional information about these service codes
        const baseServicesData = await db.collection('BaseServices')
            .find({
                ServiceCode: { $in: serviceCodes },
                ProductValidityGroup: productValidityGroup
            })
            .toArray();
            
        console.log(`Found ${baseServicesData.length} matching services in BaseServices`);
        
        // Create a lookup map for easier access
        const serviceDataMap = {};
        baseServicesData.forEach(service => {
            serviceDataMap[service.ServiceCode] = {
                hours: service.InternalNoOfHours || 0,
                months: service.InternalNoOfMonths || null,
                activityLabel: service.ServiceActivityLabel || '',
                activityPurpose: service.ActivityPurpose || ''
            };
        });
        
        // Step 5: Get labour time information for service codes, filtered by Category
        console.log('\n=== Step 5: Getting Labour Time Data ===');
        console.log('Filtering by Category:', computerCategory);
        
        // Build the query for LabourTime
        const labourTimeQuery = { 'Service Code': { $in: serviceCodes } };
        
        // Only add Category filter if it's provided and not empty
        if (computerCategory && computerCategory.trim() !== '') {
            labourTimeQuery.ComputerCategory = computerCategory;
        }
        
        const labourTimeData = await db.collection('LabourTime')
            .find(labourTimeQuery)
            .toArray();
            
        console.log(`Found ${labourTimeData.length} matching labour time records`);
        
        // Create a lookup map for labour time data
        const labourTimeMap = {};
        labourTimeData.forEach(labour => {
            labourTimeMap[labour['Service Code']] = {
                description: labour['Service Description'] || '',
                vstCode: labour['VST Code'] || '',
                vstHours: labour['VST Hours'] || '',
                servicePhase: labour.ServicePhase || '',
                computerCategory: labour.ComputerCategory || ''
            };
        });

        // Transform data for client
        const transformedComputer = {
            ...computer,
            _id: String(computer._id),
            customerId: String(computer.customerId),
            siteId: computer.siteId ? String(computer.siteId) : null,
            HoursAtContractStart: computer.HoursAtContractStart,
            // Use Category instead of ComputerCategory
            Category: computerCategory,
            ProductValidityGroup: productValidityGroup
        };

        // Combine service element data with additional service information
        const enrichedServiceElements = serviceElements.map(item => {
            const serviceCode = item.ServiceCode || '';
            const serviceInfo = serviceDataMap[serviceCode] || {};
            const labourInfo = labourTimeMap[serviceCode] || {};
            
            return {
                _id: String(item._id),
                ProductValidityGroup: item.ProductValidityGroup || '',
                ActivityPurpose: serviceInfo.activityPurpose || '',
                ServiceActivityLabel: serviceInfo.activityLabel || '',
                ServiceCode: serviceCode,
                ActionType: item.ActionType || '',
                PartNumber: item.PartNumber || '',
                UnitOfMeasure: item['Unit of Measure'] || '',
                Quantity: item.Quantity || 0,
                InternalNoOfHours: serviceInfo.hours || 0,
                InternalNoOfMonths: serviceInfo.months,
                ServiceDescription: labourInfo.description || '',
                VSTCode: labourInfo.vstCode || '',
                VSTHours: labourInfo.vstHours || '',
                ServicePhase: labourInfo.servicePhase || '',
                ComputerCategory: labourInfo.computerCategory || ''
            };
        });
        
        // Step 6: Get all LabourTime records for this Category
        console.log('\n=== Step 6: Getting Additional LabourTime Records ===');
        
        let labourTimeRecords = [];
        
        // If Category is empty, use a more general approach
        if (!computerCategory || computerCategory.trim() === '') {
            console.log('No Category specified, getting general LabourTime records');
            
            // Get records related to the service codes we already have
            if (serviceCodes.length > 0) {
                labourTimeRecords = await db.collection('LabourTime')
                    .find({ 
                        'Service Code': { $in: serviceCodes } 
                    })
                    .sort({ 
                        'Service Code': 1 
                    })
                    .limit(20) // Limit to a reasonable number
                    .toArray();
                
                console.log(`Found ${labourTimeRecords.length} LabourTime records for service codes`);
            }
            
            // If still no records, get a sample of LabourTime records
            if (labourTimeRecords.length === 0) {
                labourTimeRecords = await db.collection('LabourTime')
                    .find({})
                    .sort({ 
                        'Service Code': 1 
                    })
                    .limit(10)
                    .toArray();
                
                console.log(`Found ${labourTimeRecords.length} sample LabourTime records`);
            }
        } else {
            // Normal case: Find all LabourTime records for this ComputerCategory
            // Try using the exact Category field value from the CustomerComputers collection
            labourTimeRecords = await db.collection('LabourTime')
                .find({ 
                    ComputerCategory: computerCategory 
                })
                .sort({ 
                    'Service Code': 1 
                })
                .toArray();
                
            console.log(`Found ${labourTimeRecords.length} LabourTime records for Category: ${computerCategory}`);
            
            // If no records found, try with a direct value "D11" for testing
            if (labourTimeRecords.length === 0 && computerCategory !== "D11") {
                console.log('No records found, trying with test value "D11"');
                labourTimeRecords = await db.collection('LabourTime')
                    .find({ 
                        ComputerCategory: "D11" 
                    })
                    .sort({ 
                        'Service Code': 1 
                    })
                    .limit(10)
                    .toArray();
                    
                console.log(`Found ${labourTimeRecords.length} LabourTime records using test value D11`);
            }
        }
        
        // Transform the records
        labourTimeRecords = labourTimeRecords.map(item => ({
            _id: String(item._id),
            ServiceCode: item['Service Code'] || '',
            ServiceDescription: item['Service Description'] || '',
            VSTCode: item['VST Code'] || '',
            VSTHours: item['VST Hours'] || '',
            ServicePhase: item.ServicePhase || '',
            ComputerCategory: item.ComputerCategory || ''
        }));
        
        // Step 7: Get all ServiceContracts for this computer
        console.log('\n=== Step 7: Getting Service Contracts ===');
        
        let serviceContracts = [];
        try {
            // Query for all service contracts related to this computer
            serviceContracts = await db.collection('ServiceContracts')
                .find({ 
                    computerId: new ObjectId(computerId)
                })
                .sort({ 
                    startDate: -1 // Most recent first
                })
                .toArray();
                
            console.log(`Found ${serviceContracts.length} service contracts for computer ID: ${computerId}`);
            
            // If no contracts found directly, try alternate fields
            if (serviceContracts.length === 0) {
                console.log('No contracts found with computerId, trying ComputerId field');
                serviceContracts = await db.collection('ServiceContracts')
                    .find({ 
                        ComputerId: new ObjectId(computerId)
                    })
                    .sort({ 
                        startDate: -1 
                    })
                    .toArray();
                    
                console.log(`Found ${serviceContracts.length} service contracts using ComputerId field`);
            }
            
            // If still no contracts, try a generic query to get at least some sample data
            if (serviceContracts.length === 0) {
                console.log('Still no contracts found, getting a sample from ServiceContracts collection');
                serviceContracts = await db.collection('ServiceContracts')
                    .find({})
                    .limit(5)
                    .toArray();
                    
                console.log(`Found ${serviceContracts.length} sample service contracts`);
            }
            
            // Transform the contracts data
            serviceContracts = serviceContracts.map(contract => ({
                _id: String(contract._id),
                contractNumber: contract.contractNumber || contract.ContractNumber || '',
                startDate: contract.startDate || contract.StartDate || '',
                endDate: contract.endDate || contract.EndDate || '',
                type: contract.type || contract.Type || contract.ContractType || '',
                status: contract.status || contract.Status || 'Active',
                description: contract.description || contract.Description || '',
                customerName: contract.customerName || contract.CustomerName || '',
                amount: contract.amount || contract.Amount || 0
            }));
        } catch (error) {
            console.error('Error fetching service contracts:', error);
            // Don't throw, just return an empty array
            serviceContracts = [];
        }
        
        console.log('\n=== Data Load Complete ===');
        
        return {
            computer: transformedComputer,
            serviceElements: enrichedServiceElements,
            labourTimeRecords: labourTimeRecords,
            productValidityGroup: productValidityGroup,
            computerCategory: computerCategory,
            serviceContracts: serviceContracts,
            success: true
        };

    } catch (error) {
        console.error('\n=== Error ===');
        console.error('Details:', error);
        return {
            computer: null,
            serviceElements: [],
            labourTimeRecords: [],
            productValidityGroup: '',
            computerCategory: '',
            serviceContracts: [],
            success: false,
            error: error.message || 'Failed to load data'
        };
    }
}
