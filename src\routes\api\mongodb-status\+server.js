import { json } from '@sveltejs/kit';
import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';

/**
 * Handles GET requests to check MongoDB connection status
 * @param {import('@sveltejs/kit').RequestEvent} event - The request event
 * @returns {Promise<Response>} JSON response with connection status
 */
export async function GET() {
    let client = null;
    
    try {
        client = new MongoClient(uri);
        await client.connect();
        
        // Try to access the ServiceContracts database
        const db = client.db('ServiceContracts');
        
        // Run a simple command to verify connection
        const result = await db.command({ ping: 1 });
        
        return json({
            connected: result.ok === 1,
            database: 'ServiceContracts'
        });
    } catch (error) {
        console.error('Error checking MongoDB connection:', error);
        return json({
            connected: false,
            error: 'Failed to connect to MongoDB'
        });
    } finally {
        if (client) {
            await client.close();
        }
    }
}
