<script lang="ts">
  import BaseGrid from './grids/BaseGrid.svelte';
  
  // Props
  export let computer: any = null;
  export let contract: any = null;
  
  // Format date to display
  function formatDate(date: string | Date | null | undefined): string {
    if (!date) return 'Not set';
    try {
      return new Date(date).toLocaleDateString();
    } catch (error) {
      return 'Invalid date';
    }
  }
  
  // Calculate days remaining in contract
  $: daysRemaining = contract?.endDate ? getDaysRemaining(contract.endDate) : null;
  
  function getDaysRemaining(endDate: string | Date): number {
    const end = new Date(endDate);
    const today = new Date();
    const diffTime = end.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
</script>

<BaseGrid>
  <div class="computer-info-panel">
    <header class="panel-header">
      <h2>Computer Details</h2>
    </header>
    
    <div class="panel-content">
      {#if computer}
        <div class="info-grid">
          <div class="info-section">
            <h3>Basic Information</h3>
            <div class="info-row">
              <div class="info-label">Computer Name:</div>
              <div class="info-value">{computer.name || 'Not specified'}</div>
            </div>
            
            <div class="info-row">
              <div class="info-label">Serial Number:</div>
              <div class="info-value">{computer.serialNumber || 'Not specified'}</div>
            </div>
            
            <div class="info-row">
              <div class="info-label">Purchase Date:</div>
              <div class="info-value">{formatDate(computer.purchaseDate)}</div>
            </div>
            
            <div class="info-row">
              <div class="info-label">Delivery Date:</div>
              <div class="info-value">{formatDate(computer.deliveryDate)}</div>
            </div>
          </div>
          
          {#if contract}
            <div class="info-section">
              <h3>Contract Information</h3>
              <div class="info-row">
                <div class="info-label">Contract Type:</div>
                <div class="info-value">{contract.type || 'Not specified'}</div>
              </div>
              
              <div class="info-row">
                <div class="info-label">Start Date:</div>
                <div class="info-value">{formatDate(contract.startDate)}</div>
              </div>
              
              <div class="info-row">
                <div class="info-label">End Date:</div>
                <div class="info-value">{formatDate(contract.endDate)}</div>
              </div>
              
              {#if daysRemaining !== null}
                <div class="info-row">
                  <div class="info-label">Days Remaining:</div>
                  <div class="info-value {daysRemaining < 30 ? 'text-warning' : daysRemaining < 0 ? 'text-danger' : ''}">
                    {daysRemaining < 0 ? 'Expired' : daysRemaining}
                  </div>
                </div>
              {/if}
            </div>
          {/if}
        </div>
      {:else}
        <div class="empty-state">
          <p>Computer information not available</p>
        </div>
      {/if}
    </div>
  </div>
</BaseGrid>

<style>
  .computer-info-panel {
    display: grid;
    grid-template-rows: auto 1fr;
    height: 100%;
    background: #1e293b;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .panel-header {
    padding: 1rem;
    background: #0f172a;
    border-bottom: 1px solid #334155;
  }
  
  .panel-header h2 {
    margin: 0;
    color: #60a5fa;
    font-size: 1.25rem;
  }
  
  .panel-content {
    padding: 1.5rem;
    overflow-y: auto;
  }
  
  .info-grid {
    display: grid;
    gap: 2rem;
  }
  
  .info-section {
    display: grid;
    gap: 0.75rem;
  }
  
  h3 {
    margin: 0 0 0.75rem 0;
    color: #e2e8f0;
    font-size: 1.1rem;
    border-bottom: 1px solid #334155;
    padding-bottom: 0.5rem;
  }
  
  .info-row {
    display: grid;
    grid-template-columns: 130px 1fr;
    gap: 1rem;
    align-items: baseline;
  }
  
  .info-label {
    color: #94a3b8;
    font-size: 0.875rem;
  }
  
  .info-value {
    color: #e2e8f0;
    font-weight: 500;
  }
  
  .text-warning {
    color: #f59e0b;
  }
  
  .text-danger {
    color: #ef4444;
  }
  
  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #94a3b8;
  }
</style>
