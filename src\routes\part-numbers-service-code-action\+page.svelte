<script lang="ts">
    import { goto } from '$app/navigation';
    import { page } from '$app/stores';

    interface PartNumbersServiceCodeAction {
        _id: string;
        ServiceCode: string;
        PartNumber: string;
        ActionType: string;
        'Product Validity Group': string;
        Quantity: number;
        'Unit of Measure': string;
        createdAt?: string;
        updatedAt?: string;
    }

    export let data: { items: PartNumbersServiceCodeAction[], error?: string };

    let selectedItems: Set<string> = new Set();
    let isDeleting = false;

    function toggleSelection(id: string) {
        if (selectedItems.has(id)) {
            selectedItems.delete(id);
        } else {
            selectedItems.add(id);
        }
        selectedItems = selectedItems; // Trigger reactivity
    }

    function selectAll() {
        if (selectedItems.size === data.items.length) {
            selectedItems.clear();
        } else {
            selectedItems = new Set(data.items.map(item => item._id));
        }
        selectedItems = selectedItems; // Trigger reactivity
    }

    async function deleteSelected() {
        if (selectedItems.size === 0) return;

        if (!confirm(`Are you sure you want to delete ${selectedItems.size} item(s)?`)) {
            return;
        }

        isDeleting = true;
        const deletePromises = Array.from(selectedItems).map(id =>
            fetch(`/api/part-numbers-service-code-action/${id}`, {
                method: 'DELETE'
            })
        );

        try {
            await Promise.all(deletePromises);
            // Reload the page to refresh data
            window.location.reload();
        } catch (error) {
            console.error('Error deleting items:', error);
            alert('Error deleting some items. Please try again.');
        } finally {
            isDeleting = false;
        }
    }

    function goToDetail(id: string) {
        goto(`/part-numbers-service-code-action/${id}`);
    }

    function addNew() {
        goto('/part-numbers-service-code-action/new');
    }

    function goBack() {
        history.back();
    }
</script>

<div class="container">
    <div class="header">
        <div class="header-actions">
            <button class="btn btn-secondary" on:click={goBack}>
                ← Back
            </button>
            <h1>Part Numbers Service Code Actions</h1>
            <button class="btn btn-primary" on:click={addNew}>
                + Add New
            </button>
        </div>

        {#if selectedItems.size > 0}
            <div class="bulk-actions">
                <span class="selected-count">{selectedItems.size} item(s) selected</span>
                <button
                    class="btn btn-danger"
                    on:click={deleteSelected}
                    disabled={isDeleting}
                >
                    {isDeleting ? 'Deleting...' : 'Delete Selected'}
                </button>
            </div>
        {/if}
    </div>

    {#if data.error}
        <div class="error">
            Error: {data.error}
        </div>
    {:else if data.items.length === 0}
        <div class="empty-state">
            <p>No part numbers service code actions found.</p>
            <button class="btn btn-primary" on:click={addNew}>
                Add First Item
            </button>
        </div>
    {:else}
        <div class="table-container">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>
                            <input
                                type="checkbox"
                                checked={selectedItems.size === data.items.length && data.items.length > 0}
                                on:change={selectAll}
                            />
                        </th>
                        <th>Service Code</th>
                        <th>Part Number</th>
                        <th>Action Type</th>
                        <th>Product Validity Group</th>
                        <th>Quantity</th>
                        <th>Unit of Measure</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {#each data.items as item}
                        <tr
                            class="clickable-row"
                            class:selected={selectedItems.has(item._id)}
                            on:click={() => goToDetail(item._id)}
                            on:keydown={(e) => e.key === 'Enter' && goToDetail(item._id)}
                            tabindex="0"
                            role="button"
                            aria-label="Select item {item.ServiceCode} - {item.PartNumber}"
                        >
                            <td on:click|stopPropagation>
                                <input
                                    type="checkbox"
                                    checked={selectedItems.has(item._id)}
                                    on:change={() => toggleSelection(item._id)}
                                />
                            </td>
                            <td>{item.ServiceCode || '-'}</td>
                            <td>{item.PartNumber || '-'}</td>
                            <td>{item.ActionType || '-'}</td>
                            <td>{item['Product Validity Group'] || '-'}</td>
                            <td>{item.Quantity || '-'}</td>
                            <td>{item['Unit of Measure'] || '-'}</td>
                            <td on:click|stopPropagation>
                                <div class="action-buttons">
                                    <button
                                        class="btn btn-sm btn-primary"
                                        on:click={() => goToDetail(item._id)}
                                    >
                                        Select
                                    </button>
                                </div>
                            </td>
                        </tr>
                    {/each}
                </tbody>
            </table>
        </div>

        <div class="table-footer">
            <span class="item-count">
                Total: {data.items.length} item(s)
            </span>
        </div>
    {/if}
</div>

<style>
    .container {
        padding: 1rem;
        max-width: 1200px;
        margin: 0 auto;
    }

    .header {
        margin-bottom: 1.5rem;
    }

    .header-actions {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .header-actions h1 {
        flex: 1;
        margin: 0;
        font-size: 1.5rem;
        font-weight: 600;
    }

    .bulk-actions {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 0.75rem;
        background-color: #f8f9fa;
        border-radius: 0.375rem;
        border: 1px solid #dee2e6;
    }

    .selected-count {
        font-weight: 500;
        color: #495057;
    }

    .btn {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 0.375rem;
        cursor: pointer;
        font-size: 0.875rem;
        font-weight: 500;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        transition: all 0.2s;
    }

    .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    .btn-primary {
        background-color: #007bff;
        color: white;
    }

    .btn-primary:hover:not(:disabled) {
        background-color: #0056b3;
    }

    .btn-secondary {
        background-color: #6c757d;
        color: white;
    }

    .btn-secondary:hover:not(:disabled) {
        background-color: #545b62;
    }

    .btn-danger {
        background-color: #dc3545;
        color: white;
    }

    .btn-danger:hover:not(:disabled) {
        background-color: #c82333;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .error {
        padding: 1rem;
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
    }

    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
        color: #6c757d;
    }

    .empty-state p {
        margin-bottom: 1rem;
        font-size: 1.1rem;
    }

    .table-container {
        overflow-x: auto;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
    }

    .data-table {
        width: 100%;
        border-collapse: collapse;
        background-color: white;
    }

    .data-table th,
    .data-table td {
        padding: 0.75rem;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
    }

    .data-table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
        position: sticky;
        top: 0;
    }

    .data-table tr:hover {
        background-color: #f8f9fa;
    }

    .data-table tr.selected {
        background-color: #e3f2fd;
    }

    .clickable-row {
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .clickable-row:hover {
        background-color: #eff6ff !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .clickable-row:focus {
        outline: 2px solid #3b82f6;
        outline-offset: -2px;
        background-color: #eff6ff;
    }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
    }

    .table-footer {
        padding: 1rem;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 0.375rem 0.375rem;
        text-align: right;
    }

    .item-count {
        color: #6c757d;
        font-size: 0.875rem;
    }
</style>
