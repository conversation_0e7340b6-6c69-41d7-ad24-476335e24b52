import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

// Define valid activity types for validation
const VALID_ACTIVITY_TYPES = [
    'Regular Service',
    'Contract Service',
    'Maintenance',
    'Repair',
    'Inspection',
    'Installation',
    'Remote Support',
    'Software Update',
    'Hardware Update',
    'Training',
    'Consultation'
];

/** @type {import('./$types').RequestHandler} */
export async function POST({ request }) {
    try {
        await client.connect();
        const db = client.db('ServiceContracts');
        const activitiesCollection = db.collection('ServiceActivities');
        
        const data = await request.json();
        const { year, month, hours, activity, hasFixed, computerId } = data;
        
        // Validate required fields
        if (!year || !month) {
            return json({ 
                success: false, 
                message: 'Year and month are required',
                validActivities: VALID_ACTIVITY_TYPES
            }, { status: 400 });
        }
        
        if (hours === undefined) {
            return json({ 
                success: false, 
                message: 'Hours value is required',
                validActivities: VALID_ACTIVITY_TYPES
            }, { status: 400 });
        }
        
        if (!computerId || !ObjectId.isValid(computerId)) {
            return json({ 
                success: false, 
                message: 'Valid computerId is required',
                validActivities: VALID_ACTIVITY_TYPES
            }, { status: 400 });
        }
        
        // Check if an entry already exists for this year/month and computer
        const existingActivity = await activitiesCollection.findOne({
            computerId: new ObjectId(computerId),
            year: parseInt(year),
            month: parseInt(month)
        });
        
        if (existingActivity) {
            return json({ 
                success: false, 
                message: 'Activity already exists for this year and month',
                data: existingActivity,
                validActivities: VALID_ACTIVITY_TYPES
            }, { status: 409 }); // Conflict
        }
        
        // Create new activity document
        const newActivity = {
            computerId: new ObjectId(computerId),
            year: parseInt(year),
            month: parseInt(month),
            hours: parseFloat(hours) || 0,
            activity: activity || '',
            hasFixed: Boolean(hasFixed),
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        const result = await activitiesCollection.insertOne(newActivity);
        
        if (!result.acknowledged) {
            return json({ 
                success: false, 
                message: 'Failed to create activity',
                validActivities: VALID_ACTIVITY_TYPES
            }, { status: 500 });
        }
        
        // Get the newly created document
        const createdActivity = await activitiesCollection.findOne({ _id: result.insertedId });
        
        return json({ 
            success: true,
            message: 'Activity created successfully',
            data: createdActivity,
            validActivities: VALID_ACTIVITY_TYPES
        });
    } catch (error) {
        console.error('Error creating activity:', error);
        return json({ 
            success: false, 
            message: error.message,
            validActivities: VALID_ACTIVITY_TYPES
        }, { status: 500 });
    } finally {
        await client.close();
    }
}
