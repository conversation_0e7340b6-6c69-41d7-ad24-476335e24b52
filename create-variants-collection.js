import { MongoClient, ObjectId } from 'mongodb';
import { getCollection } from './src/lib/db/mongo.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function createEngineVariantsCollection() {
  try {
    // Get the EngineVariants collection (will be created if it doesn't exist)
    const collection = await getCollection('EngineVariants');
    
    // Check if collection already has data
    const count = await collection.countDocuments();
    if (count > 0) {
      console.log(`The EngineVariants collection already has ${count} documents.`);
      const readline = await import('readline');
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });
      
      const answer = await new Promise(resolve => {
        rl.question('Do you want to drop the existing collection and re-import? (yes/no): ', resolve);
      });
      
      rl.close();
      
      if (answer.toLowerCase() !== 'yes') {
        console.log('Import cancelled');
        return;
      }
      
      await collection.drop();
      console.log('Collection dropped successfully');
    }
    
    // Create an index on variant_number for uniqueness
    await collection.createIndex({ variant_number: 1 }, { unique: true });
    console.log('Created unique index on variant_number');
    
    // Read the calculation part numbers data from JSON file
    const dataPath = path.join(__dirname, 'data', 'variants.json');
    const jsonData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
    
    // Transform the data into variants
    const variants = [];
    const usedVariantNumbers = new Set();
    
    jsonData.forEach((item, index) => {
      // Skip items with missing calculation_number
      if (!item.calculation_number) {
        console.log(`Skipping item with missing calculation_number: ${JSON.stringify(item)}`);
        return;
      }
      
      // Generate a random price between 1000 and 10000
      const basePrice = Math.floor(Math.random() * 9000) + 1000;
      
      // Calculate other price components
      const laborCost = Math.floor(basePrice * 0.3);
      const materialCost = Math.floor(basePrice * 0.5);
      const overhead = Math.floor(basePrice * 0.2);
      
      // Add 20% markup for RRP
      const totalRRP = Math.floor(basePrice * 1.2);
      
      // Create a variant number based on the calculation number
      let variantNumber = `VAR-${item.calculation_number.replace(/\s+/g, '-')}`;
      
      // Handle duplicate variant numbers by adding a suffix
      if (usedVariantNumbers.has(variantNumber)) {
        let counter = 1;
        let tempVariantNumber = `${variantNumber}-${counter}`;
        
        // Keep incrementing the counter until we find a unique variant number
        while (usedVariantNumbers.has(tempVariantNumber)) {
          counter++;
          tempVariantNumber = `${variantNumber}-${counter}`;
        }
        
        variantNumber = tempVariantNumber;
      }
      
      // Add to the set of used variant numbers
      usedVariantNumbers.add(variantNumber);
      
      variants.push({
        _id: new ObjectId(),
        variant_number: variantNumber,
        name: `${item.calculation_number} Variant`,
        part_number: item.part_number,
        description: `${item.calculation_number} Engine Component Variant`,
        base_price: basePrice,
        labor_cost: laborCost,
        material_cost: materialCost,
        overhead: overhead,
        total_RRP: totalRRP,
        components: [
          { 
            calculation_number: item.calculation_number, 
            part_number: item.part_number,
            quantity: 1 
          }
        ],
        createdAt: new Date(),
        updatedAt: new Date()
      });
    });
    
    console.log(`Prepared ${variants.length} variants for import`);
    
    // Insert the variants in batches to avoid MongoDB document size limits
    const batchSize = 100;
    for (let i = 0; i < variants.length; i += batchSize) {
      const batch = variants.slice(i, i + batchSize);
      await collection.insertMany(batch);
      console.log(`Inserted batch ${Math.floor(i / batchSize) + 1} of ${Math.ceil(variants.length / batchSize)}`);
    }
    
    console.log(`Successfully imported ${variants.length} variants into the EngineVariants collection`);
  } catch (err) {
    console.error('Error creating EngineVariants collection:', err);
  }
}

// Run the function
createEngineVariantsCollection();
