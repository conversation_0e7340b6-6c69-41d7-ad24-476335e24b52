import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function addComputerCategory() {
    const client = new MongoClient(uri);
    
    try {
        await client.connect();
        console.log('Connected to MongoDB');
        
        const db = client.db(dbName);
        const customerComputersCollection = db.collection('CustomerComputers');

        // First, get all documents
        const computers = await customerComputersCollection.find({}).toArray();
        console.log(`Found ${computers.length} computers to update`);

        // Update each document
        for (const computer of computers) {
            // Extract category from model or type if possible
            let computerCategory = '';
            
            // Try to extract D followed by numbers (e.g., D11, D13, D16)
            const modelMatch = (computer.model || '').match(/D\d+/i);
            if (modelMatch) {
                computerCategory = modelMatch[0].toUpperCase();
            } else {
                // If no match in model, try type field
                const typeMatch = (computer.type || '').match(/D\d+/i);
                if (typeMatch) {
                    computerCategory = typeMatch[0].toUpperCase();
                }
            }

            const result = await customerComputersCollection.updateOne(
                { _id: computer._id },
                { 
                    $set: { 
                        ComputerCategory: computerCategory 
                    }
                }
            );

            console.log(`Updated computer ${computer._id}: ComputerCategory = "${computerCategory}"`);
        }

        // Verify updates
        console.log('\nVerifying updates...');
        const updatedComputers = await customerComputersCollection.find({}).limit(5).toArray();
        console.log('Sample updated computers:');
        updatedComputers.forEach(computer => {
            console.log({
                _id: computer._id,
                model: computer.model,
                type: computer.type,
                ComputerCategory: computer.ComputerCategory
            });
        });
        
    } catch (error) {
        console.error('Error updating collection:', error);
    } finally {
        await client.close();
        console.log('\nDisconnected from MongoDB');
    }
}

// Run the update
addComputerCategory().catch(console.error);
