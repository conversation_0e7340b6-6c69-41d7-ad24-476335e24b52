<script>
  export let categories = [];
  export let onEdit = () => {};
  export let onDelete = () => {};
</script>

<div class="grid-container">
  <div class="grid-header">
    <div class="cell">Name</div>
    <div class="cell">Description</div>
    <div class="cell">Color</div>
    <div class="cell">Actions</div>
  </div>
  
  <div class="grid-body">
    {#each categories as category}
      <div class="grid-row">
        <div class="cell">
          <span class="category-name" style="border-left: 4px solid {category.color}; padding-left: 8px;">
            {category.name}
          </span>
        </div>
        <div class="cell">{category.description || '-'}</div>
        <div class="cell">
          <div class="color-swatch" style="background-color: {category.color}"></div>
        </div>
        <div class="cell actions">
          <button class="icon-btn edit-btn" on:click={() => onEdit(category)} title="Edit">
            <span class="material-icons">edit</span>
          </button>
          <button class="icon-btn delete-btn" on:click={() => onDelete(category)} title="Delete">
            <span class="material-icons">delete</span>
          </button>
        </div>
      </div>
    {/each}
  </div>
</div>

<style>
  .grid-container {
    width: 100%;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .grid-header {
    display: grid;
    grid-template-columns: 1.5fr 3fr 0.5fr 0.5fr;
    background-color: #f5f5f5;
    font-weight: bold;
    border-bottom: 1px solid #e0e0e0;
  }
  
  .grid-row {
    display: grid;
    grid-template-columns: 1.5fr 3fr 0.5fr 0.5fr;
    border-bottom: 1px solid #e0e0e0;
  }
  
  .grid-row:last-child {
    border-bottom: none;
  }
  
  .grid-row:hover {
    background-color: #f9f9f9;
  }
  
  .cell {
    padding: 12px 16px;
    display: flex;
    align-items: center;
  }
  
  .category-name {
    font-weight: 500;
  }
  
  .color-swatch {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    border: 1px solid #ddd;
  }
  
  .actions {
    display: flex;
    gap: 8px;
    justify-content: center;
  }
  
  .icon-btn {
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    transition: background-color 0.2s;
  }
  
  .icon-btn:hover {
    background-color: #f1f1f1;
  }
  
  .edit-btn {
    color: #f39c12;
  }
  
  .delete-btn {
    color: #e74c3c;
  }
</style>
