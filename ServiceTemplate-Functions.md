# Service Template Application - Comprehensive Function Documentation

This document provides an exhaustive and detailed overview of all functions, components, and relationships in the Service Template application. It covers every aspect of the codebase including function signatures, parameters, return types, usage patterns, and internal implementations.

## Database Connection Functions

### MongoDB Connection (src/lib/db.ts)

#### Configuration Variables

| Variable | Type | Value | Description |
|----------|------|-------|-------------|
| `uri` | string | 'mongodb://localhost:27017' | MongoDB connection string |
| `client` | MongoClient | new MongoClient(uri) | MongoDB client instance |
| `dbName` | string | 'ServiceContracts' | Database name |

#### Functions

| Function | Parameters | Returns | Description |
|----------|------------|---------|-------------|
| `init()` | None | Promise\<Db\> | Initializes connection to MongoDB and returns database instance |
| `close()` | None | Promise\<void\> | Closes the MongoDB connection |

#### Detailed Implementation

```javascript
// init() function implementation
export async function init() {
    try {
        console.log('Connecting to MongoDB...');
        await client.connect();
        console.log('Successfully connected to MongoDB');
        // Test the connection by listing collections
        const db = client.db(dbName);
        const collections = await db.listCollections().toArray();
        console.log('Available collections:', collections.map(c => c.name));
        return db;
    } catch (err) {
        console.error('Failed to connect to MongoDB:', err);
        throw err;
    }
}

// close() function implementation
export async function close() {
    await client.close();
}
```

#### Process Termination Handlers

The module also includes handlers for process termination to ensure proper database connection cleanup:

```javascript
// Handle process termination
process.on('SIGINT', async () => {
    if (client) {
        await client.close();
    }
    process.exit();
});

process.on('SIGTERM', async () => {
    if (client) {
        await client.close();
    }
    process.exit();
});
```

### MongoDB Utility Functions (src/lib/db/mongo.js)

#### Configuration Variables

| Variable | Type | Value | Description |
|----------|------|-------|-------------|
| `uri` | string | process.env.MONGODB_URI \|\| 'mongodb://localhost:27017' | MongoDB connection string with environment variable fallback |
| `dbName` | string | process.env.MONGODB_DB_NAME \|\| 'ServiceContracts' | Database name with environment variable fallback |
| `client` | MongoClient | new MongoClient(uri) | MongoDB client instance |
| `db` | Db \| null | null | Database instance cache |

#### Functions

| Function | Parameters | Returns | Description |
|----------|------------|---------|-------------|
| `connectToDatabase()` | None | Promise\<Db\> | Connects to MongoDB and returns database instance |
| `getCollection(collectionName)` | collectionName: string | Promise\<Collection\> | Gets a MongoDB collection by name |
| `serializeDocument(doc)` | doc: Object | Object | Serializes a MongoDB document for JSON (converts ObjectIds to strings) |
| `serializeDocuments(docs)` | docs: Array\<Object\> | Array\<Object\> | Serializes an array of MongoDB documents |
| `isValidObjectId(id)` | id: string | boolean | Checks if a string is a valid MongoDB ObjectId |
| `toObjectId(id)` | id: string | ObjectId | Converts a string ID to MongoDB ObjectId |
| `toClientDocument(doc)` | doc: T | T | Converts MongoDB document for client-side use (converts ObjectIds to strings) |
| `toDatabaseDocument(doc)` | doc: T | T | Converts document for database use (converts string IDs to ObjectIds) |
| `closeConnection()` | None | Promise\<void\> | Closes the MongoDB connection |

#### Detailed Implementation

```javascript
// connectToDatabase() function implementation
async function connectToDatabase() {
  if (db) return db;

  try {
    // Connect the client to the server
    await client.connect();
    console.log('Connected successfully to MongoDB server');

    // Get reference to the database
    db = client.db(dbName);
    console.log(`Using database: ${dbName}`);
    return db;
  } catch (error) {
    console.error('MongoDB connection error:', error instanceof Error ? error.message : 'Unknown error');
    throw error;
  }
}

// getCollection() function implementation
export async function getCollection(collectionName) {
  try {
    const database = await connectToDatabase();
    return database.collection(collectionName);
  } catch (error) {
    console.error(`Error getting collection ${collectionName}:`, error instanceof Error ? error.message : 'Unknown error');
    throw error;
  }
}

// serializeDocument() function implementation
export function serializeDocument(doc) {
  if (!doc) return null;

  const result = { ...doc };

  // Convert ObjectId to string
  if (result._id instanceof ObjectId) {
    result._id = result._id.toString();
  }

  // Convert other ObjectIds to strings
  Object.keys(result).forEach(key => {
    if (result[key] instanceof ObjectId) {
      result[key] = result[key].toString();
    }
  });

  return result;
}
```

## Customer Computers Module

### List View (src/routes/customer-computers/+page.svelte)

#### Component State Variables

| Variable | Type | Default Value | Description |
|----------|------|---------------|-------------|
| `computers` | Computer[] | data.computers \|\| [] | List of computers from server |
| `customers` | Customer[] | data.customers \|\| [] | List of customers from server |
| `selectedCustomer` | Customer \| null | data.selectedCustomer | Currently selected customer |
| `selectedCustomerId` | string | selectedCustomer?._id \|\| '' | ID of selected customer |
| `showAddComputerForm` | boolean | false | Controls visibility of add computer form |
| `isSubmitting` | boolean | false | Tracks form submission state |
| `formError` | string | '' | Stores form error messages |
| `successMessage` | string | '' | Stores success messages |

#### Reactive Declarations

| Declaration | Dependencies | Description |
|-------------|--------------|-------------|
| `filteredComputers` | selectedCustomerId, computers | Filters computers based on selected customer |

#### Functions

| Function | Parameters | Returns | Description |
|----------|------------|---------|-------------|
| `formatDate(date)` | date: Date \| null \| undefined | string | Formats date for display as localized string or 'N/A' |
| `getCustomerName(customerId)` | customerId: string | string | Gets customer name by ID from customers array |
| `getCustomerType(customerId)` | customerId: string | string | Gets customer type by ID from customers array |
| `handleCustomerChange()` | None | void | Updates URL with selected customer ID and updates selectedCustomer |
| `selectComputer(computerId)` | computerId: string | void | Navigates to computer detail page using SvelteKit's goto |
| `handleFormResult(result)` | result: any | void | Processes form submission result, handles success/error states |

#### Detailed Implementation

```javascript
// formatDate function implementation
function formatDate(date: Date | null | undefined): string {
  if (!date) return 'N/A';
  return new Date(date).toLocaleDateString();
}

// getCustomerName function implementation
function getCustomerName(customerId: string): string {
  const customer = customers.find(c => c._id === customerId);
  return customer?.companyName || customer?.name || 'Unknown Customer';
}

// handleCustomerChange function implementation
function handleCustomerChange() {
  // Update URL with selected customer ID
  const url = new URL(window.location.href);
  if (selectedCustomerId) {
    url.searchParams.set('customerId', selectedCustomerId);
  } else {
    url.searchParams.delete('customerId');
  }
  history.pushState({}, '', url);

  // Update selected customer
  selectedCustomer = selectedCustomerId
    ? customers.find(c => c._id === selectedCustomerId) || null
    : null;
}

// selectComputer function implementation
function selectComputer(computerId: string) {
  goto(`/customer-computers/${computerId}`);
}

// handleFormResult function implementation
function handleFormResult(result: any) {
  isSubmitting = false;

  if (result.success) {
    successMessage = result.message || 'Operation completed successfully';
    showAddComputerForm = false;

    // Redirect to the new computer if ID is provided
    if (result.computerId) {
      goto(`/customer-computers/${result.computerId}`);
    } else {
      // Reload the page to refresh the data
      window.location.reload();
    }
  } else {
    formError = result.error || 'An error occurred';
  }
}
```

### List View Server (src/routes/customer-computers/+page.server.js)

#### Configuration Variables

| Variable | Type | Value | Description |
|----------|------|-------|-------------|
| `uri` | string | 'mongodb://localhost:27017' | MongoDB connection string |
| `dbName` | string | 'ServiceContracts' | Database name |

#### Functions

| Function | Parameters | Returns | Description |
|----------|------------|---------|-------------|
| `load({ url })` | url: URL | Promise\<Object\> | Loads computers and customers data based on URL parameters |
| `actions.addComputer({ request })` | request: Request | Promise\<Object\> | Processes form data to add a new computer |
| `actions.deleteComputer({ request })` | request: Request | Promise\<Object\> | Deletes a computer by ID from form data |

#### Detailed Implementation

```javascript
// load function implementation
export async function load({ url }) {
  let client;

  try {
    // Get customerId from query params if provided
    const customerId = url.searchParams.get('customerId');

    // Connect to MongoDB
    client = new MongoClient(uri);
    await client.connect();
    const db = client.db(dbName);

    // Build query for computers
    const query = {};
    if (customerId && ObjectId.isValid(customerId)) {
      query.customerId = new ObjectId(customerId);
    }

    // Get all customers for the dropdown
    const customers = await db.collection('Customers')
      .find({})
      .sort({ companyName: 1 })
      .project({
        _id: 1,
        companyName: 1,
        name: 1,
        type: 1,
        city: 1,
        country: 1
      })
      .toArray();

    // Get computers based on query with aggregation pipeline
    const pipeline = [
      { $match: query },
      {
        $lookup: {
          from: 'Customers',
          localField: 'customerId',
          foreignField: '_id',
          as: 'customer'
        }
      },
      {
        $unwind: {
          path: '$customer',
          preserveNullAndEmptyArrays: true
        }
      },
      // ... additional pipeline stages ...
    ];

    const computers = await db.collection('CustomerComputers')
      .aggregate(pipeline)
      .toArray();

    // Transform data for client-side use
    const serializedComputers = computers.map(computer => ({
      ...computer,
      _id: computer._id.toString(),
      customerId: computer.customerId.toString(),
      // ... additional transformations ...
    }));

    return {
      computers: serializedComputers,
      customers: serializedCustomers,
      selectedCustomer
    };
  } catch (err) {
    console.error('Error loading customer computers:', err);
    throw error(500, {
      message: 'Failed to load customer computers'
    });
  } finally {
    if (client) {
      await client.close();
    }
  }
}
```

### Detail View (src/routes/customer-computers/[id]/+page.svelte)

#### Component State Variables

| Variable | Type | Default Value | Description |
|----------|------|---------------|-------------|
| `computer` | Computer | data.computer | Computer data from server |
| `isEditing` | boolean | false | Controls edit mode state |
| `isSubmitting` | boolean | false | Tracks form submission state |
| `formError` | string | '' | Stores form error messages |
| `successMessage` | string | '' | Stores success messages |
| `formData` | Object | {...} | Form data for editing computer |

#### Functions

| Function | Parameters | Returns | Description |
|----------|------------|---------|-------------|
| `formatDate(date)` | date: Date \| null \| undefined | string | Formats date for display as localized string or 'N/A' |
| `formatDateForInput(date)` | date: Date \| null \| undefined | string | Formats date for input fields (YYYY-MM-DD) |
| `handleFormResult(result)` | result: any | void | Processes form submission result, handles success/error states |
| `toggleEditMode()` | None | void | Toggles edit mode and resets form data |
| `confirmDelete()` | None | void | Shows delete confirmation dialog and submits delete form if confirmed |

#### Detailed Implementation

```javascript
// formatDate function implementation
function formatDate(date: Date | null | undefined): string {
  if (!date) return 'N/A';
  return new Date(date).toLocaleDateString();
}

// formatDateForInput function implementation
function formatDateForInput(date: Date | null | undefined): string {
  if (!date) return '';
  const d = new Date(date);
  return d.toISOString().split('T')[0];
}

// handleFormResult function implementation
function handleFormResult(result: any) {
  isSubmitting = false;

  if (result.success) {
    successMessage = result.message || 'Operation completed successfully';
    isEditing = false;

    // Redirect if specified
    if (result.redirect) {
      goto(result.redirect);
    } else {
      // Reload the page to refresh the data
      window.location.reload();
    }
  } else {
    formError = result.error || 'An error occurred';
  }
}

// toggleEditMode function implementation
function toggleEditMode() {
  isEditing = !isEditing;

  if (isEditing) {
    // Reset form data to current computer values
    formData = {
      name: computer.name || '',
      serialNumber: computer.serialNumber || '',
      model: computer.model || '',
      // ... other fields ...
      isActive: computer.isActive !== false,
      notes: computer.notes || ''
    };
  }
}

// confirmDelete function implementation
function confirmDelete() {
  if (confirm('Are you sure you want to delete this computer? This action cannot be undone.')) {
    document.getElementById('delete-form')?.requestSubmit();
  }
}
```

### Detail View Server (src/routes/customer-computers/[id]/+page.server.js)

#### Configuration Variables

| Variable | Type | Value | Description |
|----------|------|-------|-------------|
| `uri` | string | 'mongodb://localhost:27017' | MongoDB connection string |
| `dbName` | string | 'ServiceContracts' | Database name |

#### Functions

| Function | Parameters | Returns | Description |
|----------|------------|---------|-------------|
| `load({ params })` | params: Object | Promise\<Object\> | Loads computer details by ID with customer information |
| `actions.updateComputer({ request, params })` | request: Request, params: Object | Promise\<Object\> | Updates a computer with form data |
| `actions.deleteComputer({ params })` | params: Object | Promise\<Object\> | Deletes a computer and redirects to list view |

#### Detailed Implementation

```javascript
// load function implementation
export async function load({ params }) {
  let client;

  try {
    const { id } = params;

    // Validate computer ID
    if (!id || !ObjectId.isValid(id)) {
      throw error(400, 'Invalid computer ID');
    }

    // Connect to MongoDB
    client = new MongoClient(uri);
    await client.connect();
    const db = client.db(dbName);

    // Get computer details with customer information using aggregation
    const pipeline = [
      {
        $match: {
          _id: new ObjectId(id)
        }
      },
      {
        $lookup: {
          from: 'Customers',
          localField: 'customerId',
          foreignField: '_id',
          as: 'customer'
        }
      },
      {
        $unwind: {
          path: '$customer',
          preserveNullAndEmptyArrays: true
        }
      }
    ];

    const computers = await db.collection('CustomerComputers')
      .aggregate(pipeline)
      .toArray();

    if (computers.length === 0) {
      throw error(404, 'Computer not found');
    }

    const computer = computers[0];

    // Transform data for client-side use
    const serializedComputer = {
      ...computer,
      _id: computer._id.toString(),
      customerId: computer.customerId.toString(),
      // ... other transformations ...
    };

    return {
      computer: serializedComputer
    };
  } catch (err) {
    console.error('Error loading computer details:', err);
    if (err.status) {
      throw err; // Re-throw SvelteKit errors
    }
    throw error(500, 'Failed to load computer details');
  } finally {
    if (client) {
      await client.close();
    }
  }
}
```

### New Computer View (src/routes/customer-computers/new/+page.svelte)

#### Component State Variables

| Variable | Type | Default Value | Description |
|----------|------|---------------|-------------|
| `customers` | Customer[] | data.customers \|\| [] | List of customers from server |
| `selectedCustomer` | Customer \| null | data.selectedCustomer | Currently selected customer |
| `selectedCustomerId` | string | selectedCustomer?._id \|\| '' | ID of selected customer |
| `isSubmitting` | boolean | false | Tracks form submission state |
| `formError` | string | '' | Stores form error messages |
| `formData` | Object | {...} | Form data for new computer |

#### Functions

| Function | Parameters | Returns | Description |
|----------|------------|---------|-------------|
| `handleFormResult(result)` | result: any | void | Processes form submission result, handles error states |
| `handleCustomerChange()` | None | void | Updates URL with selected customer ID and reloads page |

#### Detailed Implementation

```javascript
// handleFormResult function implementation
function handleFormResult(result: any) {
  isSubmitting = false;

  if (!result.success) {
    formError = result.error || 'An error occurred';
  }
}

// handleCustomerChange function implementation
function handleCustomerChange() {
  // Update URL with selected customer ID
  const url = new URL(window.location.href);
  if (selectedCustomerId) {
    url.searchParams.set('customerId', selectedCustomerId);
    history.pushState({}, '', url);

    // Reload the page to get the selected customer data
    window.location.reload();
  }
}
```

### New Computer Server (src/routes/customer-computers/new/+page.server.js)

#### Configuration Variables

| Variable | Type | Value | Description |
|----------|------|-------|-------------|
| `uri` | string | 'mongodb://localhost:27017' | MongoDB connection string |
| `dbName` | string | 'ServiceContracts' | Database name |

#### Functions

| Function | Parameters | Returns | Description |
|----------|------------|---------|-------------|
| `load({ url })` | url: URL | Promise\<Object\> | Loads customers data and selected customer based on URL parameters |
| `actions.addComputer({ request })` | request: Request | Promise\<Object\> | Processes form data to add a new computer and redirects to detail view |

#### Detailed Implementation

```javascript
// load function implementation
export async function load({ url }) {
  let client;

  try {
    // Get customerId from query params
    const customerId = url.searchParams.get('customerId');

    // If no customerId is provided, return empty data
    if (!customerId) {
      return {
        customers: [],
        selectedCustomer: null
      };
    }

    // Validate customerId format
    if (!ObjectId.isValid(customerId)) {
      throw error(400, 'Invalid customer ID format');
    }

    // Connect to MongoDB
    client = new MongoClient(uri);
    await client.connect();
    const db = client.db(dbName);

    // Get all customers for the dropdown
    const customers = await db.collection('Customers')
      .find({})
      .sort({ companyName: 1 })
      .project({
        _id: 1,
        companyName: 1,
        name: 1,
        type: 1,
        city: 1,
        country: 1
      })
      .toArray();

    // Get the selected customer
    const selectedCustomer = await db.collection('Customers')
      .findOne({ _id: new ObjectId(customerId) });

    if (!selectedCustomer) {
      throw error(404, 'Customer not found');
    }

    // Transform data for client-side use
    const serializedCustomers = customers.map(customer => ({
      ...customer,
      _id: customer._id.toString()
    }));

    const serializedSelectedCustomer = {
      ...selectedCustomer,
      _id: selectedCustomer._id.toString()
    };

    return {
      customers: serializedCustomers,
      selectedCustomer: serializedSelectedCustomer
    };
  } catch (err) {
    // Error handling...
  } finally {
    if (client) {
      await client.close();
    }
  }
}

// actions.addComputer implementation
export const actions = {
  addComputer: async ({ request }) => {
    let client;

    try {
      const formData = await request.formData();

      // Get and validate required fields
      const customerId = formData.get('customerId')?.toString();
      const serialNumber = formData.get('serialNumber')?.toString();
      // ... other fields ...

      // Create computer data object
      const computerData = {
        customerId: new ObjectId(customerId),
        name,
        serialNumber,
        // ... other fields ...
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Insert the computer
      const result = await computersCollection.insertOne(computerData);

      // Redirect to the new computer's detail page
      throw redirect(303, `/customer-computers/${result.insertedId.toString()}`);
    } catch (err) {
      // Error handling...
    }
  }
};
```

### Computer Form Component (src/lib/components/ComputerForm.svelte)

#### Props

| Prop | Type | Default Value | Description |
|------|------|---------------|-------------|
| `customerId` | string | '' | ID of the customer to associate with the computer |
| `editingComputerId` | string | '' | ID of computer being edited (empty for new computers) |
| `formData` | Object | {...} | Form data with default values |

#### Functions

| Function | Parameters | Returns | Description |
|----------|------------|---------|-------------|
| `handleCancel()` | None | void | Dispatches 'cancel' event to parent component |

#### Event Dispatchers

| Event | Payload | Description |
|-------|---------|-------------|
| `cancel` | None | Emitted when cancel button is clicked |

#### Detailed Implementation

```javascript
// Import statements
import { createEventDispatcher } from 'svelte';

// Props
export let customerId = '';
export let editingComputerId = '';
export let formData = {
  serialNumber: '',
  model: '',
  // ... other fields with defaults ...
};

// Event dispatcher setup
const dispatch = createEventDispatcher();

// handleCancel function implementation
function handleCancel() {
  dispatch('cancel');
}
```

## API Endpoints

### Customer Computers API (src/routes/api/customer-computers/+server.js)

#### HTTP Methods

| Method | Parameters | Returns | Description |
|--------|------------|---------|-------------|
| `GET()` | None | Promise\<Response\> | Returns all customer computers with customer information |

#### Detailed Implementation

```javascript
export async function GET() {
  try {
    const collection = await getCollection('CustomerComputers');

    const pipeline = [
      {
        $lookup: {
          from: 'Customers',
          localField: 'customerId',
          foreignField: '_id',
          as: 'customer'
        }
      },
      {
        $unwind: {
          path: '$customer',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: 'LabourTime',
          localField: 'type',
          foreignField: 'ComputerCategory',
          as: 'labourTime'
        }
      },
      {
        $project: {
          _id: 1,
          customerId: 1,
          name: 1,
          // ... other fields ...
          customerName: { $ifNull: ['$customer.companyName', 'Unknown Customer'] },
          customerEmail: '$customer.email',
          // ... other customer fields ...
          labourHours: {
            $map: {
              input: '$labourTime',
              as: 'lt',
              in: {
                serviceCode: '$$lt.Service Code',
                description: '$$lt.Service Description',
                vstCode: '$$lt.VST Code',
                vstHours: '$$lt.VST Hours',
                servicePhase: '$$lt.ServicePhase'
              }
            }
          },
          // ... contract fields ...
        }
      },
      {
        $sort: {
          customerName: 1,
          name: 1
        }
      }
    ];

    const computers = await collection.aggregate(pipeline).toArray();
    console.log(`Found ${computers.length} computers`);

    return json(computers);
  } catch (err) {
    console.error('Error details:', err);
    return new Response(
      JSON.stringify({
        error: 'Internal Server Error',
        details: err instanceof Error ? err.message : 'Unknown error'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}
```

### Customer Computers Update API (src/routes/api/customer-computers/update-schema/+server.js)

#### HTTP Methods

| Method | Parameters | Returns | Description |
|--------|------------|---------|-------------|
| `POST()` | None | Promise\<Response\> | Updates the schema of all customer computers |

#### Detailed Implementation

```javascript
export async function POST() {
  try {
    const collection = await getCollection('CustomerComputers');

    // Find all computers
    const computers = await collection.find({}).toArray();

    // Update each computer with new schema fields
    let updatedCount = 0;
    for (const computer of computers) {
      const updateData = {
        $set: {
          isActive: computer.isActive !== false, // Default to true if undefined
          updatedAt: new Date()
        }
      };

      // Only set fields that don't already exist
      if (computer.productDesignation === undefined) {
        updateData.$set.productDesignation = '';
      }

      if (computer.engineType === undefined) {
        updateData.$set.engineType = '';
      }

      // ... other schema updates ...

      const result = await collection.updateOne(
        { _id: computer._id },
        updateData
      );

      if (result.modifiedCount > 0) {
        updatedCount++;
      }
    }

    return json({
      success: true,
      message: `Updated schema for ${updatedCount} computers`
    });
  } catch (err) {
    console.error('Error updating computer schema:', err);
    return json({
      success: false,
      error: err instanceof Error ? err.message : 'Unknown error'
    }, { status: 500 });
  }
}
```

## Function Relationships

### Database Connection Flow

#### Initialization Sequence

1. Application starts
2. `init()` from db.ts is called to establish MongoDB connection:
   ```javascript
   // In server startup code
   import { init } from '$lib/db';

   async function startServer() {
     try {
       const db = await init();
       console.log('Database initialized successfully');
       // Continue with server startup
     } catch (err) {
       console.error('Failed to initialize database:', err);
       process.exit(1);
     }
   }
   ```

3. `getCollection(collectionName)` is used throughout the application to access collections:
   ```javascript
   // In API endpoints and server load functions
   import { getCollection } from '$lib/db/mongo';

   export async function GET() {
     try {
       const collection = await getCollection('CustomerComputers');
       const data = await collection.find({}).toArray();
       return json(data);
     } catch (err) {
       // Error handling
     }
   }
   ```

4. `closeConnection()` is called when the application terminates:
   ```javascript
   // In shutdown handlers
   import { closeConnection } from '$lib/db/mongo';

   process.on('SIGINT', async () => {
     await closeConnection();
     process.exit(0);
   });
   ```

#### Connection Pooling and Reuse

The application uses MongoDB's connection pooling to efficiently manage database connections:

1. The `client` instance is created once at module level
2. `connectToDatabase()` caches the database instance in the `db` variable
3. Subsequent calls to `getCollection()` reuse the existing connection
4. Each server endpoint opens and closes its own connection to ensure proper resource management

### Customer Computers CRUD Flow

#### List View Detailed Flow

1. Page loads and calls server `load({ url })` function:
   ```javascript
   // In +page.svelte
   export let data; // Data from server load function

   // Component state initialized from server data
   let computers = data.computers || [];
   let customers = data.customers || [];
   ```

2. Server retrieves data using MongoDB aggregation pipeline:
   ```javascript
   // In +page.server.js load function
   const pipeline = [
     { $match: query },
     {
       $lookup: {
         from: 'Customers',
         localField: 'customerId',
         foreignField: '_id',
         as: 'customer'
       }
     },
     // ... additional stages ...
   ];

   const computers = await db.collection('CustomerComputers')
     .aggregate(pipeline)
     .toArray();
   ```

3. Client-side reactive declarations filter and process data:
   ```javascript
   // In +page.svelte
   $: filteredComputers = selectedCustomerId
     ? computers.filter(c => c.customerId === selectedCustomerId)
     : computers;
   ```

4. Event handlers manage user interactions:
   ```javascript
   // In +page.svelte
   function handleCustomerChange() {
     // Update URL with selected customer ID
     const url = new URL(window.location.href);
     if (selectedCustomerId) {
       url.searchParams.set('customerId', selectedCustomerId);
     } else {
       url.searchParams.delete('customerId');
     }
     history.pushState({}, '', url);

     // Update selected customer
     selectedCustomer = selectedCustomerId
       ? customers.find(c => c._id === selectedCustomerId) || null
       : null;
   }
   ```

#### Detail View Detailed Flow

1. Page loads and calls server `load({ params })` function with the computer ID:
   ```javascript
   // In [id]/+page.server.js
   export async function load({ params }) {
     const { id } = params;
     // ... validate ID and connect to database ...

     const pipeline = [
       { $match: { _id: new ObjectId(id) } },
       // ... lookup stages ...
     ];

     const computers = await db.collection('CustomerComputers')
       .aggregate(pipeline)
       .toArray();

     // ... transform data ...

     return { computer: serializedComputer };
   }
   ```

2. Client component initializes with server data:
   ```javascript
   // In [id]/+page.svelte
   export let data;
   let computer = data.computer;
   let isEditing = false;
   ```

3. Form data is initialized when edit mode is toggled:
   ```javascript
   // In [id]/+page.svelte
   function toggleEditMode() {
     isEditing = !isEditing;

     if (isEditing) {
       // Reset form data to current computer values
       formData = {
         name: computer.name || '',
         serialNumber: computer.serialNumber || '',
         // ... other fields ...
       };
     }
   }
   ```

4. Form submission is handled with SvelteKit's enhance:
   ```javascript
   // In [id]/+page.svelte
   <form
     method="POST"
     action="?/updateComputer"
     use:enhance={() => {
       isSubmitting = true;
       return async ({ result }) => {
         handleFormResult(result.data);
       };
     }}
   >
     <!-- Form fields -->
   </form>
   ```

5. Server processes form submission in action handler:
   ```javascript
   // In [id]/+page.server.js
   export const actions = {
     updateComputer: async ({ request, params }) => {
       // ... process form data ...

       const result = await computersCollection.updateOne(
         { _id: new ObjectId(id) },
         { $set: updateData }
       );

       // ... return result ...
     }
   };
   ```

#### Add New Computer Detailed Flow

1. Page loads with customer selection if no customer ID is provided:
   ```javascript
   // In new/+page.svelte
   {#if !selectedCustomer}
     <div class="customer-selection">
       <select
         bind:value={selectedCustomerId}
         on:change={handleCustomerChange}
       >
         <!-- Customer options -->
       </select>
     </div>
   {:else}
     <!-- Computer form -->
   {/if}
   ```

2. Customer selection triggers page reload with updated URL:
   ```javascript
   // In new/+page.svelte
   function handleCustomerChange() {
     const url = new URL(window.location.href);
     url.searchParams.set('customerId', selectedCustomerId);
     history.pushState({}, '', url);
     window.location.reload();
   }
   ```

3. Form submission is processed by server action:
   ```javascript
   // In new/+page.server.js
   export const actions = {
     addComputer: async ({ request }) => {
       // ... process form data ...

       const result = await computersCollection.insertOne(computerData);

       // Redirect to the new computer's detail page
       throw redirect(303, `/customer-computers/${result.insertedId.toString()}`);
     }
   };
   ```

#### Delete Computer Detailed Flow

1. Confirmation dialog is shown before deletion:
   ```javascript
   // In [id]/+page.svelte
   function confirmDelete() {
     if (confirm('Are you sure you want to delete this computer? This action cannot be undone.')) {
       document.getElementById('delete-form')?.requestSubmit();
     }
   }
   ```

2. Hidden form is submitted for deletion:
   ```javascript
   // In [id]/+page.svelte
   <form
     id="delete-form"
     method="POST"
     action="?/deleteComputer"
     use:enhance={() => {
       isSubmitting = true;
       return async ({ result }) => {
         handleFormResult(result.data);
       };
     }}
     style="display: none;"
   ></form>
   ```

3. Server processes deletion and redirects:
   ```javascript
   // In [id]/+page.server.js
   export const actions = {
     deleteComputer: async ({ params }) => {
       // ... validate and connect to database ...

       const result = await db.collection('CustomerComputers').deleteOne({
         _id: new ObjectId(id)
       });

       // ... check result ...

       return {
         success: true,
         message: 'Computer deleted successfully',
         redirect: '/customer-computers'
       };
     }
   };
   ```

### API Usage and Integration

#### API Endpoints and Consumers

The Customer Computers API endpoints are used by various parts of the application:

1. **GET /api/customer-computers**
   - Used by dashboard components to display computer counts
   - Used by reporting modules to generate customer reports
   - Called by external systems via authenticated requests

2. **POST /api/customer-computers/update-schema**
   - Used by admin tools to update database schema
   - Called during application updates to migrate data

#### API Response Processing

API responses are processed consistently throughout the application:

```javascript
// Example of API consumption
async function fetchComputers() {
  try {
    const response = await fetch('/api/customer-computers');

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch computers');
    }

    const computers = await response.json();
    return computers;
  } catch (error) {
    console.error('Error fetching computers:', error);
    throw error;
  }
}
```
## Data Transformation Functions

### Document Serialization

The application consistently transforms data between MongoDB format and client-side format:

1. When retrieving data from MongoDB:
   - `serializeDocument(doc)` or `toClientDocument(doc)` converts ObjectIds to strings
   - Date objects are preserved or formatted using `formatDate(date)`

2. When sending data to MongoDB:
   - `toDatabaseDocument(doc)` converts string IDs to ObjectIds
   - Form data is parsed and validated before insertion

### Common Data Transformation Patterns

```javascript
// Converting MongoDB documents for client use
const serializedComputer = {
  ...computer,
  _id: computer._id.toString(),
  customerId: computer.customerId.toString(),
  // Convert nested document IDs
  customer: computer.customer ? {
    ...computer.customer,
    _id: computer.customer._id.toString()
  } : null,
  // Handle date fields
  createdAt: computer.createdAt ? new Date(computer.createdAt) : null,
  updatedAt: computer.updatedAt ? new Date(computer.updatedAt) : null
};

// Converting client data for MongoDB
const computerData = {
  customerId: new ObjectId(customerId),
  name,
  serialNumber,
  // Other fields...
  createdAt: new Date(),
  updatedAt: new Date()
};
```

## MongoDB Query Patterns

### Common Query Patterns

1. **Simple Find Query**:
   ```javascript
   const customers = await db.collection('Customers')
     .find({})
     .sort({ companyName: 1 })
     .project({ _id: 1, companyName: 1, name: 1 })
     .toArray();
   ```

2. **Find by ID**:
   ```javascript
   const customer = await db.collection('Customers')
     .findOne({ _id: new ObjectId(customerId) });
   ```

3. **Aggregation with Lookup**:
   ```javascript
   const pipeline = [
     { $match: { _id: new ObjectId(id) } },
     {
       $lookup: {
         from: 'Customers',
         localField: 'customerId',
         foreignField: '_id',
         as: 'customer'
       }
     },
     { $unwind: { path: '$customer', preserveNullAndEmptyArrays: true } }
   ];

   const computers = await db.collection('CustomerComputers')
     .aggregate(pipeline)
     .toArray();
   ```

4. **Update Document**:
   ```javascript
   const result = await computersCollection.updateOne(
     { _id: new ObjectId(id) },
     { $set: updateData }
   );
   ```

5. **Delete Document**:
   ```javascript
   const result = await db.collection('CustomerComputers').deleteOne({
     _id: new ObjectId(id)
   });
   ```

## Form Handling Patterns

### Form Submission with SvelteKit's enhance

The application uses SvelteKit's `enhance` function to handle form submissions without full page reloads:

```javascript
<form
  method="POST"
  action="?/updateComputer"
  use:enhance={() => {
    isSubmitting = true;
    return async ({ result }) => {
      handleFormResult(result.data);
    };
  }}
>
  <!-- Form fields -->
  <button type="submit" disabled={isSubmitting}>
    {isSubmitting ? 'Saving...' : 'Save Changes'}
  </button>
</form>
```

### Form Result Handling

```javascript
function handleFormResult(result) {
  isSubmitting = false;

  if (result.success) {
    successMessage = result.message || 'Operation completed successfully';

    // Redirect if specified
    if (result.redirect) {
      goto(result.redirect);
    } else {
      // Reload the page to refresh the data
      window.location.reload();
    }
  } else {
    formError = result.error || 'An error occurred';
  }
}
```

### Server-Side Form Actions

SvelteKit form actions are used to process form submissions:

```javascript
export const actions = {
  updateComputer: async ({ request, params }) => {
    // Get form data
    const formData = await request.formData();

    // Process and validate data
    const name = formData.get('name')?.toString();

    // Update in database
    const result = await collection.updateOne(
      { _id: new ObjectId(id) },
      { $set: updateData }
    );

    // Return result
    return {
      success: true,
      message: 'Computer updated successfully'
    };
  }
};
```

## Navigation Patterns

### Page Navigation

```javascript
// Using SvelteKit's goto for client-side navigation
import { goto } from '$app/navigation';

function selectComputer(computerId) {
  goto(`/customer-computers/${computerId}`);
}

// Using server-side redirects
import { redirect } from '@sveltejs/kit';

// In a form action
throw redirect(303, `/customer-computers/${result.insertedId.toString()}`);
```

### Back Navigation

```javascript
// Using browser history
<button class="back-button" on:click={() => history.back()}>
  ← Back
</button>

// Using anchor links
<a href="/customer-computers" class="back-button">
  ← Back to List
</a>
```

## Error Handling Patterns

### Client-Side Error Handling

```javascript
// Display error messages
{#if formError}
  <div class="alert error">
    {formError}
    <button class="close-alert" on:click={() => formError = ''}>×</button>
  </div>
{/if}

// Handle form submission errors
function handleFormResult(result) {
  if (!result.success) {
    formError = result.error || 'An error occurred';
  }
}
```

### Server-Side Error Handling

```javascript
// Using SvelteKit's error function
import { error } from '@sveltejs/kit';

// In load function
if (!selectedCustomer) {
  throw error(404, 'Customer not found');
}

// Try-catch with detailed error handling
try {
  // Database operations
} catch (err) {
  console.error('Error loading data:', err);
  if (err.status) {
    throw err; // Re-throw SvelteKit errors
  }
  throw error(500, 'Failed to load data');
}

// In API endpoints
return new Response(
  JSON.stringify({
    error: 'Internal Server Error',
    details: err instanceof Error ? err.message : 'Unknown error'
  }),
  {
    status: 500,
    headers: {
      'Content-Type': 'application/json'
    }
  }
);
```

## Component Architecture

### Component Hierarchy

The application follows a hierarchical component structure:

1. **Layout Components**
   - `+layout.svelte`: Provides the application shell and common UI elements

2. **Page Components**
   - `+page.svelte`: Main content for each route
   - Uses data loaded by `+page.server.js`

3. **Reusable Components**
   - `ComputerForm.svelte`: Reusable form for adding/editing computers
   - Used in multiple places with different configurations

### Component Communication

1. **Props**
   - Parent components pass data to child components via props
   - Example: `<ComputerForm customerId={selectedCustomerId} />`

2. **Events**
   - Child components communicate with parents via events
   - Example:
     ```javascript
     // In child component
     const dispatch = createEventDispatcher();
     function handleCancel() {
       dispatch('cancel');
     }

     // In parent component
     <ComputerForm on:cancel={() => showAddComputerForm = false} />
     ```

3. **Stores (not heavily used in this application)**
   - For global state management across components

## Database Schema

### Main Collections

1. **Customers**
   - `_id`: ObjectId
   - `companyName`: string
   - `name`: string
   - `type`: string
   - `city`: string
   - `country`: string
   - `email`: string
   - `phone`: string

2. **CustomerComputers**
   - `_id`: ObjectId
   - `customerId`: ObjectId (references Customers._id)
   - `name`: string
   - `serialNumber`: string
   - `model`: string
   - `productType`: string
   - `productDesignation`: string
   - `engineType`: string
   - `operatingHours`: number
   - `installationDate`: Date
   - `manufactureDate`: Date
   - `isActive`: boolean
   - `notes`: string
   - `createdAt`: Date
   - `updatedAt`: Date

3. **LabourTime**
   - `_id`: ObjectId
   - `ComputerCategory`: string
   - `Service Code`: string
   - `Service Description`: string
   - `VST Code`: string
   - `VST Hours`: number
   - `ServicePhase`: string

### Relationships

- `CustomerComputers.customerId` → `Customers._id` (One-to-Many)
- `CustomerComputers.type` → `LabourTime.ComputerCategory` (Many-to-Many)

## Conclusion

This document provides a comprehensive overview of the functions and patterns used in the Service Template application. The application follows a consistent structure with clear separation of concerns:

1. **Server-side code** handles data loading, processing, and database operations
2. **Client-side code** manages UI state, user interactions, and data presentation
3. **Components** provide reusable UI elements with well-defined interfaces
4. **Database operations** follow consistent patterns for querying, updating, and transforming data

By understanding these patterns and relationships, developers can efficiently maintain and extend the application's functionality.
