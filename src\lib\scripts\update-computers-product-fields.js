import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function updateCustomerComputersProductInfo() {
    try {
        await client.connect();
        console.log('Connected to MongoDB');

        const db = client.db('ServiceContracts');
        const collection = db.collection('CustomerComputers');

        // Update all documents to add ProductDesignation and ProductPartNumber fields
        const result = await collection.updateMany(
            {}, // Match all documents
            { 
                $set: { 
                    ProductDesignation: "TAD1342GE",
                    ProductPartNumber: 40869279
                } 
            }
        );

        console.log(`Updated ${result.modifiedCount} documents`);
    } catch (error) {
        console.error('Error updating documents:', error);
    } finally {
        await client.close();
        console.log('Disconnected from MongoDB');
    }
}

updateCustomerComputersProductInfo();
