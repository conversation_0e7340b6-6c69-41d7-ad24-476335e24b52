import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function updateAllRecords() {
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db('ServiceContracts');
    const collection = db.collection('ActualComputerHours');
    
    // Get all records
    const records = await collection.find({}).toArray();
    console.log(`Found ${records.length} records to update`);
    
    let updatedCount = 0;
    let skippedCount = 0;
    
    // Use current date/time for consistency across all records
    const currentDate = new Date('2025-04-22T12:15:08+02:00'); // Using the current date/time
    const reportDate = currentDate.toISOString().split('T')[0]; // YYYY-MM-DD format
    const reportTime = currentDate.toTimeString().substr(0, 8); // HH:MM:SS format
    
    console.log(`Setting ReportDate: ${reportDate} and ReportTime: ${reportTime} for all records`);
    
    // Update each record with ReportDate and ReportTime
    for (const record of records) {
      // Update record - add ReportDate and ReportTime, remove ReportDateTime
      try {
        const result = await collection.updateOne(
          { _id: record._id },
          { 
            $set: {
              ReportDate: reportDate,
              ReportTime: reportTime
            },
            $unset: {
              ReportDateTime: ""  // Remove this field
            }
          }
        );
        
        if (result.modifiedCount > 0) {
          updatedCount++;
          console.log(`Updated record ${record._id}`);
        } else {
          skippedCount++;
        }
      } catch (err) {
        console.error(`Error updating record ${record._id}:`, err);
        skippedCount++;
      }
    }
    
    console.log(`Updated ${updatedCount} records, skipped ${skippedCount}`);
    
    // Verification step: check that all records have ReportDate and ReportTime
    console.log("\nVerifying all records have ReportDate and ReportTime fields...");
    const missingFields = await collection.find({
      $or: [
        { ReportDate: { $exists: false } },
        { ReportTime: { $exists: false } }
      ]
    }).toArray();
    
    if (missingFields.length === 0) {
      console.log("✅ Verification successful: All records have ReportDate and ReportTime fields");
    } else {
      console.log(`❌ Verification failed: Found ${missingFields.length} records missing date/time fields`);
      console.log("IDs of records with missing fields:", missingFields.map(r => r._id));
    }
    
  } catch (err) {
    console.error('Error:', err);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

updateAllRecords();
