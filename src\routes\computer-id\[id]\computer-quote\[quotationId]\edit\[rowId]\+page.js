// This file handles server-side loading for the Edit Quote Row page
export async function load({ params, fetch }) {
  try {
    // Fetch the quote row data from the API
    const response = await fetch(`/api/quote-rows/${params.rowId}`);
    
    if (!response.ok) {
      // If the response is not OK, throw an error
      const errorText = await response.text();
      throw new Error(errorText || 'Failed to load quote row data');
    }
    
    const quoteRow = await response.json();
    
    // Return the data for use in the page
    return {
      computerId: params.id,
      quotationId: params.quotationId,
      rowId: params.rowId,
      quoteRow
    };
  } catch (error) {
    console.error('Error loading quote row:', error);
    // Return the error for handling in the page
    return {
      computerId: params.id,
      quotationId: params.quotationId,
      rowId: params.rowId,
      error: error.message || 'Failed to load quote row'
    };
  }
}
