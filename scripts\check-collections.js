import { MongoClient, ObjectId } from 'mongodb';

const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function checkCollections() {
  const client = new MongoClient(uri);
  
  try {
    console.log('Connecting to MongoDB...');
    await client.connect();
    console.log('Connected successfully to MongoDB server');
    
    const db = client.db(dbName);
    console.log(`Using database: ${dbName}`);
    
    // List all collections
    const collections = await db.listCollections().toArray();
    console.log('Available collections:');
    collections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });
    
    // Check ServiceCodeHeader collection
    console.log('\nChecking ServiceCodeHeader collection:');
    const serviceCodeHeaderCollection = db.collection('ServiceCodeHeader');
    const count = await serviceCodeHeaderCollection.countDocuments();
    console.log(`Total documents: ${count}`);
    
    if (count > 0) {
      // Get sample document
      const sampleDoc = await serviceCodeHeaderCollection.findOne({});
      console.log('Sample document structure:');
      console.log(JSON.stringify(sampleDoc, null, 2));
      
      // List all field names
      console.log('\nField names in collection:');
      const fieldNames = Object.keys(sampleDoc || {});
      fieldNames.forEach(field => {
        const value = sampleDoc[field];
        const type = value instanceof ObjectId ? 'ObjectId' : 
                    value instanceof Date ? 'Date' : 
                    Array.isArray(value) ? 'Array' : 
                    typeof value;
        console.log(`- ${field} (${type}): ${JSON.stringify(value)}`);
      });
    }
  } catch (error) {
    console.error('Error checking collections:', error);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

checkCollections().catch(console.error);
