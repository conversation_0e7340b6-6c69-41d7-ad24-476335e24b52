import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function updateLabourTimeSchema() {
    try {
        await client.connect();
        const db = client.db('ServiceContracts');
        const collection = db.collection('LabourTime');

        // Update all existing documents to add the new fields
        await collection.updateMany(
            {}, // match all documents
            {
                $set: {
                    ServicePhase: '',  // default empty string
                    ComputerCategory: ''  // default empty string
                }
            }
        );

        // Create indexes for all fields
        await collection.createIndexes([
            { key: { 'Service Code': 1 } },
            { key: { 'Service Description': 1 } },
            { key: { 'VST Code': 1 } },
            { key: { 'VST Hours': 1 } },
            { key: { ServicePhase: 1 } },
            { key: { ComputerCategory: 1 } }
        ]);

        console.log('Successfully updated LabourTime collection schema');

    } catch (error) {
        console.error('Error updating LabourTime collection schema:', error);
    } finally {
        await client.close();
    }
}

updateLabourTimeSchema();
