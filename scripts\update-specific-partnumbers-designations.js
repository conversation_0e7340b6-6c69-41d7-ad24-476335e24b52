// Script to update ProductDesignationShort for specific Product Validity Group values
// This script sets ProductDesignationShort to "D13B-N MH" for documents with specific Product Validity Group values

import { MongoClient } from 'mongodb';

// Connection URL and Database name
const url = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';
const collectionName = 'PartNumbersServiceCodeAction';

async function updateSpecificDesignations() {
  let client;

  try {
    // Connect to MongoDB
    client = new MongoClient(url);
    await client.connect();
    console.log('Connected to MongoDB server');

    // Get database and collection
    const db = client.db(dbName);
    const collection = db.collection(collectionName);

    // Find documents with the specific Product Validity Group values before update
    const matchingDocsBefore = await collection.find({
      'Product Validity Group': { $in: ["D13B-N MH R1", "D13B-N MH R2"] }
    }).toArray();
    
    console.log(`Found ${matchingDocsBefore.length} documents with the specified Product Validity Group values before update`);
    
    if (matchingDocsBefore.length > 0) {
      console.log('Sample documents before update:');
      matchingDocsBefore.slice(0, 5).forEach(doc => {
        console.log(`ID: ${doc._id}, Product Validity Group: ${doc['Product Validity Group']}, ProductDesignationShort: ${doc.ProductDesignationShort}`);
      });
    }

    // Update documents with the specific Product Validity Group values
    const updateResult = await collection.updateMany(
      { 'Product Validity Group': { $in: ["D13B-N MH R1", "D13B-N MH R2"] } },
      { $set: { ProductDesignationShort: "D13B-N MH" } }
    );

    console.log(`Updated ${updateResult.modifiedCount} documents with ProductDesignationShort set to "D13B-N MH"`);

    // Verify update by checking the updated documents
    const matchingDocsAfter = await collection.find({
      'Product Validity Group': { $in: ["D13B-N MH R1", "D13B-N MH R2"] }
    }).toArray();
    
    if (matchingDocsAfter.length > 0) {
      console.log('Sample documents after update:');
      matchingDocsAfter.slice(0, 5).forEach(doc => {
        console.log(`ID: ${doc._id}, Product Validity Group: ${doc['Product Validity Group']}, ProductDesignationShort: ${doc.ProductDesignationShort}`);
      });
    }

    // If no documents were found with the exact values, let's check if there are similar values
    if (matchingDocsBefore.length === 0) {
      console.log('\nChecking for similar Product Validity Group values...');
      
      // Search for documents with Product Validity Group containing "D13B"
      const d13bDocs = await collection.find({
        'Product Validity Group': { $regex: /D13B/i }
      }).toArray();
      
      console.log(`Found ${d13bDocs.length} documents with Product Validity Group containing "D13B"`);
      
      if (d13bDocs.length > 0) {
        console.log('Documents with Product Validity Group containing "D13B":');
        d13bDocs.forEach(doc => {
          console.log(`ID: ${doc._id}, Product Validity Group: ${doc['Product Validity Group']}, ProductDesignationShort: ${doc.ProductDesignationShort}`);
        });
      }
    }

  } catch (err) {
    console.error('Error updating documents:', err);
  } finally {
    // Close the connection
    if (client) {
      await client.close();
      console.log('MongoDB connection closed');
    }
  }
}

// Run the update function
updateSpecificDesignations().catch(console.error);
