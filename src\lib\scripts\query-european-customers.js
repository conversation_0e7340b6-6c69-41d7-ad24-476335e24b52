import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

// List of European countries
const europeanCountries = [
    'Albania', 'Andorra', 'Austria', 'Belarus', 'Belgium', 'Bosnia and Herzegovina',
    'Bulgaria', 'Croatia', 'Czech Republic', 'Denmark', 'Estonia', 'Finland',
    'France', 'Germany', 'Greece', 'Hungary', 'Iceland', 'Ireland', 'Italy',
    'Latvia', 'Liechtenstein', 'Lithuania', 'Luxembourg', 'Malta', 'Moldova',
    'Monaco', 'Montenegro', 'Netherlands', 'North Macedonia', 'Norway', 'Poland',
    'Portugal', 'Romania', 'Russia', 'San Marino', 'Serbia', 'Slovakia', 'Slovenia',
    'Spain', 'Sweden', 'Switzerland', 'Ukraine', 'United Kingdom', 'Vatican City'
];

async function findEuropeanCustomers() {
    const client = new MongoClient(uri);
    
    try {
        await client.connect();
        console.log('Connected to MongoDB');
        
        const db = client.db(dbName);
        const customersCollection = db.collection('Customers');
        
        // Aggregate pipeline to get customers with their computer counts
        const customers = await customersCollection.aggregate([
            {
                $match: {
                    country: { $in: europeanCountries }
                }
            },
            {
                $lookup: {
                    from: 'CustomerComputers',
                    localField: '_id',
                    foreignField: 'customerId',
                    as: 'computers'
                }
            },
            {
                $addFields: {
                    computerCount: { $size: '$computers' }
                }
            },
            {
                $project: {
                    _id: 1,
                    companyName: 1,
                    country: 1,
                    city: 1,
                    type: 1,
                    computerCount: 1
                }
            },
            {
                $sort: {
                    country: 1,
                    companyName: 1
                }
            }
        ]).toArray();

        // Group customers by country
        const customersByCountry = {};
        customers.forEach(customer => {
            if (!customersByCountry[customer.country]) {
                customersByCountry[customer.country] = [];
            }
            customersByCountry[customer.country].push(customer);
        });

        // Print results
        console.log('\nEuropean Customers by Country:\n');
        Object.entries(customersByCountry).forEach(([country, customers]) => {
            console.log(`\n${country} (${customers.length} customers):`);
            console.log('-'.repeat(40));
            customers.forEach(customer => {
                console.log(`${customer.companyName}`);
                console.log(`  City: ${customer.city}`);
                console.log(`  Type: ${customer.type}`);
                console.log(`  Computers: ${customer.computerCount}`);
                console.log();
            });
        });

        // Print summary
        const totalCustomers = customers.length;
        const countriesWithCustomers = Object.keys(customersByCountry).length;
        console.log('\nSummary:');
        console.log('-'.repeat(40));
        console.log(`Total European Customers: ${totalCustomers}`);
        console.log(`Countries with Customers: ${countriesWithCustomers}`);
        console.log(`Countries without Customers: ${europeanCountries.length - countriesWithCustomers}`);

    } catch (err) {
        console.error('Error:', err);
    } finally {
        await client.close();
        console.log('\nDisconnected from MongoDB');
    }
}

// Run the query
findEuropeanCustomers();
