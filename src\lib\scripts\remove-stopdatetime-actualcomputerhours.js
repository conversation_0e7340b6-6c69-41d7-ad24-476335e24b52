// Script to remove StopDateTime from all ActualComputerHours documents in MongoDB
import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function run() {
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    const result = await db.collection('ActualComputerHours').updateMany(
      { StopDateTime: { $exists: true } },
      { $unset: { StopDateTime: "" } }
    );
    console.log(`Removed StopDateTime from ${result.modifiedCount} documents.`);
  } finally {
    await client.close();
  }
}

run();
