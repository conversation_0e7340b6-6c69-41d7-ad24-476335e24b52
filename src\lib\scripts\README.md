# Ad-hoc Scripts

This directory contains ad-hoc JavaScript scripts for database queries, data manipulation, and other utilities. These scripts are stored for future reference and can be run directly using Node.js.

## Usage

1. Scripts can be run directly using Node.js:
   ```bash
   node src/lib/scripts/your-script.js
   ```

2. All scripts use the standard MongoDB connection from `$lib/db.js`

3. Scripts are named descriptively based on their purpose, e.g.:
   - `query-german-customers.js`
   - `update-product-codes.js`
   - `export-service-data.js`

## Creating New Scripts

When creating new scripts:
1. Use a descriptive filename in kebab-case
2. Add proper comments explaining the script's purpose
3. Include example usage if applicable
4. Handle errors appropriately
5. Close MongoDB connections when done
