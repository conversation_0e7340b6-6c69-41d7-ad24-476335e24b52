import { MongoClient, ObjectId } from 'mongodb';
import { error, fail } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';

/** @type {import('./$types').PageServerLoad} */
export async function load() {
  const client = new MongoClient(uri);

  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    const priceList = db.collection('PriceLIst');

    // Fetch all price list items
    const items = await priceList.find({}).sort({ "Product group": 1, "Part No": 1 }).toArray();

    // Get unique product groups for filtering
    const productGroups = await priceList.distinct('Product group');

    // Get unique function groups for filtering
    const functionGroups = await priceList.distinct('Function group');

    // Get unique countries for filtering
    const countries = await priceList.distinct('Country of Origin');

    return {
      items: items.map(item => ({
        ...item,
        _id: item._id.toString()
      })),
      productGroups: productGroups.filter(Boolean).sort(),
      functionGroups: functionGroups.filter(Boolean).sort(),
      countries: countries.filter(Boolean).sort()
    };

  } catch (err) {
    console.error('Error loading price list:', err);
    throw error(500, 'Failed to load price list');
  } finally {
    await client.close();
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  create: async ({ request }) => {
    const client = new MongoClient(uri);

    try {
      const formData = await request.formData();

      const itemData = {
        "Part No": parseInt(formData.get('partNo')) || 0,
        "Check digit": parseInt(formData.get('checkDigit')) || 0,
        "Description": formData.get('description'),
        "Price excl VAT": formData.get('priceExclVAT'),
        "Discount Code": parseInt(formData.get('discountCode')) || 0,
        "Product group": parseInt(formData.get('productGroup')) || 0,
        "Function group": parseInt(formData.get('functionGroup')) || 0,
        "Statistic No": parseInt(formData.get('statisticNo')) || 0,
        " Weight ": formData.get('weight') || "",
        "Country of Origin": formData.get('countryOfOrigin') || ""
      };

      // Validate required fields
      if (!itemData["Part No"] || !itemData["Description"]) {
        return fail(400, { error: 'Part number and description are required' });
      }

      await client.connect();
      const db = client.db('ServiceContracts');
      const priceList = db.collection('PriceLIst');

      const result = await priceList.insertOne(itemData);

      if (!result.acknowledged) {
        return fail(500, { error: 'Failed to create price list item' });
      }

      return { success: true, id: result.insertedId.toString() };

    } catch (err) {
      console.error('Error creating price list item:', err);
      return fail(500, { error: 'Failed to create price list item' });
    } finally {
      await client.close();
    }
  },

  update: async ({ request }) => {
    const client = new MongoClient(uri);

    try {
      const formData = await request.formData();
      const id = formData.get('_id');

      if (!ObjectId.isValid(id)) {
        return fail(400, { error: 'Invalid item ID' });
      }

      const updateData = {
        "Part No": parseInt(formData.get('partNo')) || 0,
        "Check digit": parseInt(formData.get('checkDigit')) || 0,
        "Description": formData.get('description'),
        "Price excl VAT": formData.get('priceExclVAT'),
        "Discount Code": parseInt(formData.get('discountCode')) || 0,
        "Product group": parseInt(formData.get('productGroup')) || 0,
        "Function group": parseInt(formData.get('functionGroup')) || 0,
        "Statistic No": parseInt(formData.get('statisticNo')) || 0,
        " Weight ": formData.get('weight') || "",
        "Country of Origin": formData.get('countryOfOrigin') || ""
      };

      // Validate required fields
      if (!updateData["Part No"] || !updateData["Description"]) {
        return fail(400, { error: 'Part number and description are required' });
      }

      await client.connect();
      const db = client.db('ServiceContracts');
      const priceList = db.collection('PriceLIst');

      const result = await priceList.updateOne(
        { _id: new ObjectId(id) },
        { $set: updateData }
      );

      if (result.matchedCount === 0) {
        return fail(404, { error: 'Price list item not found' });
      }

      return { success: true };

    } catch (err) {
      console.error('Error updating price list item:', err);
      return fail(500, { error: 'Failed to update price list item' });
    } finally {
      await client.close();
    }
  },

  delete: async ({ request }) => {
    const client = new MongoClient(uri);

    try {
      const formData = await request.formData();
      const id = formData.get('_id');

      if (!ObjectId.isValid(id)) {
        return fail(400, { error: 'Invalid item ID' });
      }

      await client.connect();
      const db = client.db('ServiceContracts');
      const priceList = db.collection('PriceLIst');

      const result = await priceList.deleteOne({ _id: new ObjectId(id) });

      if (result.deletedCount === 0) {
        return fail(404, { error: 'Price list item not found' });
      }

      return { success: true };

    } catch (err) {
      console.error('Error deleting price list item:', err);
      return fail(500, { error: 'Failed to delete price list item' });
    } finally {
      await client.close();
    }
  }
};
