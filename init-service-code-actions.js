import { MongoClient, ObjectId } from 'mongodb';

// Connection URI
const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

// Database Name
const dbName = 'ServiceContracts';

// Sample service code actions data
const serviceCodeActions = [
  {
    serviceCode: 'SC001',
    partNumber: 'PN12345',
    description: 'Regular maintenance service for engine type A',
    cost: 150.00,
    price: 250.00,
    active: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    serviceCode: 'SC002',
    partNumber: 'PN23456',
    description: 'Oil change service for heavy machinery',
    cost: 75.50,
    price: 120.00,
    active: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    serviceCode: 'SC003',
    partNumber: 'PN34567',
    description: 'Filter replacement for industrial equipment',
    cost: 45.25,
    price: 89.99,
    active: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    serviceCode: 'SC004',
    partNumber: 'PN45678',
    description: 'Emergency repair service for critical components',
    cost: 350.00,
    price: 650.00,
    active: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    serviceCode: 'SC005',
    partNumber: 'PN56789',
    description: 'Preventive maintenance package for diesel engines',
    cost: 225.75,
    price: 399.99,
    active: false,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

async function main() {
  try {
    // Connect to the MongoDB server
    await client.connect();
    console.log('Connected successfully to MongoDB server');

    // Get the database
    const db = client.db(dbName);

    // Get the collection
    const collection = db.collection('PartNumbersServiceCodeAction');

    // Check if collection has any documents
    const count = await collection.countDocuments();
    if (count > 0) {
      console.log(`Collection already has ${count} documents. Dropping collection...`);
      await collection.drop();
      console.log('Collection dropped successfully.');
    }

    // Create unique index for serviceCode and partNumber combination
    await collection.createIndex({ serviceCode: 1, partNumber: 1 }, { unique: true });
    console.log('Created unique index on serviceCode and partNumber');

    // Insert the sample data
    const result = await collection.insertMany(serviceCodeActions);
    console.log(`${result.insertedCount} service code actions were inserted`);
    
    // List all inserted documents
    console.log('\nInserted service code actions:');
    const allServiceCodeActions = await collection.find({}).toArray();
    allServiceCodeActions.forEach((action, index) => {
      console.log(`${index + 1}. ${action.serviceCode} - ${action.partNumber}: ${action.description}`);
    });

  } catch (err) {
    console.error('An error occurred:', err);
  } finally {
    // Close the connection
    await client.close();
    console.log('Connection closed');
  }
}

main().catch(console.error);
