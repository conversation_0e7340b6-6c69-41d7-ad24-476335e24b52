import { MongoClient, ObjectId } from 'mongodb';
import { getCollection } from './src/lib/db/mongo.js';

/**
 * Add engine variants to the Variants collection
 */
async function addEngineVariants() {
  try {
    // Get the Variants collection
    const variantsCollection = await getCollection('Variants');
    
    // Engine variants data
    const engineVariants = [
      {
        variant_number: 'VAR-PENTA-MARIN-D2',
        name: '41-PENTA MARIN ENG VE-13 RATINGS TURBO-D2',
        part_number: '00020101',
        description: 'Penta Marin Engine with VE-13 Ratings and Turbo-D2 configuration',
        components: [
          {
            calculation_number: 'IND-SPT-D13',
            part_number: '00010001',
            quantity: 1
          },
          {
            calculation_number: 'VOCOM-II-HW',
            part_number: '00010003',
            quantity: 1
          }
        ],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        variant_number: 'VAR-PENTA-INDUSTRI-P4B',
        name: '41-PEN<PERSON> INDUSTRI ENG VE-13 STM P4B',
        part_number: '00020102',
        description: 'Penta Industrial Engine with VE-13 STM P4B configuration',
        components: [
          {
            calculation_number: 'IND-SPT-D13',
            part_number: '00010001',
            quantity: 1
          },
          {
            calculation_number: 'EATS-MOT-001',
            part_number: '00010002',
            quantity: 1
          }
        ],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        variant_number: 'VAR-PENA-INDUSTRI-P4B',
        name: '41-PENA INDUSTRI ENG VE-13 STM P4B',
        part_number: '00020103',
        description: 'Pena Industrial Engine with VE-13 STM P4B configuration',
        components: [
          {
            calculation_number: 'EATS-MOT-001',
            part_number: '00010002',
            quantity: 1
          }
        ],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        variant_number: 'VAR-PENTA-INDUSTRI-P3A',
        name: '41-PENTA INDUSTRI ENG VE-13 STM P3A',
        part_number: '00020104',
        description: 'Penta Industrial Engine with VE-13 STM P3A configuration',
        components: [
          {
            calculation_number: 'IND-SPT-D13',
            part_number: '00010001',
            quantity: 1
          },
          {
            calculation_number: 'VOCOM-II-HW',
            part_number: '00010003',
            quantity: 2
          }
        ],
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        variant_number: 'VAR-PENA-INDUSTRI-P3A',
        name: '41-PENA INDUSTRI ENG VE-13 STM P3A',
        part_number: '00020105',
        description: 'Pena Industrial Engine with VE-13 STM P3A configuration',
        components: [
          {
            calculation_number: 'EATS-MOT-001',
            part_number: '00010002',
            quantity: 1
          },
          {
            calculation_number: 'VOCOM-II-HW',
            part_number: '00010003',
            quantity: 1
          }
        ],
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
    
    console.log(`Prepared ${engineVariants.length} engine variants for insertion`);
    
    // Check for existing variants to avoid duplicates
    const existingVariants = await variantsCollection.find({
      $or: engineVariants.map(v => ({ variant_number: v.variant_number }))
    }).toArray();
    
    if (existingVariants.length > 0) {
      console.log(`Found ${existingVariants.length} existing variants with the same variant numbers`);
      console.log('Existing variant numbers:', existingVariants.map(v => v.variant_number).join(', '));
      
      // Filter out variants that already exist
      const newVariants = engineVariants.filter(variant => 
        !existingVariants.some(existing => existing.variant_number === variant.variant_number)
      );
      
      if (newVariants.length === 0) {
        console.log('All engine variants already exist. No new variants to add.');
        return;
      }
      
      console.log(`Adding ${newVariants.length} new engine variants`);
      
      // Insert new variants
      const result = await variantsCollection.insertMany(newVariants);
      console.log(`Added ${result.insertedCount} new engine variants to the Variants collection`);
    } else {
      // Insert all variants
      const result = await variantsCollection.insertMany(engineVariants);
      console.log(`Added ${result.insertedCount} engine variants to the Variants collection`);
    }
    
    // List all variants in the collection
    const allVariants = await variantsCollection.find({}).toArray();
    console.log('\nAll variants in collection:');
    allVariants.forEach(variant => {
      console.log(`- ${variant.variant_number}: ${variant.name} (Part Number: ${variant.part_number})`);
      console.log(`  Components (${variant.components.length}):`);
      variant.components.forEach(component => {
        console.log(`    * ${component.calculation_number} (Part: ${component.part_number}), Quantity: ${component.quantity}`);
      });
    });
    
  } catch (error) {
    console.error('Error adding engine variants:', error);
  }
}

// Run the function
addEngineVariants();
