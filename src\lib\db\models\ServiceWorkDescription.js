import { ObjectId } from 'mongodb';
import { db } from '$lib/db';

/**
 * @typedef {Object} ServiceWorkDescription
 * @property {ObjectId} _id - MongoDB ObjectId
 * @property {string} Title - Title of the work description
 * @property {string} Description - Detailed description of the work
 * @property {string} ServiceCode - Associated service code
 * @property {Date} createdAt - Creation timestamp
 * @property {Date} updatedAt - Last update timestamp
 */

const collection = db.collection('ServiceWorkDescription');

/**
 * @returns {Promise<Array<ServiceWorkDescription & {_id: string}>>}
 */
export async function getAllServiceWorkDescriptions() {
    /** @type {Array<ServiceWorkDescription>} */
    const descriptions = await collection.find({}).toArray();
    return descriptions.map((description) => ({
        ...description,
        _id: description._id.toString()
    }));
}

/**
 * @param {string} id
 * @returns {Promise<(ServiceWorkDescription & {_id: string}) | null>}
 */
export async function getServiceWorkDescriptionById(id) {
    if (!ObjectId.isValid(id)) {
        throw new Error('Invalid ObjectId');
    }
    /** @type {ServiceWorkDescription | null} */
    const description = await collection.findOne({ _id: new ObjectId(id) });
    if (!description) return null;
    return {
        ...description,
        _id: description._id.toString()
    };
}

/**
 * @param {{Title: string, Description: string, ServiceCode: string}} data
 * @returns {Promise<string>}
 */
export async function createServiceWorkDescription(data) {
    const result = await collection.insertOne({
        ...data,
        createdAt: new Date(),
        updatedAt: new Date()
    });
    return result.insertedId.toString();
}

/**
 * @param {string} id
 * @param {{Title?: string, Description?: string, ServiceCode?: string}} data
 * @returns {Promise<boolean>}
 */
export async function updateServiceWorkDescription(id, data) {
    if (!ObjectId.isValid(id)) {
        throw new Error('Invalid ObjectId');
    }
    const result = await collection.updateOne(
        { _id: new ObjectId(id) },
        { 
            $set: {
                ...data,
                updatedAt: new Date()
            }
        }
    );
    return result.modifiedCount > 0;
}

/**
 * @param {string} id
 * @returns {Promise<boolean>}
 */
export async function deleteServiceWorkDescription(id) {
    if (!ObjectId.isValid(id)) {
        throw new Error('Invalid ObjectId');
    }
    const result = await collection.deleteOne({ _id: new ObjectId(id) });
    return result.deletedCount > 0;
}
