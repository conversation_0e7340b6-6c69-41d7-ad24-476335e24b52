<script>
  /**
   * Grid-based filter component for data tables
   * @typedef {Object} FilterField
   * @property {string} id - Field identifier matching the database field
   * @property {string} name - Human-readable name for the filter field
   * @property {string} value - Current filter value
   */
  
  /** @type {FilterField[]} */
  export let fields = [];
  
  /**
   * Apply the current filters
   */
  export function applyFilters() {
    // Use history API to build URL with all filter parameters
    const url = new URL(window.location.href);
    
    // Clear existing filter parameters (preserve other parameters)
    fields.forEach(field => {
      url.searchParams.delete(field.id);
    });
    
    // Set parameters for fields with values
    fields.forEach(field => {
      if (field.value && field.value.trim() !== '') {
        url.searchParams.set(field.id, field.value);
      }
    });
    
    // Reset to page 1 when applying new filters
    url.searchParams.set('page', '1');
    
    // Navigate to the new URL
    window.location.href = url.toString();
  }
  
  /**
   * Clear all filters
   */
  function clearFilters() {
    fields.forEach(field => field.value = '');
    
    // Apply the cleared filters
    applyFilters();
  }
  
  /**
   * Check if any filter has a value
   * @returns {boolean} - True if any filter has a value
   */
  function hasActiveFilters() {
    return fields.some(field => field.value && field.value.trim() !== '');
  }
</script>

<div class="filter-section">
  <div class="filter-header">
    <h3>Filter Service Codes</h3>
  </div>
  
  <div class="filter-content">
    <div class="filter-header-row">
      {#each fields as field}
        <div class="filter-column-header">{field.name}</div>
      {/each}
    </div>
    
    <div class="filter-input-row">
      {#each fields as field}
        <div class="filter-field">
          <input 
            type="text" 
            class="filter-input" 
            placeholder="Filter..." 
            bind:value={field.value}
          />
        </div>
      {/each}
    </div>
    
    <div class="filter-actions">
      <button type="button" class="btn-primary filter-button" on:click={applyFilters}>
        <svg xmlns="http://www.w3.org/2000/svg" class="filter-icon" viewBox="0 0 20 20" fill="currentColor" width="16" height="16">
          <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd" />
        </svg>
        Apply Filters
      </button>
      
      {#if hasActiveFilters()}
        <button type="button" class="btn-secondary filter-button" on:click={clearFilters}>
          Clear All
        </button>
      {/if}
    </div>
  </div>
</div>

<style>
  .filter-section {
    width: 100%;
    margin-bottom: 1.5rem;
    background-color: #f9fafb;
    border: 1px solid #edf2f7;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    overflow: hidden;
  }
  
  .filter-header {
    background-color: #f1f5f9;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .filter-header h3 {
    margin: 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: #475569;
  }
  
  .filter-content {
    padding: 1rem;
  }
  
  .filter-header-row {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
    align-items: center;
    margin-bottom: 0.5rem;
  }
  
  .filter-input-row {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
    align-items: center;
    margin-bottom: 1rem;
  }
  
  .filter-column-header {
    font-size: 0.75rem;
    font-weight: 600;
    color: #64748b;
    padding: 0 0.25rem;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .filter-field {
    width: 100%;
  }
  
  .filter-input {
    padding: 0.5rem 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    width: 100%;
    height: 2.25rem;
    transition: all 0.2s ease;
    background-color: white;
  }
  
  .filter-input:hover {
    border-color: #cbd5e1;
  }
  
  .filter-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.15);
  }
  
  .filter-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-start;
    border-top: 1px solid #f1f5f9;
    padding-top: 1rem;
  }
  
  .filter-icon {
    margin-right: 0.25rem;
    width: 14px;
    height: 14px;
  }
  
  .filter-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 2.25rem;
    padding: 0 1rem;
    font-size: 0.8125rem;
    font-weight: 500;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
  }
  
  .btn-primary {
    background-color: #3b82f6;
    color: white;
    border: none;
  }
  
  .btn-primary:hover {
    background-color: #2563eb;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  .btn-secondary {
    background-color: #f1f5f9;
    color: #475569;
    border: 1px solid #e2e8f0;
  }
  
  .btn-secondary:hover {
    background-color: #e2e8f0;
    border-color: #cbd5e1;
  }
</style>
