import { MongoClient } from 'mongodb';
import { json } from '@sveltejs/kit';

// MongoDB connection URI
const uri = 'mongodb://localhost:27017';

// POST: Create a new service ID item
export async function POST({ request }) {
  const client = new MongoClient(uri);
  
  try {
    // Parse the request body
    const data = await request.json();
    
    // Validate required fields
    if (!data.serviceCode) {
      return new Response(JSON.stringify({ error: 'Service Code is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    await client.connect();
    const db = client.db('ServiceContracts');
    const serviceIdCollection = db.collection('ServiceID');
    
    // Check if a service with this code already exists
    const existing = await serviceIdCollection.findOne({ serviceCode: data.serviceCode });
    if (existing) {
      return new Response(JSON.stringify({ error: 'A service with this code already exists' }), {
        status: 409,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Add timestamps
    const newItem = {
      ...data,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Insert the new service ID item
    const result = await serviceIdCollection.insertOne(newItem);
    
    // Return the created item with its ID
    return json({
      _id: result.insertedId,
      ...newItem
    });
    
  } catch (error) {
    console.error('Error creating service ID item:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  } finally {
    await client.close();
  }
}
