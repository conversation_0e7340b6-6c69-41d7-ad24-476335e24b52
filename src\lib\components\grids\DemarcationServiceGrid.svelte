<!-- DemarcationServiceGrid.svelte -->
<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    
    // Define interfaces
    interface ServiceAction {
        _id: string;
        title: string;
        description: string;
        demarcation: string;
        status: string;
        priority: string;
        assignedTo?: string;
        dueDate?: string | Date;
        createdAt?: string | Date;
        updatedAt?: string | Date;
    }
    
    // Props
    export let serviceActions: ServiceAction[] = [];
    export let demarcation: string = '';
    
    // Event dispatcher
    const dispatch = createEventDispatcher();
    
    // Sort actions by priority and status
    $: sortedActions = [...serviceActions].sort((a, b) => {
        // First sort by priority (High > Medium > Low)
        const priorityOrder = { 'High': 0, 'Medium': 1, 'Low': 2 };
        const priorityDiff = (priorityOrder[a.priority] || 3) - (priorityOrder[b.priority] || 3);
        
        if (priorityDiff !== 0) return priorityDiff;
        
        // Then sort by status (Open > In Progress > Completed)
        const statusOrder = { 'Open': 0, 'In Progress': 1, 'Completed': 2 };
        return (statusOrder[a.status] || 3) - (statusOrder[b.status] || 3);
    });
    
    // Edit action
    function editAction(action: ServiceAction) {
        dispatch('edit', action);
    }
    
    // Add new action
    function addAction() {
        dispatch('add', { demarcation });
    }
    
    // Get priority color class
    function getPriorityClass(priority: string): string {
        switch(priority.toLowerCase()) {
            case 'high': return 'priority-high';
            case 'medium': return 'priority-medium';
            case 'low': return 'priority-low';
            default: return '';
        }
    }
    
    // Get status color class
    function getStatusClass(status: string): string {
        switch(status.toLowerCase()) {
            case 'open': return 'status-open';
            case 'in progress': return 'status-progress';
            case 'completed': return 'status-completed';
            default: return '';
        }
    }
</script>

<div class="demarcation-service-grid">
    <div class="grid-header">
        <h3>Service Actions for {demarcation || 'Demarcation'}</h3>
        <button class="add-action-btn" on:click={addAction}>+ Add Action</button>
    </div>
    
    <div class="grid-container">
        <div class="grid-row header">
            <div class="grid-cell">Title</div>
            <div class="grid-cell">Description</div>
            <div class="grid-cell">Priority</div>
            <div class="grid-cell">Status</div>
            <div class="grid-cell">Assigned To</div>
            <div class="grid-cell">Due Date</div>
            <div class="grid-cell">Actions</div>
        </div>
        
        {#if sortedActions.length === 0}
            <div class="grid-row empty">
                <div class="grid-cell" style="grid-column: 1 / span 7; text-align: center;">
                    No service actions found. Click "Add Action" to create one.
                </div>
            </div>
        {:else}
            {#each sortedActions as action}
                <div class="grid-row">
                    <div class="grid-cell">{action.title}</div>
                    <div class="grid-cell description">{action.description}</div>
                    <div class="grid-cell">
                        <span class="priority-badge {getPriorityClass(action.priority)}">
                            {action.priority}
                        </span>
                    </div>
                    <div class="grid-cell">
                        <span class="status-badge {getStatusClass(action.status)}">
                            {action.status}
                        </span>
                    </div>
                    <div class="grid-cell">{action.assignedTo || '-'}</div>
                    <div class="grid-cell">
                        {#if action.dueDate}
                            {new Date(action.dueDate).toLocaleDateString()}
                        {:else}
                            -
                        {/if}
                    </div>
                    <div class="grid-cell actions">
                        <button class="action-btn edit" on:click={() => editAction(action)}>Edit</button>
                    </div>
                </div>
            {/each}
        {/if}
    </div>
</div>

<style>
    .demarcation-service-grid {
        width: 100%;
        margin-bottom: 2rem;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .grid-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background-color: #f8fafc;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .grid-header h3 {
        margin: 0;
        color: #102a54;
        font-size: 1.25rem;
    }
    
    .add-action-btn {
        background-color: #1e40af;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 0.5rem 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .add-action-btn:hover {
        background-color: #1e3a8a;
    }
    
    .grid-container {
        display: grid;
        grid-template-columns: 1fr;
        width: 100%;
    }
    
    .grid-row {
        display: grid;
        grid-template-columns: 1.5fr 2fr 1fr 1fr 1fr 1fr 0.8fr;
        grid-auto-flow: row;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .grid-row.header {
        background-color: #f8fafc;
        font-weight: 600;
        color: #102a54;
    }
    
    .grid-row.empty {
        padding: 2rem 0;
        color: #64748b;
    }
    
    .grid-cell {
        padding: 0.75rem;
        display: flex;
        align-items: center;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .description {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .priority-badge, .status-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .priority-high {
        background-color: rgba(239, 68, 68, 0.1);
        color: #ef4444;
    }
    
    .priority-medium {
        background-color: rgba(245, 158, 11, 0.1);
        color: #f59e0b;
    }
    
    .priority-low {
        background-color: rgba(16, 185, 129, 0.1);
        color: #10b981;
    }
    
    .status-open {
        background-color: rgba(59, 130, 246, 0.1);
        color: #3b82f6;
    }
    
    .status-progress {
        background-color: rgba(245, 158, 11, 0.1);
        color: #f59e0b;
    }
    
    .status-completed {
        background-color: rgba(16, 185, 129, 0.1);
        color: #10b981;
    }
    
    .action-btn {
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 500;
        cursor: pointer;
        border: none;
    }
    
    .edit {
        background-color: #3b82f6;
        color: white;
    }
    
    .edit:hover {
        background-color: #2563eb;
    }
</style>
