/**
 * <PERSON><PERSON><PERSON> to initialize the CalculationPartNumbers collection with data from JSON file
 * 
 * This script follows the project conventions:
 * - Uses PascalCase for MongoDB collection names
 * - Uses native MongoDB ObjectIds for _id fields
 * - All collections are in database "ServiceContracts"
 */

const { MongoClient, ObjectId } = require('mongodb');
const fs = require('fs');
const path = require('path');

// MongoDB connection string - update as needed
const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

// Read the JSON data
const dataPath = path.join(__dirname, 'data', 'calculation-part-numbers.json');
const jsonData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));

// Add additional fields and prepare for MongoDB
const calculationPartNumbers = jsonData.map(item => {
  // Generate a random price between 1000 and 10000
  const base_price = Math.floor(Math.random() * 9000) + 1000;
  
  // Calculate other price components
  const labor_cost = Math.floor(base_price * 0.3);
  const material_cost = Math.floor(base_price * 0.5);
  const overhead = Math.floor(base_price * 0.2);
  
  // Add 20% markup for RRP
  const total_RRP = Math.floor(base_price * 1.2);
  
  return {
    _id: new ObjectId(),
    calculation_number: item.calculation_number,
    part_number: item.part_number,
    description: `${item.calculation_number} Engine Component`,
    base_price,
    labor_cost,
    material_cost,
    overhead,
    total_RRP,
    created_at: new Date(),
    updated_at: new Date()
  };
});

async function initializeDatabase() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(dbName);
    
    // Use PascalCase for collection name according to project conventions
    const collection = db.collection('CalculationPartNumbers');
    
    // Check if collection already has data
    const count = await collection.countDocuments();
    if (count > 0) {
      console.log(`The CalculationPartNumbers collection already has ${count} documents.`);
      console.log('Do you want to drop the existing collection and re-import? (yes/no)');
      
      // This is a simple way to wait for user input in a script
      const readline = require('readline').createInterface({
        input: process.stdin,
        output: process.stdout
      });
      
      readline.question('', async (answer) => {
        if (answer.toLowerCase() === 'yes') {
          await collection.drop();
          console.log('Collection dropped successfully');
          await importData(collection);
        } else {
          console.log('Import cancelled');
        }
        readline.close();
        await client.close();
      });
    } else {
      await importData(collection);
      await client.close();
    }
  } catch (err) {
    console.error('Error:', err);
    await client.close();
  }
}

async function importData(collection) {
  try {
    const result = await collection.insertMany(calculationPartNumbers);
    console.log(`${result.insertedCount} calculation part numbers imported successfully`);
    
    // Create indexes
    await collection.createIndex({ calculation_number: 1 });
    await collection.createIndex({ part_number: 1 });
    console.log('Indexes created successfully');
  } catch (err) {
    console.error('Error importing data:', err);
  }
}

// Run the initialization
initializeDatabase();
