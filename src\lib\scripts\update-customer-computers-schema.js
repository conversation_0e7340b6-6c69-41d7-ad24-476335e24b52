import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function updateCustomerComputersSchema() {
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db('ServiceContracts');
    const collection = db.collection('CustomerComputers');
    
    // Find computers without SupplierProductName field
    const computers = await collection.find({
      SupplierProductName: { $exists: false }
    }).toArray();
    
    console.log(`Found ${computers.length} computers to update`);
    
    // Update each document
    let updateCount = 0;
    for (const computer of computers) {
      // Get the product name from the correct field (camelCase or PascalCase)
      const productName = computer.productName || computer.ProductName || '';
      
      const updateResult = await collection.updateOne(
        { _id: computer._id },
        { $set: { SupplierProductName: productName }}
      );
      
      if (updateResult.modifiedCount > 0) {
        updateCount++;
      }
    }
    
    console.log(`Successfully updated ${updateCount} of ${computers.length} computers`);
    
  } catch (error) {
    console.error('Error updating schema:', error);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the update function
updateCustomerComputersSchema();
