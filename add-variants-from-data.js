import { MongoClient, ObjectId } from 'mongodb';
import fs from 'fs';
import { getCollection } from './src/lib/db/mongo.js';

/**
 * Add engine variants to the EngineVariants collection based on calculation part numbers data
 */
async function addEngineVariantsFromData() {
  try {
    // Read calculation part numbers data from JSON file
    const calculationData = JSON.parse(fs.readFileSync('./calculation-part-numbers-data.json', 'utf8'));
    
    // Get the EngineVariants collection
    const engineVariantsCollection = await getCollection('EngineVariants');
    
    // Create variants based on each calculation part number
    const variants = calculationData.map((calculation, index) => {
      // Generate part number for variant (8-digit starting with 0002)
      const partNumberSuffix = (index + 1).toString().padStart(4, '0');
      const partNumber = `0002${partNumberSuffix}`;
      
      return {
        variant_number: `VAR-${calculation.calculation_number}`,
        name: `${calculation.name} Variant`,
        part_number: partNumber,
        description: `Variant based on ${calculation.name} calculation`,
        components: [
          {
            calculation_number: calculation.calculation_number,
            part_number: calculation.part_number,
            quantity: 1
          }
        ],
        createdAt: new Date(),
        updatedAt: new Date()
      };
    });
    
    console.log(`Prepared ${variants.length} variants for insertion`);
    
    // Check for existing variants to avoid duplicates
    const existingVariants = await engineVariantsCollection.find({
      $or: variants.map(v => ({ variant_number: v.variant_number }))
    }).toArray();
    
    if (existingVariants.length > 0) {
      console.log(`Found ${existingVariants.length} existing variants with the same variant numbers`);
      console.log('Existing variant numbers:', existingVariants.map(v => v.variant_number).join(', '));
      
      // Filter out variants that already exist
      const newVariants = variants.filter(variant => 
        !existingVariants.some(existing => existing.variant_number === variant.variant_number)
      );
      
      if (newVariants.length === 0) {
        console.log('All variants already exist. No new variants to add.');
        return;
      }
      
      console.log(`Adding ${newVariants.length} new variants`);
      
      // Insert new variants
      const result = await engineVariantsCollection.insertMany(newVariants);
      console.log(`Added ${result.insertedCount} new variants to the EngineVariants collection`);
    } else {
      // Insert all variants
      const result = await engineVariantsCollection.insertMany(variants);
      console.log(`Added ${result.insertedCount} variants to the EngineVariants collection`);
    }
    
    // List all variants in the collection
    const allVariants = await engineVariantsCollection.find({}).toArray();
    console.log('\nAll engine variants in collection:');
    allVariants.forEach(variant => {
      console.log(`- ${variant.variant_number}: ${variant.name} (Part Number: ${variant.part_number})`);
      console.log(`  Components (${variant.components.length}):`);
      variant.components.forEach(component => {
        console.log(`    * ${component.calculation_number} (Part: ${component.part_number}), Quantity: ${component.quantity}`);
      });
    });
    
  } catch (error) {
    console.error('Error adding engine variants:', error);
  }
}

// Run the function
addEngineVariantsFromData();
