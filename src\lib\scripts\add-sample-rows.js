import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function addSampleRows() {
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    
    // First, check if we have a quote
    const quotesCollection = db.collection('QuotationHeader');
    let quoteDoc = await quotesCollection.findOne({});
    
    if (!quoteDoc) {
      console.log('No quote found. Creating a sample quote...');
      const quoteResult = await quotesCollection.insertOne({
        QuoteNumber: 'Q-2025-001',
        Status: 'Draft',
        ContractLength: 12,
        TotalAmount: 0,
        CreatedAt: new Date(),
        UpdatedAt: new Date()
      });
      console.log('Created quote:', quoteResult.insertedId.toString());
      quoteDoc = await quotesCollection.findOne({ _id: quoteResult.insertedId });
      
      if (!quoteDoc) {
        throw new Error('Failed to create quote');
      }
    }

    // Now add sample rows
    const rowsCollection = db.collection('QuotationRow');
    
    const sampleRows = [
      {
        QuoteId: quoteDoc._id,
        RowType: 'BaseContract',
        RowOrder: 1,
        PackageName: 'Basic Service Package',
        ServiceActivity: 'Annual Maintenance',
        Cost: 1200.00,
        OemImporter: true,
        FleetOwner: false,
        CustomerSpecific: false,
        Required: true,
        IncludeInOffer: true,
        CreatedAt: new Date(),
        UpdatedAt: new Date()
      },
      {
        QuoteId: quoteDoc._id,
        RowType: 'DealerAddOn',
        RowOrder: 2,
        PackageName: 'Extended Support',
        ServiceActivity: '24/7 Phone Support',
        Cost: 500.00,
        OemImporter: false,
        FleetOwner: true,
        CustomerSpecific: false,
        Required: false,
        IncludeInOffer: true,
        CreatedAt: new Date(),
        UpdatedAt: new Date()
      },
      {
        QuoteId: quoteDoc._id,
        RowType: 'SupportService',
        RowOrder: 3,
        PackageName: 'On-Site Support',
        ServiceActivity: 'Emergency Response',
        Cost: 800.00,
        OemImporter: false,
        FleetOwner: false,
        CustomerSpecific: true,
        Required: false,
        IncludeInOffer: true,
        CreatedAt: new Date(),
        UpdatedAt: new Date()
      }
    ];

    // Insert the sample rows
    const result = await rowsCollection.insertMany(sampleRows);
    console.log('Added sample rows:', result.insertedIds);

    // Verify the rows
    const allRows = await rowsCollection.find({ QuoteId: quoteDoc._id }).toArray();
    console.log('All rows for quote:', allRows);

  } catch (err) {
    console.error('Error adding sample rows:', err);
  } finally {
    await client.close();
  }
}

addSampleRows();
