<script>
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import FormGrid from '$lib/components/grids/FormGrid.svelte';
	import { goto } from '$app/navigation';

	const computerId = $page.params.id;
	const rowId = $page.params.rowId;
	
	let quoteRow = {
		level: 0,
		package: '',
		serviceId: '',
		service: '',
		included: 'No',
		required: 'Yes',
		scos: 0,
		oemImport: 0,
		fleetOwner: 0,
		rrp: 0,
		selectService: 'Yes',
		qtyPerYr: 0
	};
	
	let loading = true;
	let saving = false;
	let error = null;

	onMount(async () => {
		if (rowId === 'new') {
			// Creating a new quote row
			loading = false;
			return;
		}
		
		try {
			const response = await fetch(`/api/quote-rows/detail/${rowId}`);
			if (response.ok) {
				quoteRow = await response.json();
			} else {
				error = await response.text();
				console.error('Failed to load quote row:', error);
			}
		} catch (err) {
			error = err.message;
			console.error('Error loading quote row:', err);
		} finally {
			loading = false;
		}
	});

	const handleSubmit = async () => {
		saving = true;
		
		try {
			const isNewRow = rowId === 'new';
			
			// If creating new row, add quotationId
			if (isNewRow) {
				quoteRow.quotationId = computerId;
			}
			
			const url = isNewRow 
				? `/api/quote-rows/create`
				: `/api/quote-rows/update-by-id/${rowId}`;
				
			const method = isNewRow ? 'POST' : 'PUT';
			
			const response = await fetch(url, {
				method,
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(quoteRow)
			});
			
			if (response.ok) {
				// Navigate back to list
				goto(`/computer-id/${computerId}/computer-quote`);
			} else {
				const errText = await response.text();
				throw new Error(errText);
			}
		} catch (err) {
			error = err.message;
			console.error('Error saving quote row:', err);
		} finally {
			saving = false;
		}
	};
</script>

<div class="edit-quote-container">
	<div class="header">
		<h1>{rowId === 'new' ? 'Create New' : 'Edit'} Quote Row</h1>
		<div class="actions">
			<a href="/computer-id/{computerId}/computer-quote" class="btn secondary">Cancel</a>
		</div>
	</div>

	{#if loading}
		<div class="loading">Loading quote details...</div>
	{:else if error}
		<div class="error">Error: {error}</div>
	{:else}
		<FormGrid>
			<form on:submit|preventDefault={handleSubmit}>
				<div class="form-grid">
					<div class="form-group">
						<label for="level">Level</label>
						<input 
							type="number" 
							id="level" 
							bind:value={quoteRow.level} 
							required 
							min="1"
						/>
					</div>
					
					<div class="form-group">
						<label for="package">Package</label>
						<input 
							type="text" 
							id="package" 
							bind:value={quoteRow.package} 
							required
						/>
					</div>
					
					<div class="form-group">
						<label for="serviceId">Service ID</label>
						<input 
							type="text" 
							id="serviceId" 
							bind:value={quoteRow.serviceId} 
							required
						/>
					</div>
					
					<div class="form-group">
						<label for="service">Service</label>
						<input 
							type="text" 
							id="service" 
							bind:value={quoteRow.service} 
							required
						/>
					</div>
					
					<div class="form-group">
						<label for="included">Included in Package</label>
						<select id="included" bind:value={quoteRow.included}>
							<option value="Yes">Yes</option>
							<option value="No">No</option>
						</select>
					</div>
					
					<div class="form-group">
						<label for="required">Required</label>
						<select id="required" bind:value={quoteRow.required}>
							<option value="Yes">Yes</option>
							<option value="No">No</option>
							<option value="Mandatory">Mandatory</option>
						</select>
					</div>
					
					<div class="form-group">
						<label for="scos">SCOS</label>
						<input 
							type="text" 
							id="scos" 
							bind:value={quoteRow.scos}
						/>
					</div>
					
					<div class="form-group">
						<label for="oemImport">OEM Import</label>
						<input 
							type="text" 
							id="oemImport" 
							bind:value={quoteRow.oemImport}
						/>
					</div>
					
					<div class="form-group">
						<label for="fleetOwner">Fleet Owner</label>
						<input 
							type="text" 
							id="fleetOwner" 
							bind:value={quoteRow.fleetOwner}
						/>
					</div>
					
					<div class="form-group">
						<label for="rrp">RRP</label>
						<input 
							type="text" 
							id="rrp" 
							bind:value={quoteRow.rrp}
						/>
					</div>
					
					<div class="form-group">
						<label for="selectService">Select Service</label>
						<select id="selectService" bind:value={quoteRow.selectService}>
							<option value="Yes">Yes</option>
							<option value="No">No</option>
							<option value="Mandatory">Mandatory</option>
						</select>
					</div>
					
					<div class="form-group">
						<label for="qtyPerYr">Quantity per Year</label>
						<input 
							type="number" 
							id="qtyPerYr" 
							bind:value={quoteRow.qtyPerYr} 
							min="0"
						/>
					</div>
				</div>
				
				<div class="form-actions">
					<button type="submit" class="btn primary" disabled={saving}>
						{saving ? 'Saving...' : 'Save Quote Row'}
					</button>
				</div>
			</form>
		</FormGrid>
	{/if}
</div>

<style>
	.edit-quote-container {
		padding: 1rem;
		max-width: 800px;
		margin: 0 auto;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 2rem;
	}

	.actions {
		display: flex;
		gap: 1rem;
	}

	.btn {
		padding: 0.5rem 1rem;
		border-radius: 4px;
		text-decoration: none;
		font-weight: 500;
		cursor: pointer;
		border: none;
	}

	.primary {
		background-color: #3498db;
		color: white;
	}

	.primary:disabled {
		background-color: #95c8ee;
		cursor: not-allowed;
	}

	.secondary {
		background-color: #f1f1f1;
		color: #333;
	}

	.form-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 1.5rem;
	}

	.form-group {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	label {
		font-weight: 500;
		color: #555;
	}

	input, select {
		padding: 0.75rem;
		border: 1px solid #ddd;
		border-radius: 4px;
		font-size: 1rem;
	}

	.form-actions {
		display: flex;
		justify-content: flex-end;
		margin-top: 2rem;
	}

	.loading, .error {
		padding: 2rem;
		text-align: center;
	}

	.error {
		color: #e74c3c;
	}
</style>
