<script>
  /**
   * A reusable CSS Grid layout component
   * @param {string} columns - Grid template columns (e.g., "1fr 1fr" or "repeat(auto-fit, minmax(250px, 1fr))")
   * @param {string} rows - Grid template rows (e.g., "auto auto")
   * @param {string} gap - Grid gap (e.g., "1rem" or "1rem 2rem")
   * @param {string} areas - Grid template areas
   * @param {string} justify - Justify items
   * @param {string} align - Align items
   * @param {string} className - Additional CSS classes
   */
  export let columns = "repeat(auto-fit, minmax(250px, 1fr))";
  export let rows = "auto";
  export let gap = "1rem";
  export let areas = "";
  export let justify = "stretch";
  export let align = "stretch";
  export let className = "";
</script>

<div class="grid-layout {className}" style="
  display: grid;
  grid-template-columns: {columns};
  grid-template-rows: {rows};
  gap: {gap};
  {areas ? `grid-template-areas: ${areas};` : ''}
  justify-items: {justify};
  align-items: {align};
">
  <slot />
</div>

<style>
  .grid-layout {
    width: 100%;
  }
</style>
