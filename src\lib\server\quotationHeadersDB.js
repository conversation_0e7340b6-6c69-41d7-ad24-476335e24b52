import { COLLECTIONS, serializeDocument, createObjectId, executeDbOperation } from './serviceContractDB.js';
import serviceContractDB from './serviceContractDB.js';

const db = serviceContractDB.db;
const collection = COLLECTIONS.QUOTATION_HEADERS;

/**
 * Create a new quotation header
 * @param {Object} quotationHeader - Quotation header data
 * @returns {Promise<Object>} Inserted document with ID
 */
export async function createQuotationHeader(quotationHeader) {
  // Add timestamps
  const now = new Date();
  const documentToInsert = {
    ...quotationHeader,
    status: quotationHeader.status || 'Draft',
    createdAt: now,
    updatedAt: now
  };
  
  // Convert string ID to ObjectId
  if (documentToInsert.computerId && typeof documentToInsert.computerId === 'string') {
    documentToInsert.computerId = createObjectId(documentToInsert.computerId);
  }
  
  return executeDbOperation(async () => {
    const result = await db.collection(collection).insertOne(documentToInsert);
    if (!result.acknowledged) {
      throw new Error('Failed to create quotation header');
    }
    return {
      ...serializeDocument(documentToInsert),
      _id: result.insertedId.toString()
    };
  }, 'Error creating quotation header');
}

/**
 * Get all quotation headers with optional filtering
 * @param {Object} filter - MongoDB filter object
 * @param {Object} options - MongoDB options (sort, limit, etc.)
 * @returns {Promise<Array>} Array of quotation headers
 */
export async function getQuotationHeaders(filter = {}, options = {}) {
  return executeDbOperation(async () => {
    const defaultOptions = {
      sort: { createdAt: -1 },
      limit: 100,
      ...options
    };
    
    const cursor = db.collection(collection).find(filter, defaultOptions);
    const documents = await cursor.toArray();
    return documents.map(doc => serializeDocument(doc));
  }, 'Error retrieving quotation headers');
}

/**
 * Get a single quotation header by ID
 * @param {string} id - Quotation header ID
 * @returns {Promise<Object|null>} Quotation header or null if not found
 */
export async function getQuotationHeaderById(id) {
  return executeDbOperation(async () => {
    const objectId = createObjectId(id);
    if (!objectId) return null;
    
    const document = await db.collection(collection).findOne({ _id: objectId });
    return document ? serializeDocument(document) : null;
  }, 'Error retrieving quotation header');
}

/**
 * Update a quotation header
 * @param {string} id - Quotation header ID
 * @param {Object} updates - Fields to update
 * @returns {Promise<Object|null>} Updated quotation header or null if not found
 */
export async function updateQuotationHeader(id, updates) {
  return executeDbOperation(async () => {
    const objectId = createObjectId(id);
    if (!objectId) return null;
    
    // Convert string ID to ObjectId if present
    if (updates.computerId && typeof updates.computerId === 'string') {
      updates.computerId = createObjectId(updates.computerId);
    }
    
    // Add updated timestamp
    const updateData = {
      ...updates,
      updatedAt: new Date()
    };
    
    // Don't allow updating the _id field
    if (updateData._id) {
      delete updateData._id;
    }
    
    const result = await db.collection(collection).findOneAndUpdate(
      { _id: objectId },
      { $set: updateData },
      { returnDocument: 'after' }
    );
    
    return result.value ? serializeDocument(result.value) : null;
  }, 'Error updating quotation header');
}

/**
 * Delete a quotation header
 * @param {string} id - Quotation header ID
 * @returns {Promise<boolean>} True if deleted, false if not found
 */
export async function deleteQuotationHeader(id) {
  return executeDbOperation(async () => {
    const objectId = createObjectId(id);
    if (!objectId) return false;
    
    const result = await db.collection(collection).deleteOne({ _id: objectId });
    return result.deletedCount > 0;
  }, 'Error deleting quotation header');
}

/**
 * Get quotation headers by computer ID
 * @param {string} computerId - Computer ID
 * @returns {Promise<Array>} Array of quotation headers
 */
export async function getQuotationHeadersByComputerId(computerId) {
  return executeDbOperation(async () => {
    const computerObjectId = createObjectId(computerId);
    if (!computerObjectId) return [];
    
    const documents = await db.collection(collection)
      .find({ computerId: computerObjectId })
      .sort({ createdAt: -1 })
      .toArray();
    
    return documents.map(doc => serializeDocument(doc));
  }, 'Error retrieving quotation headers by computer ID');
}

/**
 * Convert a quotation to a contract
 * @param {string} quotationId - Quotation header ID
 * @returns {Promise<Object>} Created contract header
 */
export async function convertQuotationToContract(quotationId) {
  return executeDbOperation(async () => {
    const quotation = await getQuotationHeaderById(quotationId);
    if (!quotation) {
      throw new Error('Quotation not found');
    }
    
    if (quotation.status !== 'Approved') {
      throw new Error('Only approved quotations can be converted to contracts');
    }
    
    // Get quotation lines
    const quotationLines = await db.collection(COLLECTIONS.QUOTATION_LINES)
      .find({ quotationHeaderId: createObjectId(quotationId) })
      .toArray();
    
    // Import the contract modules here to avoid circular dependencies
    const contractHeadersDB = await import('./contractHeadersDB.js');
    const contractLinesDB = await import('./contractLinesDB.js');
    
    // Create contract header
    const contractHeader = {
      computerId: quotation.computerId,
      customerName: quotation.customerName,
      customerType: quotation.customerType,
      contractLength: quotation.contractLength,
      contractStartDate: quotation.contractStartDate || new Date(),
      contractNumber: quotation.contractNumber,
      productDesignation: quotation.productDesignation,
      productValidityGroup: quotation.productValidityGroup,
      totalAmount: quotation.totalAmount,
      status: 'Draft',
      quotationId: quotationId,
      signDate: null,
      endDate: null
    };
    
    const createdContract = await contractHeadersDB.default.createContractHeader(contractHeader);
    
    // Create contract lines
    const contractLines = quotationLines.map(line => ({
      contractHeaderId: createdContract._id,
      lineType: line.rowType,
      lineNumber: line.rowOrder,
      packageId: line.packageId,
      packageName: line.packageName,
      activity: line.activity,
      cost: line.cost,
      quantity: 1,
      oemImporter: line.oemImporter,
      fleetOwner: line.fleetOwner,
      customerSpecific: line.customerSpecific
    }));
    
    await contractLinesDB.default.createContractLines(contractLines, createdContract._id);
    
    // Update quotation status
    await updateQuotationHeader(quotationId, {
      status: 'Converted',
      convertedToContractId: createdContract._id
    });
    
    return createdContract;
  }, 'Error converting quotation to contract');
}

export default {
  createQuotationHeader,
  getQuotationHeaders,
  getQuotationHeaderById,
  updateQuotationHeader,
  deleteQuotationHeader,
  getQuotationHeadersByComputerId,
  convertQuotationToContract
};
