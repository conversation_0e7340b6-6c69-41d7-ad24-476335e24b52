import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/** @type {import('./$types').PageServerLoad} */
export async function load({ params }) {
  try {
    const db = client.db('ServiceContracts');
    const computerId = params.id;
    
    console.log(`Loading quotes for computer ID: ${computerId}`);
    
    if (!computerId || !ObjectId.isValid(computerId)) {
      console.error('Invalid computer ID:', computerId);
      return {
        error: 'Invalid computer ID',
        quotes: [],
        computerInfo: null
      };
    }

    // Get computer information
    const computer = await db.collection('CustomerComputers').findOne({
      _id: new ObjectId(computerId)
    });

    if (!computer) {
      console.error('Computer not found:', computerId);
      return {
        error: 'Computer not found',
        quotes: [],
        computerInfo: null
      };
    }

    // Get customer information
    let customerName = 'N/A';
    if (computer.customerId && ObjectId.isValid(computer.customerId)) {
      const customer = await db.collection('Customers').findOne({
        _id: new ObjectId(computer.customerId)
      });
      
      if (customer) {
        customerName = customer.name || 'N/A';
      }
    }

    // Get all quotations for this computer
    const quotes = await db.collection('QuotationHeader')
      .find({ computerId: new ObjectId(computerId) })
      .sort({ createdAt: -1 })
      .toArray();

    // Convert ObjectIds to strings for client-side use
    const formattedQuotes = quotes.map(quote => ({
      ...quote,
      _id: quote._id.toString(),
      computerId: quote.computerId.toString()
    }));

    // Format computer info for client
    const computerInfo = {
      computerId: computer._id.toString(),
      serialNumber: computer.serialNumber || 'N/A',
      model: computer.model || 'N/A',
      customerName
    };

    return {
      quotes: formattedQuotes,
      computerInfo
    };
  } catch (error) {
    console.error('Error loading quotes:', error);
    return {
      error: error.message,
      quotes: [],
      computerInfo: null
    };
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  /**
   * Create a new quotation record in the QuotationHeader collection
   */
  createNewQuote: async ({ request, url, params }) => {
    try {
      const formData = await request.formData();
      const computerId = formData.get('computerId');
      
      console.log('Creating new quote for computer ID:', computerId);
      
      if (!computerId) {
        console.error('Missing computer ID');
        return { success: false, error: 'Computer ID is required' };
      }
      
      if (!ObjectId.isValid(computerId)) {
        console.error('Invalid computer ID format:', computerId);
        return { success: false, error: 'Invalid computer ID format' };
      }
      
      const db = client.db('ServiceContracts');
      
      // Generate a unique quotation number
      const latestQuote = await db.collection('QuotationHeader')
        .find({})
        .sort({ createdAt: -1 })
        .limit(1)
        .toArray();
      
      let quotationNumber = 'Q' + new Date().getFullYear().toString().substr(-2) + '25-0001';
      
      if (latestQuote.length > 0 && latestQuote[0].quotationNumber) {
        const lastNumber = latestQuote[0].quotationNumber;
        const parts = lastNumber.split('-');
        if (parts.length === 2) {
          const sequence = parseInt(parts[1], 10) + 1;
          quotationNumber = parts[0] + '-' + sequence.toString().padStart(4, '0');
        }
      }
      
      // Create the new quotation header
      const newQuote = {
        computerId: new ObjectId(computerId),
        quotationNumber,
        createdAt: new Date(),
        updatedAt: new Date(),
        status: 'Draft',
        contractLength: 12, // Default 12 months
        contractStartDate: new Date(),
        totalAmount: 0, // Initial amount is zero
        notes: ''
      };
      
      console.log('Creating new quote with data:', newQuote);
      
      try {
        const result = await db.collection('QuotationHeader').insertOne(newQuote);
        
        if (!result.acknowledged) {
          console.error('Failed to insert quote - not acknowledged');
          return { success: false, error: 'Failed to create quotation' };
        }
        
        const quotationId = result.insertedId.toString();
        console.log('Quote created successfully with ID:', quotationId);
        
        // Redirect to the quote detail page
        return {
          status: 303,
          headers: {
            location: `/computer-id/${params.id}/quote/${quotationId}`
          }
        };
      } catch (insertError) {
        console.error('Error inserting new quote:', insertError);
        return { success: false, error: 'Database error while creating quotation' };
      }
    } catch (error) {
      console.error('Unexpected error creating quotation:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Failed to create quotation' };
    }
  }
};
