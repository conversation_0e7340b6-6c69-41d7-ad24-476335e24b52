<script>
  import EnhancedServiceOffering from '$lib/components/EnhancedServiceOffering.svelte';

  // Sample data that matches the structure from the image
  const sampleQuotationPackages = {
    baseContract: [
      {
        id: '1',
        serviceCode: '4.1',
        ServiceActivity: 'Parts Supply on Request',
        cost: 0,
        required: true,
        includeInOffer: true,
        includedInPackage: true
      },
      {
        id: '2',
        serviceCode: '5.2',
        ServiceActivity: 'Monitoring',
        cost: 0,
        required: true,
        includeInOffer: true,
        includedInPackage: true
      }
    ],
    repairPackages: [
      {
        id: '3',
        serviceCode: '1.2',
        ServiceActivity: 'Delivery Inspections Only',
        cost: 2814,
        required: false,
        includeInOffer: true,
        includedInPackage: false
      },
      {
        id: '4',
        serviceCode: '6.2',
        ServiceActivity: 'Repair Parts Only',
        cost: 254251,
        required: false,
        includeInOffer: true,
        includedInPackage: false
      },
      {
        id: '5',
        serviceCode: '9.1',
        ServiceActivity: 'Preventive Program Parts',
        cost: 0,
        required: false,
        includeInOffer: false,
        includedInPackage: true
      },
      {
        id: '6',
        serviceCode: '4.3',
        ServiceActivity: 'Engine Health Inspections',
        cost: 1050.00,
        required: false,
        includeInOffer: true,
        includedInPackage: false
      },
      {
        id: '7',
        serviceCode: '4.4',
        ServiceActivity: 'Adjustments',
        cost: 0,
        required: false,
        includeInOffer: false,
        includedInPackage: true
      },
      {
        id: '8',
        serviceCode: '4.7',
        ServiceActivity: 'EATS MOT',
        cost: '#VALUE!',
        required: false,
        includeInOffer: false,
        includedInPackage: true
      },
      {
        id: '9',
        serviceCode: '4.2',
        ServiceActivity: 'Parts Supply Overhaul Service',
        cost: '#N/A',
        required: false,
        includeInOffer: false,
        includedInPackage: true
      }
    ],
    supportServices: [
      {
        id: '10',
        serviceCode: '2.1',
        ServiceActivity: 'Oil Sampling Program',
        cost: 0,
        required: false,
        includeInOffer: true,
        includedInPackage: true
      },
      {
        id: '11',
        serviceCode: '2.3',
        ServiceActivity: 'DEF Sampling Program',
        cost: 0,
        required: false,
        includeInOffer: true,
        includedInPackage: true
      },
      {
        id: '12',
        serviceCode: '3.2',
        ServiceActivity: 'Technical Support via Dealer',
        cost: 0,
        required: false,
        includeInOffer: true,
        includedInPackage: false
      },
      {
        id: '13',
        serviceCode: '4.5',
        ServiceActivity: 'Full Service & Repair',
        cost: 0,
        required: false,
        includeInOffer: true,
        includedInPackage: false
      },
      {
        id: '14',
        serviceCode: '5.3',
        ServiceActivity: 'Full Repair Coverage',
        cost: 2555.52,
        required: false,
        includeInOffer: true,
        includedInPackage: false
      }
    ],
    replacementServices: [
      {
        id: '15',
        serviceCode: '10.1',
        ServiceActivity: 'Engine Replacement at x hrs Scheduled Retirement (Rebuilt)',
        cost: 0,
        required: false,
        includeInOffer: false,
        includedInPackage: false
      },
      {
        id: '16',
        serviceCode: '10.2',
        ServiceActivity: 'Scheduled Retirement (Rebuilt)',
        cost: 0,
        required: false,
        includeInOffer: false,
        includedInPackage: false
      }
    ]
  };

  const sampleComputer = {
    serialNumber: 'ABC123456',
    productDesignation: 'Test Engine Model',
    engineHp: 450,
    tracHp: 400
  };

  function handleTogglePackage(event) {
    console.log('Package toggled:', event.detail);
  }

  function handleSelectService(event) {
    console.log('Service selected:', event.detail);
  }
</script>

<div class="test-page">
  <h1>Enhanced Service Offering Test</h1>
  <p>This page demonstrates the enhanced service offering component that matches the structure from your image.</p>

  <EnhancedServiceOffering
    quotationPackages={sampleQuotationPackages}
    computer={sampleComputer}
    on:togglePackage={handleTogglePackage}
    on:selectService={handleSelectService}
  />
</div>

<style>
  .test-page {
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
  }

  h1 {
    color: #333;
    margin-bottom: 1rem;
  }

  p {
    color: #666;
    margin-bottom: 2rem;
    font-size: 1.1rem;
  }
</style>
