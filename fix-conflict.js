// Fix the route conflict by renaming one of the conflicting directories
const fs = require('fs');
const path = require('path');

// Paths to the conflicting routes
const conflictingPath1 = path.join(__dirname, 'src', 'routes', 'api', 'quote-rows', 'update', '[id]');
const conflictingPath2 = path.join(__dirname, 'src', 'routes', 'api', 'quote-rows', 'update', '[rowId]');

// Check if directories exist
const path1Exists = fs.existsSync(conflictingPath1);
const path2Exists = fs.existsSync(conflictingPath2);

console.log(`Path 1 (${conflictingPath1}) exists: ${path1Exists}`);
console.log(`Path 2 (${conflictingPath2}) exists: ${path2Exists}`);

// Rename one of the directories if it exists
if (path1Exists) {
  // Rename the first conflicting directory
  const newPath = path.join(__dirname, 'src', 'routes', 'api', 'quote-rows', 'update', '_disabled_id');
  fs.renameSync(conflictingPath1, newPath);
  console.log(`Renamed ${conflictingPath1} to ${newPath}`);
} else if (path2Exists) {
  // Rename the second conflicting directory
  const newPath = path.join(__dirname, 'src', 'routes', 'api', 'quote-rows', 'update', '_disabled_rowId');
  fs.renameSync(conflictingPath2, newPath);
  console.log(`Renamed ${conflictingPath2} to ${newPath}`);
} else {
  console.log('Could not find either of the conflicting directories');
}

console.log('Done. Please restart your development server.');
