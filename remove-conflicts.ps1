# PowerShell script to forcefully remove route conflicts

# Stop any running processes that might lock the files
Write-Host "Stopping any running processes..."
Stop-Process -Name "node" -ErrorAction SilentlyContinue

# Define paths to the conflicting routes
$conflictPath1 = "C:\SvelteProjects\Svelte-Mongo3\src\routes\api\quote-rows\update\[id]"
$conflictPath2 = "C:\SvelteProjects\Svelte-Mongo3\src\routes\api\quote-rows\update\[rowId]"

# Check if paths exist
$path1Exists = Test-Path $conflictPath1
$path2Exists = Test-Path $conflictPath2

Write-Host "Conflicting paths:"
Write-Host "Path 1 ($conflictPath1) exists: $path1Exists"
Write-Host "Path 2 ($conflictPath2) exists: $path2Exists"

# Remove both conflicting directories
if ($path1Exists) {
    Write-Host "Removing $conflictPath1..."
    Remove-Item -Path $conflictPath1 -Recurse -Force
    Write-Host "Path 1 removal result: $(!(Test-Path $conflictPath1))"
}

if ($path2Exists) {
    Write-Host "Removing $conflictPath2..."
    Remove-Item -Path $conflictPath2 -Recurse -Force  
    Write-Host "Path 2 removal result: $(!(Test-Path $conflictPath2))"
}

# Ensure the update directory still exists but is empty
$updateDir = "C:\SvelteProjects\Svelte-Mongo3\src\routes\api\quote-rows\update"
if (!(Test-Path $updateDir)) {
    Write-Host "Creating clean update directory..."
    New-Item -Path $updateDir -ItemType Directory
}

Write-Host "Conflict resolution completed."
Write-Host "Please restart your development server with: npm run dev"
