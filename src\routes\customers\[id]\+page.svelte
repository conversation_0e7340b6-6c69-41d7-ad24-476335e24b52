<script>
  import { enhance } from '$app/forms';
  import { goto, invalidate } from '$app/navigation';
  import { CUSTOMER_TYPES } from '$lib/constants';

  /** @typedef {import('$lib/constants').CustomerType} CustomerType */

  /** @typedef {Object} CustomerData
   * @property {string} _id - MongoDB ObjectId as string
   * @property {string} companyName - Company name
   * @property {string} email - Email address
   * @property {string} phone - Phone number
   * @property {string} address - Street address
   * @property {string} city - City
   * @property {string} country - Country
   * @property {CustomerType} type - Customer type
   * @property {string} notes - Additional notes
   */

  /** @typedef {Object} ComputerData
   * @property {string} _id - MongoDB ObjectId as string
   * @property {string} customerId - Customer ID reference as string
   * @property {string} name - Computer name
   * @property {string} model - Computer model
   * @property {string} serialNumber - Computer serial number
   */

  /** @typedef {Object} ServiceContractData
   * @property {string} _id - MongoDB ObjectId as string
   * @property {string} customerId - Customer ID reference as string
   * @property {string} package - Service package code
   * @property {string} service_labour - Service labour cost
   * @property {number} travel_contribution - Travel contribution cost
   * @property {number} vat - VAT percentage
   * @property {string} service - Service code
   * @property {string} part_number - Part number
   * @property {number} scoc - SCOC value
   * @property {number} dealer_net_caf - Dealer net CAF value
   * @property {number} reimbursement - Reimbursement amount
   * @property {number} retail - Retail amount
   * @property {CustomerType} customerType - Customer type
   * @property {string} createdAt - Creation date ISO string
   * @property {string} updatedAt - Last update date ISO string
   */
   
  /** @typedef {Object} RegionData
   * @property {string} _id - MongoDB ObjectId as string
   * @property {string} name - Region name
   * @property {string} description - Region description
   * @property {string} country - Country this region belongs to
   * @property {string} createdAt - Creation date ISO string
   */

  /** @type {import('./$types').PageData} */
  export let data;

  /** @type {CustomerData} */
  const customer = /** @type {CustomerData} */ (data.customer);
  /** @type {readonly CustomerType[]} */
  const customerTypes = CUSTOMER_TYPES;
  /** @type {ComputerData[]} */
  const computers = /** @type {ComputerData[]} */ (data.computers || []);
  /** @type {ServiceContractData[]} */
  const contracts = /** @type {ServiceContractData[]} */ (data.contracts || []);
  /** @type {RegionData[]} */
  const regions = /** @type {RegionData[]} */ (data.regions || []);
  /** @type {string[]} */
  const customerRegionIds = data.customerRegionIds || [];
  
  let editing = false;
  /** @type {CustomerData} */
  let editingCustomer = { ...customer };
  
  // New region form
  let showAddRegionForm = false;
  let regionError = '';
  
  // Region assignment
  let showAssignRegionForm = false;
  let assignmentError = '';
  /** @type {string} */
  let selectedRegionId = '';

  $: computerCount = computers.length;
  $: contractCount = contracts.length;
  $: regionCount = customerRegionIds.length;
  
  // Filter regions that are not yet assigned to this customer
  $: availableRegions = regions.filter(region => !customerRegionIds.includes(region._id));
  
  // Filter regions that are assigned to this customer
  $: assignedRegions = regions.filter(region => customerRegionIds.includes(region._id));

  // Filter available regions by customer's country
  $: filteredAvailableRegions = availableRegions.filter(region => region.country === customer.country);

  // Filter assigned regions by customer's country
  $: filteredAssignedRegions = assignedRegions.filter(region => region.country === customer.country);
  
  /** @param {ServiceContractData} contract */
  function formatDate(contract) {
    try {
      const date = new Date(contract.createdAt);
      return date.toLocaleDateString();
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid date';
    }
  }

  /** @param {number} amount */
  function formatCurrency(amount) {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(amount);
  }
  
  /** @param {Event} event */
  async function handleAddRegion(event) {
    event.preventDefault();
    
    if (!selectedRegionId) {
      regionError = 'Please select a region';
      return;
    }
    
    const formData = new FormData();
    formData.append('regionId', selectedRegionId);
    
    const response = await fetch('?/addRegion', {
      method: 'POST',
      body: formData
    });
    
    const result = await response.json();
    
    if (result.success) {
      selectedRegionId = '';
      regionError = '';
      showAddRegionForm = false;
      await invalidate('regions');
      location.reload();
    } else {
      regionError = result.error;
    }
  }
  
  /** @param {string} regionId */
  async function handleAssignRegion(regionId) {
    const formData = new FormData();
    formData.append('regionId', regionId);
    
    const response = await fetch('?/assignRegion', {
      method: 'POST',
      body: formData
    });
    
    const result = await response.json();
    
    if (result.success) {
      assignmentError = '';
      await invalidate('regions');
      location.reload();
    } else {
      assignmentError = result.error;
    }
  }
  
  /** @param {string} regionId */
  async function handleUnassignRegion(regionId) {
    if (!confirm('Are you sure you want to remove this region from the customer?')) return;
    
    const formData = new FormData();
    formData.append('regionId', regionId);
    
    const response = await fetch('?/unassignRegion', {
      method: 'POST',
      body: formData
    });
    
    const result = await response.json();
    
    if (result.success) {
      await invalidate('regions');
      location.reload();
    } else {
      alert(result.error || 'Failed to remove region');
    }
  }
</script>

<div class="customer-container">
  <div class="customer-header">
    <h1>{customer.companyName}</h1>
    <div class="header-actions">
      <button class="btn-primary" on:click={() => editing = !editing}>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
          <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
        </svg>
        {editing ? 'Cancel' : 'Edit'}
      </button>
      <form 
        method="POST" 
        action="?/delete" 
        use:enhance={() => {
          return async ({ result }) => {
            if (result.type === 'success') {
              goto('/customers');
            } else if (result.type === 'error' && result.error) {
              alert(result.error);
            }
          };
        }}
      >
        <input type="hidden" name="_id" value={customer._id}>
        <button 
          class="btn-danger" 
          type="submit"
          on:click|preventDefault={(e) => {
            const form = /** @type {HTMLFormElement} */ (e.currentTarget?.form);
            if (form && confirm('Are you sure you want to delete this customer? This action cannot be undone.\n\nNote: You cannot delete a customer that has associated computers or contracts.')) {
              form.submit();
            }
          }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="3 6 5 6 21 6"></polyline>
            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
            <line x1="10" y1="11" x2="10" y2="17"></line>
            <line x1="14" y1="11" x2="14" y2="17"></line>
          </svg>
          Delete
        </button>
      </form>
      <a href={`/documentation?customerId=${customer._id}`} class="btn-secondary">View Documentation</a>
    </div>
  </div>

  {#if editing}
    <div class="edit-form">
      <form 
        method="POST" 
        action="?/update" 
        use:enhance={() => {
          return async ({ result }) => {
            if (result.type === 'success') {
              editing = false;
              await invalidate('customers');
            } else if (result.type === 'error' && result.error) {
              alert(result.error);
            }
          };
        }}
      >
        <input type="hidden" name="_id" value={editingCustomer._id}>
        <div class="form-grid">
          <div class="form-group">
            <label for="companyName">Company Name</label>
            <input 
              type="text" 
              id="companyName" 
              name="companyName" 
              required
              bind:value={editingCustomer.companyName}
            >
          </div>

          <div class="form-group">
            <label for="type">Customer Type</label>
            <select 
              id="type" 
              name="type" 
              required
              bind:value={editingCustomer.type}
            >
              {#each customerTypes as type}
                <option value={type}>{type}</option>
              {/each}
            </select>
          </div>

          <div class="form-group">
            <label for="email">Email</label>
            <input 
              type="email" 
              id="email" 
              name="email"
              bind:value={editingCustomer.email}
            >
          </div>

          <div class="form-group">
            <label for="phone">Phone</label>
            <input 
              type="tel" 
              id="phone" 
              name="phone"
              bind:value={editingCustomer.phone}
            >
          </div>

          <div class="form-group">
            <label for="address">Address</label>
            <input 
              type="text" 
              id="address" 
              name="address"
              bind:value={editingCustomer.address}
            >
          </div>

          <div class="form-group">
            <label for="city">City</label>
            <input 
              type="text" 
              id="city" 
              name="city"
              bind:value={editingCustomer.city}
            >
          </div>

          <div class="form-group">
            <label for="country">Country</label>
            <input 
              type="text" 
              id="country" 
              name="country"
              bind:value={editingCustomer.country}
            >
          </div>

          <div class="form-group">
            <label for="notes">Notes</label>
            <textarea 
              id="notes" 
              name="notes"
              rows="4"
              bind:value={editingCustomer.notes}
            ></textarea>
          </div>
        </div>
        <div class="form-actions">
          <button type="button" class="btn-secondary" on:click={() => editing = false}>Cancel</button>
          <button type="submit" class="btn-primary">Save Changes</button>
        </div>
      </form>
    </div>
  {:else}
    <div class="content-grid">
      <div class="info-card">
        <div class="info-header">
          <h2>Customer Information</h2>
        </div>
        <div class="info-content">
          <div class="info-row">
            <div class="info-label">Type</div>
            <div class="info-value">{customer.type}</div>
          </div>
          <div class="info-row">
            <div class="info-label">Email</div>
            <div class="info-value">{customer.email || 'N/A'}</div>
          </div>
          <div class="info-row">
            <div class="info-label">Phone</div>
            <div class="info-value">{customer.phone || 'N/A'}</div>
          </div>
          <div class="info-row">
            <div class="info-label">Address</div>
            <div class="info-value">{customer.address || 'N/A'}</div>
          </div>
          <div class="info-row">
            <div class="info-label">City</div>
            <div class="info-value">{customer.city || 'N/A'}</div>
          </div>
          <div class="info-row">
            <div class="info-label">Country</div>
            <div class="info-value">{customer.country || 'N/A'}</div>
          </div>
          {#if customer.notes}
            <div class="info-notes">
              <div class="info-label">Notes</div>
              <div class="info-value">{customer.notes}</div>
            </div>
          {/if}
        </div>
      </div>

      <div class="related-items">
        <div class="related-card">
          <div class="card-header">
            <h2>Service Contracts ({contractCount})</h2>
            <div class="header-actions">
              <a href={`/service-contracts/new?customerId=${customer._id}`} class="btn-primary btn-sm">Add Contract</a>
            </div>
          </div>
          {#if contracts.length > 0}
            <div class="contracts-list">
              {#each contracts as contract}
                <div class="contract-item">
                  <div class="contract-info">
                    <div class="contract-header">
                      <h3>{contract.package}</h3>
                      <span class="date">{formatDate(contract)}</span>
                    </div>
                    <div class="contract-details">
                      <span>Service: {contract.service}</span>
                      <span>Part Number: {contract.part_number}</span>
                      <span>Labour: {contract.service_labour}</span>
                      <span>Travel: {formatCurrency(contract.travel_contribution)}</span>
                      <span>VAT: {contract.vat}%</span>
                    </div>
                  </div>
                  <div class="contract-actions">
                    <a href={`/service-contracts/${contract._id}`} class="btn-link">View Details</a>
                  </div>
                </div>
              {/each}
            </div>
          {:else}
            <div class="empty-state">
              <p>No service contracts found.</p>
            </div>
          {/if}
        </div>

        <div class="related-card">
          <div class="card-header">
            <h2>Computers ({computerCount})</h2>
            <div class="header-actions">
              <a href={`/customer-computers/new?customerId=${customer._id}`} class="btn-primary btn-sm">Add Computer</a>
            </div>
          </div>
          {#if computers.length > 0}
            <div class="computers-list">
              {#each computers as computer}
                <div class="computer-item">
                  <div class="computer-info">
                    <div class="computer-header">
                      <h3>{computer.name}</h3>
                      <span class="model">{computer.model}</span>
                    </div>
                    <div class="computer-details">
                      <span>Serial: {computer.serialNumber}</span>
                    </div>
                  </div>
                  <div class="computer-actions">
                    <a href="/computer-id/{computer._id}" class="btn-link">View Computer ID</a>
                    <a href="/customer-computers/{computer._id}" class="btn-link ml-3">View Details</a>
                  </div>
                </div>
              {/each}
            </div>
          {:else}
            <div class="empty-state">
              <p>No computers found.</p>
            </div>
          {/if}
        </div>
        
        <!-- Regions Card -->
        <div class="related-card regions-card">
          <div class="card-header">
            <h2>Customer Regions ({regionCount})</h2>
            <div class="header-actions">
              <button class="btn-primary btn-sm" on:click={() => showAddRegionForm = !showAddRegionForm}>
                {showAddRegionForm ? 'Cancel' : 'Add Region'}
              </button>
              <button class="btn-secondary btn-sm" on:click={() => showAssignRegionForm = !showAssignRegionForm}>
                {showAssignRegionForm ? 'Cancel' : 'Assign Region'}
              </button>
            </div>
          </div>
          
          {#if showAddRegionForm}
            <div class="add-region-form">
              <h3>Add Region from {customer.country}</h3>
              
              {#if regionError}
                <div class="error-message">
                  {regionError}
                </div>
              {/if}
              
              {#if filteredAvailableRegions.length === 0}
                <div class="form-note">
                  <p>No available regions to add from {customer.country}. All regions from this country are already assigned to this customer.</p>
                  <p>You can go to the <a href="/regions" class="link">Regions page</a> to create new regions for {customer.country}.</p>
                </div>
              {:else}
                <form on:submit|preventDefault={handleAddRegion}>
                  <div class="form-group">
                    <label for="regionId">Select Region</label>
                    <select 
                      id="regionId" 
                      name="regionId" 
                      bind:value={selectedRegionId}
                      required
                    >
                      <option value="">-- Select a region --</option>
                      {#each filteredAvailableRegions as region}
                        <option value={region._id}>{region.name} {region.description ? `- ${region.description}` : ''}</option>
                      {/each}
                    </select>
                  </div>
                  
                  <div class="form-note">
                    <p>This region from {customer.country} will be assigned to this customer.</p>
                  </div>
                  
                  <div class="form-actions">
                    <button type="submit">Add Region</button>
                  </div>
                </form>
              {/if}
            </div>
          {/if}
          
          {#if showAssignRegionForm}
            <div class="add-region-form">
              <h3>Assign Existing Region</h3>
              
              {#if assignmentError}
                <div class="error-message">
                  {assignmentError}
                </div>
              {/if}
              
              {#if filteredAvailableRegions.length === 0}
                <div class="form-note">
                  <p>No available regions to assign. All regions from {customer.country} are already assigned to this customer.</p>
                </div>
              {:else}
                <div class="form-note">
                  <p>Select a region from {customer.country} to assign to this customer:</p>
                </div>
                
                <div class="regions-grid">
                  {#each filteredAvailableRegions as region}
                    <div class="region-card">
                      <div class="region-content">
                        <h3>{region.name}</h3>
                        <p class="description">{region.description || 'No description provided'}</p>
                      </div>
                      <div class="region-actions">
                        <button 
                          class="assign-button" 
                          on:click={() => handleAssignRegion(region._id)}
                        >
                          Assign to Customer
                        </button>
                      </div>
                    </div>
                  {/each}
                </div>
              {/if}
            </div>
          {/if}
          
          {#if filteredAssignedRegions.length > 0}
            <div class="regions-grid">
              {#each filteredAssignedRegions as region (region._id)}
                <div class="region-card">
                  <div class="region-content">
                    <h3>{region.name}</h3>
                    <p class="description">{region.description || 'No description provided'}</p>
                  </div>
                  <div class="region-actions">
                    <button 
                      class="unassign-button" 
                      on:click={() => handleUnassignRegion(region._id)}
                    >
                      Remove
                    </button>
                  </div>
                </div>
              {/each}
            </div>
          {:else}
            <div class="empty-regions">
              <p>No regions assigned to this customer yet.</p>
            </div>
          {/if}
          
          <!-- New section to show all regions in the customer's country -->
          <div class="regions-section">
            <h3>All Regions in {customer.country} ({regions.filter(r => r.country === customer.country).length})</h3>
            
            {#if regions.filter(r => r.country === customer.country).length > 0}
              <div class="regions-grid">
                {#each regions.filter(r => r.country === customer.country) as region (region._id)}
                  <div class="region-card {customerRegionIds.includes(region._id) ? 'assigned' : ''}">
                    <div class="region-content">
                      <h3>{region.name}</h3>
                      <p class="description">{region.description || 'No description provided'}</p>
                      {#if customerRegionIds.includes(region._id)}
                        <span class="assigned-badge">Assigned</span>
                      {/if}
                    </div>
                  </div>
                {/each}
              </div>
            {:else}
              <div class="empty-regions">
                <p>No regions available in {customer.country}.</p>
                <p>You can create regions for this country on the <a href="/regions" class="link">Regions page</a>.</p>
              </div>
            {/if}
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  /* Main Container */
  .customer-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 1.5rem;
    background-color: #f0f7ff;
  }

  /* Header */
  .customer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #0f2b5a, #1e3a8a);
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    color: white;
  }

  h1 {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .header-actions {
    display: flex;
    gap: 0.75rem;
  }

  /* Content Grid */
  .content-grid {
    display: grid;
    grid-template-columns: minmax(300px, 1fr) minmax(300px, 2fr);
    gap: 1.5rem;
  }

  /* Info Card */
  .info-card {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: fit-content;
  }
  
  .info-header {
    padding: 1rem 1.5rem;
    background: linear-gradient(to right, #1e3a8a, #1e40af);
    border-bottom: 1px solid #e5e7eb;
  }
  
  .info-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: white;
  }
  
  .info-content {
    padding: 1.5rem;
  }
  
  .info-row {
    display: flex;
    margin-bottom: 0.75rem;
  }
  
  .info-label {
    width: 100px;
    font-weight: 500;
    color: #1e3a8a;
  }
  
  .info-value {
    flex: 1;
    color: #1f2937;
  }
  
  .info-notes {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
  }
  
  .info-notes .info-label {
    margin-bottom: 0.5rem;
  }
  
  .info-notes .info-value {
    white-space: pre-line;
  }

  /* Related Items */
  .related-items {
    display: grid;
    gap: 2rem;
  }

  .related-card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }
  
  .regions-card {
    border-top: 4px solid #1e3a8a;
  }

  .card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(to right, #1e3a8a, #1e40af);
  }

  .card-header h2 {
    margin: 0;
    font-size: 1.25rem;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  /* Contract and Computer Lists */
  .contracts-list,
  .computers-list {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .contract-item,
  .computer-item {
    padding: 1rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border: 1px solid #e5e7eb;
  }

  .contract-info,
  .computer-info {
    flex: 1;
  }

  .contract-header,
  .computer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .contract-header h3,
  .computer-header h3 {
    margin: 0;
    font-size: 1rem;
    color: #1e3a8a;
    font-weight: 600;
  }

  .date,
  .model {
    font-size: 0.875rem;
    color: #6b7280;
  }

  .contract-details,
  .computer-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    font-size: 0.875rem;
    color: #6b7280;
  }

  .contract-actions,
  .computer-actions {
    margin-left: 1rem;
  }

  /* Buttons and Links */
  .btn-primary, .btn-secondary, .btn-danger {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .btn-primary {
    background: linear-gradient(to right, #1e3a8a, #1e40af);
    color: white;
    border: none;
  }
  
  .btn-primary:hover {
    background: linear-gradient(to right, #1e40af, #0f2b5a);
    transform: translateY(-1px);
  }
  
  .btn-secondary {
    background-color: white;
    color: #1e3a8a;
    border: 1px solid #1e3a8a;
    text-decoration: none;
  }
  
  .btn-secondary:hover {
    background-color: #eff6ff;
  }
  
  .btn-danger {
    background-color: #ef4444;
    color: white;
    border: none;
  }
  
  .btn-danger:hover {
    background-color: #dc2626;
  }

  .btn-link {
    color: #1e3a8a;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
  }

  .btn-link:hover {
    text-decoration: underline;
  }
  
  .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
  
  /* Edit Form */
  .edit-form {
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-top: 4px solid #1e3a8a;
  }
  
  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .form-group {
    margin-bottom: 1rem;
  }
  
  .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #1e3a8a;
  }
  
  .form-group input,
  .form-group select,
  .form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
  }
  
  .form-group input:focus,
  .form-group select:focus,
  .form-group textarea:focus {
    outline: none;
    border-color: #1e40af;
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.2);
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    margin-top: 1.5rem;
  }
  
  /* Add Region Form */
  .add-region-form {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .form-note {
    margin-top: 1rem;
    padding: 0.75rem;
    background-color: #eff6ff;
    border-radius: 0.375rem;
    color: #1e3a8a;
    font-size: 0.875rem;
  }
  
  .error-message {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: #fee2e2;
    border: 1px solid #fecaca;
    border-radius: 0.375rem;
    color: #b91c1c;
    font-size: 0.875rem;
  }
  
  /* Regions Grid */
  .regions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    padding: 1.5rem;
  }
  
  .region-card {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    overflow: hidden;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-top: 4px solid;
    border-image: linear-gradient(to right, #1e3a8a, #3b82f6) 1;
    display: flex;
    flex-direction: column;
  }
  
  .region-card.assigned {
    border-color: #bfdbfe;
    background-color: #eff6ff;
  }
  
  .region-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  
  .region-content {
    padding: 1rem;
    flex: 1;
  }
  
  .region-content h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #1e3a8a;
  }
  
  .description {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
  }
  
  .region-actions {
    margin-top: 1rem;
    display: flex;
    justify-content: flex-end;
  }
  
  .assign-button, .unassign-button {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    cursor: pointer;
  }
  
  .assign-button {
    background-color: #eff6ff;
    color: #2563eb;
    border: 1px solid #bfdbfe;
  }
  
  .assign-button:hover {
    background-color: #dbeafe;
  }
  
  .unassign-button {
    background-color: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
  }
  
  .unassign-button:hover {
    background-color: #fee2e2;
  }
  
  /* Region styles */
  .regions-section {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
  }
  
  .regions-section h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 1rem;
  }
  
  .regions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }
  
  .region-card {
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: all 0.2s;
  }
  
  .region-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  
  .region-card.assigned {
    border-color: #bfdbfe;
    background-color: #eff6ff;
  }
  
  .region-content h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #1e3a8a;
    margin: 0 0 0.5rem 0;
  }
  
  .region-content .description {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
  }
  
  .region-actions {
    margin-top: 1rem;
    display: flex;
    justify-content: flex-end;
  }
  
  .assign-button, .unassign-button {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    cursor: pointer;
  }
  
  .assign-button {
    background-color: #eff6ff;
    color: #2563eb;
    border: 1px solid #bfdbfe;
  }
  
  .assign-button:hover {
    background-color: #dbeafe;
  }
  
  .unassign-button {
    background-color: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
  }
  
  .unassign-button:hover {
    background-color: #fee2e2;
  }
  
  .empty-regions {
    padding: 2rem;
    text-align: center;
    color: #6b7280;
    background-color: #f9fafb;
    border-radius: 0.375rem;
    border: 1px dashed #e5e7eb;
  }
  
  .assigned-badge {
    display: inline-block;
    background-color: #2563eb;
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    margin-top: 0.5rem;
  }
  
  .link {
    color: #2563eb;
    text-decoration: underline;
  }
  
  /* Responsive Adjustments */
  @media (max-width: 1024px) {
    .content-grid {
      grid-template-columns: 1fr;
    }
    
    .regions-grid {
      grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
  }
  
  @media (max-width: 768px) {
    .form-grid {
      grid-template-columns: 1fr;
    }
    
    .customer-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
    
    .header-actions {
      width: 100%;
      justify-content: flex-start;
    }
  }
</style>
