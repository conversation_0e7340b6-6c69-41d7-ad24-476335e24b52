import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function listProductDesignations() {
    const client = new MongoClient(uri);
    
    try {
        await client.connect();
        console.log('Connected to MongoDB');
        
        const db = client.db(dbName);
        
        // List of collections we know have ProductDesignation
        const collections = [
            'ProductDesignation',
            'ProductValidityGroupPartNumber',
            'PartNumbersServiceCodeAction'
        ];

        console.log('\nCollections with ProductDesignation content:\n');
        
        for (const collectionName of collections) {
            const collection = db.collection(collectionName);
            const count = await collection.countDocuments({ ProductDesignation: { $exists: true } });
            
            console.log(`\n=== ${collectionName} ===`);
            console.log(`Documents with ProductDesignation: ${count}`);
            
            if (count > 0) {
                // Show a sample document
                const sample = await collection.findOne({ ProductDesignation: { $exists: true } });
                console.log('Sample document:');
                console.log(JSON.stringify(sample, null, 2));
            }
            console.log('-------------------');
        }

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await client.close();
        console.log('\nConnection closed');
    }
}

listProductDesignations().catch(console.error);
