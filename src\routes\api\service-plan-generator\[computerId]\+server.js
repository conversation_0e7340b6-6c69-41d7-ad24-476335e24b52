import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

/** @type {import('./$types').RequestHandler} */
export async function GET({ params, url }) {
  const { computerId } = params;
  const productDesignation = url.searchParams.get('productDesignation') || 'TAD1640-42GE-B';

  if (!computerId || !ObjectId.isValid(computerId)) {
    return json({ error: 'Valid Computer ID is required' }, { status: 400 });
  }

  const client = new MongoClient(uri);

  try {
    await client.connect();
    const db = client.db(dbName);

    console.log(`🚀 Generating service plan for Computer ID: ${computerId}`);
    console.log(`📋 Product Designation: ${productDesignation}`);

    // Step 1: Get computer details
    const computer = await db.collection('CustomerComputers')
      .findOne({ _id: new ObjectId(computerId) });

    if (!computer) {
      return json({ error: 'Computer not found' }, { status: 404 });
    }

    // Step 2: Get workload data sorted chronologically
    const workloadData = await db.collection('Workload')
      .find({ computerId: new ObjectId(computerId) })
      .sort({ year: 1, month: 1 })
      .toArray();

    console.log(`📊 Found ${workloadData.length} workload entries`);

    // Step 3: Get service code and action type items for the product designation
    const serviceCodeActionTypes = await db.collection('ServiceCodeAndActionType')
      .find({ ProductValidityGroup: productDesignation })
      .sort({ InternalNoOfHours: 1 })
      .toArray();

    console.log(`🔧 Found ${serviceCodeActionTypes.length} service code action type items`);

    // Step 4: Calculate accumulated hours month by month
    let accumulatedHours = 0;
    const monthlyAccHours = [];

    // Create a comprehensive timeline of all months from workload data
    if (workloadData.length > 0) {
      const startYear = Math.min(...workloadData.map(w => w.year));
      const endYear = Math.max(...workloadData.map(w => w.year));

      for (let year = startYear; year <= endYear; year++) {
        for (let month = 1; month <= 12; month++) {
          const workloadEntry = workloadData.find(w => w.year === year && w.month === month);
          const monthHours = workloadEntry ? (workloadEntry.hours || 0) : 0;

          accumulatedHours += monthHours;

          monthlyAccHours.push({
            year,
            month,
            monthHours,
            accumulatedHours,
            date: new Date(year, month - 1, 15), // Mid-month date
            monthName: new Date(year, month - 1).toLocaleString('default', { month: 'long' })
          });
        }
      }
    }

    console.log(`📈 Calculated accumulated hours for ${monthlyAccHours.length} months`);
    console.log(`⏱️ Total accumulated hours: ${accumulatedHours}`);

    // Step 5: Generate service schedule based on accumulated hours
    const serviceSchedule = [];

    for (const serviceItem of serviceCodeActionTypes) {
      const serviceKey = `${serviceItem.ServiceCode}-${serviceItem.ActionType}`;
      const frequency = serviceItem.InternalNoOfHours || 1000; // Use InternalNoOfHours as frequency

      // Start from the first service interval
      let nextServiceHours = frequency;

      while (nextServiceHours <= accumulatedHours) {
        // Find the month closest to this accumulated hour target
        const targetMonth = findClosestMonth(monthlyAccHours, nextServiceHours);

        if (targetMonth) {
          serviceSchedule.push({
            serviceCode: serviceItem.ServiceCode,
            actionType: serviceItem.ActionType,
            activityPurpose: serviceItem.ActivityPurpose,
            serviceActivityLabel: serviceItem.ServiceActivityLabel,
            partNumber: serviceItem.PartNumber,
            targetHours: nextServiceHours,
            actualAccHours: targetMonth.accumulatedHours,
            scheduledYear: targetMonth.year,
            scheduledMonth: targetMonth.month,
            scheduledMonthName: targetMonth.monthName,
            scheduledDate: new Date(targetMonth.year, targetMonth.month - 1, 15),
            frequency: frequency,
            sequenceNumber: serviceItem.SequenceNumber,
            unitOfMeasure: serviceItem.UnitOfMeasure,
            quantity: serviceItem.Quantity,
            exactMatch: Math.abs(targetMonth.accumulatedHours - nextServiceHours) <= 50,
            productValidityGroup: serviceItem.ProductValidityGroup
          });
        }

        // Calculate next service occurrence
        nextServiceHours += frequency;
      }
    }

    // Sort service schedule by date
    serviceSchedule.sort((a, b) => a.scheduledDate.getTime() - b.scheduledDate.getTime());

    console.log(`📅 Generated ${serviceSchedule.length} scheduled services`);

    // Step 6: Group services by month for easier planning
    const servicesByMonth = {};
    serviceSchedule.forEach(service => {
      const monthKey = `${service.scheduledYear}-${service.scheduledMonth.toString().padStart(2, '0')}`;
      if (!servicesByMonth[monthKey]) {
        servicesByMonth[monthKey] = {
          year: service.scheduledYear,
          month: service.scheduledMonth,
          monthName: service.scheduledMonthName,
          accumulatedHours: service.actualAccHours,
          services: []
        };
      }
      servicesByMonth[monthKey].services.push(service);
    });

    // Step 7: Calculate service statistics
    const serviceStats = {
      totalServices: serviceSchedule.length,
      serviceTypes: [...new Set(serviceSchedule.map(s => s.serviceCode))].length,
      timeSpan: {
        startDate: monthlyAccHours[0]?.date,
        endDate: monthlyAccHours[monthlyAccHours.length - 1]?.date,
        totalMonths: monthlyAccHours.length
      },
      utilizationStats: {
        totalHours: accumulatedHours,
        averageMonthlyHours: accumulatedHours / (monthlyAccHours.length || 1),
        peakMonth: monthlyAccHours.reduce((max, curr) =>
          curr.monthHours > max.monthHours ? curr : max, { monthHours: 0 })
      }
    };

    const servicePlanResult = {
      success: true,
      computer: {
        _id: computer._id.toString(),
        name: computer.name,
        productDesignation: productDesignation,
        serialNumber: computer.serialNumber,
        model: computer.model
      },
      workloadSummary: {
        totalEntries: workloadData.length,
        totalAccumulatedHours: accumulatedHours,
        monthlyData: monthlyAccHours
      },
      servicePlan: {
        totalServiceItems: serviceCodeActionTypes.length,
        scheduledServices: serviceSchedule,
        servicesByMonth: Object.values(servicesByMonth),
        statistics: serviceStats,
        dataSource: 'ServiceCodeAndActionType'
      },
      generatedAt: new Date().toISOString()
    };

    // Save the service plan to ComputerServicePlan collection
    try {
      const servicePlanDocument = {
        computerId: new ObjectId(computerId),
        productDesignation: productDesignation,
        computer: servicePlanResult.computer,
        workloadSummary: servicePlanResult.workloadSummary,
        servicePlan: servicePlanResult.servicePlan,
        statistics: servicePlanResult.servicePlan.statistics,
        totalServices: serviceSchedule.length,
        dataSource: 'ServiceCodeAndActionType',
        generatedAt: new Date(servicePlanResult.generatedAt),
        savedAt: new Date(),
        version: '1.0',
        disabledServices: [] // Initialize empty disabled services array
      };

      // Check if a service plan already exists
      const existingPlan = await db.collection('ComputerServicePlan')
        .findOne({
          computerId: new ObjectId(computerId),
          productDesignation: productDesignation
        });

      if (existingPlan) {
        // Update existing plan, preserving disabled services
        await db.collection('ComputerServicePlan')
          .updateOne(
            { _id: existingPlan._id },
            {
              $set: {
                ...servicePlanDocument,
                disabledServices: existingPlan.disabledServices || [], // Preserve existing disabled services
                updatedAt: new Date()
              },
              $push: {
                'previousVersions': {
                  generatedAt: existingPlan.generatedAt,
                  savedAt: existingPlan.savedAt,
                  totalServices: existingPlan.totalServices
                }
              }
            }
          );
        console.log(`💾 Updated service plan in ComputerServicePlan collection`);
      } else {
        // Create new plan
        await db.collection('ComputerServicePlan').insertOne(servicePlanDocument);
        console.log(`💾 Saved new service plan to ComputerServicePlan collection`);
      }

      // Add saved status to response
      servicePlanResult.saved = true;
      servicePlanResult.savedAt = new Date().toISOString();

    } catch (saveError) {
      console.error('⚠️ Error saving service plan to database:', saveError);
      // Don't fail the main request if saving fails
      servicePlanResult.saved = false;
      servicePlanResult.saveError = saveError.message;
    }

    return json(servicePlanResult);

  } catch (err) {
    console.error('❌ Error generating service plan:', err);
    return json({
      error: 'Failed to generate service plan',
      details: err.message
    }, { status: 500 });
  } finally {
    await client.close();
  }
}

// Helper function to find the month closest to target accumulated hours
function findClosestMonth(monthlyData, targetHours) {
  if (!monthlyData || monthlyData.length === 0) return null;

  let closestMonth = monthlyData[0];
  let smallestDiff = Math.abs(monthlyData[0].accumulatedHours - targetHours);

  for (const month of monthlyData) {
    const diff = Math.abs(month.accumulatedHours - targetHours);
    if (diff < smallestDiff) {
      smallestDiff = diff;
      closestMonth = month;
    }

    // If we've passed the target, return the previous month if it was closer
    if (month.accumulatedHours > targetHours) {
      break;
    }
  }

  return closestMonth;
}
