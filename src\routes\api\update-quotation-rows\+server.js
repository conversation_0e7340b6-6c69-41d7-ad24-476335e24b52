import { json } from '@sveltejs/kit';
import { MongoClient } from 'mongodb';

// Create a route that will update the QuotationRows collection
export async function GET() {
  try {
    // Connect to MongoDB
    const uri = 'mongodb://localhost:27017';
    const client = new MongoClient(uri);
    await client.connect();
    
    const db = client.db('ServiceContracts');
    const collection = db.collection('QuotationRows');
    
    // Update documents with 'activity' field (lowercase)
    const activityResult = await collection.updateMany(
      { activity: { $exists: true } },
      [
        { 
          $set: { 
            ServiceActivity: "$activity",
          }
        },
        {
          $unset: ["activity"]
        }
      ]
    );
    
    // Update documents with 'Activity' field (capitalized)
    const ActivityResult = await collection.updateMany(
      { Activity: { $exists: true } },
      [
        { 
          $set: { 
            ServiceActivity: "$Activity",
          }
        },
        {
          $unset: ["Activity"]
        }
      ]
    );
    
    // Update documents with 'serviceActivity' field (camelCase)
    const serviceActivityResult = await collection.updateMany(
      { serviceActivity: { $exists: true } },
      [
        { 
          $set: { 
            ServiceActivity: "$serviceActivity",
          }
        },
        {
          $unset: ["serviceActivity"]
        }
      ]
    );
    
    // Close the MongoDB connection
    await client.close();
    
    // Return the results
    return json({
      success: true,
      message: 'Field update completed successfully in QuotationRows collection',
      activityUpdated: activityResult.modifiedCount,
      ActivityUpdated: ActivityResult.modifiedCount,
      serviceActivityUpdated: serviceActivityResult.modifiedCount
    });
  } catch (error) {
    console.error('Error updating fields:', error);
    
    return json({
      success: false,
      message: 'Error updating fields',
      error: error.message
    }, { status: 500 });
  }
}
