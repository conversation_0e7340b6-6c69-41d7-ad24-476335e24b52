<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import BulkOperationsForm from '../BulkOperationsForm.svelte';
  import ActivityCell from '../ActivityCell.svelte';
  import BaseGrid from './BaseGrid.svelte';
  
  // Props
  export let months: string[] = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  export let shortMonths: string[] = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  export let years: number[] = [];
  export let workloadData: any[] = [];
  export let faultCodeMonths: Set<string> = new Set();
  export let isLoading: boolean = false;
  
  // State
  const dispatch = createEventDispatcher<{
    bulkAdd: {
      startYear: number;
      startMonth: number;
      endYear: number;
      endMonth: number;
      hours: number;
    };
    cellClick: {
      year: number;
      monthIdx: number;
    };
  }>();
  
  // Derived data
  $: monthsMatrix = shortMonths;
  $: yearsMatrix = [...new Set(years)].sort();
  
  // Methods
  function handleBulkSubmit(event: CustomEvent) {
    dispatch('bulkAdd', event.detail);
  }
  
  function handleCellClick(year: number, monthIdx: number) {
    dispatch('cellClick', { year, monthIdx });
  }

  // Helper functions 
  export function getMonthData(data: any[], year: number, monthIdx: number) {
    const item = data.find(item => 
      item.year === year && 
      item.month === (monthIdx + 1)
    );
    
    return {
      hours: item?.hours || 0,
      activity: item?.activity || '',
      hasActivity: !!item?.activity,
      hasFixed: !!item?.hasFixed,
      isSelected: false
    };
  }
</script>

<BaseGrid>
  <div class="planning-grid">
    <section class="bulk-operations">
      <h2>Bulk Operations</h2>
      <BulkOperationsForm 
        {months}
        {isLoading}
        on:submit={handleBulkSubmit}
      />
    </section>
    
    <section class="matrix-section">
      <header class="matrix-header">
        <h2>Service Activity Matrix</h2>
      </header>
      
      <div class="matrix-table-wrapper">
        <table class="matrix-table">
          <thead>
            <tr>
              <th class="year-header">Year</th>
              {#each monthsMatrix as month}
                <th>{month}</th>
              {/each}
            </tr>
          </thead>
          <tbody>
            {#each yearsMatrix as year}
              <tr>
                <td class="year-cell">{year}</td>
                {#each monthsMatrix as month, monthIdx}
                  {@const monthData = getMonthData(workloadData, year, monthIdx)}
                  <td 
                    class:has-activity={monthData.hasActivity}
                    class:is-fixed={monthData.hasFixed}
                    class:is-selected={monthData.isSelected}
                    class:faultcode-cell={faultCodeMonths.has(year + '-' + String(monthIdx + 1).padStart(2, '0'))}
                    on:click={() => handleCellClick(year, monthIdx)}
                    on:keydown={e => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        handleCellClick(year, monthIdx);
                      }
                    }}
                  >
                    <div class="cell-content">
                      <span class="hours">{monthData.hours}</span>
                      {#if monthData.hasActivity}
                        <span class="activity-tag">{monthData.activity.substring(0, 8)}</span>
                      {/if}
                    </div>
                  </td>
                {/each}
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    </section>
  </div>
</BaseGrid>

<style>
  .planning-grid {
    display: grid;
    grid-template-rows: auto 1fr;
    gap: 1.5rem;
    height: 100%;
    width: 100%;
    padding: 1rem;
    overflow: hidden;
  }
  
  h2 {
    color: #60a5fa;
    margin: 0 0 1rem 0;
    font-size: 1.25rem;
  }
  
  .bulk-operations {
    background: #1e293b;
    padding: 1.5rem;
    border-radius: 8px;
  }
  
  .matrix-section {
    display: grid;
    grid-template-rows: auto 1fr;
    overflow: hidden;
  }
  
  .matrix-header {
    margin-bottom: 0.5rem;
  }
  
  .matrix-table-wrapper {
    overflow: auto;
    width: 100%;
    background: #1e293b;
    border-radius: 8px;
    height: 100%;
  }
  
  .matrix-table {
    width: 100%;
    height: 100%;
    border-collapse: collapse;
    table-layout: fixed;
  }
  
  .matrix-table th,
  .matrix-table td {
    padding: 0.75rem;
    text-align: center;
    border: 1px solid #334155;
    min-width: 80px;
  }
  
  .matrix-table th {
    background: #0f172a;
    color: #94a3b8;
    font-weight: 500;
    position: sticky;
    top: 0;
    z-index: 2;
  }
  
  .matrix-table td {
    cursor: pointer;
    transition: background-color 0.2s;
    height: 60px;
  }
  
  .year-header {
    background: #0f172a;
    left: 0;
    z-index: 3;
  }
  
  .year-cell {
    background: #0f172a;
    color: #94a3b8;
    font-weight: 500;
    position: sticky;
    left: 0;
    z-index: 1;
  }
  
  .matrix-table td:not(.year-cell):hover {
    background: #334155;
  }
  
  .cell-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  
  .hours {
    font-weight: 600;
    font-size: 1.1rem;
  }
  
  .activity-tag {
    font-size: 0.75rem;
    color: #94a3b8;
    margin-top: 0.25rem;
  }
  
  .has-activity {
    background-color: rgba(59, 130, 246, 0.1);
  }
  
  .is-fixed {
    background-color: rgba(59, 130, 246, 0.2);
  }
  
  .is-selected {
    outline: 2px solid #60a5fa;
    z-index: 1;
    position: relative;
  }
  
  .faultcode-cell {
    position: relative;
  }
  
  .faultcode-cell::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 10px 10px 0;
    border-color: transparent #ef4444 transparent transparent;
  }
  
  /* Scrollbar styling */
  .matrix-table-wrapper::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  .matrix-table-wrapper::-webkit-scrollbar-track {
    background: rgba(15, 23, 42, 0.6);
  }
  
  .matrix-table-wrapper::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.5);
    border-radius: 4px;
  }
  
  .matrix-table-wrapper::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.7);
  }
  
  @media (max-height: 800px) {
    .planning-grid {
      grid-template-rows: minmax(auto, 30%) 1fr;
    }
    
    .bulk-operations {
      padding: 1rem;
    }
    
    h2 {
      margin-bottom: 0.5rem;
    }
  }
</style>
