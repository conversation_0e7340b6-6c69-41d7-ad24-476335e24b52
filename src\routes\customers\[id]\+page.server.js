import { error } from '@sveltejs/kit';
import { ObjectId } from 'mongodb';
import { getCollection, toClientDocument } from '$lib/db/mongo';
import { CUSTOMER_TYPES } from '$lib/constants';

/** @typedef {import('$lib/constants').CustomerType} CustomerType */

/** @typedef {Object} Customer
 * @property {ObjectId} _id - MongoDB ObjectId
 * @property {string} companyName - Company name
 * @property {string} email - Email address
 * @property {string} phone - Phone number
 * @property {string} address - Street address
 * @property {string} city - City
 * @property {string} country - Country
 * @property {CustomerType} type - Customer type
 * @property {string} notes - Additional notes
 */

/** @typedef {Object} Computer
 * @property {ObjectId} _id - MongoDB ObjectId
 * @property {ObjectId} customerId - Customer ID reference
 * @property {string} name - Computer name
 * @property {string} model - Computer model
 * @property {string} serialNumber - Computer serial number
 */

/** @typedef {Object} ServiceContract
 * @property {ObjectId} _id - MongoDB ObjectId
 * @property {ObjectId} customerId - Customer ID reference
 * @property {string} package - Service package code
 * @property {string} service_labour - Service labour cost
 * @property {number} travel_contribution - Travel contribution cost
 * @property {number} vat - VAT percentage
 * @property {string} service - Service code
 * @property {string} part_number - Part number
 * @property {number} scoc - SCOC value
 * @property {number} dealer_net_caf - Dealer net CAF value
 * @property {number} reimbursement - Reimbursement amount
 * @property {number} retail - Retail amount
 * @property {CustomerType} customerType - Customer type
 * @property {Date} createdAt - Creation date
 * @property {Date} updatedAt - Last update date
 */

/** @typedef {Object} Region
 * @property {ObjectId} _id - MongoDB ObjectId
 * @property {string} name - Region name
 * @property {string} description - Region description
 * @property {string} country - Country this region belongs to
 * @property {Date} createdAt - Creation date
 */

/** @typedef {Object} CustomerRegion
 * @property {ObjectId} _id - MongoDB ObjectId
 * @property {ObjectId} customerId - Customer ID reference
 * @property {ObjectId} regionId - Region ID reference
 * @property {Date} createdAt - Creation date
 */

/** @typedef {Object} CustomerResponse
 * @property {string} _id - MongoDB ObjectId as string
 * @property {string} companyName - Company name
 * @property {string} email - Email address
 * @property {string} phone - Phone number
 * @property {string} address - Street address
 * @property {string} city - City
 * @property {string} country - Country
 * @property {CustomerType} type - Customer type
 * @property {string} notes - Additional notes
 */

/** @typedef {Object} ComputerResponse
 * @property {string} _id - MongoDB ObjectId as string
 * @property {string} customerId - Customer ID reference as string
 * @property {string} name - Computer name
 * @property {string} model - Computer model
 * @property {string} serialNumber - Computer serial number
 */

/** @typedef {Object} ServiceContractResponse
 * @property {string} _id - MongoDB ObjectId as string
 * @property {string} customerId - Customer ID reference as string
 * @property {string} package - Service package code
 * @property {string} service_labour - Service labour cost
 * @property {number} travel_contribution - Travel contribution cost
 * @property {number} vat - VAT percentage
 * @property {string} service - Service code
 * @property {string} part_number - Part number
 * @property {number} scoc - SCOC value
 * @property {number} dealer_net_caf - Dealer net CAF value
 * @property {number} reimbursement - Reimbursement amount
 * @property {number} retail - Retail amount
 * @property {CustomerType} customerType - Customer type
 * @property {string} createdAt - Creation date ISO string
 * @property {string} updatedAt - Last update date ISO string
 */

/** @typedef {Object} RegionResponse
 * @property {string} _id - MongoDB ObjectId as string
 * @property {string} name - Region name
 * @property {string} description - Region description
 * @property {string} country - Country this region belongs to
 * @property {string} createdAt - Creation date ISO string
 */

/** @typedef {Object} PageData
 * @property {CustomerResponse} customer - Customer data
 * @property {ComputerResponse[]} computers - Computer data
 * @property {ServiceContractResponse[]} contracts - Service contract data
 * @property {RegionResponse[]} regions - Region data
 * @property {string[]} customerRegionIds - Customer's assigned region IDs
 * @property {CustomerType[]} customerTypes - Available customer types
 */

/** @type {import('./$types').PageServerLoad} */
export async function load({ params }) {
  try {
    const customerId = params.id;
    if (!ObjectId.isValid(customerId)) {
      throw error(400, 'Invalid customer ID');
    }

    const customersCollection = await getCollection('Customers');
    const computersCollection = await getCollection('CustomerComputers');
    const contractsCollection = await getCollection('ServiceContracts');
    const regionsCollection = await getCollection('Regions');
    const customerRegionsCollection = await getCollection('CustomerRegions');

    // Get customer details using native ObjectId
    const customerDoc = await customersCollection.findOne({ _id: new ObjectId(customerId) });
    if (!customerDoc) {
      throw error(404, 'Customer not found');
    }
    
    /** @type {Customer} */
    const customer = {
      _id: customerDoc._id,
      companyName: customerDoc.companyName,
      email: customerDoc.email,
      phone: customerDoc.phone,
      address: customerDoc.address,
      city: customerDoc.city,
      country: customerDoc.country,
      type: customerDoc.type,
      notes: customerDoc.notes
    };

    // Get computers for this customer using native ObjectId
    const computerDocs = await computersCollection
      .find({ customerId: new ObjectId(customerId) })
      .toArray();
      
    /** @type {Computer[]} */
    const computers = computerDocs.map(doc => ({
      _id: doc._id,
      customerId: doc.customerId,
      name: doc.name,
      model: doc.model,
      serialNumber: doc.serialNumber
    }));

    // Get service contracts for this customer using native ObjectId
    const contractDocs = await contractsCollection
      .find({ customerId: new ObjectId(customerId) })
      .sort({ createdAt: -1 }) // Sort by creation date descending (newest first)
      .toArray();
      
    /** @type {ServiceContract[]} */
    const contracts = contractDocs.map(doc => ({
      _id: doc._id,
      customerId: doc.customerId,
      package: doc.package,
      service_labour: doc.service_labour,
      travel_contribution: doc.travel_contribution,
      vat: doc.vat,
      service: doc.service,
      part_number: doc.part_number,
      scoc: doc.scoc,
      dealer_net_caf: doc.dealer_net_caf,
      reimbursement: doc.reimbursement,
      retail: doc.retail,
      customerType: doc.customerType,
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt
    }));

    // Get all regions for this customer's country ONLY
    const regionDocs = await regionsCollection
      .find({ country: customer.country })
      .toArray();
      
    /** @type {Region[]} */
    const regions = regionDocs.map(doc => ({
      _id: doc._id,
      name: doc.name,
      description: doc.description,
      country: doc.country,
      createdAt: doc.createdAt
    }));
    
    // Get customer's assigned regions
    const customerRegionDocs = await customerRegionsCollection
      .find({ customerId: new ObjectId(customerId) })
      .toArray();
      
    // Extract just the regionIds from customer regions
    const customerRegionIds = customerRegionDocs.map(doc => doc.regionId.toString());

    return {
      customer: toClientDocument(customer),
      computers: computers.map(computer => toClientDocument(computer)),
      contracts: contracts.map(contract => toClientDocument(contract)),
      regions: regions.map(region => toClientDocument(region)),
      customerRegionIds,
      customerTypes: CUSTOMER_TYPES
    };
  } catch (err) {
    console.error('Error loading customer data:', err);
    throw error(500, 'Failed to load customer data');
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  update: async ({ request, params }) => {
    try {
      const formData = await request.formData();
      const customerId = params.id;
      
      if (!ObjectId.isValid(customerId)) {
        return { success: false, error: 'Invalid customer ID' };
      }

      const customersCollection = await getCollection('Customers');
      const customer = await customersCollection.findOne({ _id: new ObjectId(customerId) });
      
      if (!customer) {
        return { success: false, error: 'Customer not found' };
      }

      const updatedCustomer = {
        companyName: formData.get('companyName')?.toString() || '',
        email: formData.get('email')?.toString() || '',
        phone: formData.get('phone')?.toString() || '',
        address: formData.get('address')?.toString() || '',
        city: formData.get('city')?.toString() || '',
        country: formData.get('country')?.toString() || '',
        type: formData.get('type')?.toString() || 'Standard',
        notes: formData.get('notes')?.toString() || ''
      };

      await customersCollection.updateOne(
        { _id: new ObjectId(customerId) },
        { $set: updatedCustomer }
      );

      return { success: true };
    } catch (error) {
      console.error('Error updating customer:', error instanceof Error ? error.message : 'Unknown error');
      return { success: false, error: error instanceof Error ? error.message : 'Failed to update customer' };
    }
  },

  delete: async ({ params }) => {
    try {
      const customerId = params.id;
      
      if (!ObjectId.isValid(customerId)) {
        return { success: false, error: 'Invalid customer ID' };
      }

      const customersCollection = await getCollection('Customers');
      const computersCollection = await getCollection('CustomerComputers');
      const contractsCollection = await getCollection('ServiceContracts');

      // Check if customer has any computers or contracts
      const computerCount = await computersCollection.countDocuments({ 
        customerId: new ObjectId(customerId) 
      });
      
      const contractCount = await contractsCollection.countDocuments({ 
        customerId: new ObjectId(customerId) 
      });

      if (computerCount > 0 || contractCount > 0) {
        return { 
          success: false, 
          error: 'Cannot delete customer with associated computers or contracts' 
        };
      }

      await customersCollection.deleteOne({ _id: new ObjectId(customerId) });
      return { success: true };
    } catch (error) {
      console.error('Error deleting customer:', error instanceof Error ? error.message : 'Unknown error');
      return { success: false, error: error instanceof Error ? error.message : 'Failed to delete customer' };
    }
  },
  
  addRegion: async ({ request, params }) => {
    try {
      const formData = await request.formData();
      const customerId = params.id;
      const regionId = formData.get('regionId')?.toString();
      
      if (!ObjectId.isValid(customerId)) {
        return { success: false, error: 'Invalid customer ID' };
      }

      if (!regionId || !ObjectId.isValid(regionId)) {
        return { success: false, error: 'Invalid region ID' };
      }

      // Get the customer
      const customersCollection = await getCollection('Customers');
      const customer = await customersCollection.findOne({ _id: new ObjectId(customerId) });
      
      if (!customer) {
        return { success: false, error: 'Customer not found' };
      }

      // Get the region from the Regions collection
      const regionsCollection = await getCollection('Regions');
      const region = await regionsCollection.findOne({ 
        _id: new ObjectId(regionId),
        country: customer.country // Ensure the region belongs to the customer's country
      });
      
      if (!region) {
        return { success: false, error: 'Region not found or does not belong to customer\'s country' };
      }

      // Check if the region is already assigned to this customer
      const customerRegionsCollection = await getCollection('CustomerRegions');
      const existingAssignment = await customerRegionsCollection.findOne({
        customerId: new ObjectId(customerId),
        regionId: new ObjectId(regionId)
      });
      
      if (existingAssignment) {
        return { success: false, error: 'Region already assigned to this customer' };
      }
      
      // Assign the existing region to the customer
      const customerRegion = {
        customerId: new ObjectId(customerId),
        regionId: new ObjectId(regionId),
        createdAt: new Date()
      };
      
      await customerRegionsCollection.insertOne(customerRegion);
      
      return { 
        success: true, 
        id: regionId
      };
    } catch (error) {
      console.error('Error adding region:', error instanceof Error ? error.message : 'Unknown error');
      return { success: false, error: error instanceof Error ? error.message : 'Failed to add region' };
    }
  },
  
  assignRegion: async ({ request, params }) => {
    try {
      const formData = await request.formData();
      const customerId = params.id;
      const regionId = formData.get('regionId')?.toString();
      
      if (!ObjectId.isValid(customerId) || !regionId || !ObjectId.isValid(regionId)) {
        return { success: false, error: 'Invalid customer ID or region ID' };
      }

      const customersCollection = await getCollection('Customers');
      const regionsCollection = await getCollection('Regions');
      const customerRegionsCollection = await getCollection('CustomerRegions');
      
      // Verify customer exists
      const customer = await customersCollection.findOne({ _id: new ObjectId(customerId) });
      if (!customer) {
        return { success: false, error: 'Customer not found' };
      }
      
      // Verify region exists
      const region = await regionsCollection.findOne({ _id: new ObjectId(regionId) });
      if (!region) {
        return { success: false, error: 'Region not found' };
      }
      
      // Check if assignment already exists
      const existingAssignment = await customerRegionsCollection.findOne({
        customerId: new ObjectId(customerId),
        regionId: new ObjectId(regionId)
      });
      
      if (existingAssignment) {
        return { success: false, error: 'Region already assigned to this customer' };
      }
      
      // Create new assignment
      const customerRegion = {
        customerId: new ObjectId(customerId),
        regionId: new ObjectId(regionId),
        createdAt: new Date()
      };
      
      await customerRegionsCollection.insertOne(customerRegion);
      
      return { success: true };
    } catch (error) {
      console.error('Error assigning region:', error instanceof Error ? error.message : 'Unknown error');
      return { success: false, error: error instanceof Error ? error.message : 'Failed to assign region' };
    }
  },
  
  unassignRegion: async ({ request, params }) => {
    try {
      const formData = await request.formData();
      const customerId = params.id;
      const regionId = formData.get('regionId')?.toString();
      
      if (!ObjectId.isValid(customerId) || !regionId || !ObjectId.isValid(regionId)) {
        return { success: false, error: 'Invalid customer ID or region ID' };
      }
      
      const customerRegionsCollection = await getCollection('CustomerRegions');
      
      await customerRegionsCollection.deleteOne({
        customerId: new ObjectId(customerId),
        regionId: new ObjectId(regionId)
      });
      
      return { success: true };
    } catch (error) {
      console.error('Error unassigning region:', error instanceof Error ? error.message : 'Unknown error');
      return { success: false, error: error instanceof Error ? error.message : 'Failed to unassign region' };
    }
  }
};
