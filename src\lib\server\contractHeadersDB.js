import { COLLECTIONS, serializeDocument, createObjectId, executeDbOperation } from './serviceContractDB.js';
import serviceContractDB from './serviceContractDB.js';

const db = serviceContractDB.db;
const collection = COLLECTIONS.CONTRACT_HEADERS;

/**
 * Create a new contract header
 * @param {Object} contractHeader - Contract header data
 * @returns {Promise<Object>} Inserted document with ID
 */
export async function createContractHeader(contractHeader) {
  // Add timestamps
  const now = new Date();
  const documentToInsert = {
    ...contractHeader,
    status: contractHeader.status || 'Draft',
    createdAt: now,
    updatedAt: now
  };
  
  return executeDbOperation(async () => {
    const result = await db.collection(collection).insertOne(documentToInsert);
    if (!result.acknowledged) {
      throw new Error('Failed to create contract header');
    }
    return {
      ...serializeDocument(documentToInsert),
      _id: result.insertedId.toString()
    };
  }, 'Error creating contract header');
}

/**
 * Get all contract headers with optional filtering
 * @param {Object} filter - MongoDB filter object
 * @param {Object} options - MongoDB options (sort, limit, etc.)
 * @returns {Promise<Array>} Array of contract headers
 */
export async function getContractHeaders(filter = {}, options = {}) {
  return executeDbOperation(async () => {
    const defaultOptions = {
      sort: { createdAt: -1 },
      limit: 100,
      ...options
    };
    
    const cursor = db.collection(collection).find(filter, defaultOptions);
    const documents = await cursor.toArray();
    return documents.map(doc => serializeDocument(doc));
  }, 'Error retrieving contract headers');
}

/**
 * Get a single contract header by ID
 * @param {string} id - Contract header ID
 * @returns {Promise<Object|null>} Contract header or null if not found
 */
export async function getContractHeaderById(id) {
  return executeDbOperation(async () => {
    const objectId = createObjectId(id);
    if (!objectId) return null;
    
    const document = await db.collection(collection).findOne({ _id: objectId });
    return document ? serializeDocument(document) : null;
  }, 'Error retrieving contract header');
}

/**
 * Update a contract header
 * @param {string} id - Contract header ID
 * @param {Object} updates - Fields to update
 * @returns {Promise<Object|null>} Updated contract header or null if not found
 */
export async function updateContractHeader(id, updates) {
  return executeDbOperation(async () => {
    const objectId = createObjectId(id);
    if (!objectId) return null;
    
    // Add updated timestamp
    const updateData = {
      ...updates,
      updatedAt: new Date()
    };
    
    // Don't allow updating the _id field
    if (updateData._id) {
      delete updateData._id;
    }
    
    const result = await db.collection(collection).findOneAndUpdate(
      { _id: objectId },
      { $set: updateData },
      { returnDocument: 'after' }
    );
    
    return result.value ? serializeDocument(result.value) : null;
  }, 'Error updating contract header');
}

/**
 * Delete a contract header
 * @param {string} id - Contract header ID
 * @returns {Promise<boolean>} True if deleted, false if not found
 */
export async function deleteContractHeader(id) {
  return executeDbOperation(async () => {
    const objectId = createObjectId(id);
    if (!objectId) return false;
    
    const result = await db.collection(collection).deleteOne({ _id: objectId });
    return result.deletedCount > 0;
  }, 'Error deleting contract header');
}

/**
 * Get contract headers by computer ID
 * @param {string} computerId - Computer ID
 * @returns {Promise<Array>} Array of contract headers
 */
export async function getContractHeadersByComputerId(computerId) {
  return executeDbOperation(async () => {
    const computerObjectId = createObjectId(computerId);
    if (!computerObjectId) return [];
    
    const documents = await db.collection(collection)
      .find({ computerId: computerObjectId })
      .sort({ createdAt: -1 })
      .toArray();
    
    return documents.map(doc => serializeDocument(doc));
  }, 'Error retrieving contract headers by computer ID');
}

/**
 * Get active contract for a computer
 * @param {string} computerId - Computer ID
 * @returns {Promise<Object|null>} Active contract or null if not found
 */
export async function getActiveContractForComputer(computerId) {
  return executeDbOperation(async () => {
    const computerObjectId = createObjectId(computerId);
    if (!computerObjectId) return null;
    
    const document = await db.collection(collection).findOne({
      computerId: computerObjectId,
      status: 'Active',
      startDate: { $lte: new Date() },
      endDate: { $gt: new Date() }
    });
    
    return document ? serializeDocument(document) : null;
  }, 'Error retrieving active contract');
}

export default {
  createContractHeader,
  getContractHeaders,
  getContractHeaderById,
  updateContractHeader,
  deleteContractHeader,
  getContractHeadersByComputerId,
  getActiveContractForComputer
};
