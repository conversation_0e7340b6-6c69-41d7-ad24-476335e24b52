<script>
  // Import any required components or utilities
  import MongoDBStatus from '$lib/components/MongoDBStatus.svelte';
</script>

<div class="app-container">
  <nav class="navbar">
    <div class="navbar-container">
      <div class="navbar-logo">
        <a href="/" class="logo-link">Service Management</a>
      </div>
      <div class="navbar-links">
        <a href="/" class="nav-link home-link">
          <span>Home</span>
        </a>
        <a href="/liststd1?collection=customers&filterlist=name" class="nav-link">
          <span>Customers & Computers</span>
        </a>
        <a href="/service-id" class="nav-link">
          <span>Service IDs</span>
        </a>
        <a href="/service-plans" class="nav-link">
          <span>📋 Service Plans</span>
        </a>
        <a href="/data-exploration" class="nav-link">
          <span>Data Exploration</span>
        </a>
        <a href="/about" class="nav-link">
          <span>About</span>
        </a>
        <div class="nav-link mongodb-status-nav">
          <MongoDBStatus />
        </div>
      </div>
    </div>
  </nav>

  <main class="content">
    <slot></slot>
  </main>
</div>

<style>
  /* Reset some default styles */
  :global(body) {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    color: #333;
    background-color: #f5f7fa;
  }

  /* Global button styles */
  :global(.btn-primary) {
    background-color: #0a2463;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }

  :global(.btn-primary:hover) {
    background-color: #1e3a8a;
  }

  :global(.btn-secondary) {
    background-color: #e5e7eb;
    color: #1f2937;
    border: 1px solid #d1d5db;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }

  :global(.btn-secondary:hover) {
    background-color: #d1d5db;
  }

  :global(.btn-danger) {
    background-color: #dc2626;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }

  :global(.btn-danger:hover) {
    background-color: #b91c1c;
  }

  :global(.btn-icon) {
    background-color: transparent;
    color: #0a2463;
    border: 1px solid #0a2463;
    padding: 0.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  :global(.btn-icon:hover) {
    background-color: #0a2463;
    color: white;
  }

  :global(.btn-icon.delete) {
    color: #dc2626;
    border-color: #dc2626;
  }

  :global(.btn-icon.delete:hover) {
    background-color: #dc2626;
    color: white;
  }

  :global(.btn-icon.view) {
    color: #0a2463;
    border-color: #0a2463;
  }

  :global(.btn-icon.view:hover) {
    background-color: #0a2463;
    color: white;
  }

  :global(.btn-icon.edit) {
    color: #0a2463;
    border-color: #0a2463;
  }

  :global(.btn-icon.edit:hover) {
    background-color: #0a2463;
    color: white;
  }

  /* App container */
  .app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }

  /* Navigation bar */
  .navbar {
    background-color: #0a2463;
    color: white;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
    position: sticky;
    top: 0;
    z-index: 100;
  }

  .navbar-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1.5rem;
    max-width: 1280px;
    margin: 0 auto;
    height: 64px;
  }

  .navbar-logo {
    font-size: 1.25rem;
    font-weight: 700;
  }

  .logo-link {
    color: white;
    text-decoration: none;
    letter-spacing: 0.5px;
  }

  .navbar-links {
    display: flex;
    gap: 1.5rem;
  }

  .nav-link {
    color: #e5e7eb;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
  }

  .mongodb-status-nav {
    margin-left: 1rem;
    cursor: default;
  }

  .mongodb-status-nav:hover {
    background-color: transparent;
  }

  /* Main content */
  .content {
    flex: 1;
    padding: 1.5rem;
  }
</style>
