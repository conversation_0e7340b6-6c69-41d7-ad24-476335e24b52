<script>
  import { enhance } from '$app/forms';
  import { goto } from '$app/navigation';
  import Grid from '$lib/components/Grid.svelte';
  
  export let item = {};
  export let showForm = false;
  export let formData = {};
  export let onSave = () => {};
  export let onCancel = () => {};
  
  // Format date for display
  function formatDate(dateStr) {
    if (!dateStr) return 'N/A';
    try {
      return new Date(dateStr).toLocaleDateString();
    } catch (e) {
      return 'Invalid Date';
    }
  }
  
  // Format date for input field
  function formatDateForInput(dateStr) {
    if (!dateStr) return '';
    try {
      return new Date(dateStr).toISOString().split('T')[0];
    } catch (e) {
      return '';
    }
  }
</script>

<div class="computer-detail">
  <form id="edit-form" on:submit|preventDefault={onSave}>
    <Grid>
      <!-- Basic Information Section -->
      <div class="section-header" style="grid-column: span 3;">
        <h2>Basic Information</h2>
      </div>
      
      <!-- Column 1 -->
      <div class="grid-column">
        <!-- Name -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Name</span>
            <span class="value">{item.name || 'N/A'}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="name">Name</label>
            <input type="text" id="name" name="name" bind:value={formData.name} />
          </div>
        {/if}
        
        <!-- Type -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Type</span>
            <span class="value">{item.type || 'N/A'}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="type">Type</label>
            <input type="text" id="type" name="type" bind:value={formData.type} />
          </div>
        {/if}
        
        <!-- Model -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Model</span>
            <span class="value">{item.model || 'N/A'}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="model">Model</label>
            <input type="text" id="model" name="model" bind:value={formData.model} />
          </div>
        {/if}
        
        <!-- Serial Number -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Serial Number</span>
            <span class="value">{item.serialNumber || 'N/A'}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="serialNumber">Serial Number</label>
            <input type="text" id="serialNumber" name="serialNumber" bind:value={formData.serialNumber} />
          </div>
        {/if}
        
        <!-- Manufacturer -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Manufacturer</span>
            <span class="value">{item.manufacturer || 'N/A'}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="manufacturer">Manufacturer</label>
            <input type="text" id="manufacturer" name="manufacturer" bind:value={formData.manufacturer} />
          </div>
        {/if}
      </div>
      
      <!-- Column 2 -->
      <div class="grid-column">
        <!-- Operating System -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Operating System</span>
            <span class="value">{item.operatingSystem || 'N/A'}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="operatingSystem">Operating System</label>
            <input type="text" id="operatingSystem" name="operatingSystem" bind:value={formData.operatingSystem} />
          </div>
        {/if}
        
        <!-- Computer Category -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Computer Category</span>
            <span class="value">{item.ComputerCategory || 'N/A'}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="ComputerCategory">Computer Category</label>
            <input type="text" id="ComputerCategory" name="ComputerCategory" bind:value={formData.ComputerCategory} />
          </div>
        {/if}
        
        <!-- Product Designation -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Product Designation</span>
            <span class="value">{item.ProductDesignation || item.productDesignation || 'N/A'}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="ProductDesignation">Product Designation</label>
            <input type="text" id="ProductDesignation" name="ProductDesignation" bind:value={formData.ProductDesignation} />
          </div>
        {/if}
        
        <!-- Product Part Number -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Product Part Number</span>
            <span class="value">{item.ProductPartNumber || 'N/A'}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="ProductPartNumber">Product Part Number</label>
            <input type="text" id="ProductPartNumber" name="ProductPartNumber" bind:value={formData.ProductPartNumber} />
          </div>
        {/if}
        
        <!-- Product Validity Group -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Product Validity Group</span>
            <span class="value">{item.ProductValidityGroup || 'N/A'}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="ProductValidityGroup">Product Validity Group</label>
            <input type="text" id="ProductValidityGroup" name="ProductValidityGroup" bind:value={formData.ProductValidityGroup} />
          </div>
        {/if}
      </div>
      
      <!-- Column 3 -->
      <div class="grid-column">
        <!-- Purchase Date -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Purchase Date</span>
            <span class="value">{formatDate(item.purchaseDate)}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="purchaseDate">Purchase Date</label>
            <input type="date" id="purchaseDate" name="purchaseDate" bind:value={formData.purchaseDate} />
          </div>
        {/if}
        
        <!-- Delivery Date -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Delivery Date</span>
            <span class="value">{formatDate(item.deliveryDate)}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="deliveryDate">Delivery Date</label>
            <input type="date" id="deliveryDate" name="deliveryDate" bind:value={formData.deliveryDate} />
          </div>
        {/if}
        
        <!-- Warranty End Date -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Warranty End Date</span>
            <span class="value">{formatDate(item.warrantyEndDate)}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="warrantyEndDate">Warranty End Date</label>
            <input type="date" id="warrantyEndDate" name="warrantyEndDate" bind:value={formData.warrantyEndDate} />
          </div>
        {/if}
        
        <!-- Created At -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Created At</span>
            <span class="value">{formatDate(item.createdAt)}</span>
          </div>
        {/if}
        
        <!-- Updated At -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Updated At</span>
            <span class="value">{formatDate(item.updatedAt)}</span>
          </div>
        {/if}
      </div>
      
      <!-- Contract Information Section -->
      <div class="section-header" style="grid-column: span 3;">
        <h2>Contract Information</h2>
      </div>
      
      <!-- Column 1 -->
      <div class="grid-column">
        <!-- Contract Number -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Contract Number</span>
            <span class="value">{item.contractNumber || 'N/A'}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="contractNumber">Contract Number</label>
            <input type="text" id="contractNumber" name="contractNumber" bind:value={formData.contractNumber} />
          </div>
        {/if}
        
        <!-- Contract Start Date -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Contract Start Date</span>
            <span class="value">{formatDate(item.contractStartDate)}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="contractStartDate">Contract Start Date</label>
            <input type="date" id="contractStartDate" name="contractStartDate" bind:value={formData.contractStartDate} />
          </div>
        {/if}
        
        <!-- Contract End Date -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Contract End Date</span>
            <span class="value">{formatDate(item.contractEndDate)}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="contractEndDate">Contract End Date</label>
            <input type="date" id="contractEndDate" name="contractEndDate" bind:value={formData.contractEndDate} />
          </div>
        {/if}
      </div>
      
      <!-- Column 2 -->
      <div class="grid-column">
        <!-- Contract Length Years -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Contract Length (Years)</span>
            <span class="value">{item.contractLengthYrs || 'N/A'}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="contractLengthYrs">Contract Length (Years)</label>
            <input type="number" id="contractLengthYrs" name="contractLengthYrs" bind:value={formData.contractLengthYrs} />
          </div>
        {/if}
        
        <!-- Desired Contract Length Years -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Desired Contract Length (Years)</span>
            <span class="value">{item.desiredContractLengthYears || 'N/A'}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="desiredContractLengthYears">Desired Contract Length (Years)</label>
            <input type="number" id="desiredContractLengthYears" name="desiredContractLengthYears" bind:value={formData.desiredContractLengthYears} />
          </div>
        {/if}
        
        <!-- Desired Contract Length Hours -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Desired Contract Length (Hours)</span>
            <span class="value">{item.desiredContractLengthHrs || 'N/A'}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="desiredContractLengthHrs">Desired Contract Length (Hours)</label>
            <input type="number" id="desiredContractLengthHrs" name="desiredContractLengthHrs" bind:value={formData.desiredContractLengthHrs} />
          </div>
        {/if}
      </div>
      
      <!-- Column 3 -->
      <div class="grid-column">
        <!-- Contract Age Limit -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Contract Age Limit</span>
            <span class="value">{item.contractAgeLimit || 'N/A'}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="contractAgeLimit">Contract Age Limit</label>
            <input type="number" id="contractAgeLimit" name="contractAgeLimit" bind:value={formData.contractAgeLimit} />
          </div>
        {/if}
        
        <!-- Contract Hours Limit -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Contract Hours Limit</span>
            <span class="value">{item.contractHoursLimit || 'N/A'}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="contractHoursLimit">Contract Hours Limit</label>
            <input type="number" id="contractHoursLimit" name="contractHoursLimit" bind:value={formData.contractHoursLimit} />
          </div>
        {/if}
        
        <!-- Hours At Contract Start -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Hours At Contract Start</span>
            <span class="value">{item.HoursContractStart || 'N/A'}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="HoursContractStart">Hours At Contract Start</label>
            <input type="number" id="HoursContractStart" name="HoursContractStart" bind:value={formData.HoursContractStart} />
          </div>
        {/if}
      </div>
      
      <!-- Engine Information Section -->
      <div class="section-header" style="grid-column: span 3;">
        <h2>Engine Information</h2>
      </div>
      
      <!-- Column 1 -->
      <div class="grid-column">
        <!-- Engine Age Years -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Engine Age (Years)</span>
            <span class="value">{item.engineAgeYrs || 'N/A'}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="engineAgeYrs">Engine Age (Years)</label>
            <input type="number" id="engineAgeYrs" name="engineAgeYrs" bind:value={formData.engineAgeYrs} />
          </div>
        {/if}
      </div>
      
      <!-- Column 2 -->
      <div class="grid-column">
        <!-- Engine Age Hours -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Engine Age (Hours)</span>
            <span class="value">{item.engineAgeHrs || 'N/A'}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="engineAgeHrs">Engine Age (Hours)</label>
            <input type="number" id="engineAgeHrs" name="engineAgeHrs" bind:value={formData.engineAgeHrs} />
          </div>
        {/if}
      </div>
      
      <!-- Column 3 -->
      <div class="grid-column">
        <!-- Estimated Utilization Hours/Year -->
        {#if !showForm}
          <div class="detail-item">
            <span class="label">Estimated Utilization (Hours/Year)</span>
            <span class="value">{item.estimatedUtilizationHrsYr || 'N/A'}</span>
          </div>
        {:else}
          <div class="form-group">
            <label for="estimatedUtilizationHrsYr">Estimated Utilization (Hours/Year)</label>
            <input type="number" step="0.0001" id="estimatedUtilizationHrsYr" name="estimatedUtilizationHrsYr" bind:value={formData.estimatedUtilizationHrsYr} />
          </div>
        {/if}
      </div>
      
      <!-- Notes Section -->
      <div class="section-header" style="grid-column: span 3;">
        <h2>Notes</h2>
      </div>
      
      <!-- Notes -->
      <div style="grid-column: span 3;">
        {#if !showForm}
          <div class="detail-item notes-item">
            <span class="value notes-value">{item.notes || 'No notes available'}</span>
          </div>
        {:else}
          <div class="form-group">
            <textarea id="notes" name="notes" bind:value={formData.notes} rows="4"></textarea>
          </div>
        {/if}
      </div>
      
      <!-- Form Buttons -->
      {#if showForm}
        <div class="form-actions" style="grid-column: span 3;">
          <button type="button" class="btn-secondary" on:click={onCancel}>Cancel</button>
          <button type="submit" class="btn-primary">Save Changes</button>
        </div>
      {/if}
    </Grid>
  </form>
</div>

<style>
  .computer-detail {
    width: 100%;
  }
  
  .section-header {
    margin-top: 1.5rem;
    margin-bottom: 0.5rem;
    border-bottom: 1px solid #e2e8f0;
  }
  
  .section-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
    padding-bottom: 0.5rem;
  }
  
  .detail-item {
    margin-bottom: 0.75rem;
  }
  
  .label {
    display: block;
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
  }
  
  .value {
    display: block;
    color: #1a202c;
  }
  
  .notes-item {
    background-color: #f7fafc;
    padding: 0.75rem;
    border-radius: 0.375rem;
    border: 1px solid #e2e8f0;
  }
  
  .notes-value {
    white-space: pre-wrap;
    font-size: 0.875rem;
    line-height: 1.5;
  }
  
  .form-group {
    margin-bottom: 1rem;
  }
  
  .form-group label {
    display: block;
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
  }
  
  .form-group input,
  .form-group select,
  .form-group textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #cbd5e0;
    border-radius: 0.25rem;
    font-size: 0.875rem;
  }
  
  .form-group textarea {
    resize: vertical;
    min-height: 5rem;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    margin-top: 1.5rem;
  }
  
  .btn-primary,
  .btn-secondary {
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .btn-primary {
    background-color: #3b82f6;
    color: white;
    border: 1px solid #3b82f6;
  }
  
  .btn-primary:hover {
    background-color: #2563eb;
    border-color: #2563eb;
  }
  
  .btn-secondary {
    background-color: white;
    color: #4b5563;
    border: 1px solid #d1d5db;
  }
  
  .btn-secondary:hover {
    background-color: #f9fafb;
    border-color: #9ca3af;
  }
</style>
