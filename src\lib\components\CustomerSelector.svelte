<script>
  import { createEventDispatcher } from 'svelte';
  
  /**
   * @typedef {Object} Customer
   * @property {string} _id - MongoDB ObjectId as string
   * @property {string} companyName - Company name
   * @property {string} [email] - Email address
   * @property {string} [phone] - Phone number
   * @property {string} [type] - Customer type
   * @property {string} [city] - City
   * @property {string} [country] - Country
   */
  
  /** @type {Customer[]} */
  export let customers = [];
  
  /** @type {string} */
  export let selectedCustomerId = '';
  
  /** @type {string} */
  export let title = 'Customers';
  
  /** @type {string} */
  export let placeholder = 'Search customers...';
  
  /** @type {boolean} */
  export let showTypeFilter = true;
  
  /** @type {boolean} */
  export let showAddButton = true;
  
  let searchTerm = '';
  let filterType = 'all';
  
  // Get unique customer types from the customers array
  $: customerTypes = [...new Set(customers.filter(c => c.type).map(customer => customer.type))];
  
  // Filter customers based on search term and type
  $: filteredCustomers = customers.filter(customer => {
    const matchesSearch = searchTerm === '' || 
      customer.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (customer.email && customer.email.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesType = filterType === 'all' || customer.type === filterType;
    
    return matchesSearch && matchesType;
  });
  
  const dispatch = createEventDispatcher();
  
  /**
   * Handle customer selection
   * @param {string} customerId - The selected customer ID
   */
  function selectCustomer(customerId) {
    selectedCustomerId = customerId;
    const selectedCustomer = customers.find(c => c._id === customerId);
    dispatch('select', { customerId, customer: selectedCustomer });
  }
  
  /**
   * Handle add customer button click
   */
  function handleAddCustomer() {
    dispatch('add');
  }
</script>

<div class="customer-selector">
  <div class="header">
    <h2>{title}</h2>
    <div class="actions">
      <div class="search-container">
        <input
          type="text"
          class="search-input"
          placeholder={placeholder}
          bind:value={searchTerm}
        />
      </div>
      
      {#if showTypeFilter && customerTypes.length > 0}
        <select class="filter-select" bind:value={filterType}>
          <option value="all">All Types</option>
          {#each customerTypes as type}
            <option value={type}>{type}</option>
          {/each}
        </select>
      {/if}
      
      {#if showAddButton}
        <button class="btn-primary" on:click={handleAddCustomer}>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
          </svg>
          Add Customer
        </button>
      {/if}
    </div>
  </div>

  <div class="customers-table-container">
    <table class="customers-table">
      <thead>
        <tr>
          <th>Company Name</th>
          <th>Type</th>
          <th>Contact</th>
          <th>Location</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {#if filteredCustomers.length === 0}
          <tr>
            <td colspan="5" class="empty-message">No customers found</td>
          </tr>
        {:else}
          {#each filteredCustomers as customer (customer._id)}
            <tr 
              class={selectedCustomerId === customer._id ? 'selected' : ''}
              on:click={() => selectCustomer(customer._id)}
            >
              <td>
                <div class="company-name">{customer.companyName}</div>
              </td>
              <td>
                {#if customer.type}
                  <span class="badge badge-{customer.type.toLowerCase()}">{customer.type}</span>
                {/if}
              </td>
              <td>
                <div class="contact-info">
                  {#if customer.email}
                    <div>{customer.email}</div>
                  {/if}
                  {#if customer.phone}
                    <div class="text-gray-500">{customer.phone}</div>
                  {/if}
                </div>
              </td>
              <td>
                <div class="location-info">
                  {#if customer.city}
                    <div>{customer.city}{customer.country ? `, ${customer.country}` : ''}</div>
                  {:else if customer.country}
                    <div>{customer.country}</div>
                  {:else}
                    <div class="text-gray-400">No location</div>
                  {/if}
                </div>
              </td>
              <td>
                <button 
                  class="btn-select"
                  on:click|stopPropagation={() => selectCustomer(customer._id)}
                >
                  Select
                </button>
              </td>
            </tr>
          {/each}
        {/if}
      </tbody>
    </table>
  </div>
</div>

<style>
  .customer-selector {
    width: 100%;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    overflow: hidden;
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem;
    background-color: #1e3a8a;
    color: white;
  }
  
  .header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
  }
  
  .actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
  }
  
  .search-container {
    position: relative;
  }
  
  .search-input {
    padding: 0.625rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    width: 16rem;
    font-size: 0.875rem;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
  }
  
  .search-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
  }
  
  .filter-select {
    padding: 0.625rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    background-color: white;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
  }
  
  .filter-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
  }
  
  .btn-primary {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    background-color: #2563eb;
    color: white;
    font-weight: 500;
    padding: 0.625rem 1.25rem;
    border-radius: 0.375rem;
    border: none;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }
  
  .btn-primary:hover {
    background-color: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  
  .customers-table-container {
    overflow-x: auto;
  }
  
  .customers-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
  }
  
  .customers-table th {
    background-color: #f1f5f9;
    color: #1e3a8a;
    font-weight: 600;
    text-align: left;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    border-bottom: 1px solid #e2e8f0;
  }
  
  .customers-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e2e8f0;
    font-size: 0.875rem;
    vertical-align: middle;
    color: #334155;
  }
  
  .customers-table tr {
    transition: background-color 0.15s ease;
    cursor: pointer;
  }
  
  .customers-table tr:hover {
    background-color: #f8fafc;
  }
  
  .customers-table tr.selected {
    background-color: #eff6ff;
  }
  
  .empty-message {
    text-align: center;
    padding: 2rem;
    color: #64748b;
  }
  
  .company-name {
    font-weight: 500;
    color: #1e3a8a;
  }
  
  .contact-info,
  .location-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .text-gray-400 {
    color: #94a3b8;
  }
  
  .text-gray-500 {
    color: #64748b;
  }
  
  .badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    color: white;
  }
  
  .badge-retail {
    background-color: #1e3a8a;
  }
  
  .badge-wholesale {
    background-color: #0f766e;
  }
  
  .badge-oem {
    background-color: #b45309;
  }
  
  .badge-distributor {
    background-color: #4f46e5;
  }
  
  .badge-education {
    background-color: #7e22ce;
  }
  
  .badge-government {
    background-color: #b91c1c;
  }
  
  .btn-select {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #1e3a8a;
    color: white;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    border: none;
    cursor: pointer;
    font-size: 0.75rem;
    transition: all 0.2s ease;
  }
  
  .btn-select:hover {
    background-color: #1e40af;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
    
    .actions {
      width: 100%;
      flex-direction: column;
    }
    
    .search-input,
    .filter-select {
      width: 100%;
    }
  }
</style>
