// Script to delete all ActualComputerHours documents from MongoDB
import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function run() {
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    const result = await db.collection('ActualComputerHours').deleteMany({});
    console.log(`Deleted ${result.deletedCount} ActualComputerHours documents.`);
  } finally {
    await client.close();
  }
}

run();
