import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/**
 * @param {import('@sveltejs/kit').RequestEvent} event
 */
export async function GET(event) {
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('QuotationRows');
    
    const rows = await collection
      .find({ ComputerId: event.params.id })
      .sort({ _id: 1 })
      .toArray();
    
    return json(rows);
  } catch (error) {
    console.error('Error fetching quotation rows:', error);
    return new Response(JSON.stringify({ error: 'Failed to fetch quotation rows' }), {
      status: 500
    });
  } finally {
    await client.close();
  }
}

/**
 * @param {import('@sveltejs/kit').RequestEvent} event
 */
export async function POST(event) {
  try {
    const data = await event.request.json();
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('QuotationRows');
    
    const newRow = {
      ...data,
      ComputerId: event.params.id,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const result = await collection.insertOne(newRow);
    
    return json({ 
      success: true, 
      _id: result.insertedId 
    });
  } catch (error) {
    console.error('Error creating quotation row:', error);
    return new Response(JSON.stringify({ error: 'Failed to create quotation row' }), {
      status: 500
    });
  } finally {
    await client.close();
  }
}

/**
 * @param {import('@sveltejs/kit').RequestEvent} event
 */
export async function PUT(event) {
  try {
    const rowId = event.url.pathname.split('/').pop();
    const data = await event.request.json();
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('QuotationRows');
    
    const result = await collection.updateOne(
      { _id: new ObjectId(rowId) },
      { 
        $set: {
          ...data,
          updatedAt: new Date()
        }
      }
    );
    
    if (result.matchedCount === 0) {
      return new Response(JSON.stringify({ error: 'Row not found' }), {
        status: 404
      });
    }
    
    return json({ success: true });
  } catch (error) {
    console.error('Error updating quotation row:', error);
    return new Response(JSON.stringify({ error: 'Failed to update quotation row' }), {
      status: 500
    });
  } finally {
    await client.close();
  }
}

/**
 * @param {import('@sveltejs/kit').RequestEvent} event
 */
export async function DELETE(event) {
  try {
    const rowId = event.url.pathname.split('/').pop();
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('QuotationRows');
    
    const result = await collection.deleteOne({
      _id: new ObjectId(rowId)
    });
    
    if (result.deletedCount === 0) {
      return new Response(JSON.stringify({ error: 'Row not found' }), {
        status: 404
      });
    }
    
    return json({ success: true });
  } catch (error) {
    console.error('Error deleting quotation row:', error);
    return new Response(JSON.stringify({ error: 'Failed to delete quotation row' }), {
      status: 500
    });
  } finally {
    await client.close();
  }
}
