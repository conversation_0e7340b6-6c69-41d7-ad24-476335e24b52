<!-- ProductDesignationPNVGGrid.svelte -->
<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  
  // Props for table layout configuration
  export let headerBgColor: string = '#f5f5f5';
  export let headerTextColor: string = '#333';
  export let rowGap: string = '0.5rem';
  export let columnGap: string = '1rem';
  export let padding: string = '1rem';
  export let columns: string = 'repeat(auto-fit, minmax(150px, 1fr))';
</script>

<BaseGrid 
  columns={columns}
  gap={`${rowGap} ${columnGap}`}
  padding={padding}
  className="product-designation-pnvg-grid"
>
  <div class="header" style:background-color={headerBgColor} style:color={headerTextColor}>
    <slot name="header" />
  </div>
  
  <div class="content">
    <slot name="content" />
  </div>
  
  <div class="filters">
    <slot name="filters" />
  </div>
  
  <div class="pagination">
    <slot name="pagination" />
  </div>
</BaseGrid>

<style>
  .header {
    font-weight: bold;
    padding: 1rem;
    border-bottom: 1px solid #e0e0e0;
    grid-column: 1 / -1;
  }

  .content {
    grid-column: 1 / -1;
    min-height: 300px;
  }
  
  .filters {
    grid-column: 1 / -1;
    padding: 1rem 0;
    border-bottom: 1px solid #e0e0e0;
  }
  
  .pagination {
    grid-column: 1 / -1;
    padding: 1rem 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /* Responsive styles */
  @media (max-width: 768px) {
    .header, .filters {
      padding: 0.75rem;
    }
  }

  @media (max-width: 480px) {
    .header, .filters {
      padding: 0.5rem;
    }
  }
</style>
