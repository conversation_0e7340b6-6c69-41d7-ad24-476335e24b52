import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/** @type {import('./$types').PageServerLoad} */
export async function load({ params }) {
    try {
        await client.connect();
        const db = client.db('ServiceContracts');
        const activitiesCollection = db.collection('ServiceActivities');
        const computersCollection = db.collection('CustomerComputers');
        const contractsCollection = db.collection('CustomerContracts');

        const computerId = params.id;
        
        // Get computer details
        const computer = await computersCollection.findOne({ _id: new ObjectId(computerId) });
        
        // Get active contract for this computer
        const contract = computer ? await contractsCollection.findOne({
            customerId: computer.customerId,
            startDate: { $exists: true },
            endDate: { $exists: true }
        }) : null;

        const activities = await activitiesCollection
            .find({ computerId: new ObjectId(computerId) })
            .sort({ createdAt: -1 })
            .toArray();

        return {
            activities: JSON.parse(JSON.stringify(activities)),
            contract: contract ? JSON.parse(JSON.stringify(contract)) : null,
            computer: computer ? JSON.parse(JSON.stringify(computer)) : null
        };
    } catch (error) {
        console.error('Error:', error);
        return {
            activities: [],
            contract: null,
            computer: null
        };
    } finally {
        await client.close();
    }
}
