import { MongoClient, ObjectId } from 'mongodb';

const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function checkBaseServices() {
  const client = new MongoClient(uri);
  
  try {
    console.log('Connecting to MongoDB...');
    await client.connect();
    console.log('Connected successfully to MongoDB server');
    
    const db = client.db(dbName);
    console.log(`Using database: ${dbName}`);
    
    // Check BaseServices collection
    console.log('\nChecking BaseServices collection:');
    const baseServicesCollection = db.collection('BaseServices');
    const count = await baseServicesCollection.countDocuments();
    console.log(`Total documents: ${count}`);
    
    if (count > 0) {
      // Get sample document
      const sampleDoc = await baseServicesCollection.findOne({});
      console.log('Sample document structure:');
      console.log(JSON.stringify(sampleDoc, null, 2));
      
      // List all field names
      console.log('\nField names in collection:');
      const fieldNames = Object.keys(sampleDoc || {});
      fieldNames.forEach(field => {
        const value = sampleDoc[field];
        const type = value instanceof ObjectId ? 'ObjectId' : 
                    value instanceof Date ? 'Date' : 
                    Array.isArray(value) ? 'Array' : 
                    typeof value;
        console.log(`- ${field} (${type}): ${JSON.stringify(value)}`);
      });
      
      // Get a few more documents to understand the data better
      console.log('\nSample documents (up to 5):');
      const sampleDocs = await baseServicesCollection.find({}).limit(5).toArray();
      sampleDocs.forEach((doc, index) => {
        console.log(`\nDocument ${index + 1}:`);
        console.log(JSON.stringify(doc, null, 2));
      });
    } else {
      console.log('No documents found in BaseServices collection');
    }
  } catch (error) {
    console.error('Error checking BaseServices collection:', error);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

checkBaseServices().catch(console.error);
