import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';

// GET: Fetch all service categories
export async function GET() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    
    // Using PascalCase for collection name as per project convention
    const categories = await db.collection('ServiceCategory')
      .find({})
      .sort({ name: 1 })
      .toArray();
    
    // Transform ObjectIds to strings for client-side use
    const formattedCategories = categories.map(category => ({
      ...category,
      _id: category._id.toString()
    }));
    
    return json(formattedCategories);
  } catch (error) {
    console.error('Error fetching service categories:', error);
    return new Response(JSON.stringify({ 
      error: 'Failed to fetch service categories' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  } finally {
    await client.close().catch(() => {});
  }
}

// POST: Create a new service category
export async function POST({ request }) {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    
    // Get category data from request
    const categoryData = await request.json();
    
    // Validate required fields
    if (!categoryData.name) {
      return new Response(JSON.stringify({ 
        error: 'Category name is required' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Check if category already exists
    const existing = await db.collection('ServiceCategory')
      .findOne({ name: categoryData.name });
      
    if (existing) {
      return new Response(JSON.stringify({ 
        error: 'Category with this name already exists' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Create new document
    const category = {
      name: categoryData.name,
      description: categoryData.description || '',
      color: categoryData.color || '#3498db',
      order: categoryData.order || 0,
      createdAt: new Date()
    };
    
    // Insert into database
    const result = await db.collection('ServiceCategory').insertOne(category);
    
    return json({ 
      success: true, 
      id: result.insertedId.toString(),
      message: 'Service category created successfully'
    });
  } catch (error) {
    console.error('Error creating service category:', error);
    return new Response(JSON.stringify({ 
      error: 'Failed to create service category' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  } finally {
    await client.close().catch(() => {});
  }
}
