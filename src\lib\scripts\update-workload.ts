import { init } from '../db';

async function updateWorkloadActivity() {
    try {
        console.log('Initializing database connection...');
        const db = await init();
        
        console.log('Connected to database. Updating Workload collection...');
        const Workload = db.collection("Workload");
        
        const result = await Workload.updateMany(
            {}, // match all documents
            { 
                $set: { 
                    activity: 'idle',
                    updatedAt: new Date()
                } 
            }
        );
        
        console.log(`Operation complete!`);
        console.log(`Matched ${result.matchedCount} documents`);
        console.log(`Modified ${result.modifiedCount} documents`);
        
        process.exit(0);
    } catch (error) {
        console.error('Error updating workload activity:', error);
        process.exit(1);
    }
}

// Run the update
updateWorkloadActivity();
