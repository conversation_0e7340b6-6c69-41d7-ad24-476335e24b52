<script>
  import { enhance } from '$app/forms';
  import { invalidate, goto } from '$app/navigation';
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { CUSTOMER_TYPES, CUSTOMER_DIVISIONS, DEFAULT_CUSTOMER_DIVISION, VERSATILE_OPTIONS } from '$lib/constants';
  import CustomersGrid from '$lib/components/CustomersGrid.svelte';
  
  /**
   * @typedef {Object} CustomerData
   * @property {string} _id - MongoDB ObjectId as string
   * @property {string} companyName - Company name
   * @property {string} email - Email address
   * @property {string} phone - Phone number
   * @property {string} address - Street address
   * @property {string} city - City
   * @property {string} country - Country
   * @property {string} type - Customer type
   * @property {string} division - Customer division (Marine or Industrial)
   * @property {string[]} versatile - Versatile options
   * @property {string} notes - Additional notes
   * @property {number} computerCount - Number of associated computers
   */

  /** @type {import('./$types').PageData} */
  export let data;

  // Get parameters from URL
  $: collection = $page.url.searchParams.get('collection') || 'customers';
  $: filterlist = $page.url.searchParams.get('filterlist') || 'name';
  
  /** @type {CustomerData[]} */
  let items = /** @type {CustomerData[]} */ (data.items || []);
  
  // Function to refresh data with a full page reload
  async function refreshData() {
    try {
      // Force a complete page refresh using browser's reload functionality
      window.location.reload();
      console.log('Full page refresh initiated');
    } catch (error) {
      console.error('Error refreshing page:', error);
    }
  }
  
  // Function to filter items using the API with filterValue
  /** @param {string} value - The filter value to apply */
  async function applyFilter(value) {
    try {
      if (!value) {
        // If filter is empty, just reload the page without filter
        window.location.href = `/liststd1?collection=${collection}&filterlist=${filterlist}`;
        return;
      }
      
      // Navigate to the same page with filter parameter
      window.location.href = `/liststd1?collection=${collection}&filterlist=${filterlist}&filterValue=${encodeURIComponent(value)}`;
    } catch (error) {
      console.error('Error applying filter:', error);
    }
  }
  
  // Special function specifically for handling new item creation
  function refreshAfterCreate() {
    console.log('New item added - refreshing page');
    // Small delay to ensure form closure completes
    setTimeout(() => {
      window.location.reload();
    }, 300); // Longer delay to ensure MongoDB operation completes
  }
  
  // Initialize searchTerm from URL parameter
  let searchTerm = $page.url.searchParams.get('filterValue') || '';
  let filterType = 'all';
  let countryFilter = '';
  
  let showForm = false;
  /** @type {any|null} */
  let editingItem = null;
  /** @type {any|null} */
  let selectedItem = null;

  /** @type {any} */
  let newItem = {
    _id: '',
    companyName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    country: '',
    type: 'Retail',
    division: DEFAULT_CUSTOMER_DIVISION,
    versatile: [],
    notes: '',
    computerCount: 0
  };

  /** @type {any} */
  let formData = newItem;

  $: {
    formData = editingItem || newItem;
  }

  $: filteredItems = items.filter(item => {
    const matchesSearch = searchTerm === '' || 
      item.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.phone?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.city?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.country?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = filterType === 'all' || item.type === filterType;
    
    const matchesCountry = !countryFilter || item.country?.toLowerCase().includes(countryFilter.toLowerCase());
    
    return matchesSearch && matchesType && matchesCountry;
  });

  function resetForm() {
    editingItem = null;
    newItem = {
      _id: '',
      companyName: '',
      email: '',
      phone: '',
      address: '',
      city: '',
      country: '',
      type: 'Retail',
      division: DEFAULT_CUSTOMER_DIVISION,
      versatile: [],
      notes: '',
      computerCount: 0
    };
    showForm = false;
  }
  
  /** @param {any} item - The item to edit */
  function editItem(item) {
    editingItem = { ...item };
    showForm = true;
  }
  
  /**
   * Handle item selection and show details
   * @param {{detail: {customer: any}}} event - Selection event with item data
   */
  function handleItemSelect(event) {
    selectedItem = event.detail.customer;
  }
</script>

<div class="container">
  <div class="header-content">
    <h1>{collection.charAt(0).toUpperCase() + collection.slice(1)}</h1>
    <div class="header-actions">
      <div class="grid-container-actions">
        <div class="search-container">
          <form on:submit|preventDefault={() => applyFilter(searchTerm)}>
            <div class="search-grid">
              <input 
                type="text" 
                placeholder="Search by {filterlist}..." 
                class="search-input" 
                bind:value={searchTerm}
              />
              <button type="submit" class="btn-search">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" width="20" height="20">
                  <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                </svg>
                Search
              </button>
              {#if $page.url.searchParams.get('filterValue')}
                <button 
                  type="button" 
                  class="btn-secondary" 
                  on:click={() => applyFilter('')}
                >
                  Clear Filter
                </button>
              {/if}
            </div>
          </form>
          
          {#if collection === 'customers'}
          <select class="filter-select" bind:value={filterType}>
            <option value="all">All Types</option>
            {#each CUSTOMER_TYPES as type}
              <option value={type}>{type}</option>
            {/each}
          </select>
          
          <input 
            type="text" 
            placeholder="Filter by country..." 
            class="filter-input" 
            bind:value={countryFilter}
          />
          {/if}
        </div>
        
        <button 
          class="btn-primary" 
          on:click={() => { showForm = true; }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" width="20" height="20">
            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
          </svg>
          Add {collection.slice(0, -1).charAt(0).toUpperCase() + collection.slice(0, -1).slice(1)}
        </button>
      </div>
    </div>
  </div>
  
  {#if items.length === 0}
    <div class="empty-state">
      <h2>No {collection} found</h2>
      <p>Get started by adding your first {collection.slice(0, -1)}.</p>
      <button 
        class="btn-primary" 
        on:click={() => { showForm = true; }}
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" width="20" height="20">
          <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
        </svg>
        Add {collection.slice(0, -1).charAt(0).toUpperCase() + collection.slice(0, -1).slice(1)}
      </button>
    </div>
  {:else}
    <div class="page-layout">
      <div class="grid-container">
        <CustomersGrid 
          customers={filteredItems} 
          selectedId={selectedItem ? selectedItem._id : null}
          onEdit={(item) => editItem(item)}
          onDelete={(item) => {
            if (!confirm(`Are you sure you want to delete ${item.companyName}?`)) return;
            
            // For delete, we'll use a form submission
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '?/deleteItem';
            
            const idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.name = '_id';
            idInput.value = item._id;
            form.appendChild(idInput);
            
            document.body.appendChild(form);
            
            form.addEventListener('submit', async (event) => {
              event.preventDefault();
              
              const formData = new FormData(form);
              const response = await fetch('?/deleteItem', {
                method: 'POST',
                body: formData
              });
              
              if (response.ok) {
                refreshData();
              }
              
              document.body.removeChild(form);
            });
            
            form.requestSubmit();
          }}
          on:select={(event) => {
            const selectedItem = event.detail.customer;
            if (selectedItem) {
              // Convert collection to PascalCase
              const pascalCaseCollection = collection.charAt(0).toUpperCase() + collection.slice(1);
              goto(`/liststd1-one-page?collection=${pascalCaseCollection}&itemId=${selectedItem._id}`);
            }
          }}
        />
      </div>
      
      {#if selectedItem}
        <div class="item-details">
          <div class="details-header">
            <h2>{selectedItem.companyName}</h2>
            <div class="badge-container">
              {#if selectedItem.type}
              <span class="badge badge-{selectedItem.type.toLowerCase().replace(' ', '-')}">
                {selectedItem.type}
              </span>
              {/if}
              {#if selectedItem.division}
              <span class="badge badge-{selectedItem.division.toLowerCase().replace(' ', '-')}">
                {selectedItem.division}
              </span>
              {/if}
            </div>
          </div>
          
          <div class="details-content">
            <div class="info-grid">
              <div class="info-item">
                <span class="label">Email:</span>
                <span>{selectedItem.email || 'Not provided'}</span>
              </div>
              
              <div class="info-item">
                <span class="label">Phone:</span>
                <span>{selectedItem.phone || 'Not provided'}</span>
              </div>
              
              <div class="info-item">
                <span class="label">Address:</span>
                <span>{selectedItem.address || 'Not provided'}</span>
              </div>
              
              <div class="info-item">
                <span class="label">City:</span>
                <span>{selectedItem.city || 'Not provided'}</span>
              </div>
              
              <div class="info-item">
                <span class="label">Country:</span>
                <span>{selectedItem.country || 'Not provided'}</span>
              </div>
              
              {#if selectedItem.notes}
              <div class="info-item full-width">
                <span class="label">Notes:</span>
                <span>{selectedItem.notes}</span>
              </div>
              {/if}
            </div>
          </div>
        </div>
      {/if}
    </div>
  {/if}

  {#if showForm}
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h2 class="modal-title">{editingItem ? 'Edit Item' : 'Add New Item'}</h2>
          <button 
            type="button" 
            class="modal-close" 
            on:click={resetForm}
            aria-label="Close form"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" width="24" height="24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <form 
          action={editingItem ? '?/updateItem' : '?/createItem'} 
          method="POST" 
          use:enhance={() => {
            return async ({ result }) => {
              if (result.type === 'success') {
                if (editingItem) {
                  refreshData();
                } else {
                  refreshAfterCreate();
                }
                resetForm();
              }
            };
          }}
        >
          {#if editingItem}
            <input type="hidden" name="_id" value={formData._id} />
          {/if}
          
          <div class="form-grid">
            <div class="form-group">
              <label for="companyName">Company Name</label>
              <input
                type="text"
                id="companyName"
                name="companyName"
                required
                bind:value={formData.companyName}
              />
            </div>
            
            <div class="form-group">
              <label for="email">Email</label>
              <input
                type="email"
                id="email"
                name="email"
                bind:value={formData.email}
              />
            </div>
            
            <div class="form-group">
              <label for="phone">Phone</label>
              <input
                type="text"
                id="phone"
                name="phone"
                bind:value={formData.phone}
              />
            </div>
            
            <div class="form-group">
              <label for="address">Address</label>
              <input
                type="text"
                id="address"
                name="address"
                bind:value={formData.address}
              />
            </div>
            
            <div class="form-group">
              <label for="city">City</label>
              <input
                type="text"
                id="city"
                name="city"
                bind:value={formData.city}
              />
            </div>
            
            <div class="form-group">
              <label for="country">Country</label>
              <input
                type="text"
                id="country"
                name="country"
                bind:value={formData.country}
              />
            </div>
            
            <div class="form-group">
              <label for="type">Type</label>
              <select
                id="type"
                name="type"
                bind:value={formData.type}
              >
                {#each CUSTOMER_TYPES as type}
                  <option value={type}>{type}</option>
                {/each}
              </select>
            </div>
            
            <div class="form-group">
              <label for="division">Division</label>
              <select
                id="division"
                name="division"
                bind:value={formData.division}
              >
                {#each CUSTOMER_DIVISIONS as division}
                  <option value={division}>{division}</option>
                {/each}
              </select>
            </div>
            
            <div class="form-group full-width">
              <label for="notes">Notes</label>
              <textarea
                id="notes"
                name="notes"
                rows="3"
                bind:value={formData.notes}
              ></textarea>
            </div>
          </div>
          
          <div class="form-actions">
            <button type="button" class="btn-secondary" on:click={resetForm}>Cancel</button>
            <button type="submit" class="btn-primary">{editingItem ? 'Update' : 'Create'}</button>
          </div>
        </form>
      </div>
    </div>
  {/if}
</div>

<style>
  /* Base styles */
  .container {
    max-width: 1400px;
    margin: 0 auto; /* Center the container */
    padding: 2rem 1rem;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: #f8fafc;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  h1 {
    font-size: 1.875rem;
    font-weight: 600;
    color: #0f172a;
    margin: 0 0 1.5rem 0;
  }
  
  .header-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
    
    @media (min-width: 640px) {
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .header-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: center;
  }
  
  /* Grid-based layout for actions */
  .grid-container-actions {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 1rem;
    width: 100%;
    align-items: center;
    
    @media (max-width: 640px) {
      grid-template-columns: 1fr;
    }
  }
  
  .search-container {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 1rem;
    align-items: center;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
  
  .search-grid {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 1rem;
    align-items: center;
    width: 100%;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }
  
  .btn-search {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: #0a2463;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
  }
  
  .search-input, .filter-input {
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    min-width: 200px;
  }
  
  .filter-select {
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    background-color: white;
  }
  
  /* Empty state */
  .empty-state {
    text-align: center;
    padding: 3rem 1.5rem;
    background-color: white;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  }
  
  .empty-state h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-bottom: 1rem;
  }
  
  .empty-state p {
    color: #64748b;
    margin-bottom: 1.5rem;
  }
  
  /* Page Layout using CSS Grid */
  .page-layout {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    width: 100%;
    
    @media (min-width: 1200px) {
      grid-template-columns: 3fr 2fr; /* Change ratio to give more space to item list */
    }
  }
  
  .grid-container {
    background-color: white;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    overflow: hidden;
  }
  
  /* Item Details */
  .item-details {
    background-color: white;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }
  
  .details-header {
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    gap: 1rem;
    background-color: #1e3a8a;
    color: white;
    padding: 1rem;
  }
  
  .details-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
  }
  
  .details-content {
    padding: 1.5rem;
  }
  
  .info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    
    @media (min-width: 640px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  .info-item {
    display: grid;
    grid-template-columns: 100px 1fr;
    gap: 0.5rem;
  }
  
  .info-item.full-width {
    grid-column: 1 / -1;
    grid-template-columns: 100px 1fr;
  }
  
  .label {
    font-weight: 500;
    color: #64748b;
  }
  
  /* Modal for the form */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 1rem;
  }
  
  .modal-content {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
  }
  
  .modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e3a8a;
    margin: 0;
  }
  
  .modal-close {
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .modal-close:hover {
    color: #1e3a8a;
  }
  
  form {
    padding: 1rem;
  }
  
  .form-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
    
    @media (min-width: 640px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .form-group.full-width {
    grid-column: 1 / -1;
  }
  
  label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
  }
  
  input, select, textarea {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
  }
  
  input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #93c5fd;
    box-shadow: 0 0 0 3px rgba(147, 197, 253, 0.5);
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
  }
  
  /* Badges */
  .badge-container {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }
  
  .badge {
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
  }
  
  .badge-retail {
    background-color: #93c5fd;
    color: #1e3a8a;
  }
  
  .badge-oem {
    background-color: #a5b4fc;
    color: #3730a3;
  }
  
  .badge-distributor {
    background-color: #c4b5fd;
    color: #5b21b6;
  }
  
  .badge-industrial {
    background-color: #f9a8d4;
    color: #9d174d;
  }
  
  .badge-marine {
    background-color: #a7f3d0;
    color: #065f46;
  }
</style>
