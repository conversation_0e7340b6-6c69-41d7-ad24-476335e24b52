<script lang="ts">
  export let showModal = false;
  export let title = '';

  function closeModal() {
    showModal = false;
  }
</script>

<div class="modal-backdrop" class:show={showModal} on:click={closeModal}>
  <div class="modal-content" on:click|stopPropagation>
    <div class="modal-header">
      <h2>{title}</h2>
      <button class="close-btn" on:click={closeModal}>&times;</button>
    </div>
    <div class="modal-body">
      <slot />
    </div>
  </div>
</div>

<style>
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .modal-backdrop.show {
    display: flex;
  }

  .modal-content {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    min-width: 400px;
    max-width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: #2196f3;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0.5rem;
  }

  .close-btn:hover {
    color: #333;
  }

  .modal-body {
    position: relative;
  }
</style>
