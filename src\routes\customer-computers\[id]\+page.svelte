<script lang="ts">
  import { enhance } from '$app/forms';
  import { goto } from '$app/navigation';
  
  // Get data from server
  export let data;
  
  // Component state
  let computer = data.computer;
  let isEditing = false;
  let isSubmitting = false;
  let formError = '';
  let successMessage = '';
  
  // Form data for editing
  let formData = {
    name: computer.name || '',
    serialNumber: computer.serialNumber || '',
    model: computer.model || '',
    productType: computer.productType || '',
    productDesignation: computer.productDesignation || '',
    engineType: computer.engineType || '',
    operatingHours: computer.operatingHours || 0,
    installationDate: computer.installationDate ? formatDateForInput(computer.installationDate) : '',
    manufactureDate: computer.manufactureDate ? formatDateForInput(computer.manufactureDate) : '',
    isActive: computer.isActive !== false, // Default to true if undefined
    notes: computer.notes || ''
  };
  
  // Format date for display
  function formatDate(date: Date | null | undefined): string {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString();
  }
  
  // Format date for input fields (YYYY-MM-DD)
  function formatDateForInput(date: Date | null | undefined): string {
    if (!date) return '';
    const d = new Date(date);
    return d.toISOString().split('T')[0];
  }
  
  // Handle form submission result
  function handleFormResult(result: any) {
    isSubmitting = false;
    
    if (result.success) {
      successMessage = result.message || 'Operation completed successfully';
      isEditing = false;
      
      // Redirect if specified
      if (result.redirect) {
        goto(result.redirect);
      } else {
        // Reload the page to refresh the data
        window.location.reload();
      }
    } else {
      formError = result.error || 'An error occurred';
    }
  }
  
  // Toggle edit mode
  function toggleEditMode() {
    isEditing = !isEditing;
    
    if (isEditing) {
      // Reset form data to current computer values
      formData = {
        name: computer.name || '',
        serialNumber: computer.serialNumber || '',
        model: computer.model || '',
        productType: computer.productType || '',
        productDesignation: computer.productDesignation || '',
        engineType: computer.engineType || '',
        operatingHours: computer.operatingHours || 0,
        installationDate: computer.installationDate ? formatDateForInput(computer.installationDate) : '',
        manufactureDate: computer.manufactureDate ? formatDateForInput(computer.manufactureDate) : '',
        isActive: computer.isActive !== false,
        notes: computer.notes || ''
      };
    }
  }
  
  // Handle delete confirmation
  function confirmDelete() {
    if (confirm('Are you sure you want to delete this computer? This action cannot be undone.')) {
      document.getElementById('delete-form')?.requestSubmit();
    }
  }
</script>

<div class="container">
  <header>
    <div class="header-content">
      <h1>{computer.name}</h1>
      {#if computer.customer}
        <div class="customer-info">
          <span class="customer-name">{computer.customer.companyName || computer.customer.name}</span>
          <span class="customer-type {computer.customer.type.toLowerCase().replace(' ', '-')}">
            {computer.customer.type}
          </span>
        </div>
      {/if}
    </div>
    
    <div class="header-actions">
      <a href="/customer-computers" class="back-button">
        ← Back to List
      </a>
      
      {#if !isEditing}
        <button class="edit-button" on:click={toggleEditMode}>
          Edit Computer
        </button>
        <button class="delete-button" on:click={confirmDelete}>
          Delete Computer
        </button>
      {/if}
    </div>
  </header>
  
  {#if formError}
    <div class="alert error">
      {formError}
      <button class="close-alert" on:click={() => formError = ''}>×</button>
    </div>
  {/if}
  
  {#if successMessage}
    <div class="alert success">
      {successMessage}
      <button class="close-alert" on:click={() => successMessage = ''}>×</button>
    </div>
  {/if}
  
  {#if isEditing}
    <div class="edit-form">
      <form 
        method="POST" 
        action="?/updateComputer"
        use:enhance={() => {
          isSubmitting = true;
          return async ({ result }) => {
            handleFormResult(result.data);
          };
        }}
      >
        <div class="form-grid">
          <div class="form-group">
            <label for="name">Name</label>
            <input 
              type="text"
              id="name"
              name="name"
              bind:value={formData.name}
            />
          </div>
          
          <div class="form-group">
            <label for="serialNumber">Serial Number*</label>
            <input 
              type="text"
              id="serialNumber"
              name="serialNumber"
              bind:value={formData.serialNumber}
              required
            />
          </div>
          
          <div class="form-group">
            <label for="model">Model*</label>
            <input 
              type="text"
              id="model"
              name="model"
              bind:value={formData.model}
              required
            />
          </div>
          
          <div class="form-group">
            <label for="productType">Product Type*</label>
            <input 
              type="text"
              id="productType"
              name="productType"
              bind:value={formData.productType}
              required
            />
          </div>
          
          <div class="form-group">
            <label for="productDesignation">Product Designation</label>
            <input 
              type="text"
              id="productDesignation"
              name="productDesignation"
              bind:value={formData.productDesignation}
            />
          </div>
          
          <div class="form-group">
            <label for="engineType">Engine Type</label>
            <input 
              type="text"
              id="engineType"
              name="engineType"
              bind:value={formData.engineType}
            />
          </div>
          
          <div class="form-group">
            <label for="operatingHours">Operating Hours*</label>
            <input 
              type="number"
              id="operatingHours"
              name="operatingHours"
              bind:value={formData.operatingHours}
              min="0"
              required
            />
          </div>
          
          <div class="form-group">
            <label for="installationDate">Installation Date</label>
            <input 
              type="date"
              id="installationDate"
              name="installationDate"
              bind:value={formData.installationDate}
            />
          </div>
          
          <div class="form-group">
            <label for="manufactureDate">Manufacture Date</label>
            <input 
              type="date"
              id="manufactureDate"
              name="manufactureDate"
              bind:value={formData.manufactureDate}
            />
          </div>
          
          <div class="form-group">
            <label class="checkbox-label">
              <input 
                type="checkbox"
                name="isActive"
                bind:checked={formData.isActive}
              />
              Active
            </label>
          </div>
          
          <div class="form-group full-width">
            <label for="notes">Notes</label>
            <textarea 
              id="notes"
              name="notes"
              bind:value={formData.notes}
              rows="3"
            ></textarea>
          </div>
        </div>
        
        <div class="form-actions">
          <button type="button" class="btn-secondary" on:click={toggleEditMode}>Cancel</button>
          <button type="submit" class="btn-primary" disabled={isSubmitting}>
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  {:else}
    <div class="computer-details">
      <div class="info-grid">
        <div class="info-card">
          <h2>Basic Information</h2>
          <div class="info-content">
            <div class="info-row">
              <span class="label">Serial Number</span>
              <span class="value">{computer.serialNumber || 'N/A'}</span>
            </div>
            <div class="info-row">
              <span class="label">Model</span>
              <span class="value">{computer.model || 'N/A'}</span>
            </div>
            <div class="info-row">
              <span class="label">Product Type</span>
              <span class="value">{computer.productType || 'N/A'}</span>
            </div>
            <div class="info-row">
              <span class="label">Product Designation</span>
              <span class="value">{computer.productDesignation || 'N/A'}</span>
            </div>
            <div class="info-row">
              <span class="label">Status</span>
              <span class="value">
                <span class="status-badge {computer.isActive ? 'active' : 'inactive'}">
                  {computer.isActive ? 'Active' : 'Inactive'}
                </span>
              </span>
            </div>
          </div>
        </div>
        
        <div class="info-card">
          <h2>Technical Details</h2>
          <div class="info-content">
            <div class="info-row">
              <span class="label">Engine Type</span>
              <span class="value">{computer.engineType || 'N/A'}</span>
            </div>
            <div class="info-row">
              <span class="label">Operating Hours</span>
              <span class="value">{computer.operatingHours || 0}</span>
            </div>
            <div class="info-row">
              <span class="label">Installation Date</span>
              <span class="value">{formatDate(computer.installationDate)}</span>
            </div>
            <div class="info-row">
              <span class="label">Manufacture Date</span>
              <span class="value">{formatDate(computer.manufactureDate)}</span>
            </div>
          </div>
        </div>
        
        <div class="info-card">
          <h2>System Information</h2>
          <div class="info-content">
            <div class="info-row">
              <span class="label">Created</span>
              <span class="value">{formatDate(computer.createdAt)}</span>
            </div>
            <div class="info-row">
              <span class="label">Last Updated</span>
              <span class="value">{formatDate(computer.updatedAt)}</span>
            </div>
          </div>
        </div>
        
        {#if computer.notes}
          <div class="info-card full-width">
            <h2>Notes</h2>
            <div class="info-content">
              <p class="notes">{computer.notes}</p>
            </div>
          </div>
        {/if}
      </div>
    </div>
  {/if}
  
  <!-- Hidden delete form -->
  <form 
    id="delete-form"
    method="POST" 
    action="?/deleteComputer"
    use:enhance={() => {
      isSubmitting = true;
      return async ({ result }) => {
        handleFormResult(result.data);
      };
    }}
    style="display: none;"
  ></form>
</div>

<style>
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
  }
  
  header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .header-content h1 {
    margin: 0 0 0.5rem;
    font-size: 1.8rem;
    color: #2d3748;
  }
  
  .customer-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }
  
  .customer-name {
    font-size: 1rem;
    color: #4a5568;
  }
  
  .customer-type {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    background-color: #ebf8ff;
    color: #2b6cb0;
  }
  
  .header-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
  }
  
  .back-button {
    padding: 0.5rem 1rem;
    background: none;
    border: none;
    color: #4299e1;
    cursor: pointer;
    font-size: 1rem;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .back-button:hover {
    text-decoration: underline;
  }
  
  .edit-button {
    padding: 0.5rem 1rem;
    background-color: #4299e1;
    color: white;
    border: none;
    border-radius: 0.25rem;
    cursor: pointer;
    font-weight: 500;
  }
  
  .edit-button:hover {
    background-color: #3182ce;
  }
  
  .delete-button {
    padding: 0.5rem 1rem;
    background-color: #f56565;
    color: white;
    border: none;
    border-radius: 0.25rem;
    cursor: pointer;
    font-weight: 500;
  }
  
  .delete-button:hover {
    background-color: #e53e3e;
  }
  
  .alert {
    padding: 0.75rem 1rem;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .alert.error {
    background-color: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
  }
  
  .alert.success {
    background-color: #c6f6d5;
    color: #2f855a;
    border: 1px solid #9ae6b4;
  }
  
  .close-alert {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: inherit;
  }
  
  .edit-form {
    background-color: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }
  
  .form-group {
    margin-bottom: 1rem;
  }
  
  .form-group.full-width {
    grid-column: 1 / -1;
  }
  
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #4a5568;
  }
  
  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
  }
  
  input[type="text"],
  input[type="number"],
  input[type="date"],
  textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    font-size: 0.875rem;
  }
  
  input[type="checkbox"] {
    margin: 0;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    margin-top: 1.5rem;
  }
  
  .btn-primary,
  .btn-secondary {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.25rem;
    cursor: pointer;
    font-weight: 500;
  }
  
  .btn-primary {
    background-color: #4299e1;
    color: white;
  }
  
  .btn-primary:hover {
    background-color: #3182ce;
  }
  
  .btn-primary:disabled {
    background-color: #a0aec0;
    cursor: not-allowed;
  }
  
  .btn-secondary {
    background-color: #e2e8f0;
    color: #4a5568;
  }
  
  .btn-secondary:hover {
    background-color: #cbd5e0;
  }
  
  .computer-details {
    margin-bottom: 2rem;
  }
  
  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
  }
  
  .info-card {
    background-color: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    overflow: hidden;
  }
  
  .info-card.full-width {
    grid-column: 1 / -1;
  }
  
  .info-card h2 {
    margin: 0;
    padding: 1rem;
    background-color: #edf2f7;
    font-size: 1.1rem;
    color: #2d3748;
    border-bottom: 1px solid #e2e8f0;
  }
  
  .info-content {
    padding: 1rem;
  }
  
  .info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
  }
  
  .info-row:last-child {
    margin-bottom: 0;
  }
  
  .label {
    font-weight: 500;
    color: #4a5568;
  }
  
  .value {
    color: #2d3748;
    text-align: right;
  }
  
  .status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
  }
  
  .status-badge.active {
    background-color: #c6f6d5;
    color: #2f855a;
  }
  
  .status-badge.inactive {
    background-color: #fed7d7;
    color: #c53030;
  }
  
  .notes {
    margin: 0;
    white-space: pre-wrap;
    color: #4a5568;
  }
</style>
