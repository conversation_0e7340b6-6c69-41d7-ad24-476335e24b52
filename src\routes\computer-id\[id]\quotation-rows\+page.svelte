<!-- @component
This component displays and manages quotation rows for a specific computer.
Uses TableGrid for layout and provides CRUD operations for quotation rows.
-->
<script lang="ts">
  import { page } from '$app/stores';
  import TableGrid from '$lib/components/grids/TableGrid.svelte';
  import Modal from '$lib/components/Modal.svelte';
  import { invalidateAll } from '$app/navigation';

  interface Quote {
    _id: string;
    QuoteNumber?: string;
    status?: string;
    contractLength?: number;
    totalAmount?: number;
  }

  interface PageData {
    rows: QuotationRow[];
    quotes: Quote[];
    computerId: string;
    activeQuoteId?: string;
    activeQuote?: Quote;
  }

  export let data: PageData;

  interface QuotationRow {
    _id: string;
    ServiceCode: string;
    PartNumber: string | null;
    Description: string;
    Quantity: number;
    'Unit of Measure': string;
    UnitPrice: number;
    TotalPrice: number;
    QuoteId: string;
    ComputerId: string;
  }

  let showModal = false;
  let editingRow: QuotationRow | null = null;
  let formData: QuotationRow = {
    _id: '',
    ServiceCode: '',
    PartNumber: '',
    Description: '',
    Quantity: 0,
    'Unit of Measure': '',
    UnitPrice: 0,
    TotalPrice: 0,
    QuoteId: '',
    ComputerId: $page.params.id
  };

  function editRow(row: QuotationRow) {
    editingRow = row;
    formData = { ...row };
    showModal = true;
  }

  function addNewRow() {
    editingRow = null;
    formData = {
      _id: '',
      ServiceCode: '',
      PartNumber: '',
      Description: '',
      Quantity: 0,
      'Unit of Measure': '',
      UnitPrice: 0,
      TotalPrice: 0,
      QuoteId: data.activeQuoteId || data.quotes[0]?._id || '',
      ComputerId: $page.params.id
    };
    showModal = true;
  }

  async function deleteRow(id: string) {
    if (!confirm('Are you sure you want to delete this row?')) return;

    try {
      const res = await fetch(`/api/computer-id/${$page.params.id}/quotation-rows/${id}`, {
        method: 'DELETE'
      });

      if (!res.ok) throw new Error('Failed to delete row');
      await invalidateAll();
    } catch (error) {
      console.error('Error deleting row:', error);
      alert('Failed to delete row');
    }
  }

  async function handleSubmit() {
    const endpoint = `/api/computer-id/${$page.params.id}/quotation-rows${editingRow ? `/${editingRow._id}` : ''}`;
    const method = editingRow ? 'PUT' : 'POST';

    try {
      formData.TotalPrice = formData.Quantity * formData.UnitPrice;
      
      const res = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      if (!res.ok) throw new Error(`Failed to ${editingRow ? 'update' : 'add'} row`);
      
      closeModal();
      await invalidateAll();
    } catch (error) {
      console.error('Error submitting form:', error);
      alert(`Failed to ${editingRow ? 'update' : 'add'} row`);
    }
  }

  function closeModal() {
    showModal = false;
    editingRow = null;
    formData = {
      _id: '',
      ServiceCode: '',
      PartNumber: '',
      Description: '',
      Quantity: 0,
      'Unit of Measure': '',
      UnitPrice: 0,
      TotalPrice: 0,
      QuoteId: data.activeQuoteId || data.quotes[0]?._id || '',
      ComputerId: $page.params.id
    };
  }

  $: totalAmount = data.rows.reduce((sum: number, row: QuotationRow) => sum + (row.TotalPrice || 0), 0);
</script>

<TableGrid>
  <div slot="header">
    <div class="header-content">
      <div class="header-left">
        <h2>Quotation Rows for Computer {$page.params.id}</h2>
        <div class="navigation-links">
          <a href="/computer-id/{$page.params.id}/quote-list" class="nav-link">← Back to Quotes</a>
          {#if data.activeQuoteId}
            <div class="quote-info">
              <span class="quote-filter">
                Quote: {data.activeQuote?.QuoteNumber || data.activeQuoteId}
                {#if data.activeQuote}
                  <span class="quote-details">
                    ({data.activeQuote.status} • {data.activeQuote.contractLength} months • £{data.activeQuote.totalAmount?.toFixed(2) || '0.00'})
                  </span>
                {/if}
              </span>
              <a href="/computer-id/{$page.params.id}/quotation-rows" class="nav-link">Show All Rows</a>
            </div>
          {/if}
        </div>
      </div>
      <button class="primary-button" on:click={addNewRow}>Add New Row</button>
    </div>
  </div>
  <div slot="content">
    <div class="grid-container">
      <div class="grid-header">
        <div>Service Code</div>
        <div>Part Number</div>
        <div>Description</div>
        <div>Quantity</div>
        <div>Unit of Measure</div>
        <div>Unit Price</div>
        <div>Total Price</div>
        <div>Actions</div>
      </div>
      {#each data.rows as row (row._id)}
        <div class="grid-row">
          <div>{row.ServiceCode}</div>
          <div>{row.PartNumber || 'N/A'}</div>
          <div>{row.Description}</div>
          <div>{row.Quantity}</div>
          <div>{row['Unit of Measure']}</div>
          <div>{row.UnitPrice?.toFixed(2)}</div>
          <div>{row.TotalPrice?.toFixed(2)}</div>
          <div class="actions">
            <button class="edit-button" on:click={() => editRow(row)}>Edit</button>
            <button class="delete-button" on:click={() => deleteRow(row._id)}>Delete</button>
            <a href="/quote-demo?rowId={row._id}" class="view-button">View Demo</a>
          </div>
        </div>
      {:else}
        <div class="no-data">No quotation rows found</div>
      {/each}
      {#if data.rows.length > 0}
        <div class="total-row">
          <div class="total-label">Total Amount:</div>
          <div class="total-amount">{totalAmount.toFixed(2)}</div>
        </div>
      {/if}
    </div>
  </div>
</TableGrid>

{#if showModal}
  <Modal on:close={closeModal}>
    <h3>{editingRow ? 'Edit' : 'Add'} Quotation Row</h3>
    <form on:submit|preventDefault={handleSubmit}>
      <div class="form-group">
        <label for="serviceCode">Service Code</label>
        <input 
          type="text" 
          id="serviceCode" 
          bind:value={formData.ServiceCode} 
          required
        />
      </div>
      <div class="form-group">
        <label for="partNumber">Part Number</label>
        <input 
          type="text" 
          id="partNumber" 
          bind:value={formData.PartNumber}
        />
      </div>
      <div class="form-group">
        <label for="description">Description</label>
        <input 
          type="text" 
          id="description" 
          bind:value={formData.Description} 
          required
        />
      </div>
      <div class="form-group">
        <label for="quantity">Quantity</label>
        <input 
          type="number" 
          id="quantity" 
          bind:value={formData.Quantity} 
          required
          min="0"
          step="1"
        />
      </div>
      <div class="form-group">
        <label for="unitOfMeasure">Unit of Measure</label>
        <input 
          type="text" 
          id="unitOfMeasure" 
          bind:value={formData['Unit of Measure']} 
          required
        />
      </div>
      <div class="form-group">
        <label for="unitPrice">Unit Price</label>
        <input 
          type="number" 
          id="unitPrice" 
          bind:value={formData.UnitPrice} 
          required
          min="0"
          step="0.01"
        />
      </div>
      {#if data.quotes.length > 0}
        <div class="form-group">
          <label for="quoteId">Quote</label>
          <select id="quoteId" bind:value={formData.QuoteId}>
            {#each data.quotes as quote}
              <option value={quote._id}>
                {quote.QuoteNumber || quote._id}
              </option>
            {/each}
          </select>
        </div>
      {/if}
      <div class="buttons">
        <button type="submit" class="primary-button">{editingRow ? 'Update' : 'Add'}</button>
        <button type="button" class="secondary-button" on:click={closeModal}>Cancel</button>
      </div>
    </form>
  </Modal>
{/if}

<style>
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 1rem;
  }

  .header-left {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .navigation-links {
    display: flex;
    gap: 1rem;
    margin-top: 0.25rem;
    align-items: center;
  }

  .nav-link {
    color: #4a90e2;
    text-decoration: none;
    font-size: 0.9rem;
  }

  .nav-link:hover {
    text-decoration: underline;
  }

  .quote-info {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .quote-filter {
    color: #666;
    font-size: 0.9rem;
    padding: 0.25rem 0.5rem;
    background-color: #f4f4f4;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .quote-details {
    color: #888;
    font-size: 0.85rem;
  }

  .grid-container {
    display: grid;
    gap: 0.5rem;
    padding: 1rem;
  }

  .grid-header {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 0.5rem;
    font-weight: bold;
    padding: 0.5rem;
    background-color: #f4f4f4;
    border-radius: 4px;
  }

  .grid-row {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 0.5rem;
    padding: 0.5rem;
    align-items: center;
    border-bottom: 1px solid #eee;
  }

  .actions {
    display: flex;
    gap: 0.5rem;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }

  .form-group input,
  .form-group select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
  }

  .buttons {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
  }

  .no-data {
    text-align: center;
    padding: 2rem;
    grid-column: 1 / -1;
    color: #666;
  }

  .total-row {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 0.5rem;
    padding: 1rem 0.5rem;
    margin-top: 1rem;
    border-top: 2px solid #ddd;
    font-weight: bold;
  }

  .total-label {
    grid-column: 6;
    text-align: right;
  }

  .total-amount {
    grid-column: 7;
  }

  .primary-button {
    background-color: #4a90e2;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
  }

  .primary-button:hover {
    background-color: #357abd;
  }

  .secondary-button {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
  }

  .secondary-button:hover {
    background-color: #5a6268;
  }

  .edit-button {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
  }

  .edit-button:hover {
    background-color: #218838;
  }

  .delete-button {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
  }

  .delete-button:hover {
    background-color: #c82333;
  }

  .view-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
  }

  .view-button:hover {
    background-color: #0056b3;
  }
</style>
