import { error, json } from '@sveltejs/kit';
import { MongoClient, ObjectId } from 'mongodb';

/**
 * GET handler for fetching computers for a specific customer
 * @param {import('@sveltejs/kit').RequestEvent} event
 */
export async function GET({ params }) {
  try {
    // Get customer ID from params
    const customerId = params.id;
    
    // Validate customer ID format
    if (!customerId || !ObjectId.isValid(customerId)) {
      throw error(400, 'Invalid customer ID');
    }

    // Connect to MongoDB
    const uri = 'mongodb://localhost:27017';
    const client = new MongoClient(uri);
    await client.connect();
    
    // Get the CustomerComputers collection from ServiceContracts database
    const db = client.db('ServiceContracts');
    const collection = db.collection('CustomerComputers');
    
    // Find computers for this customer
    const computers = await collection
      .find({ customerId: new ObjectId(customerId) })
      .toArray();
    
    // Convert ObjectIds to strings for client-side use
    const computersForClient = computers.map(computer => ({
      ...computer,
      _id: computer._id.toString(),
      customerId: computer.customerId.toString()
    }));
    
    // Close the MongoDB connection
    await client.close();
    
    // Return the computers as JSON
    return json({
      computers: computersForClient
    });
  } catch (err) {
    console.error(`Error fetching computers for customer: ${err.message}`);
    throw error(500, { message: 'Failed to fetch computers' });
  }
}
