<!-- @component
A page component for displaying quotation rows
-->
<script lang="ts">
  import type { QuotationRow } from '$lib/types';
  import { formatCurrency } from '$lib/utils/formatters';
  import { page } from '$app/stores';

  interface PageData {
    rows: QuotationRow[];
    currentQuote: {
      _id: string;
      QuoteNumber: string;
      [key: string]: any;
    };
    totalAmount: number;
    totalSSP: number;
    totalOemImporter: number;
    totalFleetOwner: number;
  }

  /** @type {import('./$types').PageData} */
  export let data: PageData;

  // Get quote ID from URL params
  $: currentQuoteId = $page.params.quoteId;

  // Group rows by RowType
  $: groupedRowsByType = data.rows.reduce((groups, row) => {
    const type = row.RowType || 'Other';
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push(row);
    return groups;
  }, {} as Record<string, QuotationRow[]>);

  async function handleDataUpdate() {
    // Force a full page reload to get fresh data
    window.location.reload();
  }
</script>

<div class="container">
  <div class="header">
    <div class="header-content">
      <div class="header-left">
        <h1>Quote Details</h1>
      </div>
      <div class="header-right">
        <span class="quote-info">Quote Number: {data.currentQuote.QuoteNumber}</span>
      </div>
    </div>
  </div>

  <div class="summary-section">
    <div class="summary-card">
      <h3>Quote Summary</h3>
      <div class="summary-grid">
        <div>
          <span class="summary-label">Total Rows:</span>
          <span class="summary-value">{data.rows.length}</span>
        </div>
        <div>
          <span class="summary-label">Total KCOS:</span>
          <span class="summary-value">{formatCurrency(data.totalAmount)}</span>
        </div>
        <div>
          <span class="summary-label">Total OEM Importer:</span>
          <span class="summary-value">{formatCurrency(data.totalOemImporter)}</span>
        </div>
        <div>
          <span class="summary-label">Total Fleet Owner:</span>
          <span class="summary-value">{formatCurrency(data.totalFleetOwner)}</span>
        </div>
        <div>
          <span class="summary-label">Total SSP:</span>
          <span class="summary-value">{formatCurrency(data.totalSSP)}</span>
        </div>
      </div>
    </div>
  </div>

  <div class="rows-section">
    {#if data.rows.length === 0}
      <div class="empty-state">
        <p>No rows found for this quote.</p>
        <p>Click the "Add Row" button to create a new row.</p>
      </div>
    {:else}
      <div class="add-button-container">
        <button class="add-button">Add Row</button>
      </div>
      
      {#each Object.entries(groupedRowsByType) as [type, rows]}
        <div class="group-section">
          <h2 class="group-title">{type}</h2>
          
          <div class="table-responsive">
            <table class="quotation-table">
              <thead>
                <tr>
                  <th>Level</th>
                  <th>Package</th>
                  <th>Service ID</th>
                  <th>Service</th>
                  <th>Included in Package Pricing?</th>
                  <th>Required</th>
                  <th>KCOS</th>
                  <th>OEM Importer</th>
                  <th>Fleet Owner</th>
                  <th>SSP</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {#each rows as row (row._id)}
                  <tr>
                    <td>{row.RowType}</td>
                    <td>{row.PackageName || '-'}</td>
                    <td>{row.RowOrder || '-'}</td>
                    <td>{row.ServiceActivity || '-'}</td>
                    <td>{row.IncludeInOffer ? 'Yes' : 'No'}</td>
                    <td>{row.Required ? 'Mandatory' : 'No'}</td>
                    <td class="number-cell">{row.Cost ? formatCurrency(row.Cost) : '€ -'}</td>
                    <td class="number-cell">{row.OemImporter ? formatCurrency(row.OemImporter) : '€ -'}</td>
                    <td class="number-cell">{row.FleetOwner ? formatCurrency(row.FleetOwner) : '€ -'}</td>
                    <td class="number-cell">{row.SSP ? formatCurrency(row.SSP) : '€ -'}</td>
                    <td class="actions">
                      <button class="action-button edit">Edit</button>
                      <button class="action-button delete">Delete</button>
                    </td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        </div>
      {/each}
    {/if}
  </div>
</div>

<style>
  .container {
    display: grid;
    grid-template-rows: auto auto 1fr;
    gap: 2rem;
    max-width: 95%;
    margin: 0 auto;
    padding: 2rem;
  }

  .header {
    display: grid;
  }

  .header-content {
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    gap: 1rem;
  }

  .header-left h1 {
    margin: 0;
    font-size: 2rem;
    color: #2c3e50;
  }

  .header-right .quote-info {
    font-size: 1.2rem;
    color: #2c3e50;
    font-weight: 500;
  }

  .summary-section {
    display: grid;
  }

  .summary-card {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  }

  .summary-card h3 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
  }

  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .summary-label {
    display: block;
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 0.25rem;
  }

  .summary-value {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1rem;
  }

  .rows-section {
    display: grid;
    gap: 2rem;
  }

  .empty-state {
    display: grid;
    place-items: center;
    gap: 0.5rem;
    padding: 2rem;
    background-color: #f8f9fa;
    border-radius: 8px;
  }

  .empty-state p {
    margin: 0;
    color: #6c757d;
  }

  .add-button-container {
    display: grid;
    justify-content: start;
    margin-bottom: 1rem;
  }

  .add-button {
    padding: 0.75rem 1.5rem;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: background-color 0.2s, transform 0.1s;
  }

  .add-button:hover {
    background-color: #218838;
    transform: translateY(-1px);
  }

  .group-section {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .group-title {
    margin: 0;
    padding: 1rem;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    color: #2c3e50;
    font-size: 1.25rem;
  }

  .table-responsive {
    overflow-x: auto;
    width: 100%;
  }

  .quotation-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #dee2e6;
    font-size: 0.875rem;
  }

  .quotation-table th,
  .quotation-table td {
    padding: 0.75rem;
    text-align: left;
    border: 1px solid #dee2e6;
  }

  .quotation-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
  }

  .quotation-table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
  }

  .quotation-table tbody tr:hover {
    background-color: #e9ecef;
  }

  .number-cell {
    text-align: right;
    white-space: nowrap;
  }

  .actions {
    display: flex;
    gap: 0.5rem;
  }

  .action-button {
    padding: 0.5rem;
    border: none;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;
  }

  .action-button:hover {
    transform: translateY(-1px);
  }

  .edit {
    background-color: #e9ecef;
    color: #495057;
  }

  .edit:hover {
    background-color: #dee2e6;
  }

  .delete {
    background-color: #dc3545;
    color: white;
  }

  .delete:hover {
    background-color: #c82333;
  }
</style>
