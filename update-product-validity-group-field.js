const { MongoClient } = require('mongodb');

async function updateFieldName() {
    const uri = 'mongodb://localhost:27017';
    const client = new MongoClient(uri);

    try {
        await client.connect();
        const db = client.db('ServiceContracts');
        const collection = db.collection('ProductValidityGroup');

        // Update all documents to rename the field
        const result = await collection.updateMany(
            { "Product Validity GRoup": { $exists: true } },
            [{
                $set: {
                    "ProductValidityGroup": "$Product Validity GRoup",
                    "Product Validity GRoup": "$$REMOVE"
                }
            }]
        );

        console.log(`Updated ${result.modifiedCount} documents`);
    } catch (error) {
        console.error('Error updating field name:', error);
    } finally {
        await client.close();
    }
}

updateFieldName();
