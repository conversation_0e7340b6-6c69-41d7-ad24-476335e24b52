const { MongoClient, ObjectId } = require('mongodb');

async function updateContractDates() {
    const uri = "mongodb://localhost:27017";
    const client = new MongoClient(uri);

    try {
        await client.connect();
        console.log('Connected to MongoDB');

        const db = client.db("ServiceContracts");
        const CustomerComputers = db.collection("CustomerComputers");

        // Get all customer computers
        const computers = await CustomerComputers.find({}).toArray();
        console.log(`Found ${computers.length} computers to process`);

        let updated = 0;
        for (const computer of computers) {
            // Skip if no desired contract length
            if (!computer.desiredContractLengthYears) {
                console.log(`Skipping computer ${computer._id} - no contract length specified`);
                continue;
            }

            // Calculate desired contract start date
            // If purchaseDate exists, use that as base, otherwise use current date
            const baseDate = computer.purchaseDate ? new Date(computer.purchaseDate) : new Date();
            
            // Create the desired contract start date
            const desiredContractStartDate = new Date(baseDate);
            
            // Update the document
            await CustomerComputers.updateOne(
                { _id: computer._id },
                { 
                    $set: { 
                        desiredContractStartDate: desiredContractStartDate,
                        updatedAt: new Date()
                    } 
                }
            );
            updated++;
            console.log(`Updated computer ${computer._id}`);
        }

        console.log(`Successfully updated ${updated} computers`);

    } catch (error) {
        console.error('Error updating contract dates:', error);
    } finally {
        await client.close();
        console.log('Disconnected from MongoDB');
    }
}

updateContractDates().catch(console.error);
