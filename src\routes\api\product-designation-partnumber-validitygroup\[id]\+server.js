import { json } from '@sveltejs/kit';
import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/** @type {import('./$types').RequestHandler} */
export async function GET({ params }) {
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('ProductValidityGroupPartNumber');
    
    const item = await collection.findOne({ _id: new ObjectId(params.id) });
    
    if (!item) {
      return json({ error: 'Item not found' }, { status: 404 });
    }
    
    return json({ item });
  } catch (error) {
    console.error('Error fetching item:', error);
    return json({ error: 'Failed to fetch item' }, { status: 500 });
  }
}

/** @type {import('./$types').RequestHandler} */
export async function PUT({ params, request }) {
  try {
    const data = await request.json();
    
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('ProductValidityGroupPartNumber');
    
    // Ensure required fields are present
    if (!data.ProductName || !data.ProductDesignation) {
      return json({ error: 'ProductName and ProductDesignation are required' }, { status: 400 });
    }
    
    // Convert numeric fields if provided as strings
    if (data.ProductPartNumber && typeof data.ProductPartNumber === 'string') {
      data.ProductPartNumber = parseInt(data.ProductPartNumber);
    }
    
    if (data.ProductGroupId && typeof data.ProductGroupId === 'string') {
      data.ProductGroupId = parseInt(data.ProductGroupId);
    }
    
    if (data.Id && typeof data.Id === 'string') {
      data.Id = parseInt(data.Id);
    }
    
    // Remove _id from the update data if present
    const { _id, ...updateData } = data;
    
    // Add updated timestamp
    updateData.updatedAt = new Date();
    
    const result = await collection.updateOne(
      { _id: new ObjectId(params.id) },
      { $set: updateData }
    );
    
    if (result.matchedCount === 0) {
      return json({ error: 'Item not found' }, { status: 404 });
    }
    
    return json({ 
      success: true, 
      message: 'Item updated successfully' 
    });
  } catch (error) {
    console.error('Error updating item:', error);
    return json({ error: 'Failed to update item' }, { status: 500 });
  }
}

/** @type {import('./$types').RequestHandler} */
export async function DELETE({ params }) {
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('ProductValidityGroupPartNumber');
    
    const result = await collection.deleteOne({ _id: new ObjectId(params.id) });
    
    if (result.deletedCount === 0) {
      return json({ error: 'Item not found' }, { status: 404 });
    }
    
    return json({ 
      success: true, 
      message: 'Item deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting item:', error);
    return json({ error: 'Failed to delete item' }, { status: 500 });
  }
}
