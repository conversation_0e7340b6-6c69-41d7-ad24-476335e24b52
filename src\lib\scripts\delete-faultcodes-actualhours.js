// Script to delete all documents from ActiveFaultCodes and ActualComputerHours
import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function run() {
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    await db.collection('ActiveFaultCodes').deleteMany({});
    await db.collection('ActualComputerHours').deleteMany({});
    console.log('All documents removed from ActiveFaultCodes and ActualComputerHours.');
  } finally {
    await client.close();
  }
}

run().catch(console.error);
