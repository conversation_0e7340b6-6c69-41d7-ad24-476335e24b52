import { MongoClient, ObjectId } from 'mongodb';
import { error, fail } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

/** @type {import('./$types').PageServerLoad} */
export async function load({ url }) {
  console.log('🚀 ServicePlanProductDesignation load function started');
  console.log('📍 URL:', url.href);
  console.log('🔗 MongoDB URI:', uri);
  console.log('🗄️ Database name:', dbName);

  const client = new MongoClient(uri);

  try {
    console.log('🔌 Attempting to connect to MongoDB...');
    await client.connect();
    console.log('✅ MongoDB connection successful');

    const db = client.db(dbName);
    console.log('📊 Database object created');

    // Test if collection exists
    console.log('🔍 Checking if ServicePlanProductDesignation collection exists...');
    const collections = await db.listCollections({ name: 'ServicePlanProductDesignation' }).toArray();
    console.log('📋 Collection check result:', collections.length > 0 ? 'EXISTS' : 'NOT FOUND');

    if (collections.length === 0) {
      console.error('❌ ServicePlanProductDesignation collection does not exist!');
      throw new Error('ServicePlanProductDesignation collection not found');
    }

    // Get unique ProductValidityGroups for the dropdown
    console.log('🔍 Fetching distinct productDesignation values...');
    const productValidityGroups = await db.collection('ServicePlanProductDesignation')
      .distinct('productDesignation');
    console.log('📊 Found', productValidityGroups.length, 'distinct ProductDesignations');
    console.log('📝 First 5 ProductDesignations:', productValidityGroups.slice(0, 5));

    // Only load items if a specific ProductValidityGroup is selected
    const selectedGroup = url.searchParams.get('productValidityGroup');
    console.log('🎯 Selected ProductValidityGroup:', selectedGroup || 'NONE');

    let items = [];

    if (selectedGroup) {
      console.log('🔍 Fetching items for selected group:', selectedGroup);
      items = await db.collection('ServicePlanProductDesignation')
        .find({ productDesignation: selectedGroup })
        .sort({ accHours: 1 })
        .toArray();
      console.log('📊 Found', items.length, 'items for selected group');

      // Convert ObjectIds to strings for client-side use
      console.log('🔄 Converting ObjectIds to strings...');
      items = items.map(item => ({
        ...item,
        _id: item._id.toString()
      }));
      console.log('✅ ObjectId conversion completed');
    }

    const result = {
      items,
      productValidityGroups: productValidityGroups.sort(),
      selectedProductValidityGroup: selectedGroup || ''
    };

    console.log('📤 Returning data:', {
      itemsCount: result.items.length,
      productValidityGroupsCount: result.productValidityGroups.length,
      selectedGroup: result.selectedProductValidityGroup
    });

    return result;
  } catch (err) {
    console.error('❌ ERROR in load function:', err);
    console.error('📍 Error name:', err.name);
    console.error('📍 Error message:', err.message);
    console.error('📍 Error stack:', err.stack);
    throw error(500, `Failed to load service plan data: ${err.message}`);
  } finally {
    console.log('🔌 Closing MongoDB connection...');
    await client.close();
    console.log('✅ MongoDB connection closed');
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  create: async ({ request }) => {
    const client = new MongoClient(uri);

    try {
      const data = await request.formData();

      // Extract form data
      const itemData = {
        productDesignation: data.get('productDesignation')?.toString() || '',
        serviceCode: data.get('serviceCode')?.toString() || '',
        actionType: data.get('actionType')?.toString() || '',
        activityPurpose: data.get('activityPurpose')?.toString() || '',
        serviceActivityLabel: data.get('serviceActivityLabel')?.toString() || '',
        partNumber: parseInt(data.get('partNumber')?.toString() || '0'),
        unitOfMeasure: data.get('unitOfMeasure')?.toString() || '',
        quantity: parseInt(data.get('quantity')?.toString() || '0'),
        accHours: parseInt(data.get('accHours')?.toString() || '0'),
        frequency: parseInt(data.get('frequency')?.toString() || '1000'),
        sequenceNumber: parseInt(data.get('sequenceNumber')?.toString() || '1'),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Validate required fields
      if (!itemData.productDesignation || !itemData.serviceCode || !itemData.actionType) {
        return fail(400, {
          error: 'Product Designation, Service Code, and Action Type are required'
        });
      }

      await client.connect();
      const db = client.db(dbName);

      // Insert the new item
      const result = await db.collection('ServicePlanProductDesignation').insertOne(itemData);

      if (!result.acknowledged) {
        return fail(500, {
          error: 'Failed to create service plan item'
        });
      }

      return {
        success: true,
        message: 'Service plan item created successfully'
      };

    } catch (err) {
      console.error('Error creating service plan item:', err);
      return fail(500, {
        error: 'Failed to create service plan item'
      });
    } finally {
      await client.close();
    }
  }
};


