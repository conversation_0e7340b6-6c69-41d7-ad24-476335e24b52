<script>
  import { onMount } from 'svelte';
  import { enhance } from '$app/forms';
  import { page } from '$app/stores';
  import { CUSTOMER_TYPES, CUSTOMER_DIVISIONS, DEFAULT_CUSTOMER_DIVISION, VERSATILE_OPTIONS } from '$lib/constants';
  import { getCollection } from '$lib/db/mongo';
  
  /** @typedef {Object} CustomerData
   * @property {string} _id - MongoDB ObjectId as string
   * @property {string} companyName - Company name
   * @property {string} email - Email address
   * @property {string} phone - Phone number
   * @property {string} address - Street address
   * @property {string} city - City
   * @property {string} country - Country
   * @property {string} type - Customer type
   * @property {string} division - Customer division (Marine or Industrial)
   * @property {string[]} versatile - Versatile options
   * @property {string} notes - Additional notes
   * @property {number} computerCount - Number of associated computers
   */

  /** @type {import('./$types').PageData} */
  export let data;

  // Get the selected item ID from URL
  let itemId = $page.url.searchParams.get('itemId');
  let collection = $page.url.searchParams.get('collection') || 'Customers';
  
  // State variables
  let item = null;
  let showForm = false;
  let showDeleteConfirm = false;
  let editingItem = null;
  
  // Form data
  let formData = {
    companyName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    country: '',
    type: 'Retail',
    division: DEFAULT_CUSTOMER_DIVISION,
    versatile: [],
    notes: '',
    computerCount: 0
  };

  // Load the selected item
  async function loadItem() {
    if (!itemId) return;
    
    try {
      const collection = await getCollection(collection);
      item = await collection.findOne({ _id: new ObjectId(itemId) });
      
      if (!item) {
        throw error(404, 'Item not found');
      }
      
      // Convert ObjectId to string for display
      item._id = item._id.toString();
      
      // Initialize form data with item data
      formData = { ...item };
      editingItem = { ...item };
    } catch (err) {
      console.error('Error loading item:', err);
    }
  }

  // Lifecycle
  onMount(() => {
    loadItem();
  });

  // Form handling
  function editItem() {
    editingItem = { ...item };
    showForm = true;
  }

  async function updateItem() {
    try {
      const collection = await getCollection(collection);
      
      // Prepare update data
      const updateData = { ...formData };
      delete updateData._id;
      
      // Update in database
      await collection.updateOne(
        { _id: new ObjectId(itemId) },
        { $set: updateData }
      );
      
      // Update local state
      item = { ...item, ...updateData };
      editingItem = null;
      showForm = false;
    } catch (err) {
      console.error('Error updating item:', err);
    }
  }

  async function deleteItem() {
    try {
      const collection = await getCollection(collection);
      await collection.deleteOne({ _id: new ObjectId(itemId) });
      
      // Redirect back to list view
      window.location.href = `/liststd1?collection=${collection}`;
    } catch (err) {
      console.error('Error deleting item:', err);
    }
  }

  function cancelEdit() {
    showForm = false;
    editingItem = null;
  }
</script>

<div class="one-page-container">
  <div class="header-bar">
    <button 
      class="btn-secondary" 
      on:click={() => window.location.href = `/liststd1?collection=${collection}`}
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
      </svg>
      Back to List
    </button>
    
    <div class="header-actions">
      <button 
        class="btn-primary" 
        on:click={editItem}
      >
        Edit
      </button>
      <button 
        class="btn-danger" 
        on:click={() => showDeleteConfirm = true}
      >
        Delete
      </button>
    </div>
  </div>

  <!-- Display item details -->
  {#if item}
    <div class="item-details">
      <h1>{item.companyName}</h1>
      
      <div class="details-grid">
        <div class="detail-group">
          <div class="detail-item">
            <span class="label">Type:</span>
            <span class="value">{item.type}</span>
          </div>
          
          <div class="detail-item">
            <span class="label">Division:</span>
            <span class="value">{item.division}</span>
          </div>
          
          <div class="detail-item">
            <span class="label">Email:</span>
            <span class="value">{item.email || 'Not provided'}</span>
          </div>
          
          <div class="detail-item">
            <span class="label">Phone:</span>
            <span class="value">{item.phone || 'Not provided'}</span>
          </div>
          
          <div class="detail-item">
            <span class="label">Address:</span>
            <span class="value">{item.address || 'Not provided'}</span>
          </div>
          
          <div class="detail-item">
            <span class="label">City:</span>
            <span class="value">{item.city || 'Not provided'}</span>
          </div>
          
          <div class="detail-item">
            <span class="label">Country:</span>
            <span class="value">{item.country || 'Not provided'}</span>
          </div>
          
          {#if item.versatile && item.versatile.length > 0}
          <div class="detail-item">
            <span class="label">Versatile Options:</span>
            <span class="value">{item.versatile.join(', ')}</span>
          </div>
          {/if}
          
          {#if item.notes}
          <div class="detail-item full-width">
            <span class="label">Notes:</span>
            <span class="value">{item.notes}</span>
          </div>
          {/if}
        </div>
      </div>
    </div>
  {/if}

  <!-- Edit form -->
  {#if showForm}
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h2>Edit Item</h2>
          <button 
            type="button" 
            class="modal-close" 
            on:click={cancelEdit}
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form 
          class="form-grid"
          on:submit|preventDefault={updateItem}
        >
          <div class="form-group">
            <label for="companyName">Company Name</label>
            <input
              type="text"
              id="companyName"
              name="companyName"
              bind:value={formData.companyName}
              required
            />
          </div>

          <div class="form-group">
            <label for="email">Email</label>
            <input
              type="email"
              id="email"
              name="email"
              bind:value={formData.email}
            />
          </div>

          <div class="form-group">
            <label for="phone">Phone</label>
            <input
              type="text"
              id="phone"
              name="phone"
              bind:value={formData.phone}
            />
          </div>

          <div class="form-group">
            <label for="address">Address</label>
            <input
              type="text"
              id="address"
              name="address"
              bind:value={formData.address}
            />
          </div>

          <div class="form-group">
            <label for="city">City</label>
            <input
              type="text"
              id="city"
              name="city"
              bind:value={formData.city}
            />
          </div>

          <div class="form-group">
            <label for="country">Country</label>
            <input
              type="text"
              id="country"
              name="country"
              bind:value={formData.country}
            />
          </div>

          <div class="form-group">
            <label for="type">Type</label>
            <select
              id="type"
              name="type"
              bind:value={formData.type}
            >
              {#each CUSTOMER_TYPES as type}
                <option value={type}>{type}</option>
              {/each}
            </select>
          </div>

          <div class="form-group">
            <label for="division">Division</label>
            <select
              id="division"
              name="division"
              bind:value={formData.division}
            >
              {#each CUSTOMER_DIVISIONS as division}
                <option value={division}>{division}</option>
              {/each}
            </select>
          </div>

          <div class="form-group">
            <label for="versatile">Versatile Options</label>
            <select
              id="versatile"
              name="versatile"
              multiple
              bind:value={formData.versatile}
            >
              {#each VERSATILE_OPTIONS as option}
                <option value={option}>{option}</option>
              {/each}
            </select>
          </div>

          <div class="form-group full-width">
            <label for="notes">Notes</label>
            <textarea
              id="notes"
              name="notes"
              rows="3"
              bind:value={formData.notes}
            ></textarea>
          </div>

          <div class="form-actions">
            <button type="button" class="btn-secondary" on:click={cancelEdit}>Cancel</button>
            <button type="submit" class="btn-primary">Save Changes</button>
          </div>
        </form>
      </div>
    </div>
  {/if}

  <!-- Delete confirmation dialog -->
  {#if showDeleteConfirm}
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h2>Confirm Delete</h2>
          <button 
            type="button" 
            class="modal-close" 
            on:click={() => showDeleteConfirm = false}
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="modal-body">
          <p>Are you sure you want to delete this item?</p>
          <p class="warning">This action cannot be undone.</p>
        </div>

        <div class="modal-actions">
          <button 
            class="btn-secondary" 
            on:click={() => showDeleteConfirm = false}
          >
            Cancel
          </button>
          <button 
            class="btn-danger" 
            on:click={deleteItem}
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .one-page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  }

  .header-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: #f8fafc;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .header-actions {
    display: flex;
    gap: 1rem;
  }

  .item-details {
    background-color: white;
    border-radius: 0.5rem;
    padding: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .details-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin-top: 1.5rem;
  }

  .detail-group {
    display: grid;
    gap: 1rem;
  }

  .detail-item {
    display: grid;
    grid-template-columns: 120px 1fr;
    gap: 0.5rem;
    align-items: start;
  }

  .detail-item.full-width {
    grid-column: 1 / -1;
  }

  .label {
    font-weight: 500;
    color: #64748b;
  }

  .value {
    color: #1e293b;
    word-break: break-word;
  }

  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 1rem;
  }

  .modal-content {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
  }

  .modal-close {
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .modal-body {
    padding: 1.5rem;
  }

  .warning {
    color: #dc2626;
    margin-top: 0.5rem;
  }

  .modal-actions {
    display: flex;
    justify-content: flex-end;
    padding: 1rem;
    gap: 1rem;
    border-top: 1px solid #e2e8f0;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-group.full-width {
    grid-column: 1 / -1;
  }

  label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
  }

  input, select, textarea {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
  }

  select[multiple] {
    min-height: 100px;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1.5rem;
  }
</style>
