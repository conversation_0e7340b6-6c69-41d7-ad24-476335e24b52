import { MongoClient, ObjectId } from 'mongodb';
import { error } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017/';
const dbName = 'ServiceContracts';

// Valid customer types based on memory requirement
const CUSTOMER_TYPES = ['OEM Importer', 'Fleet Owner', 'Retail'];
const DEFAULT_CUSTOMER_TYPE = 'Retail';

/** @type {MongoClient | null} */
let mongoClient = null;

/** @returns {Promise<MongoClient>} */
async function getMongoClient() {
  if (!mongoClient) {
    mongoClient = new MongoClient(uri);
    await mongoClient.connect();
  }
  return mongoClient;
}

/** @type {import('./$types').PageServerLoad} */
export async function load({ url }) {
  /** @type {MongoClient} */
  let client;
  
  try {
    client = await getMongoClient();
    const db = client.db(dbName);

    // Get customerId from URL parameter
    const customerId = url.searchParams.get('customerId');
    let selectedCustomer = null;

    // If customerId is provided and valid, fetch the specific customer
    if (customerId && ObjectId.isValid(customerId)) {
      selectedCustomer = await db.collection('Customers')
        .findOne({ _id: new ObjectId(customerId) });
      
      if (selectedCustomer) {
        selectedCustomer = {
          ...selectedCustomer,
          _id: selectedCustomer._id.toString(),
          type: selectedCustomer.type || DEFAULT_CUSTOMER_TYPE
        };
      }
    }

    // Get all customers with proper sorting
    const customers = await db.collection('Customers')
      .find({})
      .sort({ companyName: 1 })
      .toArray();

    // Get computers, filtered by customerId if provided
    const computersQuery = customerId && ObjectId.isValid(customerId) 
      ? { customerId: new ObjectId(customerId) }
      : {};

    const computers = await db.collection('CustomerComputers')
      .find(computersQuery)
      .sort({ name: 1 })
      .toArray();

    // Convert ObjectIds to strings for client-side use as per memory requirement
    return {
      selectedCustomer,
      customers: customers.map(customer => ({
        ...customer,
        _id: customer._id.toString(),
        type: customer.type || DEFAULT_CUSTOMER_TYPE
      })),
      computers: computers.map(computer => ({
        ...computer,
        _id: computer._id.toString(),
        customerId: computer.customerId.toString(),
        purchaseDate: computer.purchaseDate ? new Date(computer.purchaseDate) : null,
        warrantyEndDate: computer.warrantyEndDate ? new Date(computer.warrantyEndDate) : null,
        createdAt: computer.createdAt ? new Date(computer.createdAt) : new Date()
      }))
    };
  } catch (err) {
    console.error('Error loading customer computers:', err);
    throw error(500, 'Failed to load customer computers');
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  addComputer: async ({ request }) => {
    try {
      const formData = await request.formData();
      
      // Get required fields
      const customerId = formData.get('customerId')?.toString();
      const serialNumber = formData.get('serialNumber')?.toString();
      const model = formData.get('model')?.toString();
      const productType = formData.get('productType')?.toString();
      
      // Validate required fields
      if (!customerId || !serialNumber || !model || !productType) {
        return { success: false, error: 'Missing required fields' };
      }
      
      // Validate ObjectId format
      if (!ObjectId.isValid(customerId)) {
        return { success: false, error: 'Invalid customer ID format' };
      }
      
      // Create the computer object
      const computerData = {
        customerId: new ObjectId(customerId),
        name: `${model} - ${serialNumber}`, // Format a descriptive name for the computer
        serialNumber,
        model,
        productType,
        productDesignation: formData.get('productDesignation')?.toString() || '',
        engineType: formData.get('engineType')?.toString() || '',
        operatingHours: parseInt(formData.get('operatingHours')?.toString() || '0', 10),
        installationDate: formData.get('installationDate')?.toString() || null,
        manufactureDate: formData.get('manufactureDate')?.toString() || null,
        isActive: formData.get('isActive') === 'on',
        notes: formData.get('notes')?.toString() || '',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const client = await getMongoClient();
      const db = client.db(dbName);
      const computersCollection = db.collection('CustomerComputers');
      
      // Insert the computer
      const result = await computersCollection.insertOne(computerData);
      
      if (!result.acknowledged) {
        return { success: false, error: 'Failed to add computer' };
      }
      
      return { 
        success: true, 
        type: 'success',
        message: 'Computer added successfully'
      };
    } catch (err) {
      console.error('Error adding computer:', err);
      return { success: false, error: err.message || 'Operation failed' };
    }
  },
  
  updateComputer: async ({ request }) => {
    try {
      const formData = await request.formData();
      
      // Get required fields
      const computerId = formData.get('_id')?.toString();
      const customerId = formData.get('customerId')?.toString();
      const serialNumber = formData.get('serialNumber')?.toString();
      const model = formData.get('model')?.toString();
      const productType = formData.get('productType')?.toString();
      
      // Validate required fields
      if (!computerId || !customerId || !serialNumber || !model || !productType) {
        return { success: false, error: 'Missing required fields' };
      }
      
      // Validate ObjectId format
      if (!ObjectId.isValid(computerId) || !ObjectId.isValid(customerId)) {
        return { success: false, error: 'Invalid ID format' };
      }
      
      // Create the update data
      const updateData = {
        customerId: new ObjectId(customerId),
        name: `${model} - ${serialNumber}`, // Format a descriptive name for the computer
        serialNumber,
        model,
        productType,
        productDesignation: formData.get('productDesignation')?.toString() || '',
        engineType: formData.get('engineType')?.toString() || '',
        operatingHours: parseInt(formData.get('operatingHours')?.toString() || '0', 10),
        installationDate: formData.get('installationDate')?.toString() || null,
        manufactureDate: formData.get('manufactureDate')?.toString() || null,
        isActive: formData.get('isActive') === 'on',
        notes: formData.get('notes')?.toString() || '',
        updatedAt: new Date()
      };
      
      const client = await getMongoClient();
      const db = client.db(dbName);
      const computersCollection = db.collection('CustomerComputers');
      
      // Update the computer
      const result = await computersCollection.updateOne(
        { _id: new ObjectId(computerId) },
        { $set: updateData }
      );
      
      if (result.matchedCount === 0) {
        return { success: false, error: 'Computer not found' };
      }
      
      return { 
        success: true, 
        type: 'success',
        message: 'Computer updated successfully'
      };
    } catch (err) {
      console.error('Error updating computer:', err);
      return { success: false, error: err.message || 'Operation failed' };
    }
  },
  
  deleteComputer: async ({ request }) => {
    try {
      const formData = await request.formData();
      
      // Get required fields
      const computerId = formData.get('_id')?.toString();
      const customerId = formData.get('customerId')?.toString();
      
      // Validate required fields
      if (!computerId || !customerId) {
        return { success: false, error: 'Missing required fields' };
      }
      
      // Validate ObjectId format
      if (!ObjectId.isValid(computerId) || !ObjectId.isValid(customerId)) {
        return { success: false, error: 'Invalid ID format' };
      }
      
      const client = await getMongoClient();
      const db = client.db(dbName);
      const computersCollection = db.collection('CustomerComputers');
      
      // Delete the computer
      const result = await computersCollection.deleteOne({ _id: new ObjectId(computerId) });
      
      if (result.deletedCount === 0) {
        return { success: false, error: 'Computer not found' };
      }
      
      return { 
        success: true, 
        type: 'success',
        message: 'Computer deleted successfully'
      };
    } catch (err) {
      console.error('Error deleting computer:', err);
      return { success: false, error: err.message || 'Operation failed' };
    }
  },
  
  delete: async ({ request }) => {
    /** @type {MongoClient} */
    let client;
    
    try {
      const formData = await request.formData();
      const computerId = formData.get('computerId')?.toString();
      const customerId = formData.get('customerId')?.toString();

      // Validate ObjectIds as per memory requirement
      if (!computerId || !ObjectId.isValid(computerId) || !customerId || !ObjectId.isValid(customerId)) {
        return {
          success: false,
          error: 'Invalid ID format'
        };
      }

      client = await getMongoClient();
      const db = client.db(dbName);

      // Delete computer using native ObjectIds for querying
      const result = await db.collection('CustomerComputers').deleteOne({
        _id: new ObjectId(computerId),
        customerId: new ObjectId(customerId)
      });

      if (!result.deletedCount) {
        return {
          success: false,
          error: 'Computer not found or does not belong to this customer'
        };
      }

      return {
        success: true
      };
    } catch (error) {
      console.error('Error deleting computer:', error);
      return {
        success: false,
        error: 'Failed to delete computer'
      };
    }
  }
};
