<script>
  import BaseGrid from './BaseGrid.svelte';
  
  export let data = {
    labourTime: [],
    serviceCodeHeader: [],
    partNumbersServiceCodeAction: [],
    counts: {}
  };
</script>

<BaseGrid>
  <div class="workload-display-container">
    <h1>Workload Data Display</h1>
    
    <!-- Summary section -->
    <div class="summary-section">
      <h2>Collection Summary</h2>
      <div class="summary-grid">
        <div class="summary-card">
          <h3>Labour Time</h3>
          <p>Total documents: {data.counts.labourTime || 0}</p>
        </div>
        <div class="summary-card">
          <h3>Service Code Header</h3>
          <p>Total documents: {data.counts.serviceCodeHeader || 0}</p>
        </div>
        <div class="summary-card">
          <h3>PartNumbers Service Code Action</h3>
          <p>Total documents: {data.counts.partNumbersServiceCodeAction || 0}</p>
        </div>
      </div>
    </div>
    
    <!-- Labour Time section -->
    <div class="data-section">
      <h2>Labour Time Data</h2>
      <div class="labour-grid data-grid">
        <div class="header">Service Code</div>
        <div class="header">Service Description</div>
        <div class="header">VST Code</div>
        <div class="header">VST Hours</div>
        <div class="header">Service Phase</div>
        <div class="header">Computer Category</div>
        
        {#each data.labourTime as item}
          <div class="cell">{item['Service Code'] || 'N/A'}</div>
          <div class="cell">{item['Service Description'] || 'N/A'}</div>
          <div class="cell">{item['VST Code'] || 'N/A'}</div>
          <div class="cell">{item['VST Hours'] || 'N/A'}</div>
          <div class="cell">{item.ServicePhase || 'N/A'}</div>
          <div class="cell">{item.ComputerCategory || 'N/A'}</div>
        {/each}
      </div>
    </div>
    
    <!-- Service Code Header section -->
    <div class="data-section">
      <h2>Service Code Header Data</h2>
      <div class="service-header-grid data-grid">
        <div class="header">Service Code</div>
        <div class="header">Service Activity Label</div>
        <div class="header">Activity Purpose</div>
        <div class="header">Product Validity Group</div>
        <div class="header">Hours</div>
        <div class="header">Months</div>
        
        {#each data.serviceCodeHeader as item}
          <div class="cell">{item.ServiceCode || 'N/A'}</div>
          <div class="cell">{item['Service activity Label'] || 'N/A'}</div>
          <div class="cell">{item['Activity purpose'] || 'N/A'}</div>
          <div class="cell">{item.ProductValidityGroup || item['Product Validity Group'] || 'N/A'}</div>
          <div class="cell">{item['Internal No of Hours'] || 'N/A'}</div>
          <div class="cell">{item['Internal No of Months'] || 'N/A'}</div>
        {/each}
      </div>
    </div>
    
    <!-- PartNumbers Service Code Action section -->
    <div class="data-section">
      <h2>PartNumbers Service Code Action Data</h2>
      <div class="part-numbers-grid data-grid">
        <div class="header">Service Code</div>
        <div class="header">Part Number</div>
        <div class="header">Action Type</div>
        <div class="header">Product Validity Group</div>
        <div class="header">Quantity</div>
        <div class="header">Unit of Measure</div>
        
        {#each data.partNumbersServiceCodeAction as item}
          <div class="cell">{item.ServiceCode || 'N/A'}</div>
          <div class="cell">{item.PartNumber || 'N/A'}</div>
          <div class="cell">{item.ActionType || 'N/A'}</div>
          <div class="cell">{item.ProductValidityGroup || item['Product Validity Group'] || 'N/A'}</div>
          <div class="cell">{item.Quantity || 'N/A'}</div>
          <div class="cell">{item['Unit of Measure'] || 'N/A'}</div>
        {/each}
      </div>
    </div>
  </div>
</BaseGrid>

<style>
  .workload-display-container {
    width: 100%;
    padding: 1rem;
    color: #333;
  }
  
  h1 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #2c3e50;
    text-align: center;
  }
  
  h2 {
    font-size: 1.5rem;
    margin: 1rem 0;
    color: #3498db;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 0.5rem;
  }
  
  h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
  }
  
  .summary-section, .data-section {
    margin-bottom: 2rem;
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  }
  
  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }
  
  .summary-card {
    background-color: #ecf0f1;
    padding: 1rem;
    border-radius: 5px;
    text-align: center;
  }
  
  .data-grid {
    display: grid;
    gap: 0.25rem;
    margin-top: 1rem;
    background-color: #f9f9f9;
    border-radius: 5px;
    overflow: hidden;
  }
  
  .labour-grid {
    grid-template-columns: repeat(6, 1fr);
  }
  
  .service-header-grid {
    grid-template-columns: repeat(6, 1fr);
  }
  
  .part-numbers-grid {
    grid-template-columns: repeat(6, 1fr);
  }
  
  .header {
    background-color: #3498db;
    color: white;
    padding: 0.75rem;
    font-weight: bold;
    text-align: center;
  }
  
  .cell {
    padding: 0.5rem;
    border-bottom: 1px solid #ecf0f1;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .cell:nth-child(odd) {
    background-color: #f4f6f7;
  }
  
  @media (max-width: 900px) {
    .labour-grid, .service-header-grid, .part-numbers-grid {
      grid-template-columns: repeat(3, 1fr);
    }
    
    .header, .cell {
      padding: 0.5rem;
    }
  }
  
  @media (max-width: 600px) {
    .labour-grid, .service-header-grid, .part-numbers-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
