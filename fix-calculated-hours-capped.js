// Script to fix CalculatedHoursPerMonth with the proper constraints
// 1. Can never be higher than the hours value
// 2. Can never be more than 744 (max hours in a month)

import { MongoClient, ObjectId } from 'mongodb';

async function fixCalculatedHoursField() {
  // Connection URI
  const uri = 'mongodb://localhost:27017';
  const client = new MongoClient(uri);

  const MAX_HOURS_PER_MONTH = 744; // Maximum hours in a month

  try {
    // Connect to MongoDB
    await client.connect();
    console.log('Connected to MongoDB');

    // Access the ServiceContracts database and ActualComputerHours collection
    const db = client.db('ServiceContracts');
    const collection = db.collection('ActualComputerHours');

    // Find all documents
    const docs = await collection.find({}).toArray();
    console.log(`Found ${docs.length} documents in ActualComputerHours collection`);

    // Update each document
    let updateCount = 0;
    
    for (const doc of docs) {
      // Ensure hours is an integer
      const hours = parseInt(doc.hours || 0);
      
      // Calculate capped hours - the smaller of the hours, or 744
      const calculatedHours = Math.min(hours, MAX_HOURS_PER_MONTH);
      
      // Update the document
      await collection.updateOne(
        { _id: doc._id },
        { 
          $set: { 
            hours: hours,  // Ensure hours is an integer
            CalculatedHoursPerMonth: calculatedHours 
          }
        }
      );
      
      console.log(`Updated document ${doc._id}: set hours=${hours}, CalculatedHoursPerMonth=${calculatedHours}`);
      updateCount++;
    }

    console.log(`\nUpdated ${updateCount} documents with proper hour caps`);

    // Verify some updated documents
    const updatedSamples = await collection.find({}).limit(5).toArray();
    console.log('\nSample documents after update:');
    updatedSamples.forEach(doc => {
      console.log(
        `ID: ${doc._id}, ` +
        `Hours: ${doc.hours}, ` + 
        `CalculatedHoursPerMonth: ${doc.CalculatedHoursPerMonth} ` +
        `(Month: ${doc.month}/${doc.year})`
      );
    });

  } catch (error) {
    console.error('Error updating documents:', error);
  } finally {
    // Close the connection
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the function
fixCalculatedHoursField().catch(console.error);
