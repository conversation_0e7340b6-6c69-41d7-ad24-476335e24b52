import { json } from '@sveltejs/kit';
import { ObjectId } from 'mongodb';
import { getCollection } from '$lib/db/mongo.js';

// Log API activities
function logServerActivity(activity, details) {
  console.log(`[API LOG] ${activity}`, details || '');
}

export async function POST({ request }) {
  try {
    const data = await request.json();
    logServerActivity('POST /api/active-faultcode request', data);
    
    // Validate essential fields
    if (!data.computerId || !data.DOID) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }), 
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    // Prepare document for MongoDB
    const doc = {
      computerId: data.computerId && new ObjectId(data.computerId),
      DOID: data.DOID,
      ProprietaryName: data.ProprietaryName || '',
      activity: data.activity || 'VM Request Electronic Data Link',
      description: data.description || '',
      priority: data.priority || 'Medium',
      status: data.status || 'Active',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Insert into database
    const collection = await getCollection('ActiveFaultCodes');
    const result = await collection.insertOne(doc);
    
    if (!result.acknowledged) {
      throw new Error('Database operation failed');
    }
    
    logServerActivity('Document created', { id: result.insertedId });
    
    return json({
      success: true,
      id: result.insertedId.toString()
    });
  } catch (err) {
    logServerActivity('POST error', err);
    
    return new Response(
      JSON.stringify({ 
        error: err.message || 'An unknown error occurred' 
      }),
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' } 
      }
    );
  }
}

export async function GET({ url }) {
  try {
    const id = url.searchParams.get('id');
    
    if (id) {
      // Get a specific fault code by ID
      const collection = await getCollection('ActiveFaultCodes');
      const faultCode = await collection.findOne({ _id: new ObjectId(id) });
      
      if (!faultCode) {
        return new Response(
          JSON.stringify({ error: 'Fault code not found' }),
          { status: 404, headers: { 'Content-Type': 'application/json' } }
        );
      }
      
      // Convert ObjectIds to strings for client
      if (faultCode._id) faultCode._id = faultCode._id.toString();
      if (faultCode.computerId) faultCode.computerId = faultCode.computerId.toString();
      
      return json(faultCode);
    } else {
      // Get all fault codes
      const collection = await getCollection('ActiveFaultCodes');
      const faultCodes = await collection.find({}).limit(100).toArray();
      
      // Convert ObjectIds to strings for client
      faultCodes.forEach(code => {
        if (code._id) code._id = code._id.toString();
        if (code.computerId) code.computerId = code.computerId.toString();
      });
      
      return json(faultCodes);
    }
  } catch (err) {
    logServerActivity('GET error', err);
    
    return new Response(
      JSON.stringify({ 
        error: err.message || 'An unknown error occurred' 
      }),
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' } 
      }
    );
  }
}

export async function DELETE({ url }) {
  try {
    const id = url.searchParams.get('id');
    
    if (!id) {
      return new Response(
        JSON.stringify({ error: 'Missing ID parameter' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    const collection = await getCollection('ActiveFaultCodes');
    const result = await collection.deleteOne({ _id: new ObjectId(id) });
    
    if (result.deletedCount === 0) {
      return new Response(
        JSON.stringify({ error: 'Fault code not found or already deleted' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    return json({ success: true });
  } catch (err) {
    logServerActivity('DELETE error', err);
    
    return new Response(
      JSON.stringify({ 
        error: err.message || 'An unknown error occurred' 
      }),
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' } 
      }
    );
  }
}
