<script lang="ts">
  import { invalidate } from '$app/navigation';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import MatrixGrid from '$lib/components/grids/MatrixGrid.svelte';
  import TableGrid from '$lib/components/grids/TableGrid.svelte';
  import PlanningGrid from '$lib/components/grids/PlanningGrid.svelte';
  import ComputerMonthsGrid from '$lib/components/grids/ComputerMonthsGrid.svelte';
  import ActivityPlanningGrid from '$lib/components/grids/ActivityPlanningGrid.svelte';
  import ActiveFaultCodesGrid from '$lib/components/grids/ActiveFaultCodesGrid.svelte';
  import ActualComputerHoursGrid from '$lib/components/grids/ActualComputerHoursGrid.svelte';
  import ActualComputerHoursVersion2Grid from '$lib/components/grids/ActualComputerHoursVersion2Grid.svelte';
  import WorkloadGrid from '$lib/components/grids/WorkloadGrid.svelte';
  import DemarcationServiceGrid from '$lib/components/grids/DemarcationServiceGrid.svelte';
  import ServiceCodeTypesGrid from '$lib/components/grids/ServiceCodeTypesGrid.svelte';
  import ServiceActionEditor from '$lib/components/ServiceActionEditor.svelte';
  import MonthsRow from '$lib/components/MonthsRow.svelte';
  import WorkloadCell from '$lib/components/WorkloadCell.svelte';
  import EditActivityModal from '$lib/components/EditActivityModal.svelte';
  import ActivityCell from '$lib/components/ActivityCell.svelte';
  import BulkOperationsForm from '$lib/components/BulkOperationsForm.svelte';
  import YearMonthSelector from '$lib/components/YearMonthSelector.svelte';
  import MatrixCellEditor from '$lib/components/MatrixCellEditor.svelte';
  import { formatDate, formatDateRange } from '$lib/utils/dates.js';
  import { writable } from 'svelte/store';
  import type { Activity } from '$lib/types/activity';
  
  // Page data
  export let data: { 
    computer: any;
    contract: any;
    workload: any[];
    activeFaultCodes: any[];
    actualComputerHours: any[];
    serviceActions?: any[];
    serviceCodeTypes?: any[];
    error?: string;
  };
  
  // Constants
  const computerId = $page.params.id;
  const months = [
    'January', 'February', 'March', 'April', 
    'May', 'June', 'July', 'August', 
    'September', 'October', 'November', 'December'
  ];
  
  const shortMonths = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const monthsMatrix = shortMonths; // For table headers
  const yearsMatrix = [...new Set(data.workload.map(item => item.year))].sort();
  
  // Service Actions
  let selectedServiceAction = null;
  let showServiceActionEditor = false;
  
  // Demarcation name derived from computer data
  $: demarcationName = data.computer?.demarcation || 'General';
  
  // Calculate fault code months
  const faultCodeStartMonths = new Set(
    data.activeFaultCodes?.map(fc => `${fc.year}-${String(fc.month).padStart(2, '0')}`) || []
  );
  
  // Create writable store for workload
  const workloadStore = writable(data.workload || []);
  
  // Create a lookup map for quick access to workload data
  $: workloadMap = $workloadStore.reduce((acc, item) => {
    const key = `${item.year}-${item.month}`;
    acc[key] = item;
    return acc;
  }, {} as Record<string, any>);
  
  $: workloadData = Object.values(workloadMap);
  
  // Calculate total hours for each year
  $: yearTotals = $workloadStore.reduce((acc, item) => {
    acc[item.year] = (acc[item.year] || 0) + (parseFloat(item.hours) || 0);
    return acc;
  }, {} as Record<number, number>);
  
  // UI state
  let showAllRecords = false;
  let filterYear: number | null = null;
  
  // Get all unique years from the data
  $: years = [...new Set($workloadStore.map(item => item.year))].sort();
  
  // Default to current year if available
  $: {
    if (!filterYear && years.length > 0) {
      const currentYear = new Date().getFullYear();
      filterYear = years.includes(currentYear) ? currentYear : years[years.length - 1];
    }
  }
  
  // Cell editing state
  let selectedCell: { year: number; month: number; hours: number; activity: string; hasFixed: boolean; _id: string } | null = null;
  let editingHours = '';
  let editingActivity = '';
  let editingHasFixed = false;
  let saving = false;
  
  // Modal state for editing
  let showEditModal = false;
  let selectedActivity: Activity | null = null;
  let selectedMonth = '';
  let selectedYear = '';
  
  // Bulk operations state
  let bulkStartYear = new Date().getFullYear();
  let bulkStartMonth = 1;
  let bulkEndYear = bulkStartYear;
  let bulkEndMonth = 12;
  let bulkHours = 1; // Initialize with valid default
  let bulkActivity = '';
  let bulkHasFixed = false;
  let bulkLoading = false;
  
  // Cell editing with matrix editor
  let showCellEditor = false;
  let editPosition = { x: 0, y: 0 };
  let editingCell = null;
  
  // Functions for handling cell selection and editing
  function selectCell(year: number, month: number) {
    const workloadItem = getWorkloadData(year, month);
    
    if (workloadItem) {
      selectedCell = { 
        year, 
        month, 
        hours: workloadItem.hours, 
        activity: workloadItem.activity, 
        hasFixed: workloadItem.hasFixed,
        _id: workloadItem._id
      };
      editingHours = workloadItem.hours.toString();
      editingActivity = workloadItem.activity;
      editingHasFixed = workloadItem.hasFixed;
    } else {
      selectedCell = { 
        year, 
        month, 
        hours: 0, 
        activity: '', 
        hasFixed: false,
        _id: ''
      };
      editingHours = '0';
      editingActivity = '';
      editingHasFixed = false;
    }
  }
  
  function getWorkloadData(year: number, month: number): any {
    return workloadMap[`${year}-${month}`];
  }
  
  function getMonthData(year: number, monthIdx: number) {
    const item = getWorkloadData(year, monthIdx + 1);
    return {
      hours: item?.hours || 0,
      activity: item?.activity || '',
      hasActivity: !!item?.activity,
      hasFixed: !!item?.hasFixed,
      isSelected: selectedCell?.year === year && selectedCell?.month === (monthIdx + 1)
    };
  }
  
  function getActivityClass(activity: string): string {
    if (!activity) return '';
    
    if (activity.toLowerCase().includes('contract')) return 'activity-contract';
    if (activity.toLowerCase().includes('delivery')) return 'activity-delivery';
    if (activity.toLowerCase().includes('service')) return 'activity-service';
    if (activity.toLowerCase().includes('repair')) return 'activity-repair';
    if (activity.toLowerCase().includes('inspection')) return 'activity-inspection';
    if (activity.toLowerCase().includes('maintenance')) return 'activity-maintenance';
    
    return 'activity-default';
  }
  
  function cancelEdit() {
    selectedCell = null;
  }
  
  async function saveWorkload() {
    if (!selectedCell || saving) return;

    try {
      saving = true;
      
      const formData = new FormData();
      formData.append('data', JSON.stringify({
        _id: selectedCell._id,
        computerId,
        year: selectedCell.year,
        month: selectedCell.month,
        hours: parseInt(editingHours) || 0,
        activity: editingActivity,
        hasFixed: editingHasFixed
      }));
      
      const response = await fetch(`/computer-id/${computerId}/service-activities/service-planning`, {
        method: 'PUT',  
        body: formData
      });

      const result = await response.json();
      
      if (result.success) {
        refreshData();
        selectedCell = null;
      } else {
        alert(`Failed to update workload: ${result.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error updating workload:', error);
      alert('An error occurred while updating the workload data');
    } finally {
      saving = false;
    }
  }
  
  async function handleActivityUpdated(event: CustomEvent<Activity>) {
    try {
      const updatedActivity = event.detail;
      
      console.log('Saving activity with data:', {
        computerId,
        year: updatedActivity.year,
        month: updatedActivity.month,
        value: updatedActivity.value,
        activity: updatedActivity.activity,
        hasFixed: updatedActivity.hasFixed
      });

      const formData = new FormData();
      const data = {
        computerId,
        year: updatedActivity.year, 
        month: updatedActivity.month,
        hours: updatedActivity.value,
        activity: updatedActivity.activity,
        hasFixed: updatedActivity.hasFixed
      };
      formData.append('data', JSON.stringify(data));

      // Check if an entry already exists for this year/month
      const existingEntry = workloadMap[`${updatedActivity.year}-${updatedActivity.month}`];
      const method = existingEntry ? 'PUT' : 'POST';
      
      console.log('Request details:', {
        method,
        url: `/computer-id/${computerId}/service-activities/service-planning`,
        existingEntry: !!existingEntry
      });
      
      const response = await fetch(`/computer-id/${computerId}/service-activities/service-planning`, {
        method,
        body: formData
      });

      const result = await response.json();
      
      if (response.ok) {
        // Refresh the data
        refreshData();
        
        // Update workloadMap
        if (!existingEntry) {
          workloadMap[`${updatedActivity.year}-${updatedActivity.month}`] = result.workload;
        } else {
          workloadMap[`${updatedActivity.year}-${updatedActivity.month}`] = {
            ...existingEntry,
            hours: updatedActivity.value,
            activity: updatedActivity.activity,
            hasFixed: updatedActivity.hasFixed
          };
        }
        
        // Update workloadData
        workloadData = Object.values(workloadMap);
        
        // Close the modal
        showEditModal = false;
        selectedActivity = null;
      } else {
        console.error('Failed to save activity:', result);
      }
    } catch (error) {
      console.error('Error saving activity:', error);
    }
  }
  
  // Handle bulk add hours operation
  async function handleBulkAddHours(event) {
    try {
      bulkLoading = true;
      
      const { startYear, startMonth, endYear, endMonth, hours, activity, hasFixed } = event.detail;
      
      // Validate hours value
      const numericHours = Number(hours);
      console.log('Bulk add hours - received value:', hours, 'Type:', typeof hours);
      console.log('Bulk add hours - after Number():', numericHours, 'Type:', typeof numericHours);
      
      if (isNaN(numericHours) || numericHours <= 0) {
        console.log('Frontend validation failed:', { isNaN: isNaN(numericHours), isZeroOrNegative: numericHours <= 0 });
        throw new Error('Hours must be a positive whole number');
      }
      
      // Validate activity is provided
      if (!activity || activity.trim() === '') {
        throw new Error('Activity is required');
      }
      
      // Create data for server request - ensure all values are properly formatted
      const requestData = {
        computerId,
        startYear: Number(startYear),
        startMonth: Number(startMonth),
        endYear: Number(endYear), 
        endMonth: Number(endMonth),
        hours: numericHours,
        activity: activity.trim(),
        hasFixed: Boolean(hasFixed)
      };
      
      console.log('Sending bulk operation to server:', JSON.stringify(requestData));
      
      const response = await fetch(`/api/service-activities/bulk`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });
      
      const result = await response.json();
      console.log('Server response:', result);
      
      if (!response.ok) {
        throw new Error(result.message || 'Failed to process activities');
      }
      
      // Refresh data
      refreshData();
      
      // Show success message
      const dateRangeText = formatDateRange(
        new Date(startYear, startMonth - 1, 1),
        new Date(endYear, endMonth - 1, 1)
      );
      
      alert(`Successfully added ${numericHours} hours with activity "${activity}" to ${dateRangeText}`);
      
    } catch (error) {
      console.error('Error in bulk add:', error);
      alert(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      bulkLoading = false;
    }
  }
  
  // Handle bulk operations form submission
  function handleBulkSubmit() {
    handleBulkOperation();
  }
  
  // Bulk operations handler
  async function handleBulkOperation() {
    try {
      bulkLoading = true;
      
      // Create the event detail object
      const detail = {
        startYear: bulkStartYear,
        startMonth: bulkStartMonth,
        endYear: bulkEndYear,
        endMonth: bulkEndMonth,
        hours: bulkHours,
        activity: bulkActivity,
        hasFixed: bulkHasFixed
      };
      
      // Call the bulk add hours function with our detail
      await handleBulkAddHours({ detail });
      
    } catch (error) {
      console.error('Error in bulk operation:', error);
      alert(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      bulkLoading = false;
    }
  }
  
  // Handle "Add Hours" button click
  function handleAddHours() {
    if (!bulkActivity) {
      alert('Activity is required');
      return;
    }

    // Call the function to add hours to Workload collection with the value from the text field
    addToWorkload(bulkHours);
  }

  // Function to add hours to Workload collection
  async function addToWorkload(hours) {
    try {
      bulkLoading = true;
      
      // Create the request data
      const requestData = {
        computerId,
        startYear: bulkStartYear,
        startMonth: bulkStartMonth,
        endYear: bulkEndYear,
        endMonth: bulkEndMonth,
        hours: hours, // Use the actual hours value from the text field
        activity: bulkActivity,
        hasFixed: bulkHasFixed
      };
      
      console.log('Adding to Workload collection:', requestData);
      
      // Send request to the workload bulk endpoint
      const response = await fetch(`/api/workload/bulk`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });
      
      const result = await response.json();
      console.log('Workload operation result:', result);
      
      if (!response.ok) {
        throw new Error(result.message || 'Failed to add workload entries');
      }
      
      // Refresh data
      refreshData();
      
      // Show success message
      const dateRangeText = formatDateRange(
        new Date(bulkStartYear, bulkStartMonth - 1, 1),
        new Date(bulkEndYear, bulkEndMonth - 1, 1)
      );
      
      alert(`Successfully added ${hours} hours to Workload for ${dateRangeText}`);
      
    } catch (error) {
      console.error('Error in workload operation:', error);
      alert(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      bulkLoading = false;
    }
  }
  
  // Handle cell click to edit
  function handleCellClick(year: number, monthIdx: number) {
    selectedYear = year.toString();
    selectedMonth = months[monthIdx];
    
    const monthIndex = monthIdx + 1;
    const existingEntry = workloadMap[`${year}-${monthIndex}`];
    
    if (existingEntry) {
      selectedActivity = {
        _id: existingEntry._id || '',
        year: year,
        month: monthIndex,
        value: existingEntry.hours || 0,
        activity: existingEntry.activity || '',
        hasFixed: existingEntry.hasFixed || false
      };
    } else {
      // Create a new activity object
      selectedActivity = {
        _id: '',
        year: year,
        month: monthIndex,
        value: 0,
        activity: '',
        hasFixed: false
      };
    }
    
    showEditModal = true;
  }
  
  // Handle keyboard events
  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      if (showEditModal) {
        showEditModal = false;
        selectedActivity = null;
      } else if (selectedCell) {
        cancelEdit();
      }
    } else if (event.key === 'Enter' && !event.shiftKey && !event.ctrlKey && !event.altKey && selectedCell) {
      saveWorkload();
    }
  }
  
  // Refresh data
  function refreshData() {
    invalidate(`/computer-id/${computerId}/service-activities/service-planning`);
  }
  
  // Create new service activity
  function addNewService() {
    // Use current year if available, otherwise use the latest year
    const currentYear = new Date().getFullYear();
    const useYear = years.includes(currentYear) ? currentYear : years[years.length - 1] || currentYear;
    
    handleCellClick(useYear, new Date().getMonth());
  }
  
  // Calculate statistics
  $: totalRecords = $workloadStore.length;
  
  // Handle cell click for inline editing
  function handleMatrixCellClick(event, year, monthIdx) {
    // Get click position for editor placement
    const rect = event.currentTarget.getBoundingClientRect();
    editPosition = {
      x: rect.left + rect.width / 2,
      y: rect.top + rect.height / 2
    };
    
    const monthIndex = monthIdx + 1;
    const mapKey = `${year}-${monthIndex}`;
    const existingEntry = workloadMap[mapKey];
    
    // Check if we have an existing activity for this cell
    if (existingEntry && existingEntry._id) {
      // Edit existing activity
      editingCell = {
        _id: existingEntry._id,
        year: year,
        month: monthIndex,
        hours: existingEntry.hours || 0,
        activity: existingEntry.activity || '',
        hasFixed: existingEntry.hasFixed || false
      };
      console.log("Editing existing activity:", editingCell);
    } else {
      // Create a new activity object
      editingCell = {
        year: year,
        month: monthIndex,
        hours: 0,
        activity: '',
        hasFixed: false
      };
      console.log("Creating new activity:", editingCell);
    }
    
    showCellEditor = true;
  }
  
  // Handle cell edit save
  async function handleCellEdited(event) {
    const updatedActivity = event.detail;
    console.log('Activity saved:', updatedActivity);
    
    // Refresh the data to ensure we have latest from server
    refreshData();
    
    // Update local data immediately for a responsive UI
    const mapKey = `${updatedActivity.year}-${updatedActivity.month}`;
    workloadMap[mapKey] = {
      ...updatedActivity,
      // Ensure we have these fields set correctly
      _id: updatedActivity._id,
      year: updatedActivity.year,
      month: updatedActivity.month,
      hours: updatedActivity.hours,
      activity: updatedActivity.activity, 
      hasFixed: updatedActivity.hasFixed
    };
    
    // Update the store to trigger reactivity
    workloadStore.set(Object.values(workloadMap));
  }
  
  // Handle service action edit
  function handleServiceActionEdit(event) {
    selectedServiceAction = event.detail;
    showServiceActionEditor = true;
  }
  
  // Handle new service action
  function handleAddServiceAction(event) {
    // Create a new service action template
    selectedServiceAction = {
      _id: `temp-${Date.now()}`,
      title: '',
      description: '',
      demarcation: event.detail.demarcation || demarcationName,
      status: 'Open',
      priority: 'Medium',
      computerId: computerId,
      assignedTo: '',
      dueDate: new Date().toISOString().split('T')[0] // Today's date in YYYY-MM-DD format
    };
    showServiceActionEditor = true;
  }
  
  // Handle service action save
  async function handleServiceActionSave(event) {
    try {
      const actionData = event.detail;
      
      // Ensure computerId is set
      actionData.computerId = computerId;
      
      // Determine if it's an update or create
      const isUpdate = actionData._id && !actionData._id.startsWith('temp-');
      
      // API endpoint
      const endpoint = isUpdate 
        ? `/api/service-actions/${actionData._id}`
        : '/api/service-actions';
      
      // HTTP method
      const method = isUpdate ? 'PUT' : 'POST';
      
      // Save to database
      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(actionData),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to save: ${response.statusText}`);
      }
      
      // Get the updated data
      const result = await response.json();
      console.log('Saved service action:', result);
      
      // Refresh data
      await invalidate('app:data');
      
      // Close editor
      showServiceActionEditor = false;
      selectedServiceAction = null;
      
    } catch (error) {
      console.error('Error saving service action:', error);
      alert(`Error saving service action: ${error.message}`);
    }
  }
  
  // Handle service action cancel
  function handleServiceActionCancel() {
    showServiceActionEditor = false;
    selectedServiceAction = null;
  }
</script>

<svelte:window on:keydown={handleKeyDown}/>

{#if data.error}
  <div class="error-container">
    <div class="error-message">
      <h2>Error</h2>
      <p>{data.error}</p>
      <button class="btn-primary" on:click={refreshData}>Retry</button>
    </div>
  </div>
{:else if !data.computer}
  <div class="loading-container">
    <div class="loading-spinner"></div>
    <p>Loading service planning data...</p>
  </div>
{:else}
  <div class="fullscreen-layout">
    <!-- Header Section -->
    <header class="app-header">
      <div class="header-content">
        <h1 class="page-title">Service Management</h1>
        <div class="header-nav">
          <a href="/" class="nav-link">Home</a>
          <a href="/customers" class="nav-link">Customers & Computers</a>
          <a href="/service-ids" class="nav-link">Service IDs</a>
          <a href="/data-exploration" class="nav-link">Data Exploration</a>
          <a href="/about" class="nav-link">About</a>
        </div>
      </div>
      <div class="computer-info">
        <span class="computer-name">{data.computer.name || 'Computer'}</span>
        <span class="computer-id">{computerId}</span>
        <span class="product-designation">{data.computer.productDesignation || data.computer.ProductDesignation || 'N/A'}</span>
      </div>
    </header>
    
    <!-- Main Content Section -->
    <div class="main-content">
      <!-- Bulk Operations Panel -->
      <div class="content-section bulk-operations-panel">
        <h2>Bulk Operations</h2>
        <div class="bulk-form">
          <div class="form-row">
            <div class="form-group">
              <label for="bulkStartYear">Start Year</label>
              <input 
                id="bulkStartYear"
                type="number" 
                bind:value={bulkStartYear} 
                min="2000" 
                max="2100" 
                class="form-input" 
              />
            </div>
            
            <div class="form-group">
              <label for="bulkStartMonth">Start Month</label>
              <select 
                id="bulkStartMonth" 
                bind:value={bulkStartMonth} 
                class="form-select"
              >
                {#each months as month, i}
                  <option value={i + 1}>{month}</option>
                {/each}
              </select>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="bulkEndYear">End Year</label>
              <input 
                id="bulkEndYear"
                type="number" 
                bind:value={bulkEndYear} 
                min="2000" 
                max="2100" 
                class="form-input" 
              />
            </div>
            
            <div class="form-group">
              <label for="bulkEndMonth">End Month</label>
              <select 
                id="bulkEndMonth"
                bind:value={bulkEndMonth} 
                class="form-select"
              >
                {#each months as month, i}
                  <option value={i + 1}>{month}</option>
                {/each}
              </select>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="bulkHours">Hours to Add</label>
              <div class="integer-input-container">
                <input 
                  id="bulkHours"
                  type="text" 
                  value={bulkHours || 0}
                  on:input={(e) => {
                    // Strip any non-digit characters
                    const digitOnly = e.target.value.replace(/\D/g, '');
                    // If empty, set to 0
                    const value = digitOnly === '' ? '0' : digitOnly;
                    // Force to integer
                    e.target.value = value;
                    // Update model with integer
                    bulkHours = parseInt(value, 10);
                  }}
                  on:keydown={(e) => {
                    // Block any key that's not a digit, except for control keys
                    const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'];
                    if (!/^\d$/.test(e.key) && !allowedKeys.includes(e.key)) {
                      e.preventDefault();
                      return false;
                    }
                  }}
                  class="form-input highlight-input" 
                  style="padding-right: 90px;"
                />
                <span class="integer-badge">INTEGERS ONLY</span>
              </div>
              <small class="helper-text">Only whole numbers allowed - no decimals or text</small>
            </div>
            
            <div class="form-group">
              <label for="bulkActivity">Activity</label>
              <select 
                id="bulkActivity"
                bind:value={bulkActivity} 
                class="form-select"
              >
                <option value="">-- Select activity --</option>
                <option value="Regular Service">Regular Service</option>
                <option value="Contract Service">Contract Service</option>
                <option value="Maintenance">Maintenance</option>
                <option value="Repair">Repair</option>
                <option value="Inspection">Inspection</option>
                <option value="Installation">Installation</option>
                <option value="Remote Support">Remote Support</option>
                <option value="Software Update">Software Update</option>
                <option value="Hardware Update">Hardware Update</option>
                <option value="Training">Training</option>
                <option value="Consultation">Consultation</option>
              </select>
            </div>
            
            <div class="form-group">
              <label for="bulkHasFixed">Fixed Schedule</label>
              <input 
                id="bulkHasFixed"
                type="checkbox" 
                bind:checked={bulkHasFixed} 
                class="form-checkbox" 
              />
            </div>
            
            <div class="form-group action-buttons">
              <button 
                type="button" 
                class="btn-primary" 
                disabled={bulkLoading} 
                on:click={handleAddHours}
              >
                {bulkLoading ? 'Processing...' : 'Add Hours'}
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Activity Matrix Area -->
      <div class="content-section activity-matrix-panel">
        <h2>Service Activity Matrix</h2>
        <div class="matrix-controls">
          <div class="year-selector">
            <label for="filterYear">Filter by Year:</label>
            <select id="filterYear" bind:value={filterYear}>
              {#each years as year}
                <option value={year}>{year}</option>
              {/each}
              <option value={null}>All Years</option>
            </select>
          </div>
        </div>
        
        {#if $workloadStore.length === 0}
          <p>No service activity data available.</p>
        {:else}
          <div class="matrix-container">
            <div class="matrix-wrapper enhanced-matrix">
              <MatrixGrid 
                data={showAllRecords ? $workloadStore : $workloadStore.filter(item => filterYear === null || item.year === filterYear)}
                columnLabels={monthsMatrix}
                rowLabels={showAllRecords ? yearsMatrix : [filterYear]}
                getColumnKey={(_, colIndex) => colIndex + 1}
                getRowKey={(item) => item.year}
                getCellData={(year, month) => getMonthData(year, month - 1)}
                renderCell={(cellData, year, month) => {
                  if (!cellData) return '';
                  
                  const yearMonth = `${year}-${String(month).padStart(2, '0')}`;
                  const hasFaultCode = faultCodeStartMonths.has(yearMonth);
                  const colorClass = cellData.hours > 0 ? (cellData.hasFixed ? 'cell-success' : 'cell-warning') : '';
                  
                  return `
                    <div class="cell-content ${colorClass} ${cellData.isSelected ? 'selected' : ''} ${hasFaultCode ? 'faultcode-cell' : ''}">
                      <div class="hours" style="font-size: 1.2rem; font-weight: bold;">${cellData.hours || 0}</div>
                      <div class="activity-label" style="font-size: 1.1rem;">${cellData.activity || ''}</div>
                      ${hasFaultCode ? '<div class="fault-indicator" style="font-size: 1.2rem;">⚠️</div>' : ''}
                    </div>
                  `;
                }}
                onCellClick={(year, month) => {
                  selectCell(year, month);
                  
                  // Also position for the matrix cell editor
                  const cells = document.querySelectorAll('.matrix-cell');
                  const targetCell = Array.from(cells).find(cell => {
                    const y = parseInt(cell.getAttribute('data-row-key'));
                    const x = parseInt(cell.getAttribute('data-col-key'));
                    return y === year && x === month;
                  });
                  
                  if (targetCell) {
                    const rect = targetCell.getBoundingClientRect();
                    editPosition = { 
                      x: rect.left, 
                      y: rect.top 
                    };
                    showCellEditor = true;
                    editingCell = { year, month };
                  }
                }}
              />
            </div>
          </div>
        {/if}
      </div>
      
      <!-- Workload Grid -->
      <div class="content-section full-width-component">
        <h2>Service Workload</h2>
        <div class="grid-container">
          <WorkloadGrid 
            workload={data.workload}
            onEdit={async (item) => {
              try {
                console.log('Saving workload item:', item);
                
                // Prepare data for saving
                const workloadData = {
                  ...item,
                  computerId: computerId
                };
                
                // Determine if it's an update or create
                const isUpdate = item._id && !item._id.startsWith('temp-');
                
                // API endpoint
                const endpoint = isUpdate 
                  ? `/api/service-activities/workload/${item._id}`
                  : '/api/service-activities/workload';
                
                // HTTP method
                const method = isUpdate ? 'PUT' : 'POST';
                
                // Save to database
                const response = await fetch(endpoint, {
                  method,
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify(workloadData),
                });
                
                if (!response.ok) {
                  throw new Error(`Failed to save: ${response.statusText}`);
                }
                
                // Get the updated data for logging only
                const result = await response.json();
                console.log('Saved workload data:', result);
                
                // Don't refresh or invalidate data to keep the display stable
                // The saved data will be available next time the page loads
                
              } catch (error) {
                console.error('Error saving workload data:', error);
                alert(`Error saving workload data: ${error.message}`);
              }
            }}
          />
        </div>
      </div>
      
      <!-- Add ActualComputerHoursVersion2 Grid -->
      <div class="content-section full-width-component">
        <h2>Actual Computer Hours Version 2</h2>
        <div class="grid-container">
          <ActualComputerHoursVersion2Grid 
            actualComputerHours={data.actualComputerHours}
            filterComputerId={computerId}
          />
        </div>
      </div>
      
      <!-- Add ActualComputerHours Grid in a window component -->
      <div class="content-section full-width-component">
        <h2>Actual Computer Hours</h2>
        <div class="grid-container">
          <ActualComputerHoursGrid 
            actualComputerHours={data.actualComputerHours}
            filterComputerId={computerId}
          />
        </div>
      </div>
      
      <!-- Active Fault Codes Grid in a window component -->
      <div class="content-section full-width-component">
        <h2>Active Fault Codes</h2>
        <div class="grid-container">
          <ActiveFaultCodesGrid 
            faultCodes={data.activeFaultCodes}
          />
        </div>
      </div>
      
      <!-- Service Actions for Demarcation -->
      <div class="content-section full-width-component">
        <h2>Service Actions for Demarcation</h2>
        <div class="grid-container">
          <DemarcationServiceGrid 
            serviceActions={data.serviceActions || []}
            demarcation={demarcationName}
            on:edit={handleServiceActionEdit}
            on:add={handleAddServiceAction}
          />
        </div>
      </div>
      
      <!-- Service Code Types Grid -->
      <div class="content-section full-width-component">
        <h2>Service Code Types</h2>
        <div class="grid-container">
          <ServiceCodeTypesGrid 
            serviceCodeTypes={data.serviceCodeTypes || []}
            productDemarcation={data.computer?.productDesignation || data.computer?.ProductDesignation || ''}
          />
        </div>
      </div>
    </div>
  </div>
{/if}

<!-- Edit Activity Modal -->
<EditActivityModal 
  activity={selectedActivity}
  show={showEditModal}
  on:close={() => showEditModal = false}
  on:updated={handleActivityUpdated}
/>

<!-- Matrix Cell Editor -->
{#if showCellEditor && editingCell}
  <MatrixCellEditor
    cellData={editingCell}
    position={editPosition}
    computerId={computerId}
    isNew={!editingCell._id}
    on:close={() => showCellEditor = false}
    on:saved={handleCellEdited}
    on:error={(e) => console.error('Cell edit error:', e.detail.message)}
  />
{/if}

<!-- Service Action Editor -->
{#if showServiceActionEditor && selectedServiceAction}
  <ServiceActionEditor
    action={selectedServiceAction}
    on:save={handleServiceActionSave}
    on:cancel={handleServiceActionCancel}
  />
{/if}

<style>
  .fullscreen-layout {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: linear-gradient(to bottom, #1a1f2e, #111827);
    color: #f3f4f6;
  }
  
  .app-header {
    background: rgba(17, 24, 39, 0.95);
    backdrop-filter: blur(8px);
    border-bottom: 1px solid rgba(75, 85, 99, 0.3);
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    position: sticky;
    top: 0;
    z-index: 10;
  }
  
  .main-content {
    display: grid;
    grid-template-columns: 1fr;
    grid-auto-rows: min-content;
    padding: 1.5rem;
    gap: 2.5rem;
  }
  
  .content-section {
    background: rgba(17, 24, 39, 0.9);
    backdrop-filter: blur(12px);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(63, 63, 70, 0.4);
    width: 100%;
    display: grid;
    grid-template-rows: auto 1fr;
  }
  
  .bulk-operations-panel h2,
  .activity-matrix-panel h2,
  .full-width-component h2 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 1.25rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  
  .header-content {
    display: flex;
    align-items: center;
    gap: 2rem;
    width: 100%;
  }
  
  .page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #f3f4f6;
    margin: 0;
    white-space: nowrap;
  }
  
  .breadcrumb {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    color: #9ca3af;
    font-size: 0.875rem;
  }
  
  .breadcrumb-item {
    cursor: pointer;
  }
  
  .breadcrumb-divider {
    color: #6b7280;
  }
  
  .computer-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .computer-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #f3f4f6;
    margin: 0;
  }
  
  .computer-id {
    font-size: 0.9rem;
    color: #64748b;
    font-weight: 400;
    margin-left: 0.5rem;
  }
  
  .product-designation {
    font-size: 1rem;
    color: #1e40af;
    font-weight: 500;
    margin-left: 1rem;
    padding: 0.25rem 0.75rem;
    background-color: rgba(219, 234, 254, 0.7);
    border-radius: 4px;
  }
  
  .matrix-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(75, 85, 99, 0.2);
  }
  
  .year-filter {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }
  
  .year-filter label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }
  
  .year-filter select {
    padding: 0.5rem 2rem 0.5rem 0.75rem;
    border-radius: 6px;
    background-color: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(75, 85, 99, 0.5);
    color: #f3f4f6;
    font-size: 0.875rem;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%239ca3af'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.5rem center;
    background-size: 1rem;
  }
  
  .show-all-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .show-all-toggle label {
    font-size: 0.875rem;
    font-weight: 500;
    user-select: none;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }
  
  .matrix-container {
    overflow: auto;
    margin-top: 0.5rem;
    border-radius: 8px;
    flex: 1;
    padding: 0 1.5rem 1.5rem;
  }
  
  .matrix-container table {
    width: 100%;
    border-collapse: collapse;
  }
  
  .matrix-container th {
    position: sticky;
    top: 0;
    background-color: rgba(17, 24, 39, 0.95);
    z-index: 10;
    padding: 0.75rem;
    text-align: center;
    font-weight: 500;
    font-size: 0.875rem;
    color: #d1d5db;
    border-bottom: 2px solid rgba(75, 85, 99, 0.3);
  }
  
  .matrix-container td {
    padding: 0.5rem;
    font-size: 0.875rem;
    border: 1px solid rgba(75, 85, 99, 0.2);
    cursor: pointer;
    text-align: center;
    transition: background-color 0.15s ease;
  }
  
  .matrix-container td:hover {
    background-color: rgba(59, 130, 246, 0.1);
  }
  
  .matrix-container tr:nth-child(even) {
    background-color: rgba(30, 41, 59, 0.3);
  }
  
  .matrix-container tr:nth-child(odd) {
    background-color: rgba(30, 41, 59, 0.1);
  }
  
  .matrix-container tr:hover {
    background-color: rgba(30, 41, 59, 0.5);
  }
  
  .year-label {
    font-weight: 500;
    color: #e5e7eb;
    min-width: 5rem;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding-left: 1rem;
  }
  
  .year-total {
    font-weight: 500;
    color: #93c5fd;
  }
  
  .current-month {
    position: relative;
  }
  
  .current-month::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(90deg, #60a5fa, #3b82f6);
    border-radius: 3px;
  }
  
  .matrix-container::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }
  
  .matrix-container::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.2);
    border-radius: 10px;
  }
  
  .matrix-container::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, rgba(37, 99, 235, 0.5), rgba(59, 130, 246, 0.6));
    border-radius: 10px;
  }
  
  .matrix-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, rgba(59, 130, 246, 0.7), rgba(37, 99, 235, 0.8));
  }
  
  /* Card-style form elements */
  .form-group {
    position: relative;
    transition: transform 0.2s ease;
  }
  
  .form-group:hover {
    transform: translateY(-2px);
  }
  
  .form-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }
  
  .form-input, .form-select {
    padding: 0.75rem 1rem;
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 0.875rem;
    transition: all 0.2s ease;
  }
  
  .integer-badge {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(90deg, #2563eb, #3b82f6);
    color: white;
    font-size: 0.65rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    letter-spacing: 0.05em;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  .helper-text {
    font-size: 0.75rem;
    color: #e5e7eb;
    margin-top: 0.25rem;
  }
  
  /* Enhanced date range styling */
  .date-ranges {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    background: rgba(30, 41, 59, 0.3);
    padding: 1rem;
    border-radius: 10px;
    border: 1px solid rgba(75, 85, 99, 0.3);
    margin-bottom: 1rem;
  }
  
  .date-section h3 {
    margin-top: 0;
    margin-bottom: 0.75rem;
    font-size: 1rem;
    color: #93c5fd;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .date-section h3::before {
    content: '';
    display: block;
    width: 4px;
    height: 16px;
    background: linear-gradient(90deg, #3b82f6, #2563eb);
    border-radius: 2px;
  }
  
  /* Enhanced engine hours styling */
  .integer-input-container {
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 2px solid rgba(75, 85, 99, 0.5);
    transition: all 0.2s ease;
    background: rgba(30, 41, 59, 0.8);
  }
  
  .integer-input-container:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
  }
  
  .form-input {
    border: none;
    box-shadow: none;
    background: transparent;
  }
  
  /* Action button with pulsing effect */
  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
    }
  }
  
  .btn-primary {
    animation: pulse 2s infinite;
    background: linear-gradient(45deg, #2563eb, #4f46e5);
    font-weight: 600;
    letter-spacing: 0.03em;
    position: relative;
    overflow: hidden;
    box-shadow: 0 6px 12px -2px rgba(37, 99, 235, 0.5);
  }
  
  .btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    transition: left 0.5s;
  }
  
  .btn-primary:hover::before {
    left: 100%;
  }
  
  .btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: linear-gradient(to right, #4b5563, #374151);
    box-shadow: none;
    animation: none;
  }
  
  /* Responsive adjustments */
  @media (max-width: 1200px) {
    .main-content {
      grid-template-columns: 1fr;
      grid-template-rows: auto 1fr;
    }
    
    .bulk-operations-panel {
      height: auto;
    }
  }
  
  @media (max-width: 768px) {
    .form-row {
      grid-template-columns: 1fr;
    }
    
    .date-ranges {
      grid-template-columns: 1fr;
    }
    
    .app-header {
      padding: 1rem;
    }
    
    .main-content {
      padding: 1rem;
      gap: 1rem;
    }
  }
  
  .matrix-table .faultcode-cell {
    position: relative;
    background-color: rgba(239, 68, 68, 0.15);
    border: 1px solid rgba(239, 68, 68, 0.3);
  }
  
  .matrix-table .faultcode-cell:hover {
    background-color: rgba(239, 68, 68, 0.25);
  }
  
  .fault-codes-section {
    margin-top: 2rem;
    padding: 1.5rem;
    background-color: rgba(17, 24, 39, 0.7);
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  
  .fault-codes-section h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 1rem;
    color: #ffffff;
    border-bottom: 1px solid rgba(75, 85, 99, 0.3);
    padding-bottom: 0.75rem;
  }
  
  .no-data-message {
    padding: 1.5rem;
    text-align: center;
    color: #9ca3af;
    font-style: italic;
    background-color: rgba(31, 41, 55, 0.5);
    border-radius: 0.5rem;
  }
  
  .activity-label {
    font-size: 0.8rem;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .fault-indicator {
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 0.8rem;
  }
  
  /* Full width component styles */
  .full-width-component {
    width: 100%;
    display: grid;
    grid-template-rows: auto 1fr;
    min-height: 500px;
    margin-bottom: 0; /* Remove bottom margin */
  }
  
  .grid-container {
    display: grid;
    grid-template-columns: 1fr;
    height: 100%;
    min-height: 450px;
    overflow: auto;
    width: 100%;
  }
  
  /* Ensure grids take full height and width of their containers */
  :global(.grid-container > :global(*)) {
    height: 100%;
    width: 100%;
  }

  /* Base Grid Layout - make sure grids use full width */
  :global(.basegrid) {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-rows: 1fr;
  }
  
  .cell-success {
    background-color: #34c759;
  }
  
  .cell-warning {
    background-color: #ff9900;
  }
  
  .enhanced-matrix {
    background-color: #1a1f2e;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
  }
</style>
