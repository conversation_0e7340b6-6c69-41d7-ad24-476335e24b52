import { MongoClient } from 'mongodb';
import { json } from '@sveltejs/kit';

// MongoDB connection URI
const uri = 'mongodb://localhost:27017';

// POST: Create a new price list item
export async function POST({ request }) {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    
    // Using PascalCase for collection name per project convention
    const priceList = db.collection('PriceList');
    
    // Get the new item data from the request body
    const itemData = await request.json();
    
    // Validate required fields
    if (!itemData.name) {
      return new Response(JSON.stringify({ error: 'Name is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Insert the new price list item
    const result = await priceList.insertOne(itemData);
    
    // Return success response with the new ID
    return json({ 
      success: true, 
      id: result.insertedId.toString(),
      message: 'Price list item created successfully'
    });
    
  } catch (error) {
    console.error('Error creating price list item:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  } finally {
    await client.close();
  }
}
