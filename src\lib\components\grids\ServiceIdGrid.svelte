<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  
  /**
   * ServiceIdGrid - A specialized grid component for displaying service ID data
   * Extends the BaseGrid component with specific styling for service ID listings
   */
  
  // Define the ServiceItem interface to properly type our data
  interface ServiceItem {
    _id: string;
    "Service Code": string;
    "Part Description": string;
    "Part Number": string;
    "Qty Per Service": string;
    "Discount Code": string;
    "SCOS": string;
    "SCOS Qty Adj": string;
    "RRP": string;
    "Dealer Net": string;
    "CAF": string;
    "Dealer Reimbursement": string;
    "RRP Qty Adj": string;
    "isActive": boolean;
    "machine": string;
    "serviceCategory": string;
    "createdAt": string;
    "updatedAt": string;
    [key: string]: any; // Allow for additional properties
  }
  
  export let items: ServiceItem[] = [];
  export let onDelete: (item: ServiceItem) => void = () => {};
  export let onSelect: (item: ServiceItem) => void = () => {};
  export let onView: (item: ServiceItem) => void = () => {};
  export let onEdit: (id: string) => string = (id: string) => `/service-id/edit/${id}`;
  export let loading: boolean = false;
  export let searchQuery: string = '';
  export let filterType: string = 'All Types';
  export let onClearFilters: () => void = () => {};
</script>

<BaseGrid>
  <div class="service-id-grid">
    {#if loading}
      <div class="loading-container">
        <div class="spinner"></div>
        <span>Loading service data...</span>
      </div>
    {:else if items.length === 0 && (searchQuery || filterType !== 'All Types')}
      <div class="empty-state">
        <div class="empty-icon">
          <span class="material-icons">filter_alt_off</span>
        </div>
        <h3>No matching services found</h3>
        <p>No service items match your current filters</p>
        <button class="btn secondary" on:click={onClearFilters}>
          <span class="material-icons">clear</span>
          Clear Filters
        </button>
      </div>
    {:else if items.length === 0}
      <div class="empty-state">
        <div class="empty-icon">
          <span class="material-icons">inventory</span>
        </div>
        <h3>No Service IDs Found</h3>
        <p>Get started by adding your first service ID</p>
        <a href="/service-id/create" class="btn primary">
          <span class="material-icons">add</span>
          Add New Service
        </a>
      </div>
    {:else}
      <div class="table-header">
        <div class="col actions">Actions</div>
        <div class="col">Service Code</div>
        <div class="col">Part Description</div>
        <div class="col">Part Number</div>
        <div class="col">Qty Per Service</div>
        <div class="col">Discount Code</div>
        <div class="col">SCOS</div>
        <div class="col">SCOS Qty Adj</div>
        <div class="col">RRP</div>
        <div class="col">Dealer Net</div>
        <div class="col">CAF</div>
        <div class="col">Dealer Reimbursement</div>
        <div class="col">RRP Qty Adj</div>
        <div class="col">isActive</div>
        <div class="col">machine</div>
        <div class="col">serviceCategory</div>
        <div class="col">Created At</div>
        <div class="col">Updated At</div>
      </div>
      <div class="table-body">
        {#each items as item (item._id)}
          <div class="table-row">
            <div class="col actions">
              <button class="btn select" on:click={() => onSelect(item)}>Select</button>
              <button class="btn delete" on:click={() => onDelete(item)}>Delete</button>
              <button class="action-button view" title="View Details" on:click={() => onView(item)}>
                <span class="material-icons">visibility</span>
              </button>
              <a href={onEdit(item._id)} class="action-button edit" title="Edit Service">
                <span class="material-icons">edit</span>
              </a>
            </div>
            <div class="col">{item["Service Code"]}</div>
            <div class="col">{item["Part Description"]}</div>
            <div class="col">{item["Part Number"]}</div>
            <div class="col">{item["Qty Per Service"]}</div>
            <div class="col">{item["Discount Code"]}</div>
            <div class="col">{item["SCOS"]}</div>
            <div class="col">{item["SCOS Qty Adj"]}</div>
            <div class="col">{item["RRP"]}</div>
            <div class="col">{item["Dealer Net"]}</div>
            <div class="col">{item["CAF"]}</div>
            <div class="col">{item["Dealer Reimbursement"]}</div>
            <div class="col">{item["RRP Qty Adj"]}</div>
            <div class="col">{item["isActive"] ? 'Yes' : 'No'}</div>
            <div class="col">{item["machine"]}</div>
            <div class="col">{item["serviceCategory"]}</div>
            <div class="col">{item["createdAt"]}</div>
            <div class="col">{item["updatedAt"]}</div>
          </div>
        {/each}
      </div>
    {/if}
  </div>
</BaseGrid>

<style>
  .service-id-grid {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
  }
  
  .table-header, .table-row {
    display: grid;
    grid-template-columns: repeat(18, minmax(120px, 1fr));
    align-items: center;
    gap: 0.25rem;
  }
  
  .table-header {
    font-weight: bold;
    background: #f0f0f0;
    border-radius: 6px;
    padding: 0.5rem 0;
  }
  
  .table-row {
    background: #fff;
    border-radius: 6px;
    padding: 0.5rem 0;
    transition: background 0.2s;
  }
  
  .table-row:hover {
    background: #f9f9f9;
  }
  
  .col.actions {
    display: flex;
    gap: 0.5rem;
  }
  
  .btn.select {
    background: #1976d2;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 0.25rem 0.75rem;
    cursor: pointer;
  }
  
  .btn.delete {
    background: #d32f2f;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 0.25rem 0.75rem;
    cursor: pointer;
  }
  
  .action-button {
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 4px;
    transition: all 0.2s ease;
    color: #6c757d;
  }
  
  .action-button:hover {
    background-color: #f1f3f5;
  }
  
  .action-button.view:hover {
    color: #0a2463;
  }
  
  .action-button.edit:hover {
    color: #0d6efd;
  }
  
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    text-align: center;
    height: 100%;
  }
  
  .empty-icon {
    font-size: 4rem;
    color: #e0e6ed;
    margin-bottom: 1rem;
  }
  
  .empty-icon .material-icons {
    font-size: 4rem;
  }
  
  .empty-state h3 {
    margin: 0 0 0.5rem;
    color: #34495e;
    font-size: 1.5rem;
  }
  
  .empty-state p {
    margin: 0 0 1.5rem;
    color: #6c757d;
  }
  
  .btn {
    padding: 0.6rem 1.2rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: none;
  }
  
  .primary {
    background-color: #0a2463;
    color: white;
  }
  
  .primary:hover {
    background-color: #1e3a8a;
  }
  
  .secondary {
    background-color: #e9ecef;
    color: #495057;
  }
  
  .secondary:hover {
    background-color: #dee2e6;
  }
  
  .loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #6c757d;
    gap: 1rem;
    height: 100%;
  }
  
  .spinner {
    width: 24px;
    height: 24px;
    border: 3px solid rgba(10, 36, 99, 0.3);
    border-radius: 50%;
    border-top-color: #0a2463;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
  
  @media (max-width: 1200px) {
    .table-header, .table-row {
      grid-template-columns: repeat(12, minmax(120px, 1fr));
    }
    
    .col.SCOSSQtyAdj, .col.RRPQtyAdj, .col.isActive, .col.machine, .col.serviceCategory {
      display: none;
    }
  }
  
  @media (max-width: 768px) {
    .table-header, .table-row {
      grid-template-columns: repeat(6, minmax(120px, 1fr));
    }
    
    .col.DiscountCode,
    .col.SCOSSQtyAdj,
    .col.RRPQtyAdj,
    .col.isActive,
    .col.machine,
    .col.serviceCategory,
    .col.createdAt,
    .col.updatedAt {
      display: none;
    }
  }
</style>
