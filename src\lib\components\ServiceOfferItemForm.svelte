<script>
  import { createEventDispatcher } from 'svelte';

  const dispatch = createEventDispatcher();

  export let item = {};
  export let priceListItems = [];

  // Form data
  let formData = {
    _id: item._id || '',
    serviceCode: item.serviceCode || '',
    description: item.description || '',
    activity: item.activity || '',
    quantity: item.quantity || 1,
    unitPrice: item.unitPrice || 0,
    totalPrice: item.totalPrice || 0,
    isEnabled: item.isEnabled !== false,
    isRequired: item.isRequired || false,
    category: item.category || 'Custom',
    notes: item.notes || '',
    source: item.source || 'Manual'
  };

  // Categories
  const categories = [
    'Service',
    'Maintenance', 
    'Repair',
    'Installation',
    'Consulting',
    'Parts',
    'Custom'
  ];

  // Activities
  const activities = [
    'Inspection',
    'Replacement',
    'Repair',
    'Calibration',
    'Testing',
    'Installation',
    'Consulting',
    'Training',
    'Other'
  ];

  // Update total price when quantity or unit price changes
  $: formData.totalPrice = (formData.quantity || 0) * (formData.unitPrice || 0);

  // Find matching price list item
  function findPriceListItem() {
    if (!formData.serviceCode) return null;
    return priceListItems.find(item => 
      item["Part No"]?.toString() === formData.serviceCode ||
      item["Description"]?.toLowerCase().includes(formData.serviceCode.toLowerCase())
    );
  }

  // Auto-fill from price list
  function autoFillFromPriceList() {
    const priceItem = findPriceListItem();
    if (priceItem) {
      formData.description = priceItem["Description"] || formData.description;
      formData.unitPrice = parseFloat(priceItem["Price excl VAT"]?.replace(',', '.')) || formData.unitPrice;
    }
  }

  // Save item
  function saveItem() {
    // Validate required fields
    if (!formData.serviceCode.trim()) {
      alert('Service code is required');
      return;
    }
    
    if (!formData.description.trim()) {
      alert('Description is required');
      return;
    }

    if (formData.quantity <= 0) {
      alert('Quantity must be greater than 0');
      return;
    }

    dispatch('save', formData);
  }

  // Cancel
  function cancel() {
    dispatch('cancel');
  }
</script>

<div class="modal-overlay" on:click={cancel}>
  <div class="modal" on:click|stopPropagation>
    <div class="modal-header">
      <h3>{item._id?.startsWith('new-') ? 'Add New Service Item' : 'Edit Service Item'}</h3>
      <button class="close-button" on:click={cancel}>×</button>
    </div>

    <div class="modal-body">
      <form on:submit|preventDefault={saveItem}>
        <div class="form-grid">
          <div class="form-group">
            <label for="serviceCode">Service Code *</label>
            <div class="input-with-button">
              <input
                type="text"
                id="serviceCode"
                bind:value={formData.serviceCode}
                required
                placeholder="Enter service code"
              />
              <button 
                type="button" 
                class="auto-fill-button"
                on:click={autoFillFromPriceList}
                title="Auto-fill from price list"
              >
                🔍
              </button>
            </div>
          </div>

          <div class="form-group">
            <label for="category">Category</label>
            <select id="category" bind:value={formData.category}>
              {#each categories as category}
                <option value={category}>{category}</option>
              {/each}
            </select>
          </div>

          <div class="form-group full-width">
            <label for="description">Description *</label>
            <input
              type="text"
              id="description"
              bind:value={formData.description}
              required
              placeholder="Enter service description"
            />
          </div>

          <div class="form-group full-width">
            <label for="activity">Activity</label>
            <select id="activity" bind:value={formData.activity}>
              <option value="">Select activity</option>
              {#each activities as activity}
                <option value={activity}>{activity}</option>
              {/each}
            </select>
          </div>

          <div class="form-group">
            <label for="quantity">Quantity *</label>
            <input
              type="number"
              id="quantity"
              bind:value={formData.quantity}
              min="0.01"
              step="0.01"
              required
            />
          </div>

          <div class="form-group">
            <label for="unitPrice">Unit Price (EUR) *</label>
            <input
              type="number"
              id="unitPrice"
              bind:value={formData.unitPrice}
              min="0"
              step="0.01"
              required
            />
          </div>

          <div class="form-group">
            <label>Total Price (EUR)</label>
            <div class="calculated-value">
              {new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'EUR'
              }).format(formData.totalPrice)}
            </div>
          </div>

          <div class="form-group">
            <div class="checkbox-group">
              <label class="checkbox-label">
                <input
                  type="checkbox"
                  bind:checked={formData.isRequired}
                />
                Required Service
              </label>
            </div>
          </div>

          <div class="form-group full-width">
            <label for="notes">Notes</label>
            <textarea
              id="notes"
              bind:value={formData.notes}
              rows="3"
              placeholder="Additional notes or specifications"
            ></textarea>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" on:click={cancel}>
            Cancel
          </button>
          <button type="submit" class="btn btn-primary">
            {item._id?.startsWith('new-') ? 'Add Item' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<style>
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
  }

  .modal {
    background: white;
    border-radius: 8px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
  }

  .modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: #111827;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6b7280;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
  }

  .close-button:hover {
    background: #f3f4f6;
    color: #374151;
  }

  .modal-body {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
  }

  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .form-group.full-width {
    grid-column: 1 / -1;
  }

  .form-group label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    font-family: inherit;
  }

  .form-group input:focus,
  .form-group select:focus,
  .form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .input-with-button {
    display: flex;
    gap: 8px;
  }

  .input-with-button input {
    flex: 1;
  }

  .auto-fill-button {
    padding: 8px 12px;
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
  }

  .auto-fill-button:hover {
    background: #e5e7eb;
  }

  .calculated-value {
    padding: 8px 12px;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    color: #059669;
  }

  .checkbox-group {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
  }

  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
    color: #374151;
  }

  .checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    margin: 0;
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px;
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
  }

  .btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
  }

  .btn-primary {
    background: #3b82f6;
    color: white;
  }

  .btn-primary:hover {
    background: #2563eb;
  }

  .btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
  }

  .btn-secondary:hover {
    background: #e5e7eb;
  }

  @media (max-width: 640px) {
    .modal {
      margin: 10px;
      max-height: calc(100vh - 20px);
    }

    .form-grid {
      grid-template-columns: 1fr;
    }

    .modal-footer {
      flex-direction: column-reverse;
    }
  }
</style>
