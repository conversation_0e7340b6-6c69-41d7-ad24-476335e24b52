import { MongoClient } from 'mongodb';

// This script directly checks the PartNumbersServiceCodeAction collection
// to verify field names and data

async function checkCollection() {
  const uri = 'mongodb://localhost:27017';
  const client = new MongoClient(uri);

  try {
    console.log('Connecting to MongoDB...');
    await client.connect();
    console.log('Connected successfully');

    const db = client.db('ServiceContracts');
    console.log('Using ServiceContracts database');

    // Check if collection exists
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);
    console.log('Available collections:', collectionNames);

    if (!collectionNames.includes('PartNumbersServiceCodeAction')) {
      console.error('ERROR: PartNumbersServiceCodeAction collection does not exist!');
      return;
    }

    // Count documents
    const collection = db.collection('PartNumbersServiceCodeAction');
    const count = await collection.countDocuments();
    console.log(`PartNumbersServiceCodeAction collection has ${count} documents`);

    // Get sample documents
    const samples = await collection.find().limit(5).toArray();
    console.log('Sample documents:');
    samples.forEach((doc, i) => {
      console.log(`\nDocument ${i+1}:`);
      console.log('_id:', doc._id.toString());
      console.log('Field names:', Object.keys(doc));
      console.log('Document content:', JSON.stringify(doc, null, 2));
      
      // Check specifically for Product Validity Group field
      console.log('Has "Product Validity Group":', doc.hasOwnProperty('Product Validity Group'));
      if (doc['Product Validity Group']) {
        console.log('Product Validity Group value:', doc['Product Validity Group']);
      }
      
      // Check for other similar field names
      const allKeys = Object.keys(doc);
      const similarKeys = allKeys.filter(key => 
        key.toLowerCase().includes('product') && 
        key.toLowerCase().includes('validity')
      );
      if (similarKeys.length > 0) {
        console.log('Similar field names found:', similarKeys);
      }
    });

  } catch (err) {
    console.error('Database error:', err);
  } finally {
    await client.close();
    console.log('Connection closed');
  }
}

checkCollection();
