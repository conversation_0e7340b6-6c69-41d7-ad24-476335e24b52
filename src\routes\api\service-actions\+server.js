// API endpoint for handling service actions operations
import { json } from '@sveltejs/kit';
import { ObjectId } from 'mongodb';
import { client } from '$lib/db';

const dbName = 'ServiceContracts';

// Get all service actions
export async function GET({ url }) {
  try {
    const db = client.db(dbName);
    
    // Get query parameters
    const demarcation = url.searchParams.get('demarcation');
    const computerId = url.searchParams.get('computerId');
    
    // Build query
    const query = {};
    if (demarcation) query.demarcation = demarcation;
    if (computerId) query.computerId = computerId;
    
    const serviceActions = await db.collection('ServiceActions').find(query).toArray();
    
    return json(serviceActions);
  } catch (error) {
    console.error('Error fetching service actions:', error);
    return json({ error: String(error) }, { status: 500 });
  }
}

// Create a new service action
export async function POST({ request }) {
  try {
    const db = client.db(dbName);
    const data = await request.json();
    
    // Validate required fields
    if (!data.title || !data.demarcation) {
      return json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    // Convert computerId to ObjectId if it's a string
    if (data.computerId && typeof data.computerId === 'string') {
      data.computerId = new ObjectId(data.computerId);
    }
    
    // Add timestamps
    const now = new Date();
    data.createdAt = now;
    data.updatedAt = now;
    
    // Insert record
    const result = await db.collection('ServiceActions').insertOne(data);
    
    return json({ 
      _id: result.insertedId,
      ...data
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating service action:', error);
    return json({ error: String(error) }, { status: 500 });
  }
}
