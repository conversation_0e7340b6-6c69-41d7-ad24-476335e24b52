<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  
  // Props
  export let minCardWidth: string = "300px";
  export let gap: string = "1.5rem";
  export let padding: string = "1.5rem";
</script>

<BaseGrid>
  <div class="card-layout-grid" style="--min-card-width: {minCardWidth}; --gap: {gap}; --padding: {padding}">
    <slot></slot>
  </div>
</BaseGrid>

<style>
  .card-layout-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(var(--min-card-width), 1fr));
    gap: var(--gap);
    padding: var(--padding);
    height: 100%;
    width: 100%;
  }
</style>
