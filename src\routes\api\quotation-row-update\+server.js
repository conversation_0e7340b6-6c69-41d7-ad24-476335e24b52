import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/**
 * PUT: Update a quote row by ID
 * This endpoint replaces the conflicting routes
 */
export async function PUT({ request }) {
  try {
    const db = client.db('ServiceContracts');
    const updateData = await request.json();
    
    // Get row ID from the request body
    const { _id, ...dataToUpdate } = updateData;
    
    if (!_id) {
      return json({ success: false, message: 'Missing row ID' }, { status: 400 });
    }
    
    // Validate ObjectId format
    if (!ObjectId.isValid(_id)) {
      return json({ success: false, message: 'Invalid row ID format' }, { status: 400 });
    }
    
    // Handle ObjectId fields if present
    if (dataToUpdate.quotationId && ObjectId.isValid(dataToUpdate.quotationId)) {
      dataToUpdate.quotationId = new ObjectId(dataToUpdate.quotationId);
    }
    
    // Add timestamp
    dataToUpdate.updatedAt = new Date();
    
    console.log(`Updating quote row ${_id} with data:`, dataToUpdate);
    
    // Update the document in QuotationRows collection (using PascalCase per convention)
    const result = await db.collection('QuotationRows').updateOne(
      { _id: new ObjectId(_id) },
      { $set: dataToUpdate }
    );
    
    if (result.matchedCount === 0) {
      return json({ success: false, message: 'Quote row not found' }, { status: 404 });
    }
    
    return json({ 
      success: true, 
      message: 'Quote row updated successfully',
      modifiedCount: result.modifiedCount
    });
  } catch (error) {
    console.error('Error updating quote row:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return json({ success: false, message: errorMessage }, { status: 500 });
  }
}
