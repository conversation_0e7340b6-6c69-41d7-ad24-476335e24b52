<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  
  // Props
  export let years: number[] = [];
  export let selectedYear: number | null = null;
  export let showAllOption: boolean = true;
  
  // Event dispatcher
  const dispatch = createEventDispatcher<{
    select: number | null;
  }>();
  
  // Handle year selection
  function selectYear(year: number | null) {
    selectedYear = year;
    dispatch('select', year);
  }
</script>

<div class="year-filter-tabs">
  {#if showAllOption}
    <button 
      class="year-tab {selectedYear === null ? 'active' : ''}" 
      on:click={() => selectYear(null)}
    >
      All Years
    </button>
  {/if}
  
  {#each years as year}
    <button 
      class="year-tab {selectedYear === year ? 'active' : ''}" 
      on:click={() => selectYear(year)}
    >
      {year}
    </button>
  {/each}
</div>

<style>
  .year-filter-tabs {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 0.5rem;
    background: #1e293b;
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
  }
  
  .year-tab {
    background: #0f172a;
    color: #94a3b8;
    border: 1px solid #334155;
    border-radius: 6px;
    padding: 0.5rem;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;
  }
  
  .year-tab:hover {
    background: #1e293b;
    color: #e2e8f0;
  }
  
  .year-tab.active {
    background: #3b82f6;
    color: #ffffff;
    border-color: #3b82f6;
  }
</style>
