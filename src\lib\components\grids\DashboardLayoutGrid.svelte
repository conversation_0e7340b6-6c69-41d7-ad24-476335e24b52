<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  
  // Props
  export let columns: number = 3;
  export let gap: string = "1.5rem";
</script>

<BaseGrid>
  <div class="dashboard-grid" style="--columns: {columns}; --gap: {gap}">
    <slot></slot>
  </div>
</BaseGrid>

<style>
  .dashboard-grid {
    display: grid;
    grid-template-columns: repeat(var(--columns), 1fr);
    gap: var(--gap);
    width: 100%;
    height: 100%;
  }
  
  @media (max-width: 1200px) {
    .dashboard-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media (max-width: 768px) {
    .dashboard-grid {
      grid-template-columns: 1fr;
    }
  }
</style>
