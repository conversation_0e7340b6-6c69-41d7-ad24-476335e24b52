const { MongoClient } = require('mongodb');

// Connection URI
const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

// Database Name
const dbName = 'ServiceContracts';

// Collections and their expected field names to check
const collectionsToCheck = [
    {
        name: 'BaseServices',
        fieldToFind: 'Product Validity Group'
    },
    {
        name: 'PartNumbersServiceCodeAction',
        fieldToFind: 'Product Validity Group'
    },
    {
        name: 'ProductValidityGroup',
        fieldToFind: 'Product Validity GRoup'  // Note the typo in GRoup
    },
    {
        name: 'ServiceCodeHeader',
        fieldToFind: 'Product Validity Group'
    }
];

async function checkFieldNames() {
    try {
        await client.connect();
        const db = client.db(dbName);

        // Check each collection
        for (const collection of collectionsToCheck) {
            console.log(`\nChecking collection: ${collection.name}`);
            
            // Find documents with the old field name
            const query = { [collection.fieldToFind]: { $exists: true } };
            const count = await db.collection(collection.name).countDocuments(query);
            console.log(`Documents with field "${collection.fieldToFind}": ${count}`);

            if (count > 0) {
                // Get a sample document
                const doc = await db.collection(collection.name).findOne(query);
                console.log('\nSample document field names and types:');
                Object.entries(doc).forEach(([key, value]) => {
                    console.log(`  - ${key}: ${typeof value} = ${JSON.stringify(value)}`);
                });

                // Show the specific field value
                console.log(`\nValue of "${collection.fieldToFind}":`, doc[collection.fieldToFind]);
            }

            // Check if any documents have the new field name
            const newFieldQuery = { 'ProductValidityGroup': { $exists: true } };
            const newFieldCount = await db.collection(collection.name).countDocuments(newFieldQuery);
            console.log(`Documents with new field "ProductValidityGroup": ${newFieldCount}`);

            if (newFieldCount > 0) {
                const doc = await db.collection(collection.name).findOne(newFieldQuery);
                console.log('Sample value of new field:', doc.ProductValidityGroup);
            }
        }

    } finally {
        await client.close();
    }
}

// Run the check
checkFieldNames();
