import { MongoClient } from 'mongodb';

// MongoDB connection URI
const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function showWorkloadData() {
  try {
    // Connect to MongoDB
    await client.connect();
    console.log('Connected to MongoDB');

    // Get the ServiceContracts database
    const db = client.db('ServiceContracts');
    
    // Collections to check for workload data
    const workloadCollections = [
      'LabourTime',
      'ServiceCodeHeader',
      'BaseServices',
      'PartNumbersServiceCodeAction'
    ];
    
    // Check each collection for workload data
    for (const collectionName of workloadCollections) {
      const collection = db.collection(collectionName);
      const count = await collection.countDocuments();
      
      console.log(`
===== ${collectionName} Collection (${count} documents) =====`);
      
      if (count > 0) {
        // Get a sample of documents (limit to 5 for readability)
        const items = await collection.find({}).limit(5).toArray();
        
        // Display the details for each document
        items.forEach((item, index) => {
          console.log(`
[${index + 1}] Document Details:`);
          
          // Format based on collection type
          if (collectionName === 'LabourTime') {
            console.log(`Service Code: ${item['Service Code'] || 'N/A'}`);
            console.log(`Service Description: ${item['Service Description'] || 'N/A'}`);
            console.log(`VST Code: ${item['VST Code'] || 'N/A'}`);
            console.log(`VST Hours: ${item['VST Hours'] || 'N/A'}`);
            console.log(`ServicePhase: ${item.ServicePhase || 'N/A'}`);
            console.log(`ComputerCategory: ${item.ComputerCategory || 'N/A'}`);
          } else if (collectionName === 'ServiceCodeHeader' || collectionName === 'BaseServices') {
            console.log(`ServiceCode: ${item.ServiceCode || 'N/A'}`);
            console.log(`Service activity Label: ${item['Service activity Label'] || 'N/A'}`);
            console.log(`Activity purpose: ${item['Activity purpose'] || 'N/A'}`);
            console.log(`Product Validity Group: ${item.ProductValidityGroup || item['Product Validity Group'] || 'N/A'}`);
            console.log(`Internal No of Hours: ${item['Internal No of Hours'] || 'N/A'}`);
            console.log(`Internal No of Months: ${item['Internal No of Months'] || 'N/A'}`);
          } else if (collectionName === 'PartNumbersServiceCodeAction') {
            console.log(`ServiceCode: ${item.ServiceCode || 'N/A'}`);
            console.log(`PartNumber: ${item.PartNumber || 'N/A'}`);
            console.log(`ActionType: ${item.ActionType || 'N/A'}`);
            console.log(`Product Validity Group: ${item.ProductValidityGroup || item['Product Validity Group'] || 'N/A'}`);
            console.log(`Quantity: ${item.Quantity || 'N/A'}`);
            console.log(`Unit of Measure: ${item['Unit of Measure'] || 'N/A'}`);
          } else {
            // Generic display for other collections
            const displayKeys = Object.keys(item).filter(key => key !== '_id');
            displayKeys.forEach(key => {
              console.log(`${key}: ${JSON.stringify(item[key])}`);
            });
          }
        });
        
        if (count > 5) {
          console.log(`
... and ${count - 5} more documents`);
        }
      } else {
        console.log('No documents found in this collection');
      }
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the connection
    await client.close();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the function
showWorkloadData();
