<script>
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import TableGrid from '$lib/components/grids/TableGrid.svelte';
	import { invalidateAll } from '$app/navigation';

	export let data;

	let quoteRows = [];
	let loading = true;
	let computerId = $page.params.id;
	let errorMessage = '';

	onMount(async () => {
		try {
			const response = await fetch(`/api/quote-rows/${computerId}`);
			if (response.ok) {
				quoteRows = await response.json();
			} else {
				errorMessage = await response.text();
				console.error('Failed to load quote rows:', errorMessage);
			}
		} catch (err) {
			errorMessage = err.message;
			console.error('Error loading quote rows:', err);
		} finally {
			loading = false;
		}
	});

	const handleView = (row) => {
		// Navigate to view page
		window.location.href = `/computer-id/${computerId}/computer-quote/view/${row._id}`;
	};

	const handleEdit = (row) => {
		// Navigate to edit page
		window.location.href = `/computer-id/${computerId}/computer-quote/edit/${row._id}`;
	};

	const handleDelete = async (row) => {
		if (confirm(`Are you sure you want to delete this quote row for ${row.service}?`)) {
			try {
				const response = await fetch(`/api/quote-rows/${row._id}`, {
					method: 'DELETE'
				});
				
				if (response.ok) {
					// Remove item from array to update UI
					quoteRows = quoteRows.filter(item => item._id !== row._id);
				} else {
					alert('Failed to delete item');
					console.error('Delete failed:', await response.text());
				}
			} catch (err) {
				alert('Error deleting item');
				console.error('Delete error:', err);
			}
		}
	};
</script>

<div class="computer-quote-container">
	<div class="header">
		<h1>Computer Quote Management</h1>
		<div class="computer-info">
			<h2>Computer ID: {computerId}</h2>
		</div>
		<div class="actions">
			<a href="/computer-id/{computerId}/quote-list" class="btn secondary">Back to Quote List</a>
			<a href="/computer-id/{computerId}/computer-quote/new" class="btn primary">Add New Quote Item</a>
		</div>
	</div>

	{#if loading}
		<div class="loading">Loading quote data...</div>
	{:else if errorMessage}
		<div class="error">Error: {errorMessage}</div>
	{:else if quoteRows.length === 0}
		<div class="no-data">No quote rows found for this computer.</div>
	{:else}
		<TableGrid>
			<div slot="header" class="quote-table-header">
				<div class="cell">Level</div>
				<div class="cell">Package</div>
				<div class="cell">Service ID</div>
				<div class="cell">Service</div>
				<div class="cell">Included</div>
				<div class="cell">Required</div>
				<div class="cell">SCOS</div>
				<div class="cell">OEM Import</div>
				<div class="cell">Fleet Owner</div>
				<div class="cell">RRP</div>
				<div class="cell">Select Service</div>
				<div class="cell">Qty per Yr</div>
				<div class="cell">Actions</div>
			</div>
			<div slot="content" class="quote-table-body">
				{#each quoteRows as row}
					<div class="row">
						<div class="cell">{row.level}</div>
						<div class="cell">{row.package}</div>
						<div class="cell">{row.serviceId}</div>
						<div class="cell">{row.service}</div>
						<div class="cell">{row.included}</div>
						<div class="cell">{row.required}</div>
						<div class="cell">{typeof row.scos === 'number' ? `£ ${row.scos.toFixed(2)}` : row.scos}</div>
						<div class="cell">{typeof row.oemImport === 'number' ? `£ ${row.oemImport.toFixed(2)}` : row.oemImport}</div>
						<div class="cell">{typeof row.fleetOwner === 'number' ? `£ ${row.fleetOwner.toFixed(2)}` : row.fleetOwner}</div>
						<div class="cell">{typeof row.rrp === 'number' ? `£ ${row.rrp.toFixed(2)}` : row.rrp}</div>
						<div class="cell">{row.selectService}</div>
						<div class="cell">{row.qtyPerYr}</div>
						<div class="cell actions">
							<button on:click={() => handleView(row)} class="icon-btn view-btn" title="View">
								<span class="material-icons">visibility</span>
							</button>
							<button on:click={() => handleEdit(row)} class="icon-btn edit-btn" title="Edit">
								<span class="material-icons">edit</span>
							</button>
							<button on:click={() => handleDelete(row)} class="icon-btn delete-btn" title="Delete">
								<span class="material-icons">delete</span>
							</button>
						</div>
					</div>
				{/each}
			</div>
		</TableGrid>
	{/if}
</div>

<style>
	.computer-quote-container {
		padding: 1rem;
		max-width: 100%;
		overflow-x: auto;
	}

	.header {
		display: flex;
		flex-direction: column;
		margin-bottom: 1.5rem;
	}

	.computer-info {
		margin-bottom: 1rem;
	}

	.actions {
		display: flex;
		gap: 1rem;
		margin-bottom: 1rem;
	}

	.btn {
		padding: 0.5rem 1rem;
		border-radius: 4px;
		text-decoration: none;
		font-weight: 500;
		cursor: pointer;
	}

	.primary {
		background-color: #3498db;
		color: white;
	}

	.secondary {
		background-color: #f1f1f1;
		color: #333;
	}

	.quote-table-header, .row {
		display: grid;
		grid-template-columns: 
			60px 150px 100px 180px 100px 100px 
			100px 100px 100px 100px 130px 80px 120px;
		grid-gap: 8px;
		align-items: center;
	}

	.quote-table-header {
		font-weight: bold;
		background-color: #f1f1f1;
		border-radius: 4px 4px 0 0;
		padding: 0.5rem;
	}

	.quote-table-body {
		width: 100%;
	}

	.row {
		padding: 0.5rem;
		border-bottom: 1px solid #eee;
	}

	.row:nth-child(even) {
		background-color: #f9f9f9;
	}

	.row:hover {
		background-color: #f0f0f0;
	}

	.actions {
		display: flex;
		gap: 0.5rem;
		justify-content: center;
	}

	.icon-btn {
		background: none;
		border: none;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 4px;
		border-radius: 4px;
	}

	.view-btn {
		color: #3498db;
	}

	.edit-btn {
		color: #f39c12;
	}

	.delete-btn {
		color: #e74c3c;
	}

	.icon-btn:hover {
		background-color: rgba(0, 0, 0, 0.05);
	}

	.loading, .error, .no-data {
		padding: 2rem;
		text-align: center;
	}

	.error {
		color: #e74c3c;
	}
</style>
