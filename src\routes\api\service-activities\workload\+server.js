// API endpoint for handling workload operations
import { json } from '@sveltejs/kit';
import { ObjectId } from 'mongodb';
import { client } from '$lib/db';

const dbName = 'ServiceContracts';

// Create a new workload record
export async function POST({ request }) {
  try {
    // Get the database
    const db = client.db(dbName);
    const data = await request.json();
    
    // Validate required fields
    if (!data.computerId || !data.year || !data.month || data.hours === undefined) {
      return json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    // Convert computerId to ObjectId if it's a string
    if (typeof data.computerId === 'string') {
      data.computerId = new ObjectId(data.computerId);
    }
    
    // Add timestamps
    const now = new Date();
    data.createdAt = now;
    data.updatedAt = now;
    
    // Insert record
    const result = await db.collection('Workload').insertOne(data);
    
    return json({ 
      _id: result.insertedId,
      ...data
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating workload record:', error);
    return json({ error: error.message }, { status: 500 });
  }
}

// Get all workload records
export async function GET() {
  try {
    // Get the database
    const db = client.db(dbName);
    const workload = await db.collection('Workload').find({}).toArray();
    
    return json(workload);
  } catch (error) {
    console.error('Error fetching workload records:', error);
    return json({ error: error.message }, { status: 500 });
  }
}
