<script>
  import { goto } from '$app/navigation';
</script>

<div class="workflow-activity">
  <h1>Add a New Customer</h1>
  <button class="back-btn" on:click={() => goto('/')}>Back</button>
</div>

<style>
.workflow-activity {
  max-width: 480px;
  margin: 4rem auto;
  padding: 2rem;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 16px rgba(0,0,0,0.08);
  text-align: center;
}
.workflow-activity h1 {
  color: #0a2463;
  font-size: 2rem;
  margin-bottom: 2rem;
}
.back-btn {
  background: orangered;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.15s;
}
.back-btn:hover {
  background: #c0392b;
}
</style>
