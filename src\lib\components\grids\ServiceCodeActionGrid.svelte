<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  
  // Define column type
  interface Column {
    id: string;
    label: string;
    width: string;
  }

  // Define the column structure - each column has a consistent ID, header label and filter placeholder
  export let columns: Column[] = [
    { id: 'productGroup', label: 'Product Group', width: '10%' },
    { id: 'activity', label: 'Activity', width: '10%' },
    { id: 'label', label: 'Label', width: '10%' },
    { id: 'serviceCode', label: 'Service Code', width: '10%' },
    { id: 'actionType', label: 'Action Type', width: '10%' },
    { id: 'partNumber', label: 'Part #', width: '10%' },
    { id: 'unit', label: 'Unit', width: '10%' },
    { id: 'qty', label: 'Qty', width: '10%' },
    { id: 'hours', label: 'Hours', width: '10%' },
    { id: 'months', label: 'Months', width: '10%' } 
  ];
  
  // Optional extra column for actions
  export let showActions = true;
  
  // Controls whether filter inputs are shown
  export let showFilters = true;
  
  // Pass any items through as a prop with proper typing
  export let items: Record<string, any>[] = [];
  
  // Compute CSS variable for columns
  $: columnCount = showActions ? columns.length + 1 : columns.length;
  $: columnTemplate = columns.map(col => col.width).join(' ') + (showActions ? ' auto' : '');
</script>

<BaseGrid>
  <div class="service-code-action-grid" style="--column-count: {columnCount}; --column-template: {columnTemplate};">
    <!-- Filter Row -->
    {#if showFilters}
      <div class="filter-row">
        {#each columns as column}
          <div class="filter-cell" style="width: {column.width}">
            <!-- Using static slots for each column type -->
            {#if column.id === 'productGroup'}
              <slot name="filter-productGroup">
                <div class="filter-placeholder">{column.label}</div>
              </slot>
            {:else if column.id === 'activity'}
              <slot name="filter-activity">
                <div class="filter-placeholder">{column.label}</div>
              </slot>
            {:else if column.id === 'label'}
              <slot name="filter-label">
                <div class="filter-placeholder">{column.label}</div>
              </slot>
            {:else if column.id === 'serviceCode'}
              <slot name="filter-serviceCode">
                <div class="filter-placeholder">{column.label}</div>
              </slot>
            {:else if column.id === 'actionType'}
              <slot name="filter-actionType">
                <div class="filter-placeholder">{column.label}</div>
              </slot>
            {:else if column.id === 'partNumber'}
              <slot name="filter-partNumber">
                <div class="filter-placeholder">{column.label}</div>
              </slot>
            {:else if column.id === 'unit'}
              <slot name="filter-unit">
                <div class="filter-placeholder">{column.label}</div>
              </slot>
            {:else if column.id === 'qty'}
              <slot name="filter-qty">
                <div class="filter-placeholder">{column.label}</div>
              </slot>
            {:else if column.id === 'hours'}
              <slot name="filter-hours">
                <div class="filter-placeholder">{column.label}</div>
              </slot>
            {:else if column.id === 'months'}
              <slot name="filter-months">
                <div class="filter-placeholder">{column.label}</div>
              </slot>
            {:else}
              <div class="filter-placeholder">{column.label}</div>
            {/if}
          </div>
        {/each}
        
        {#if showActions}
          <div class="filter-cell actions-cell">
            <slot name="filter-actions"></slot>
          </div>
        {/if}
      </div>
    {/if}
    
    <!-- Header Row -->
    <div class="header-row">
      {#each columns as column}
        <div class="header-cell" style="width: {column.width}">
          <!-- Using static slots for header cells -->
          {#if column.id === 'productGroup'}
            <slot name="header-productGroup">{column.label}</slot>
          {:else if column.id === 'activity'}
            <slot name="header-activity">{column.label}</slot>
          {:else if column.id === 'label'}
            <slot name="header-label">{column.label}</slot>
          {:else if column.id === 'serviceCode'}
            <slot name="header-serviceCode">{column.label}</slot>
          {:else if column.id === 'actionType'}
            <slot name="header-actionType">{column.label}</slot>
          {:else if column.id === 'partNumber'}
            <slot name="header-partNumber">{column.label}</slot>
          {:else if column.id === 'unit'}
            <slot name="header-unit">{column.label}</slot>
          {:else if column.id === 'qty'}
            <slot name="header-qty">{column.label}</slot>
          {:else if column.id === 'hours'}
            <slot name="header-hours">{column.label}</slot>
          {:else if column.id === 'months'}
            <slot name="header-months">{column.label}</slot>
          {:else}
            {column.label}
          {/if}
        </div>
      {/each}
      
      {#if showActions}
        <div class="header-cell actions-cell">
          <slot name="header-actions">Actions</slot>
        </div>
      {/if}
    </div>
    
    <!-- Content Rows -->
    <div class="content-rows">
      <slot name="content">
        {#each items as item}
          <div class="data-row">
            {#each columns as column}
              <div class="data-cell" style="width: {column.width}">
                <!-- Using static slots for data cells -->
                {#if column.id === 'productGroup'}
                  <slot name="cell-productGroup" {item}>{item[column.id] || ''}</slot>
                {:else if column.id === 'activity'}
                  <slot name="cell-activity" {item}>{item[column.id] || ''}</slot>
                {:else if column.id === 'label'}
                  <slot name="cell-label" {item}>{item[column.id] || ''}</slot>
                {:else if column.id === 'serviceCode'}
                  <slot name="cell-serviceCode" {item}>{item[column.id] || ''}</slot>
                {:else if column.id === 'actionType'}
                  <slot name="cell-actionType" {item}>{item[column.id] || ''}</slot>
                {:else if column.id === 'partNumber'}
                  <slot name="cell-partNumber" {item}>{item[column.id] || ''}</slot>
                {:else if column.id === 'unit'}
                  <slot name="cell-unit" {item}>{item[column.id] || ''}</slot>
                {:else if column.id === 'qty'}
                  <slot name="cell-qty" {item}>{item[column.id] || ''}</slot>
                {:else if column.id === 'hours'}
                  <slot name="cell-hours" {item}>{item[column.id] || ''}</slot>
                {:else if column.id === 'months'}
                  <slot name="cell-months" {item}>{item[column.id] || ''}</slot>
                {:else}
                  {item[column.id] || ''}
                {/if}
              </div>
            {/each}
            
            {#if showActions}
              <div class="data-cell actions-cell">
                <slot name="cell-actions" {item}>
                  <!-- Default actions -->
                </slot>
              </div>
            {/if}
          </div>
        {/each}
      </slot>
    </div>
  </div>
</BaseGrid>

<style>
  .service-code-action-grid {
    display: grid;
    grid-template-rows: auto auto 1fr;
    width: 100%;
    border-collapse: collapse;
    overflow-x: auto;
  }
  
  .filter-row, .header-row, .data-row {
    display: grid;
    grid-template-columns: var(--column-template);
    width: 100%;
    min-width: max-content; /* Prevents content from being cut off */
  }
  
  .filter-cell, .header-cell, .data-cell {
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    overflow: visible; /* Allow long content to be fully visible */
    white-space: normal; /* Allow text to wrap */
    word-break: break-word; /* Break long words if needed */
  }
  
  .header-cell {
    background-color: #f7fafc;
    font-weight: 600;
    text-align: left;
    border-bottom: 2px solid #a0aec0;
  }
  
  .filter-placeholder {
    color: #a0aec0;
    font-style: italic;
    font-size: 0.9rem;
  }
  
  .actions-cell {
    justify-self: center;
    text-align: center;
  }
  
  .data-row:nth-child(even) {
    background-color: #f9fafb;
  }
  
  .data-row:hover {
    background-color: #edf2f7;
  }
  
  .content-rows {
    display: contents; /* This makes the children of this div behave as direct children of the grid */
  }
  
  /* Mobile optimization */
  @media (max-width: 768px) {
    .service-code-action-grid {
      overflow-x: auto;
    }
    
    .filter-row, .header-row, .data-row {
      width: max-content;
      min-width: 100%;
    }
  }
</style>
