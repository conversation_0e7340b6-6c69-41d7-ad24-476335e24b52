import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/** @type {import('./$types').RequestHandler} */
export async function PUT({ request, params }) {
    try {
        const computerId = params.id;
        const updates = await request.json();
        
        const db = client.db('ServiceContracts');
        
        // Validate computer exists
        const existingComputer = await db.collection('CustomerComputers')
            .findOne({ _id: new ObjectId(computerId) });
            
        if (!existingComputer) {
            return new Response(JSON.stringify({
                success: false,
                message: 'Computer not found'
            }), {
                status: 404,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }

        // Remove _id from updates if present
        delete updates._id;
        
        // Convert string IDs back to ObjectIds
        if (updates.customerId) {
            updates.customerId = new ObjectId(updates.customerId);
        }

        // Update computer
        await db.collection('CustomerComputers')
            .updateOne(
                { _id: new ObjectId(computerId) },
                { $set: updates }
            );

        // Also update ComputerDetails if needed
        if (updates['Product Designation'] || updates['Product Part Number'] || updates['Product Validity Group']) {
            await db.collection('ComputerDetails').updateOne(
                { computerId: new ObjectId(computerId) },
                {
                    $set: {
                        'Product Designation': updates['Product Designation'],
                        'Product Part Number': updates['Product Part Number'],
                        'Product Validity Group': updates['Product Validity Group'],
                        updatedAt: new Date()
                    },
                    $setOnInsert: {
                        computerId: new ObjectId(computerId),
                        createdAt: new Date()
                    }
                },
                { upsert: true }
            );
        }

        return new Response(JSON.stringify({
            success: true,
            message: 'Computer updated successfully'
        }), {
            headers: {
                'Content-Type': 'application/json'
            }
        });

    } catch (error) {
        console.error('Error updating computer:', error);
        return new Response(JSON.stringify({
            success: false,
            message: 'Failed to update computer'
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
}
