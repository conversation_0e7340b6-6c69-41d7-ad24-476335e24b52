<!-- WorkloadEditor.svelte -->
<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    
    const dispatch = createEventDispatcher();
    
    // Define the workload item interface
    export let item = {
        _id: '',
        computerId: '',
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
        hours: 0,
        activity: '',
        hasFixed: false,
        type: 'Workload'
    };
    
    export let isOpen = false;
    
    // Local state for editing
    let editedItem = { ...item };
    
    // Activities options
    const activityOptions = [
        'Contract Service',
        'Installation',
        'Repair',
        'Service',
        'Maintenance',
        'Training',
        'Consulting',
        'Project',
        'Other'
    ];
    
    // Reset form when item changes
    $: if (item && item._id) {
        editedItem = { ...item };
    }
    
    function handleSave() {
        // Validate the form
        if (!editedItem.activity) {
            alert('Please select an activity');
            return;
        }
        
        if (editedItem.hours < 0) {
            alert('Hours cannot be negative');
            return;
        }
        
        // Dispatch save event with the edited item
        dispatch('save', editedItem);
    }
    
    function handleCancel() {
        // Close without saving
        dispatch('cancel');
    }
</script>

<div class="workload-editor-backdrop" class:visible={isOpen} on:click={handleCancel} role="dialog" aria-modal="true" tabindex="-1" on:keydown={(e) => e.key === 'Escape' && handleCancel()}>
    <div class="workload-editor-modal" on:click|stopPropagation role="group" tabindex="-1">
        <div class="editor-header">
            <h3>Edit Workload Entry</h3>
            <button class="close-button" on:click={handleCancel}>&times;</button>
        </div>
        
        <div class="editor-form">
            <div class="form-section">
                <h4>Time Period (Fixed)</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="year">Year</label>
                        <input 
                            id="year" 
                            type="text" 
                            value={editedItem.year}
                            readonly
                            class="readonly-field"
                        />
                    </div>
                    
                    <div class="form-group">
                        <label for="month">Month</label>
                        <input 
                            id="month" 
                            type="text" 
                            value={[
                                'January', 'February', 'March', 'April', 
                                'May', 'June', 'July', 'August', 
                                'September', 'October', 'November', 'December'
                            ][editedItem.month - 1]}
                            readonly
                            class="readonly-field"
                        />
                    </div>
                </div>
            </div>
            
            <div class="form-section">
                <h4>Workload Details</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="hours">Hours</label>
                        <input 
                            id="hours" 
                            type="number" 
                            bind:value={editedItem.hours} 
                            min="0" 
                            max="999" 
                            step="0.5"
                            class="focus-field"
                        />
                    </div>
                    
                    <div class="form-group">
                        <label for="activity">Activity</label>
                        <select id="activity" bind:value={editedItem.activity} class="focus-field">
                            <option value="">Select an activity</option>
                            {#each activityOptions as option}
                                <option value={option}>{option}</option>
                            {/each}
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group checkbox-group">
                        <label>
                            <input type="checkbox" bind:checked={editedItem.hasFixed} />
                            <span>Fixed</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="button-row">
                <button class="cancel-button" on:click={handleCancel}>Cancel</button>
                <button class="save-button" on:click={handleSave}>Save</button>
            </div>
        </div>
    </div>
</div>

<style>
    .workload-editor-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.2s ease;
    }
    
    .workload-editor-backdrop.visible {
        opacity: 1;
        pointer-events: auto;
    }
    
    .workload-editor-modal {
        background-color: white;
        border-radius: 8px;
        width: 500px;
        max-width: 90%;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }
    
    .editor-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background-color: #102a54;
        color: white;
    }
    
    .editor-header h3 {
        margin: 0;
        font-size: 1.25rem;
    }
    
    .close-button {
        background: transparent;
        border: none;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
    }
    
    .editor-form {
        padding: 1.5rem;
    }
    
    .form-section {
        margin-bottom: 1.5rem;
    }
    
    .form-section h4 {
        margin-top: 0;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #4b5563;
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1rem;
    }
    
    .form-group {
        display: flex;
        flex-direction: column;
    }
    
    .form-group label {
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #4b5563;
    }
    
    .form-group input,
    .form-group select {
        padding: 0.5rem;
        border: 1px solid #d1d5db;
        border-radius: 4px;
        font-size: 1rem;
    }
    
    .readonly-field {
        background-color: #f3f4f6;
        cursor: not-allowed;
    }
    
    .focus-field {
        border-color: #1e40af;
        box-shadow: 0 0 0 2px #1e40af;
    }
    
    .checkbox-group {
        display: flex;
        align-items: center;
    }
    
    .checkbox-group label {
        display: flex;
        align-items: center;
        cursor: pointer;
    }
    
    .checkbox-group input {
        margin-right: 0.5rem;
    }
    
    .button-row {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        margin-top: 1.5rem;
    }
    
    .cancel-button,
    .save-button {
        padding: 0.5rem 1rem;
        border-radius: 4px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .cancel-button {
        background-color: #f3f4f6;
        border: 1px solid #d1d5db;
        color: #4b5563;
    }
    
    .save-button {
        background-color: #1e40af;
        border: 1px solid #1e3a8a;
        color: white;
    }
    
    .cancel-button:hover {
        background-color: #e5e7eb;
    }
    
    .save-button:hover {
        background-color: #1e3a8a;
    }
</style>
