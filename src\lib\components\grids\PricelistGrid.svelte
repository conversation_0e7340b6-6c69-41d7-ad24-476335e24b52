<script>
  import { createEventDispatcher } from 'svelte';
  
  /**
   * @type {Array<{
   *   _id: string;
   *   partNumber: string;
   *   description: string;
   *   unitPrice: number;
   *   currency: string;
   *   category: string;
   *   validFrom: Date | null;
   *   validTo: Date | null;
   *   supplier: string;
   *   discountCategory: string;
   *   isActive: boolean;
   *   updatedAt: Date | null;
   *   [key: string]: any; // Index signature for dynamic access
   * }>}
   */
  export let items = [];
  
  // For filtering and sorting
  export let searchTerm = '';
  export let sortField = 'partNumber';
  export let sortDirection = 'asc';
  
  // For pagination (optional)
  export let pageSize = 10;
  export let currentPage = 1;
  
  const dispatch = createEventDispatcher();
  
  // Filter items based on search term
  $: filteredItems = items.filter(item => {
    if (!searchTerm) return true;
    
    const term = searchTerm.toLowerCase();
    return (
      (item.partNumber && item.partNumber.toLowerCase().includes(term)) ||
      (item.description && item.description.toLowerCase().includes(term)) ||
      (item.category && item.category.toLowerCase().includes(term)) ||
      (item.supplier && item.supplier.toLowerCase().includes(term))
    );
  });
  
  // Sort items
  $: sortedItems = [...filteredItems].sort((a, b) => {
    let valueA = a[sortField];
    let valueB = b[sortField];
    
    // Handle special case for price (numeric)
    if (sortField === 'unitPrice') {
      valueA = parseFloat(valueA) || 0;
      valueB = parseFloat(valueB) || 0;
    } else {
      // String comparison for other fields
      valueA = valueA ? valueA.toString().toLowerCase() : '';
      valueB = valueB ? valueB.toString().toLowerCase() : '';
    }
    
    if (valueA < valueB) return sortDirection === 'asc' ? -1 : 1;
    if (valueA > valueB) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });
  
  // Get paginated items
  $: start = (currentPage - 1) * pageSize;
  $: end = start + pageSize;
  $: paginatedItems = sortedItems.slice(start, end);
  
  // Handle sorting
  function handleSort(field) {
    if (sortField === field) {
      sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      sortField = field;
      sortDirection = 'asc';
    }
  }
  
  // Handle keyboard interaction for sortable columns
  function handleKeyDown(event, field) {
    // Handle Enter or Space key for accessible sorting
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleSort(field);
    }
  }
  
  // Handle view details
  function viewDetails(id) {
    dispatch('viewDetails', id);
  }
  
  // Format date for display
  function formatDate(date) {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString();
  }
  
  // Format currency for display
  function formatCurrency(amount, currency) {
    return `${parseFloat(String(amount)).toFixed(2)} ${currency}`;
  }
</script>

<div class="pricelist-grid-container">
  <div class="pricelist-grid">
    <div class="pricelist-header" role="rowgroup">
      <div class="column sortable" role="columnheader" tabindex="0" on:click={() => handleSort('partNumber')} on:keydown={(e) => handleKeyDown(e, 'partNumber')}>
        Part Number
        {#if sortField === 'partNumber'}
          <span class="sort-icon">{sortDirection === 'asc' ? '↑' : '↓'}</span>
        {/if}
      </div>
      <div class="column sortable" role="columnheader" tabindex="0" on:click={() => handleSort('description')} on:keydown={(e) => handleKeyDown(e, 'description')}>
        Description
        {#if sortField === 'description'}
          <span class="sort-icon">{sortDirection === 'asc' ? '↑' : '↓'}</span>
        {/if}
      </div>
      <div class="column sortable" role="columnheader" tabindex="0" on:click={() => handleSort('unitPrice')} on:keydown={(e) => handleKeyDown(e, 'unitPrice')}>
        Price
        {#if sortField === 'unitPrice'}
          <span class="sort-icon">{sortDirection === 'asc' ? '↑' : '↓'}</span>
        {/if}
      </div>
      <div class="column sortable" role="columnheader" tabindex="0" on:click={() => handleSort('category')} on:keydown={(e) => handleKeyDown(e, 'category')}>
        Category
        {#if sortField === 'category'}
          <span class="sort-icon">{sortDirection === 'asc' ? '↑' : '↓'}</span>
        {/if}
      </div>
      <div class="column sortable" role="columnheader" tabindex="0" on:click={() => handleSort('supplier')} on:keydown={(e) => handleKeyDown(e, 'supplier')}>
        Supplier
        {#if sortField === 'supplier'}
          <span class="sort-icon">{sortDirection === 'asc' ? '↑' : '↓'}</span>
        {/if}
      </div>
      <div class="column sortable" role="columnheader" tabindex="0" on:click={() => handleSort('isActive')} on:keydown={(e) => handleKeyDown(e, 'isActive')}>
        Status
        {#if sortField === 'isActive'}
          <span class="sort-icon">{sortDirection === 'asc' ? '↑' : '↓'}</span>
        {/if}
      </div>
      <div class="column" role="columnheader">Actions</div>
    </div>
    
    {#if paginatedItems.length > 0}
      {#each paginatedItems as item (item._id)}
        <div class="pricelist-row" role="row">
          <div class="column" role="cell">{item.partNumber || 'N/A'}</div>
          <div class="column" role="cell">{item.description || 'N/A'}</div>
          <div class="column" role="cell">{formatCurrency(item.unitPrice, item.currency)}</div>
          <div class="column" role="cell">{item.category || 'N/A'}</div>
          <div class="column" role="cell">{item.supplier || 'N/A'}</div>
          <div class="column" role="cell">
            <span class="status-badge {item.isActive ? 'active' : 'inactive'}">
              {item.isActive ? 'Active' : 'Inactive'}
            </span>
          </div>
          <div class="column actions" role="cell">
            <button class="view-button" on:click={() => viewDetails(item._id)}>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                <circle cx="12" cy="12" r="3"></circle>
              </svg>
              View
            </button>
          </div>
        </div>
      {/each}
    {:else}
      <div class="empty-message">
        <p>No pricelist items found{searchTerm ? ' matching "' + searchTerm + '"' : ''}.</p>
      </div>
    {/if}
  </div>
  
  {#if sortedItems.length > pageSize}
    <div class="pagination">
      <div class="pagination-info">
        Showing {start + 1} to {Math.min(end, sortedItems.length)} of {sortedItems.length} items
      </div>
      <div class="pagination-controls">
        <button 
          class="pagination-button" 
          disabled={currentPage === 1}
          on:click={() => currentPage = Math.max(1, currentPage - 1)}
        >
          Previous
        </button>
        
        {#each Array(Math.ceil(sortedItems.length / pageSize)) as _, i}
          <button 
            class="pagination-button {currentPage === i + 1 ? 'active' : ''}" 
            on:click={() => currentPage = i + 1}
          >
            {i + 1}
          </button>
        {/each}
        
        <button 
          class="pagination-button" 
          disabled={currentPage === Math.ceil(sortedItems.length / pageSize)}
          on:click={() => currentPage = Math.min(Math.ceil(sortedItems.length / pageSize), currentPage + 1)}
        >
          Next
        </button>
      </div>
    </div>
  {/if}
</div>

<style>
  /* CSS Grid-based layout, as per user preference */
  .pricelist-grid-container {
    width: 100%;
    overflow-x: auto;
  }
  
  .pricelist-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0;
    border: 1px solid #eee;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .pricelist-header {
    display: grid;
    grid-template-columns: 1.5fr 2fr 1fr 1fr 1fr 1fr 1fr;
    gap: 0;
    background-color: #f5f5f5;
    font-weight: bold;
    border-bottom: 2px solid #ddd;
  }
  
  .pricelist-row {
    display: grid;
    grid-template-columns: 1.5fr 2fr 1fr 1fr 1fr 1fr 1fr;
    gap: 0;
    border-bottom: 1px solid #eee;
  }
  
  .pricelist-row:hover {
    background-color: #f9f9f9;
  }
  
  .column {
    padding: 0.75rem;
    display: flex;
    align-items: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .sortable {
    cursor: pointer;
  }
  
  .sortable:hover {
    background-color: #eee;
  }
  
  .sort-icon {
    margin-left: 0.5rem;
    font-size: 0.8rem;
  }
  
  .status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
  }
  
  .status-badge.active {
    background-color: #e8f5e9;
    color: #2e7d32;
  }
  
  .status-badge.inactive {
    background-color: #ffebee;
    color: #c62828;
  }
  
  .actions {
    display: flex;
    gap: 0.5rem;
  }
  
  .view-button {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    background-color: #2196f3;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
  }
  
  .view-button:hover {
    background-color: #1976d2;
  }
  
  .empty-message {
    grid-column: 1 / -1;
    padding: 2rem;
    text-align: center;
    color: #757575;
  }
  
  .pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
  }
  
  .pagination-info {
    color: #757575;
    font-size: 0.9rem;
  }
  
  .pagination-controls {
    display: flex;
    gap: 0.25rem;
  }
  
  .pagination-button {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    min-width: 2rem;
    text-align: center;
  }
  
  .pagination-button.active {
    background-color: #2196f3;
    color: white;
    border-color: #2196f3;
  }
  
  .pagination-button:disabled {
    background-color: #f5f5f5;
    color: #bdbdbd;
    cursor: not-allowed;
  }
  
  @media (max-width: 992px) {
    .pricelist-header, 
    .pricelist-row {
      grid-template-columns: 1.5fr 1.5fr 1fr 1fr 1fr 1fr;
    }
    
    .column:nth-child(5) {
      display: none;
    }
  }
  
  @media (max-width: 768px) {
    .pricelist-header, 
    .pricelist-row {
      grid-template-columns: 1.5fr 1.5fr 1fr 1fr;
    }
    
    .column:nth-child(4),
    .column:nth-child(5) {
      display: none;
    }
  }
</style>
