import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function renameCollection() {
    const client = new MongoClient(uri);
    
    try {
        await client.connect();
        console.log('Connected to MongoDB');
        
        const db = client.db(dbName);
        
        // Check if the old collection exists
        const collections = await db.listCollections().toArray();
        const oldCollectionExists = collections.some(col => col.name === 'Laborinfo');
        
        if (!oldCollectionExists) {
            console.log('Collection "Laborinfo" does not exist, no need to rename');
            return;
        }

        // Rename the collection
        await db.collection('Laborinfo').rename('LabourTime');
        console.log('Successfully renamed collection from "Laborinfo" to "LabourTime"');
        
    } catch (error) {
        console.error('Error renaming collection:', error);
    } finally {
        await client.close();
        console.log('Disconnected from MongoDB');
    }
}

// Run the rename operation
renameCollection().catch(console.error);
