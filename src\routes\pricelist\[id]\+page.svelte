<script lang="ts">
  import { enhance } from '$app/forms';
  import { goto } from '$app/navigation';

  export let data: import('./$types').PageData;

  let item = data.item as any;
  let isEditing = false;
  let formData = {
    partNo: item["Part No"] || '',
    checkDigit: item["Check digit"] || 0,
    description: item["Description"] || '',
    priceExclVAT: item["Price excl VAT"] || '',
    discountCode: item["Discount Code"] || 0,
    productGroup: item["Product group"] || 0,
    functionGroup: item["Function group"] || 0,
    statisticNo: item["Statistic No"] || 0,
    weight: item[" Weight "] || '',
    countryOfOrigin: item["Country of Origin"] || ''
  };

  function toggleEdit() {
    isEditing = !isEditing;
    if (!isEditing) {
      // Reset form data when canceling edit
      formData = {
        partNo: item["Part No"] || '',
        checkDigit: item["Check digit"] || 0,
        description: item["Description"] || '',
        priceExclVAT: item["Price excl VAT"] || '',
        discountCode: item["Discount Code"] || 0,
        productGroup: item["Product group"] || 0,
        functionGroup: item["Function group"] || 0,
        statisticNo: item["Statistic No"] || 0,
        weight: item[" Weight "] || '',
        countryOfOrigin: item["Country of Origin"] || ''
      };
    }
  }

  function deleteItem() {
    if (confirm(`Are you sure you want to delete "${item["Part No"]}"?`)) {
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = '?/delete';

      document.body.appendChild(form);
      form.addEventListener('submit', async (e) => {
        e.preventDefault();

        const response = await fetch('?/delete', {
          method: 'POST'
        });

        if (response.ok) {
          goto('/pricelist');
        }

        document.body.removeChild(form);
      });

      form.requestSubmit();
    }
  }


</script>

<div class="container">
  <div class="header">
    <div class="header-left">
      <button class="btn-secondary back-button" on:click={() => goto('/pricelist')}>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
        </svg>
        Back to Price List
      </button>
      <h1>Price List Item Details</h1>
    </div>
    <div class="header-actions">
      {#if !isEditing}
        <button class="btn-primary" on:click={toggleEdit}>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
          </svg>
          Edit Item
        </button>
        <button class="btn-danger" on:click={deleteItem}>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
          Delete Item
        </button>
      {/if}
    </div>
  </div>

  {#if isEditing}
    <!-- Edit Form -->
    <div class="edit-form">
      <h2>Edit Price List Item</h2>
      <form
        action="?/update"
        method="POST"
        use:enhance={() => {
          return async ({ result }) => {
            if (result.type === 'success') {
              isEditing = false;
              window.location.reload();
            }
          };
        }}
      >
        <div class="form-grid">
          <div class="form-group">
            <label for="partNo">Part Number*</label>
            <input
              type="number"
              id="partNo"
              name="partNo"
              required
              bind:value={formData.partNo}
            />
          </div>

          <div class="form-group">
            <label for="checkDigit">Check Digit</label>
            <input
              type="number"
              id="checkDigit"
              name="checkDigit"
              min="0"
              bind:value={formData.checkDigit}
            />
          </div>

          <div class="form-group">
            <label for="description">Description*</label>
            <input
              type="text"
              id="description"
              name="description"
              required
              bind:value={formData.description}
            />
          </div>

          <div class="form-group">
            <label for="priceExclVAT">Price excl VAT*</label>
            <input
              type="text"
              id="priceExclVAT"
              name="priceExclVAT"
              required
              bind:value={formData.priceExclVAT}
              placeholder="e.g., 1,90"
            />
          </div>

          <div class="form-group">
            <label for="discountCode">Discount Code</label>
            <input
              type="number"
              id="discountCode"
              name="discountCode"
              min="0"
              bind:value={formData.discountCode}
            />
          </div>

          <div class="form-group">
            <label for="productGroup">Product Group</label>
            <input
              type="number"
              id="productGroup"
              name="productGroup"
              min="0"
              bind:value={formData.productGroup}
            />
          </div>

          <div class="form-group">
            <label for="functionGroup">Function Group</label>
            <input
              type="number"
              id="functionGroup"
              name="functionGroup"
              min="0"
              bind:value={formData.functionGroup}
            />
          </div>

          <div class="form-group">
            <label for="statisticNo">Statistic No</label>
            <input
              type="number"
              id="statisticNo"
              name="statisticNo"
              min="0"
              bind:value={formData.statisticNo}
            />
          </div>

          <div class="form-group">
            <label for="weight">Weight</label>
            <input
              type="text"
              id="weight"
              name="weight"
              bind:value={formData.weight}
              placeholder="e.g., 0,034"
            />
          </div>

          <div class="form-group">
            <label for="countryOfOrigin">Country of Origin</label>
            <input
              type="text"
              id="countryOfOrigin"
              name="countryOfOrigin"
              bind:value={formData.countryOfOrigin}
              maxlength="2"
              placeholder="e.g., DE"
            />
          </div>
        </div>

        <div class="form-actions">
          <button type="button" class="btn-secondary" on:click={toggleEdit}>Cancel</button>
          <button type="submit" class="btn-primary">Save Changes</button>
        </div>
      </form>
    </div>
  {:else}
    <!-- View Mode -->
    <div class="item-details">
      <div class="details-grid">
        <div class="detail-section">
          <h3>Basic Information</h3>
          <div class="detail-row">
            <span class="label">Part Number:</span>
            <span class="value">{item["Part No"] || 'N/A'}</span>
          </div>
          <div class="detail-row">
            <span class="label">Check Digit:</span>
            <span class="value">{item["Check digit"] || 'N/A'}</span>
          </div>
          <div class="detail-row">
            <span class="label">Description:</span>
            <span class="value">{item["Description"] || 'N/A'}</span>
          </div>
          <div class="detail-row">
            <span class="label">Price excl VAT:</span>
            <span class="value price">{item["Price excl VAT"] || 'N/A'}</span>
          </div>
          <div class="detail-row">
            <span class="label">Weight:</span>
            <span class="value">{item[" Weight "] || 'N/A'}</span>
          </div>
        </div>

        <div class="detail-section">
          <h3>Classification</h3>
          <div class="detail-row">
            <span class="label">Product Group:</span>
            <span class="value">{item["Product group"] || 'N/A'}</span>
          </div>
          <div class="detail-row">
            <span class="label">Function Group:</span>
            <span class="value">{item["Function group"] || 'N/A'}</span>
          </div>
          <div class="detail-row">
            <span class="label">Discount Code:</span>
            <span class="value">{item["Discount Code"] || 'N/A'}</span>
          </div>
          <div class="detail-row">
            <span class="label">Statistic No:</span>
            <span class="value">{item["Statistic No"] || 'N/A'}</span>
          </div>
        </div>

        <div class="detail-section">
          <h3>Origin Information</h3>
          <div class="detail-row">
            <span class="label">Country of Origin:</span>
            <span class="value">{item["Country of Origin"] || 'N/A'}</span>
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e2e8f0;
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .header-actions {
    display: flex;
    gap: 1rem;
  }

  .back-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    color: #64748b;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .back-button:hover {
    background-color: #e2e8f0;
    color: #475569;
  }

  h1 {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1e3a8a;
    margin: 0;
  }

  .item-details {
    background-color: white;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    padding: 2rem;
  }

  .detail-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .detail-section h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e3a8a;
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e2e8f0;
  }

  .detail-row {
    display: grid;
    grid-template-columns: 140px 1fr;
    gap: 1rem;
    align-items: center;
    padding: 0.5rem 0;
  }

  .label {
    font-weight: 500;
    color: #64748b;
    font-size: 0.875rem;
  }

  .value {
    color: #1f2937;
    font-weight: 500;
  }

  .value.price {
    font-size: 1.125rem;
    font-weight: 600;
    color: #059669;
  }

  .status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    width: fit-content;
  }

  .status-badge.active {
    background-color: #dcfce7;
    color: #166534;
  }

  .status-badge.inactive {
    background-color: #fee2e2;
    color: #991b1b;
  }

  .edit-form {
    background-color: white;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    padding: 2rem;
  }

  .edit-form h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e3a8a;
    margin: 0 0 1.5rem 0;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
  }

  .form-group.checkbox-group {
    grid-column: span 2;
    flex-direction: row;
    align-items: center;
  }

  .form-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0 !important;
  }

  .form-group input,
  .form-group select {
    padding: 0.625rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
  }

  .form-group input:focus,
  .form-group select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e2e8f0;
  }

  /* Button styles */
  .btn-primary,
  .btn-secondary,
  .btn-danger {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.625rem 1.25rem;
    border-radius: 0.375rem;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    border: 1px solid transparent;
  }

  .btn-primary {
    background-color: #3b82f6;
    color: white;
    border-color: #3b82f6;
  }

  .btn-primary:hover {
    background-color: #2563eb;
    border-color: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .btn-secondary {
    background-color: #f8fafc;
    color: #64748b;
    border-color: #e2e8f0;
  }

  .btn-secondary:hover {
    background-color: #e2e8f0;
    color: #475569;
  }

  .btn-danger {
    background-color: #dc2626;
    color: white;
    border-color: #dc2626;
  }

  .btn-danger:hover {
    background-color: #b91c1c;
    border-color: #b91c1c;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }

    .header-actions {
      width: 100%;
      justify-content: flex-start;
    }

    .details-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
      padding: 1rem;
    }

    .form-grid {
      grid-template-columns: 1fr;
    }

    .form-group.checkbox-group {
      grid-column: span 1;
    }

    .detail-row {
      grid-template-columns: 1fr;
      gap: 0.25rem;
    }
  }
</style>
