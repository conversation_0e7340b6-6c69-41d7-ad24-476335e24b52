import { MongoClient } from 'mongodb';
import { json } from '@sveltejs/kit';

// MongoDB connection URI
const uri = 'mongodb://localhost:27017';

// GET: Fetch all service ID items
export async function GET() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    
    // Using PascalCase for collection name per project convention
    const serviceIdCollection = db.collection('ServiceID');
    
    // Fetch all items from the ServiceID collection, sorted by serviceCode
    const items = await serviceIdCollection.find({}).sort({ serviceCode: 1 }).toArray();
    
    // Return the items as JSON
    return json(items);
    
  } catch (error) {
    console.error('Error fetching service ID items:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  } finally {
    await client.close();
  }
}
