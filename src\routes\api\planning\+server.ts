import { init } from '$lib/db';
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request }) => {
    try {
        const db = await init();
        const CustomerComputers = db.collection("CustomerComputers");
        
        const data = await request.json();
        const { computerId } = data;

        if (!computerId) {
            return json(
                { success: false, message: 'Computer ID is required' },
                { status: 400 }
            );
        }

        // Update the computer with contract dates
        const result = await CustomerComputers.updateOne(
            { _id: computerId },
            { 
                $set: { 
                    updatedAt: new Date()
                }
            }
        );

        if (result.modifiedCount === 0) {
            return json(
                { success: false, message: 'No computer was updated' },
                { status: 404 }
            );
        }

        return json({
            success: true,
            message: 'Contract dates updated successfully'
        });
    } catch (error) {
        console.error('Error:', error);
        return json(
            { 
                success: false, 
                message: 'Failed to update contract dates',
                error: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
};
