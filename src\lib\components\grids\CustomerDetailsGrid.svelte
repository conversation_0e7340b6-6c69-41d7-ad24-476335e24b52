<script>
  import BaseGrid from './BaseGrid.svelte';
  
  /**
   * @type {Array<{_id: string, customerId: string, serialNumber: string, model: string, productType: string, productDesignation?: string, engineType?: string, operatingHours: number, installationDate?: string, manufactureDate?: string, isActive: boolean, notes?: string}>}
   */
  export let computers = [];
  
  /**
   * @type {string|null}
   */
  export let selectedId = null;
  
  /**
   * @type {function(string): void}
   */
  export let onSelect;
</script>

<BaseGrid>
  <div class="computers-grid">
    <div class="computers-grid-header">
      <div class="grid-cell header">Serial Number</div>
      <div class="grid-cell header">Model</div>
      <div class="grid-cell header">Product Type</div>
      <div class="grid-cell header">Operating Hours</div>
      <div class="grid-cell header">Status</div>
    </div>
    
    <div class="computers-grid-body">
      {#if computers.length > 0}
        {#each computers as computer (computer._id)}
          <div 
            class="grid-row {selectedId === computer._id ? 'selected' : ''}"
            on:click={() => onSelect(computer._id)}
            tabindex="0"
            on:keydown={e => e.key === 'Enter' && onSelect(computer._id)}
          >
            <div class="grid-cell">{computer.serialNumber}</div>
            <div class="grid-cell">{computer.model}</div>
            <div class="grid-cell">{computer.productType}</div>
            <div class="grid-cell">{computer.operatingHours}</div>
            <div class="grid-cell status-cell">
              <span class={computer.isActive ? 'status-active' : 'status-inactive'}>
                {computer.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
        {/each}
      {:else}
        <div class="empty-state">
          <p>No computers found for this customer.</p>
        </div>
      {/if}
    </div>
  </div>
</BaseGrid>

<style>
  .computers-grid {
    display: grid;
    grid-template-rows: auto 1fr;
    gap: 0.5rem;
    height: 100%;
    width: 100%;
  }
  
  .computers-grid-header {
    display: grid;
    grid-template-columns: 1.5fr 1.5fr 1fr 1fr 1fr;
    background-color: #f3f4f6;
    border-radius: 0.25rem;
    font-weight: 600;
  }
  
  .computers-grid-body {
    display: grid;
    grid-auto-rows: max-content;
    gap: 0.5rem;
    overflow-y: auto;
  }
  
  .grid-row {
    display: grid;
    grid-template-columns: 1.5fr 1.5fr 1fr 1fr 1fr;
    padding: 0.75rem;
    border-radius: 0.25rem;
    background-color: white;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .grid-row:hover {
    background-color: #f9fafb;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
  
  .grid-row.selected {
    background-color: #e5edff;
    border: 1px solid #3b82f6;
  }
  
  .grid-cell {
    padding: 0.5rem;
    display: flex;
    align-items: center;
  }
  
  .grid-cell.header {
    font-weight: 600;
    color: #4b5563;
  }
  
  .status-cell {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .status-active {
    background-color: #d1fae5;
    color: #065f46;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
  }
  
  .status-inactive {
    background-color: #fee2e2;
    color: #b91c1c;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
  }
  
  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    background-color: #f9fafb;
    border-radius: 0.25rem;
    grid-column: 1 / -1;
  }
</style>
