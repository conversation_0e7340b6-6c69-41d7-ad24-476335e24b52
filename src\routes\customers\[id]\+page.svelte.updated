<script>
  import { enhance } from '$app/forms';
  import { goto } from '$app/navigation';
  export let data;

  let { customer, customerTypes, regions, customerRegionIds, relatedCounts } = data;
  let editing = false;
  let editingCustomer = { ...customer };

  // Function to validate MongoDB ObjectId format
  function isValidObjectId(id) {
    if (!id || typeof id !== 'string') return false;
    return /^[0-9a-fA-F]{24}$/.test(id);
  }

  // Delete customer function with ObjectId validation
  async function deleteCustomer() {
    if (!customer._id || !isValidObjectId(customer._id)) {
      console.error('Invalid customer ID');
      return;
    }

    if (!confirm('Are you sure you want to delete this customer?')) return;
    
    const response = await fetch('?/delete', {
      method: 'POST'
    });
    
    const result = await response.json();
    if (result.success) {
      goto('/customers');
    } else {
      alert(result.error);
    }
  }

  // Function to check if a region is assigned to the customer
  function isRegionAssigned(regionId) {
    return customerRegionIds.includes(regionId);
  }

  // Function to assign a region to the customer
  async function assignRegion(regionId) {
    if (!isValidObjectId(regionId)) {
      console.error('Invalid region ID');
      return;
    }

    const formData = new FormData();
    formData.append('regionId', regionId);

    const response = await fetch('?/assignRegion', {
      method: 'POST',
      body: formData
    });

    const result = await response.json();
    if (result.success) {
      // Add the region ID to the local array to update UI immediately
      customerRegionIds = [...customerRegionIds, regionId];
    } else {
      alert(result.error || 'Failed to assign region');
    }
  }

  // Function to unassign a region from the customer
  async function unassignRegion(regionId) {
    if (!isValidObjectId(regionId)) {
      console.error('Invalid region ID');
      return;
    }

    const formData = new FormData();
    formData.append('regionId', regionId);

    const response = await fetch('?/unassignRegion', {
      method: 'POST',
      body: formData
    });

    const result = await response.json();
    if (result.success) {
      // Remove the region ID from the local array to update UI immediately
      customerRegionIds = customerRegionIds.filter(id => id !== regionId);
    } else {
      alert(result.error || 'Failed to unassign region');
    }
  }
</script>

<div class="container">
  <div class="header-content">
    <h1>Customer Details</h1>
    <div class="header-actions">
      <button class="btn btn-edit" on:click={() => editing = !editing}>
        {editing ? 'Cancel' : 'Edit'}
      </button>
      {#if !editing}
          <button class="btn btn-delete" on:click={deleteCustomer}>
          Delete
        </button>
      {/if}
    </div>
  </div>

  {#if editing}
    <div class="edit-form">
      <form method="POST" action="?/update" use:enhance={() => {
        return ({ result }) => {
          if (result.success) {
            editing = false;
            customer = result.customer;
            editingCustomer = { ...customer };
          }
        };
      }}>
        <div class="form-grid">
          <!-- Left Column -->
          <div class="form-left">
            <div class="form-section">
              <h2>Basic Information</h2>
              <div class="form-group">
                <label for="name">Customer Name</label>
                <input 
                  type="text" 
                  id="name" 
                  name="name" 
                  bind:value={editingCustomer.name} 
                  required
                />
              </div>
              
              <div class="form-group">
                <label for="type">Customer Type</label>
                <select 
                  id="type" 
                  name="type" 
                  bind:value={editingCustomer.type}
                  required
                >
                  {#each customerTypes as type}
                    <option value={type}>{type}</option>
                  {/each}
                </select>
              </div>
              
              <div class="form-group">
                <label for="email">Email</label>
                <input 
                  type="email" 
                  id="email" 
                  name="email" 
                  bind:value={editingCustomer.email}
                />
              </div>
              
              <div class="form-group">
                <label for="phone">Phone</label>
                <input 
                  type="tel" 
                  id="phone" 
                  name="phone" 
                  bind:value={editingCustomer.phone}
                />
              </div>
            </div>
            
            <div class="form-section">
              <h2>Address</h2>
              <div class="form-group">
                <label for="address">Street Address</label>
                <input 
                  type="text" 
                  id="address" 
                  name="address" 
                  bind:value={editingCustomer.address}
                />
              </div>
              
              <div class="form-group">
                <label for="city">City</label>
                <input 
                  type="text" 
                  id="city" 
                  name="city" 
                  bind:value={editingCustomer.city}
                />
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label for="state">State/Province</label>
                  <input 
                    type="text" 
                    id="state" 
                    name="state" 
                    bind:value={editingCustomer.state}
                  />
                </div>
                
                <div class="form-group">
                  <label for="postalCode">Postal Code</label>
                  <input 
                    type="text" 
                    id="postalCode" 
                    name="postalCode" 
                    bind:value={editingCustomer.postalCode}
                  />
                </div>
              </div>
              
              <div class="form-group">
                <label for="country">Country</label>
                <input 
                  type="text" 
                  id="country" 
                  name="country" 
                  bind:value={editingCustomer.country}
                />
              </div>
            </div>
          </div>
          
          <!-- Right Column -->
          <div class="form-right">
            <div class="form-section">
              <h2>Additional Information</h2>
              <div class="form-group">
                <label for="website">Website</label>
                <input 
                  type="url" 
                  id="website" 
                  name="website" 
                  bind:value={editingCustomer.website}
                />
              </div>
              
              <div class="form-group">
                <label for="industry">Industry</label>
                <input 
                  type="text" 
                  id="industry" 
                  name="industry" 
                  bind:value={editingCustomer.industry}
                />
              </div>
              
              <div class="form-group">
                <label for="notes">Notes</label>
                <textarea 
                  id="notes" 
                  name="notes" 
                  rows="4" 
                  bind:value={editingCustomer.notes}
                ></textarea>
              </div>
            </div>
            
            <div class="form-actions">
              <button type="submit" class="btn btn-save">Save Changes</button>
              <button type="button" class="btn btn-cancel" on:click={() => {
                editing = false;
                editingCustomer = { ...customer };
              }}>
                Cancel
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  {:else}
    <div class="detail-grid">
      <!-- Left Column: Customer Info -->
      <div class="left-column">
        <div class="section-card">
          <h2>Basic Information</h2>
          <div class="info-group">
            <div class="info-label">Customer Name</div>
            <div class="info-value">{customer.name}</div>
          </div>
          
          <div class="info-group">
            <div class="info-label">Customer Type</div>
            <div class="info-value">
              <span class="badge badge-{customer.type.toLowerCase().replace(/\s+/g, '-')}">
                {customer.type}
              </span>
            </div>
          </div>
          
          {#if customer.email}
            <div class="info-group">
              <div class="info-label">Email</div>
              <div class="info-value">
                <a href="mailto:{customer.email}" class="link">{customer.email}</a>
              </div>
            </div>
          {/if}
          
          {#if customer.phone}
            <div class="info-group">
              <div class="info-label">Phone</div>
              <div class="info-value">
                <a href="tel:{customer.phone}" class="link">{customer.phone}</a>
              </div>
            </div>
          {/if}
          
          {#if customer.website}
            <div class="info-group">
              <div class="info-label">Website</div>
              <div class="info-value">
                <a href="{customer.website}" target="_blank" rel="noopener noreferrer" class="link">
                  {customer.website}
                </a>
              </div>
            </div>
          {/if}
          
          {#if customer.industry}
            <div class="info-group">
              <div class="info-label">Industry</div>
              <div class="info-value">{customer.industry}</div>
            </div>
          {/if}
        </div>
        
        {#if customer.address || customer.city || customer.state || customer.postalCode || customer.country}
          <div class="section-card">
            <h2>Address</h2>
            <div class="address">
              {#if customer.address}<div>{customer.address}</div>{/if}
              {#if customer.city || customer.state || customer.postalCode}
                <div>
                  {#if customer.city}{customer.city}{/if}
                  {#if customer.state}{customer.city ? ', ' : ''}{customer.state}{/if}
                  {#if customer.postalCode} {customer.postalCode}{/if}
                </div>
              {/if}
              {#if customer.country}<div>{customer.country}</div>{/if}
            </div>
          </div>
        {/if}
        
        {#if customer.notes}
          <div class="section-card">
            <h2>Notes</h2>
            <div class="notes">{customer.notes}</div>
          </div>
        {/if}
      </div>
      
      <!-- Right Column: Related Items -->
      <div class="right-column">
        <div class="section-card">
          <h2>Related Items</h2>
          
          <div class="related-item">
            <div class="related-info">
              <h3>Computers</h3>
              <div class="related-count">{relatedCounts.computers}</div>
            </div>
            <a href="{customer._id}/computers" class="btn btn-small">View Computers</a>
          </div>
          
          <div class="related-item">
            <div class="related-info">
              <h3>Service Contracts</h3>
              <div class="related-count">{relatedCounts.serviceContracts}</div>
            </div>
            <a href="{customer._id}/service-contracts" class="btn btn-small">View Contracts</a>
          </div>
          
          <div class="related-item">
            <div class="related-info">
              <h3>Service History</h3>
              <div class="related-count">{relatedCounts.serviceHistory}</div>
            </div>
            <a href="{customer._id}/service-history" class="btn btn-small">View History</a>
          </div>
        </div>
        
        <!-- Regions Section -->
        <div class="section-card">
          <h2>Regions in {customer.country || 'Customer Country'}</h2>
          
          {#if !regions || regions.length === 0}
            <p class="empty-state">No regions available for {customer.country || 'this country'}.</p>
          {:else}
            <div class="regions-list">
              {#each regions as region (region._id)}
                <div class="region-item">
                  <div class="region-info">
                    <span class="region-name">{region.name}</span>
                    {#if region.description}
                      <span class="region-description">{region.description}</span>
                    {/if}
                  </div>
                  <div class="region-actions">
                    {#if isRegionAssigned(region._id)}
                      <button 
                        class="btn btn-small btn-outline" 
                        on:click={() => unassignRegion(region._id)}
                      >
                        Remove
                      </button>
                    {:else}
                      <button 
                        class="btn btn-small btn-primary" 
                        on:click={() => assignRegion(region._id)}
                      >
                        Assign
                      </button>
                    {/if}
                  </div>
                </div>
              {/each}
            </div>
          {/if}
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
  }
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }
  
  h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
  }
  
  .header-actions {
    display: flex;
    gap: 0.75rem;
  }
  
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    text-decoration: none;
  }
  
  .btn-small {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }
  
  .btn-edit {
    background: #4f46e5;
    color: white;
  }
  
  .btn-edit:hover {
    background: #4338ca;
  }
  
  .btn-view {
    background: #10b981;
    color: white;
  }
  
  .btn-view:hover {
    background: #059669;
  }
  
  .btn-delete {
    background: #ef4444;
    color: white;
  }
  
  .btn-delete:hover {
    background: #dc2626;
  }
  
  .btn-save {
    background: #10b981;
    color: white;
  }
  
  .btn-save:hover {
    background: #059669;
  }
  
  .btn-cancel {
    background: #f3f4f6;
    color: #4b5563;
  }
  
  .btn-cancel:hover {
    background: #e5e7eb;
  }
  
  .detail-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  @media (min-width: 768px) {
    .detail-grid {
      grid-template-columns: 2fr 1fr;
    }
  }
  
  .section-card {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }
  
  .section-card h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-top: 0;
    margin-bottom: 1.25rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .info-group {
    margin-bottom: 1rem;
  }
  
  .info-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    margin-bottom: 0.25rem;
  }
  
  .info-value {
    font-size: 1rem;
    color: #1f2937;
  }
  
  .badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
  }
  
  .badge-oem-importer {
    background-color: #dbeafe;
    color: #1e40af;
  }
  
  .badge-fleet-owner {
    background-color: #ede9fe;
    color: #5b21b6;
  }
  
  .badge-retail {
    background-color: #d1fae5;
    color: #065f46;
  }
  
  .address {
    font-size: 1rem;
    color: #1f2937;
    line-height: 1.5;
  }
  
  .notes {
    font-size: 1rem;
    color: #1f2937;
    line-height: 1.6;
    white-space: pre-line;
  }
  
  .link {
    color: #4f46e5;
    text-decoration: none;
  }
  
  .link:hover {
    text-decoration: underline;
  }
  
  .related-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .related-item:last-child {
    border-bottom: none;
  }
  
  .related-info h3 {
    font-size: 1rem;
    font-weight: 500;
    color: #1f2937;
    margin: 0 0 0.25rem 0;
  }
  
  .related-count {
    font-size: 0.875rem;
    color: #6b7280;
  }
  
  /* Region styles */
  .regions-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1rem;
  }
  
  .region-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background-color: #f9f9f9;
    border-radius: 0.25rem;
    border: 1px solid #eaeaea;
  }
  
  .region-info {
    display: flex;
    flex-direction: column;
  }
  
  .region-name {
    font-weight: 500;
    color: #333;
  }
  
  .region-description {
    font-size: 0.85rem;
    color: #666;
    margin-top: 0.25rem;
  }
  
  .region-actions {
    display: flex;
    gap: 0.5rem;
  }
  
  .btn-small {
    padding: 0.25rem 0.5rem;
    font-size: 0.85rem;
  }
  
  .btn-outline {
    background-color: transparent;
    border: 1px solid #d32f2f;
    color: #d32f2f;
  }
  
  .btn-outline:hover {
    background-color: #ffebee;
  }
  
  .empty-state {
    color: #666;
    font-style: italic;
    padding: 1rem 0;
  }
</style>
