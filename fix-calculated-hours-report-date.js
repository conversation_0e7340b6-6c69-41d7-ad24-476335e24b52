// <PERSON>ript to fix CalculatedHoursPerMonth with the correct calculation logic:
// 1. Use ReportDateTime as the base for calculation
// 2. Count months between report dates
// 3. Divide hours by number of months
// 4. Cap at 744

import { MongoClient, ObjectId } from 'mongodb';

async function fixCalculatedHoursField() {
  // Connection URI
  const uri = 'mongodb://localhost:27017';
  const client = new MongoClient(uri);

  const MAX_HOURS_PER_MONTH = 744; // Maximum hours in a month

  try {
    // Connect to MongoDB
    await client.connect();
    console.log('Connected to MongoDB');

    // Access the ServiceContracts database and ActualComputerHours collection
    const db = client.db('ServiceContracts');
    const collection = db.collection('ActualComputerHours');

    // Find all documents
    const docs = await collection.find({}).toArray();
    console.log(`Found ${docs.length} documents in ActualComputerHours collection`);

    // Group documents by computerId for correct calculation
    const computerGroups = {};
    
    // Group documents by computerId
    docs.forEach(doc => {
      const computerIdStr = doc.computerId.toString();
      if (!computerGroups[computerIdStr]) {
        computerGroups[computerIdStr] = [];
      }
      computerGroups[computerIdStr].push(doc);
    });

    // Process each computer group
    for (const [computerIdStr, computerDocs] of Object.entries(computerGroups)) {
      console.log(`Processing ${computerDocs.length} documents for computer ${computerIdStr}`);
      
      // Sort documents by ReportDateTime if available, otherwise fallback to year/month
      computerDocs.sort((a, b) => {
        // Try to use ReportDateTime first (might be stored in different case variations)
        const aReportDate = a.ReportDateTime || a.reportDateTime || a.reportdatetime;
        const bReportDate = b.ReportDateTime || b.reportDateTime || b.reportdatetime;
        
        if (aReportDate && bReportDate) {
          return new Date(aReportDate) - new Date(bReportDate);
        }
        
        // Fallback to year/month if ReportDateTime not available
        if (a.year !== b.year) return a.year - b.year;
        return a.month - b.month;
      });
      
      // Process each document in chronological order
      let previousDoc = null;
      for (const doc of computerDocs) {
        // Ensure hours is an integer
        const hours = parseInt(doc.hours || 0);
        
        // Calculate months since previous entry
        let calculatedHours;
        if (!previousDoc) {
          // First entry - just use hours (capped at max)
          calculatedHours = Math.min(hours, MAX_HOURS_PER_MONTH);
        } else {
          // Get report dates for calculation
          const currentReportDate = doc.ReportDateTime || doc.reportDateTime || doc.reportdatetime;
          const previousReportDate = previousDoc.ReportDateTime || previousDoc.reportDateTime || previousDoc.reportdatetime;
          
          let monthsDiff = 1; // Default to 1 month if dates not available
          
          if (currentReportDate && previousReportDate) {
            const currentDate = new Date(currentReportDate);
            const previousDate = new Date(previousReportDate);
            
            // Calculate months difference
            monthsDiff = (currentDate.getFullYear() - previousDate.getFullYear()) * 12 + 
                         (currentDate.getMonth() - previousDate.getMonth());
            
            // Ensure at least 1 month difference
            monthsDiff = Math.max(1, monthsDiff);
          }
          
          // Divide hours by number of months
          calculatedHours = Math.floor(hours / monthsDiff);
        }
        
        // Cap at maximum hours per month
        calculatedHours = Math.min(calculatedHours, MAX_HOURS_PER_MONTH);
        
        // Update document with calculated value
        await collection.updateOne(
          { _id: doc._id },
          { $set: { 
              hours: hours,
              CalculatedHoursPerMonth: calculatedHours 
            }
          }
        );
        
        const reportDate = doc.ReportDateTime || doc.reportDateTime || doc.reportdatetime || 'N/A';
        console.log(`Updated document ${doc._id}: set hours=${hours}, CalculatedHoursPerMonth=${calculatedHours}, reportDate=${reportDate}`);
        
        // Store current document as previous for next iteration
        previousDoc = doc;
      }
    }

    // Verify some updated documents
    const updatedSamples = await collection.find({}).limit(5).toArray();
    console.log('\nSample documents after update:');
    updatedSamples.forEach(doc => {
      const reportDate = doc.ReportDateTime || doc.reportDateTime || doc.reportdatetime || 'N/A';
      console.log(
        `ID: ${doc._id}, ` +
        `Hours: ${doc.hours}, ` + 
        `CalculatedHoursPerMonth: ${doc.CalculatedHoursPerMonth}, ` +
        `ReportDateTime: ${reportDate}`
      );
    });

  } catch (error) {
    console.error('Error updating documents:', error);
  } finally {
    // Close the connection
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the function
fixCalculatedHoursField().catch(console.error);
