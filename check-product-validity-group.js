import { MongoClient } from 'mongodb';

// Connection URI
const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

// Database Name
const dbName = 'ServiceContracts';

async function main() {
  try {
    // Connect to the MongoDB server
    await client.connect();
    console.log('Connected successfully to MongoDB server');

    // Get the database
    const db = client.db(dbName);

    // Check if ProductValidityGroup collection exists
    const collections = await db.listCollections().toArray();
    const productValidityGroupExists = collections.some(
      collection => collection.name === 'ProductValidityGroupPartNumber'
    );
    
    if (productValidityGroupExists) {
      // Count documents in the collection
      const count = await db.collection('ProductValidityGroupPartNumber').countDocuments();
      console.log(`\nProductValidityGroupPartNumber collection exists and has ${count} documents`);
      
      // Show a sample document
      if (count > 0) {
        const sampleDocument = await db.collection('ProductValidityGroupPartNumber').findOne({});
        console.log('\nSample document:');
        console.log(JSON.stringify(sampleDocument, null, 2));
        
        // Show all field names from the sample document
        console.log('\nField names in the sample document:');
        Object.keys(sampleDocument).forEach(key => {
          console.log(` - ${key}: ${typeof sampleDocument[key]}`);
        });
        
        // Show a few more documents to understand the structure better
        console.log('\nShowing 5 more documents:');
        const documents = await db.collection('ProductValidityGroupPartNumber').find({}).limit(5).toArray();
        documents.forEach((doc, index) => {
          console.log(`\nDocument ${index + 1}:`);
          console.log(JSON.stringify(doc, null, 2));
        });
      }
    } else {
      console.log('\nProductValidityGroupPartNumber collection does not exist');
    }

  } catch (err) {
    console.error('An error occurred:', err);
  } finally {
    // Close the connection
    await client.close();
    console.log('\nConnection closed');
  }
}

main().catch(console.error);
