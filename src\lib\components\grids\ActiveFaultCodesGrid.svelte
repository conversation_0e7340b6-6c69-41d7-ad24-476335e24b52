<!-- ActiveFaultCodesGrid.svelte -->
<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  import { goto } from '$app/navigation';
  import { createEventDispatcher } from 'svelte';
  
  // Props
  export let faultCodes: any[] = [];
  
  // Event dispatcher
  const dispatch = createEventDispatcher();
  
  function pretty(val: any, field?: string): string {
    if ((field === 'createdAt' || field === 'updatedAt') && val) {
      return new Date(val).toLocaleString();
    }
    if (val === null || val === undefined) return '';
    if (typeof val === 'object') return JSON.stringify(val);
    return val.toString();
  }
  
  // Define grid style to match ActualComputerHoursGrid
  const displayFields = [
    'actions',
    'index', 
    '_id',
    'computerId',
    'DOID',
    'ProprietaryName', 
    'description',
    'activity', 
    'priority',
    'status',
    'createdAt',
    'updatedAt'
  ];

  // Grid column style
  const gridStyle = `
    grid-template-columns: 
      1fr /* actions */
      0.3fr /* # */
      2fr /* _id */
      2fr /* computerId */
      1.5fr /* DOID */
      1.5fr /* ProprietaryName */
      2fr /* description */
      1.5fr /* activity */
      1fr /* priority */
      1fr /* status */
      1.5fr /* createdAt */
      1.5fr /* updatedAt */
  `;
  
  // Process data to match ActualComputerHoursGrid
  $: processedCodes = faultCodes.map((code, index) => {
    // Deep clone to avoid mutation issues
    const newRow = {...code, index: index + 1};
    
    // Convert _id and computerId to string if needed
    if (newRow._id && typeof newRow._id === 'object' && newRow._id.$oid) {
      newRow._id = newRow._id.$oid;
    }
    if (newRow.computerId && typeof newRow.computerId === 'object' && newRow.computerId.$oid) {
      newRow.computerId = newRow.computerId.$oid;
    }
    
    return newRow;
  });
</script>

<BaseGrid>
  <div slot="header" class="ach-header ach-row" style={gridStyle}>
    {#each displayFields as field}
      <div class="ach-cell {field === 'createdAt' || field === 'updatedAt' ? 'date-time-cell' : ''}">
        {field === 'actions' ? 'Select' :
         field === 'index' ? '#' :
         field === '_id' ? 'DocumentId' :
         field === 'computerId' ? 'ComputerId' :
         field === 'DOID' ? 'DO ID' :
         field === 'ProprietaryName' ? 'Prop. Name' :
         field === 'description' ? 'Description' :
         field === 'activity' ? 'Activity' :
         field === 'priority' ? 'Priority' :
         field === 'status' ? 'Status' :
         field.charAt(0).toUpperCase() + field.slice(1)}
      </div>
    {/each}
  </div>
  <div slot="content">
    {#if processedCodes.length === 0}
      <div class="ach-row"><div class="ach-cell" style="grid-column: span {displayFields.length}; text-align:center;">No active fault codes found.</div></div>
    {:else}
      {#each processedCodes as row, i}
        <div 
          class="ach-row selectable" 
          style={gridStyle}
          role="button"
          aria-label={`Select Active Fault Code for DocumentId ${row._id}`}
          on:click={() => {
            dispatch('select', { faultCode: row });
          }}
          on:keydown={(e) => { if (e.key === 'Enter' || e.key === ' ') { 
            dispatch('select', { faultCode: row }); 
          } }}
        >
          {#each displayFields as field}
            <div class="ach-cell {field === 'createdAt' || field === 'updatedAt' ? 'date-time-cell' : ''}">
              {#if field === 'actions'}
                <button class="action-button edit" type="button" tabindex="-1" on:click|stopPropagation={() => dispatch('select', { faultCode: row })}>Select</button>
              {:else if field === 'index'}
                {row.index}
              {:else if field === '_id'}
                {row._id}
              {:else if field === 'computerId'}
                {row.computerId}
              {:else if field === 'status'}
                <span class="status-badge {row.status?.toLowerCase() || ''}">
                  {row.status || '-'}
                </span>
              {:else if field === 'priority'}
                <span class="priority-badge {row.priority?.toLowerCase() || ''}">
                  {row.priority || '-'}
                </span>
              {:else if field === 'createdAt' || field === 'updatedAt'}
                {row[field] ? new Date(row[field]).toLocaleString() : '-'}
              {:else}
                {row[field] || '-'}
              {/if}
            </div>
          {/each}
        </div>
      {/each}
    {/if}
  </div>
</BaseGrid>

<!-- Debug Panel: Shows all data as a grid with headlines from document keys and a Select button at the start of each row -->
<div class="debug-panel" style="margin-top:1rem; background:#fff; border:1px solid #102a54; padding:1em; border-radius:6px; font-size:0.95em; overflow-x:auto;">
  <div style="margin-top:0.7em;">
    {#if processedCodes.length > 0}
      <table class="debug-table" style="border-collapse:collapse; min-width:900px;">
        <thead>
          <tr>
            <th style="border:1px solid #e0e7ef; padding:0.3em 0.6em; background:#f8fafc; color:#102a54; font-weight:600;">Select</th>
            {#each displayFields.filter(f => f !== 'actions') as key}
              <th style="border:1px solid #e0e7ef; padding:0.3em 0.6em; background:#f8fafc; color:#102a54; font-weight:600;">{key}</th>
            {/each}
          </tr>
        </thead>
        <tbody>
          {#each processedCodes as row}
            <tr>
              <td style="border:1px solid #e0e7ef; padding:0.3em 0.6em; background:#fff;">
                <button class="action-button" type="button" on:click={() => dispatch('select', { faultCode: row })}>Select</button>
              </td>
              {#each displayFields.filter(f => f !== 'actions') as key}
                <td style="border:1px solid #e0e7ef; padding:0.3em 0.6em; color:#102a54; background:#fff;">
                  {typeof row[key] === 'object' && row[key] !== null ? JSON.stringify(row[key]) : row[key]}
                </td>
              {/each}
            </tr>
          {/each}
        </tbody>
      </table>
    {:else}
      <div>No data to display.</div>
    {/if}
  </div>
</div>

<style>
.ach-row {
  display: grid;
  gap: 0.5rem;
  align-items: center;
  color: #102a54; /* dark blue text */
  background: #fff !important;
}
.ach-cell {
  padding: 0.6em 0.8em;
  color: #102a54; /* dark blue text */
  background: #fff !important;
  font-size: 1em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ach-header {
  font-weight: bold;
  color: #102a54;
  background: #fff !important;
}
.date-time-cell {
  /* additional styling for date/time cells if needed */
}
.action-button {
  background: #fff;
  color: #102a54;
  border: 1px solid #102a54;
  border-radius: 5px;
  font-weight: 600;
  padding: 0.4em 1em;
  font-size: 1em;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
}
.action-button:hover {
  background: #102a54;
  color: #fff;
}

.selectable {
  cursor: pointer;
  transition: background-color 0.2s;
}

.selectable:hover {
  background-color: rgba(16, 42, 84, 0.05) !important;
}

.status-badge {
  display: inline-block;
  padding: 0.125rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
  background-color: rgba(55, 65, 81, 0.8);
  color: #e2e8f0;
}

.status-badge.active {
  background-color: rgba(22, 163, 74, 0.2);
  color: #4ade80;
  border: 1px solid rgba(22, 163, 74, 0.3);
}

.priority-badge {
  display: inline-block;
  padding: 0.125rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
  background-color: rgba(55, 65, 81, 0.8);
  color: #e2e8f0;
}

.priority-badge.high {
  background-color: rgba(220, 38, 38, 0.2);
  color: #f87171;
  border: 1px solid rgba(220, 38, 38, 0.3);
}

.priority-badge.medium {
  background-color: rgba(217, 119, 6, 0.2);
  color: #fbbf24;
  border: 1px solid rgba(217, 119, 6, 0.3);
}

.priority-badge.low {
  background-color: rgba(37, 99, 235, 0.2);
  color: #93c5fd;
  border: 1px solid rgba(37, 99, 235, 0.3);
}

.debug-panel {
  color: #102a54;
  background: #fff !important;
  border: 1px solid #102a54;
}
</style>
