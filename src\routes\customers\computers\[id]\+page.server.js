import { MongoClient, ObjectId } from 'mongodb';
import { error } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017/';
const dbName = 'ServiceContracts';

/** @type {MongoClient | null} */
let mongoClient = null;

/** @returns {Promise<MongoClient>} */
async function getMongoClient() {
    if (!mongoClient) {
        mongoClient = new MongoClient(uri);
        await mongoClient.connect();
    }
    return mongoClient;
}

/** @type {import('./$types').PageServerLoad} */
export async function load({ params }) {
    try {
        const client = await getMongoClient();
        const db = client.db(dbName);

        // Validate computer ID
        if (!params.id || !ObjectId.isValid(params.id)) {
            throw error(400, 'Invalid computer ID');
        }

        // Get computer details
        const computer = await db.collection('CustomerComputers')
            .findOne({ _id: new ObjectId(params.id) });

        if (!computer) {
            throw error(404, 'Computer not found');
        }

        // Get customer details
        const customer = await db.collection('Customers')
            .findOne({ _id: new ObjectId(computer.customerId) });

        if (!customer) {
            throw error(404, 'Customer not found');
        }

        // Format dates and IDs for client-side use
        return {
            computer: {
                ...computer,
                _id: computer._id.toString(),
                customerId: computer.customerId.toString(),
                purchaseDate: computer.purchaseDate ? new Date(computer.purchaseDate) : null,
                warrantyEndDate: computer.warrantyEndDate ? new Date(computer.warrantyEndDate) : null,
                createdAt: computer.createdAt ? new Date(computer.createdAt) : new Date()
            },
            customer: {
                ...customer,
                _id: customer._id.toString(),
                type: customer.type || 'Retail'
            }
        };
    } catch (err) {
        console.error('Error loading computer details:', err);
        if (err instanceof Error) {
            if ('status' in err) throw err; // Re-throw SvelteKit errors
            throw error(500, err.message);
        }
        throw error(500, 'Failed to load computer details');
    }
}
