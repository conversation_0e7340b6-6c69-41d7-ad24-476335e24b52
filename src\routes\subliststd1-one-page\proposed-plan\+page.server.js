import { error } from '@sveltejs/kit';
import { ObjectId } from 'mongodb';
import { getCollection } from '$lib/db/mongo';

/** @typedef {import('mongodb').WithId<import('mongodb').Document>} MongoDocument */

/**
 * @typedef {Object} WorkloadData
 * @property {string} _id
 * @property {string} computerId
 * @property {number} year
 * @property {number} month
 * @property {number} hours
 * @property {string} type
 * @property {string} [description]
 * @property {Date} createdAt
 * @property {Date} updatedAt
 */

/** @type {import('./$types').PageServerLoad} */
export async function load({ url }) {
  const computerId = url.searchParams.get('computerId');
  
  if (!computerId) {
    throw error(400, 'Computer ID is required');
  }

  if (!ObjectId.isValid(computerId)) {
    throw error(400, 'Invalid computer ID format');
  }

  try {
    // Get the computer details
    const computersColl = await getCollection('CustomerComputers');
    const computer = await computersColl.findOne({ _id: new ObjectId(computerId) });
    
    if (!computer) {
      throw error(404, 'Computer not found');
    }

    // Get the parent customer details
    const customersColl = await getCollection('Customers');
    const customer = await customersColl.findOne({ _id: computer.customerId });
    
    if (!customer) {
      throw error(404, 'Customer not found');
    }

    // Get workload data for the computer
    const workloadColl = await getCollection('Workload');
    const workloadCursor = workloadColl.find({
      computerId: new ObjectId(computerId)
    });
    
    // Transform the data to match our WorkloadData type
    const workloadData = (await workloadCursor.toArray()).map(doc => ({
      _id: doc._id.toString(),
      computerId: doc.computerId.toString(),
      year: doc.year,
      month: doc.month,
      hours: doc.hours,
      type: doc.type,
      description: doc.description || '',
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt
    }));

    return {
      computer: {
        ...computer,
        _id: computer._id.toString(),
        customerId: computer.customerId.toString()
      },
      customer: {
        ...customer,
        _id: customer._id.toString()
      },
      workloadData
    };
  } catch (err) {
    console.error('Error in workload data load:', err);
    throw error(500, 'Failed to load workload data');
  }
}
