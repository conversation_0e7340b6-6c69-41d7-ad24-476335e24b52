<script>
  import { createEventDispatcher } from 'svelte';

  /**
   * @typedef {Object} ComputerConfig
   * @property {string} [_id] - MongoDB ObjectId as string (when editing)
   * @property {string} serialNumber - Serial number of the computer
   * @property {string} model - Computer model
   * @property {string} productType - Product type 
   * @property {string} [productDesignation] - Product designation
   * @property {string} [engineType] - Engine type
   * @property {number} operatingHours - Current operating hours
   * @property {string} [installationDate] - Date of installation
   * @property {string} [manufactureDate] - Date of manufacture
   * @property {boolean} [isActive] - Is computer active
   * @property {string} [notes] - Additional notes
   */

  /**
   * @type {string} Customer ID this computer belongs to
   */
  export let customerId;
  
  /**
   * @type {ComputerConfig|null} - For editing existing computer
   */
  export let existingComputer = null;
  
  const dispatch = createEventDispatcher();
  
  /** @type {ComputerConfig} */
  let formData = {
    serialNumber: '',
    model: '',
    productType: '',
    productDesignation: '',
    engineType: '',
    operatingHours: 0,
    installationDate: '',
    manufactureDate: '',
    isActive: true,
    notes: ''
  };
  
  // If editing an existing computer, populate the form
  $: if (existingComputer) {
    formData = { ...existingComputer };
  }
  
  function handleSubmit() {
    dispatch('save', {
      computer: {
        ...formData,
        customerId
      }
    });
  }
  
  function handleCancel() {
    dispatch('cancel');
  }
</script>

<div class="computer-form">
  <h3>{existingComputer ? 'Edit Computer' : 'Add New Computer'}</h3>
  
  <form on:submit|preventDefault={handleSubmit}>
    <div class="form-grid">
      <div class="form-group">
        <label for="serialNumber">Serial Number*</label>
        <input 
          type="text" 
          id="serialNumber" 
          bind:value={formData.serialNumber}
          required
        />
      </div>
      
      <div class="form-group">
        <label for="model">Model*</label>
        <input 
          type="text" 
          id="model" 
          bind:value={formData.model}
          required
        />
      </div>
      
      <div class="form-group">
        <label for="productType">Product Type*</label>
        <input 
          type="text" 
          id="productType" 
          bind:value={formData.productType}
          required
        />
      </div>
      
      <div class="form-group">
        <label for="productDesignation">Product Designation</label>
        <input 
          type="text" 
          id="productDesignation" 
          bind:value={formData.productDesignation}
        />
      </div>
      
      <div class="form-group">
        <label for="engineType">Engine Type</label>
        <input 
          type="text" 
          id="engineType" 
          bind:value={formData.engineType}
        />
      </div>
      
      <div class="form-group">
        <label for="operatingHours">Operating Hours*</label>
        <input 
          type="number" 
          id="operatingHours" 
          bind:value={formData.operatingHours}
          min="0"
          required
        />
      </div>
      
      <div class="form-group">
        <label for="installationDate">Installation Date</label>
        <input 
          type="date" 
          id="installationDate" 
          bind:value={formData.installationDate}
        />
      </div>
      
      <div class="form-group">
        <label for="manufactureDate">Manufacture Date</label>
        <input 
          type="date" 
          id="manufactureDate" 
          bind:value={formData.manufactureDate}
        />
      </div>
      
      <div class="form-group">
        <label class="checkbox-label">
          <input 
            type="checkbox" 
            bind:checked={formData.isActive}
          />
          Active
        </label>
      </div>
      
      <div class="form-group full-width">
        <label for="notes">Notes</label>
        <textarea 
          id="notes" 
          bind:value={formData.notes}
          rows="3"
        ></textarea>
      </div>
    </div>
    
    <div class="form-actions">
      <button type="button" class="btn-secondary" on:click={handleCancel}>Cancel</button>
      <button type="submit" class="btn-primary">Save Computer</button>
    </div>
  </form>
</div>

<style>
  .computer-form {
    background-color: #f8fafc;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  h3 {
    font-size: 1.25rem;
    margin-top: 0;
    margin-bottom: 1.5rem;
    color: #1e40af;
  }
  
  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
  
  .form-group {
    margin-bottom: 1rem;
  }
  
  .full-width {
    grid-column: 1 / -1;
  }
  
  label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
    color: #4b5563;
  }
  
  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
  }
  
  input[type="text"],
  input[type="number"],
  input[type="date"],
  textarea,
  select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    background-color: white;
  }
  
  input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    cursor: pointer;
  }
  
  textarea {
    resize: vertical;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    margin-top: 1.5rem;
  }
</style>
