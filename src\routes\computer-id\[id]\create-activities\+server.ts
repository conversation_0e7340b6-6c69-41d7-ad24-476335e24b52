import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';
import type { RequestEvent } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

interface ServiceElement {
    _id: ObjectId;
    ServiceCode: string;
    ServiceActivityLabel?: string;
    ActivityPurpose?: string;
    ActionType: string;
    Hrs: number;
    Months?: number;
    "Product Validity Group"?: string;
}

interface WorkloadEntry {
    _id: ObjectId;
    computerId: ObjectId;
    year: number;
    month: number;
    hours: number;
    hasFixed: boolean;
    activity: string | null;
}

/** @type {import('./$types').RequestHandler} */
export async function POST({ params }: RequestEvent) {
    try {
        await client.connect();
        const db = client.db('ServiceContracts');
        
        // Get all required collections
        const computersCollection = db.collection('CustomerComputers');
        const pvgCollection = db.collection('ProductValidityGroupPartNumber');
        const serviceCodeCollection = db.collection('ServiceCodeAndActionType');
        const activitiesCollection = db.collection('ServiceActivities');
        const workloadCollection = db.collection('Workload');

        console.log('Starting service activities creation for computer:', params.id);

        // 1. Get computer details
        const computer = await computersCollection.findOne({ _id: new ObjectId(params.id) });
        if (!computer) {
            throw new Error('Computer not found');
        }
        console.log('Found computer:', {
            id: computer._id,
            partNumber: computer.ProductPartNumber,
            pvg: computer["Product Validity Group"],
            contractDates: {
                start: computer.contractStartDate,
                end: computer.contractEndDate
            },
            hours: computer.HoursContractStart
        });

        // 2. Get Product Validity Group if not directly on computer
        let productValidityGroup = computer["Product Validity Group"];
        if (!productValidityGroup && computer.ProductPartNumber) {
            const pvgDoc = await pvgCollection.findOne({
                ProductPartNumber: parseInt(computer.ProductPartNumber)
            });
            productValidityGroup = pvgDoc?.["Product Validity Group"];
            console.log('Found PVG from lookup:', productValidityGroup);
        }

        if (!productValidityGroup) {
            throw new Error('No Product Validity Group found for computer');
        }

        // 3. Get Service Elements for this Product Validity Group
        const serviceElements = await serviceCodeCollection
            .find({ 
                "Product Validity Group": productValidityGroup 
            })
            .toArray();

        console.log('Found service elements:', {
            count: serviceElements.length,
            pvg: productValidityGroup,
            samples: serviceElements.slice(0, 2).map(e => ({
                code: e.ServiceCode,
                pvg: e["Product Validity Group"]
            }))
        });

        if (serviceElements.length === 0) {
            throw new Error(`No service elements found for Product Validity Group: ${productValidityGroup}`);
        }

        // 4. Delete existing activities
        await activitiesCollection.deleteMany({ computerId: new ObjectId(params.id) });
        console.log('Deleted existing activities');

        // 5. Initialize tracking variables
        const startDate = new Date(computer.contractStartDate);
        const endDate = new Date(computer.contractEndDate);
        let currentDate = new Date(startDate);
        const activities = [];
        const serviceTracking = new Map();

        // Initialize service tracking
        for (const element of serviceElements) {
            if (!element.ServiceCode) {
                console.warn('Service element missing ServiceCode:', element);
                continue;
            }
            serviceTracking.set(element.ServiceCode, {
                lastServiceDate: new Date(startDate),
                accumulatedHours: 0
            });
        }

        // 6. Process month by month
        while (currentDate <= endDate) {
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth() + 1;

            // Get or calculate monthly hours
            const monthlyHours = Math.round(computer.HoursContractStart / 12); // Distribute evenly

            // Update accumulated hours for each service type
            for (const element of serviceElements) {
                if (!element.ServiceCode || !element.Hrs) {
                    console.warn('Skipping invalid service element:', element);
                    continue;
                }

                const tracking = serviceTracking.get(element.ServiceCode);
                if (!tracking) continue;

                tracking.accumulatedHours += monthlyHours;

                // Check if service is needed based on hours threshold
                if (tracking.accumulatedHours >= element.Hrs) {
                    console.log('Creating activity for:', {
                        serviceCode: element.ServiceCode,
                        date: currentDate,
                        accumulatedHours: tracking.accumulatedHours,
                        threshold: element.Hrs
                    });

                    // Create service activity
                    activities.push({
                        computerId: new ObjectId(params.id),
                        serviceElementId: element._id,
                        ServiceCode: element.ServiceCode,
                        ServiceActivityLabel: element.ServiceActivityLabel || element.ActivityPurpose,
                        ActionType: element.ActionType,
                        plannedDate: new Date(currentDate),
                        hours: element.Hrs,
                        status: 'planned',
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });

                    // Update workload
                    await workloadCollection.updateOne(
                        { computerId: new ObjectId(params.id), year, month },
                        {
                            $set: {
                                hours: monthlyHours,
                                hasFixed: true,
                                activity: element.ServiceCode,
                                lastUpdated: new Date()
                            }
                        },
                        { upsert: true }
                    );

                    // Reset tracking for this service
                    tracking.accumulatedHours = 0;
                    tracking.lastServiceDate = new Date(currentDate);
                }

                // Update tracking in map
                serviceTracking.set(element.ServiceCode, tracking);
            }

            // Move to next month
            currentDate.setMonth(currentDate.getMonth() + 1);
        }

        // 7. Insert all activities
        console.log('Creating activities:', activities.length);
        if (activities.length > 0) {
            await activitiesCollection.insertMany(activities);
        }

        return json({
            success: true,
            message: `Created ${activities.length} service activities`,
            activityCount: activities.length,
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
            details: {
                productValidityGroup,
                serviceElements: serviceElements.map(e => ({
                    serviceCode: e.ServiceCode,
                    hoursThreshold: e.Hrs
                }))
            }
        });

    } catch (error: unknown) {
        console.error('Error creating service activities:', error);
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        console.error('Detailed error:', errorMessage);
        return json({
            success: false,
            message: errorMessage
        }, { status: 500 });
    } finally {
        await client.close();
    }
}
