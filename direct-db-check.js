import { MongoClient } from 'mongodb';

// This script directly connects to MongoDB and checks the ServiceCodeHeader collection
// Run with: node direct-db-check.js

async function checkServiceCodeHeader() {
  const uri = 'mongodb://localhost:27017';
  const client = new MongoClient(uri);

  try {
    console.log('Connecting to MongoDB...');
    await client.connect();
    console.log('Connected successfully');

    const db = client.db('ServiceContracts');
    console.log('Using ServiceContracts database');

    // Check if collection exists
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);
    console.log('Available collections:', collectionNames);

    if (!collectionNames.includes('ServiceCodeHeader')) {
      console.error('ERROR: ServiceCodeHeader collection does not exist!');
      return;
    }

    // Count documents
    const collection = db.collection('ServiceCodeHeader');
    const count = await collection.countDocuments();
    console.log(`ServiceCodeHeader collection has ${count} documents`);

    // Get sample documents
    const samples = await collection.find().limit(5).toArray();
    console.log('Sample documents:');
    samples.forEach((doc, i) => {
      console.log(`\nDocument ${i+1}:`);
      console.log('_id:', doc._id.toString());
      console.log('ServiceCode:', doc.ServiceCode);
      console.log('Service activity Label:', doc['Service activity Label']);
      console.log('Activity purpose:', doc['Activity purpose']);
      console.log('Product Validity Group:', doc['Product Validity Group']);
      console.log('Internal No of Hours:', doc['Internal No of Hours']);
      console.log('Internal No of Months:', doc['Internal No of Months']);
    });

    // Check field names across all documents
    console.log('\nChecking field names across all documents...');
    const fieldNames = new Set();
    const cursor = collection.find().limit(100);
    let docCount = 0;
    
    await cursor.forEach(doc => {
      docCount++;
      Object.keys(doc).forEach(key => fieldNames.add(key));
    });
    
    console.log(`Analyzed ${docCount} documents`);
    console.log('All field names found:', Array.from(fieldNames));

  } catch (err) {
    console.error('Database error:', err);
  } finally {
    await client.close();
    console.log('Connection closed');
  }
}

checkServiceCodeHeader();
