<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  
  // Props
  export let label: string = '';
  export let year: number = new Date().getFullYear();
  export let month: number | null = null;
  export let months: string[] = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  export let yearMin: number = 2000;
  export let yearMax: number = 2100;
  export let id: string = '';
  export let required: boolean = false;
  
  // Event dispatcher
  const dispatch = createEventDispatcher<{
    change: { year: number; month: number | null };
  }>();
  
  // Handle change event
  function onChange() {
    dispatch('change', { year, month });
  }
</script>

<div class="selector-container">
  {#if label}
    <label for={id || 'yearInput'}>{label}</label>
  {/if}
  
  <div class="inputs-group">
    <input 
      id={id || 'yearInput'} 
      type="number" 
      bind:value={year} 
      min={yearMin} 
      max={yearMax}
      style="width:5em;" 
      on:change={onChange}
      {required}
    />
    
    {#if month !== null}
      <select 
        bind:value={month} 
        on:change={onChange}
        {required}
      >
        {#each months as monthName, i}
          <option value={i + 1}>{monthName}</option>
        {/each}
      </select>
    {/if}
  </div>
</div>

<style>
  .selector-container {
    display: grid;
    gap: 0.5rem;
  }
  
  label {
    font-size: 0.875rem;
    color: #94a3b8;
    font-weight: 500;
  }
  
  .inputs-group {
    display: flex;
    gap: 0.75rem;
    align-items: center;
  }
  
  input, select {
    background-color: #0f172a;
    border: 1px solid #334155;
    border-radius: 6px;
    color: #e2e8f0;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
  
  input:focus, select:focus {
    border-color: #60a5fa;
    outline: none;
  }
</style>
