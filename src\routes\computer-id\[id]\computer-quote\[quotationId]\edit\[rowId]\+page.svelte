<script>
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import FormGrid from '$lib/components/grids/FormGrid.svelte';
	import { goto } from '$app/navigation';

	const computerId = $page.params.id;
	const quotationId = $page.params.quotationId;
	const rowId = $page.params.rowId;
	
	// Define the standard package options in the required order
	const packageOptions = [
		'Base Contract Offering [Fixed]',
		'Dealer Add-Ons [Fixed]',
		'Dealer Add-Ons [Variable/hr]',
		'Support (Self Service) [Fixed]',
		'Customer Package [Fixed]',
		'Customer Package- VODIA [Fixed]',
		'Support (Dealer) [Fixed]',
		'Support (Dealer) [Variable/hr]',
		'Retirement Plan [Variable/hr]'
	];
	
	// Define quote row type options
	const rowTypeOptions = ['Labour', 'Part'];
	
	// Define service options with their IDs (for internal mapping only)
	const serviceOptions = [
		{ id: 'SVC001', name: 'Preventive Maintenance' },
		{ id: 'SVC002', name: 'Remote Support' },
		{ id: 'SVC003', name: 'On-site Support' },
		{ id: 'SVC004', name: 'Software Updates' },
		{ id: 'SVC005', name: 'Hardware Replacement' },
		{ id: 'SVC006', name: 'Training' },
		{ id: 'SVC007', name: 'Consulting' },
		{ id: 'SVC008', name: 'System Monitoring' },
		{ id: 'SVC009', name: 'Disaster Recovery' },
		{ id: 'SVC010', name: 'Custom Service' }
	];
	
	let quoteRow = {
		level: 0,
		package: packageOptions[0], // Default to first package in the list
		serviceId: '',  // Will be auto-populated, not shown to user
		service: '',
		included: 'Yes',
		required: 'Yes',
		scos: 0,
		oemImport: 0,
		fleetOwner: 0,
		rrp: 0,
		selectService: 'Yes',
		qtyPerYr: 0,
		QuoteRowType: 'Labour', // Default to Labour
		PartNumber: '', // New field for parts
		PartName: '',   // New field for parts
		PartQty: 1,     // New field for parts with default quantity of 1
		CustomerPrice: 0, // New price field in dollar
		DealerPrice: 0,   // New price field in dollar
		InternalCost: 0   // New cost field in dollar
	};
	
	// Function to automatically update serviceId when service is selected
	function updateServiceId(selectedService) {
		const serviceOption = serviceOptions.find(opt => opt.name === selectedService);
		if (serviceOption) {
			quoteRow.serviceId = serviceOption.id;
		} else if (quoteRow.service && !quoteRow.serviceId) {
			// Generate a placeholder ID if service is custom and no ID exists
			quoteRow.serviceId = 'SVC' + Math.floor(Math.random() * 1000).toString().padStart(3, '0');
		}
	}
	
	let loading = true;
	let saving = false;
	let error = null;

	onMount(async () => {
		if (rowId === 'new') {
			// Creating a new quote row
			loading = false;
			return;
		}
		
		try {
			const response = await fetch(`/api/quote-rows/detail/${rowId}`);
			if (response.ok) {
				quoteRow = await response.json();
				
				// Set default QuoteRowType if it doesn't exist
				if (!quoteRow.QuoteRowType) {
					quoteRow.QuoteRowType = 'Labour';
				}
				
				// Set default part fields if they don't exist
				if (quoteRow.QuoteRowType === 'Part') {
					if (!quoteRow.PartNumber) quoteRow.PartNumber = '';
					if (!quoteRow.PartName) quoteRow.PartName = '';
					if (!quoteRow.PartQty) quoteRow.PartQty = 1;
				}
				
				// Set default price fields if they don't exist
				if (!quoteRow.CustomerPrice) quoteRow.CustomerPrice = 0;
				if (!quoteRow.DealerPrice) quoteRow.DealerPrice = 0;
				if (!quoteRow.InternalCost) quoteRow.InternalCost = 0;
			} else {
				error = await response.text();
				console.error('Failed to load quote row:', error);
			}
		} catch (err) {
			error = err.message;
			console.error('Error loading quote row:', err);
		} finally {
			loading = false;
		}
	});

	const handleSubmit = async () => {
		saving = true;
		
		try {
			const isNewRow = rowId === 'new';
			
			// If creating new row, add quotationId
			if (isNewRow) {
				quoteRow.quotationId = quotationId;
			}
			
			// Auto-assign serviceId based on selected service
			const serviceOption = serviceOptions.find(opt => opt.name === quoteRow.service);
			if (serviceOption) {
				quoteRow.serviceId = serviceOption.id;
			} else if (quoteRow.service && !quoteRow.serviceId) {
				// Generate a placeholder ID if service is custom and no ID exists
				quoteRow.serviceId = 'SVC' + Math.floor(Math.random() * 1000).toString().padStart(3, '0');
			}
			
			const url = isNewRow 
				? `/api/quote-rows/create`
				: `/api/quote-rows/update-by-id/${rowId}`;
				
			const method = isNewRow ? 'POST' : 'PUT';
			
			const response = await fetch(url, {
				method,
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(quoteRow)
			});
			
			if (response.ok) {
				// Navigate back to list
				goto(`/computer-id/${computerId}/computer-quote/${quotationId}`);
			} else {
				const errText = await response.text();
				throw new Error(errText);
			}
		} catch (err) {
			error = err.message;
			console.error('Error saving quote row:', err);
		} finally {
			saving = false;
		}
	};
	
	// Handle row type change
	function handleRowTypeChange(newType) {
		quoteRow.QuoteRowType = newType;
		
		// If changing to Part, set default values for part fields
		if (newType === 'Part') {
			if (!quoteRow.PartNumber) quoteRow.PartNumber = '';
			if (!quoteRow.PartName) quoteRow.PartName = '';
			if (!quoteRow.PartQty) quoteRow.PartQty = 1;
		}
	}
	
	// Format currency for display
	function formatDollar(value) {
		return new Intl.NumberFormat('en-US', {
			style: 'currency',
			currency: 'USD'
		}).format(value);
	}
</script>

<div class="quote-container">
	<div class="header">
		<h1>{rowId === 'new' ? 'Create New Quote Row' : 'Edit Quote Row'}</h1>
		<button 
			class="btn-cancel" 
			on:click={() => goto(`/computer-id/${computerId}/computer-quote/${quotationId}`)}
		>
			Cancel
		</button>
	</div>

	{#if loading}
		<div class="loading-container">
			<div class="loading-spinner"></div>
			<p>Loading quote details...</p>
		</div>
	{:else if error}
		<div class="error-container">
			<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
				<circle cx="12" cy="12" r="10"></circle>
				<line x1="12" y1="8" x2="12" y2="12"></line>
				<line x1="12" y1="16" x2="12.01" y2="16"></line>
			</svg>
			<p>Error: {error}</p>
		</div>
	{:else}
		<div class="form-container">
			<form on:submit|preventDefault={handleSubmit}>
				<div class="form-grid">
					<!-- Left Column -->
					<div class="form-column">
						<div class="form-group">
							<label for="QuoteRowType">Row Type</label>
							<select 
								id="QuoteRowType" 
								bind:value={quoteRow.QuoteRowType} 
								on:change={(e) => handleRowTypeChange(e.target.value)}
								required
							>
								{#each rowTypeOptions as option}
									<option value={option}>{option}</option>
								{/each}
							</select>
						</div>
						
						{#if quoteRow.QuoteRowType === 'Part'}
							<!-- Part-specific fields -->
							<div class="form-group part-field">
								<label for="PartNumber">Part Number</label>
								<input 
									type="text" 
									id="PartNumber" 
									bind:value={quoteRow.PartNumber} 
									required
								/>
							</div>
							
							<div class="form-group part-field">
								<label for="PartName">Part Name</label>
								<input 
									type="text" 
									id="PartName" 
									bind:value={quoteRow.PartName} 
									required
								/>
							</div>
							
							<div class="form-group part-field">
								<label for="PartQty">Part Quantity</label>
								<input 
									type="number" 
									id="PartQty" 
									bind:value={quoteRow.PartQty} 
									min="1"
									required
								/>
							</div>
							
							<!-- Hidden fields for parts - we keep the values but don't show them -->
							<input type="hidden" bind:value={quoteRow.serviceId} />
							<input type="hidden" bind:value={quoteRow.service} />
						{:else}
							<!-- Labour-specific fields -->
							<div class="form-group">
								<label for="level">Level</label>
								<input 
									type="number" 
									id="level" 
									bind:value={quoteRow.level} 
									required 
									min="0"
								/>
							</div>
							
							<div class="form-group">
								<label for="package">Package</label>
								<select id="package" bind:value={quoteRow.package} required>
									{#each packageOptions as option}
										<option value={option}>{option}</option>
									{/each}
								</select>
							</div>
							
							<!-- Service selection -->
							<div class="form-group">
								<label for="service">Service</label>
								<select 
									id="service" 
									bind:value={quoteRow.service}
									required
								>
									<option value="">Select a service</option>
									{#each serviceOptions as option}
										<option value={option.name}>{option.name}</option>
									{/each}
									<option value="Custom">Custom Service...</option>
								</select>
							</div>
							
							<!-- Custom service name input (shows only when "Custom" is selected) -->
							{#if quoteRow.service === 'Custom'}
								<div class="form-group">
									<label for="customService">Custom Service Name</label>
									<input 
										type="text" 
										id="customService" 
										bind:value={quoteRow.service}
										placeholder="Enter custom service name" 
										required
									/>
								</div>
							{/if}
						{/if}
						
						<!-- Price fields - shown for both Labour and Part -->
						<div class="form-group price-field">
							<label for="CustomerPrice">Customer Price ($)</label>
							<div class="input-with-prefix">
								<span class="currency-prefix">$</span>
								<input 
									type="number" 
									id="CustomerPrice" 
									bind:value={quoteRow.CustomerPrice} 
									min="0"
									step="0.01"
									required
								/>
							</div>
						</div>
						
						<div class="form-group price-field">
							<label for="DealerPrice">Dealer Price ($)</label>
							<div class="input-with-prefix">
								<span class="currency-prefix">$</span>
								<input 
									type="number" 
									id="DealerPrice" 
									bind:value={quoteRow.DealerPrice} 
									min="0"
									step="0.01"
									required
								/>
							</div>
						</div>
						
						<div class="form-group price-field">
							<label for="InternalCost">Internal Cost ($)</label>
							<div class="input-with-prefix">
								<span class="currency-prefix">$</span>
								<input 
									type="number" 
									id="InternalCost" 
									bind:value={quoteRow.InternalCost} 
									min="0"
									step="0.01"
									required
								/>
							</div>
						</div>
						
						<div class="form-group">
							<label for="included">Included in Package</label>
							<select id="included" bind:value={quoteRow.included}>
								<option value="Yes">Yes</option>
								<option value="No">No</option>
							</select>
						</div>
					</div>
					
					<!-- Right Column -->
					<div class="form-column">
						<div class="form-group">
							<label for="required">Required</label>
							<select id="required" bind:value={quoteRow.required}>
								<option value="Yes">Yes</option>
								<option value="No">No</option>
								<option value="Mandatory">Mandatory</option>
							</select>
						</div>
						
						<div class="form-group">
							<label for="scos">SCOS</label>
							<input 
								type="number" 
								id="scos" 
								bind:value={quoteRow.scos}
							/>
						</div>
						
						<div class="form-group">
							<label for="oemImport">OEM Import</label>
							<input 
								type="number" 
								id="oemImport" 
								bind:value={quoteRow.oemImport}
							/>
						</div>
						
						<div class="form-group">
							<label for="fleetOwner">Fleet Owner</label>
							<input 
								type="number" 
								id="fleetOwner" 
								bind:value={quoteRow.fleetOwner}
							/>
						</div>
						
						<div class="form-group">
							<label for="rrp">RRP</label>
							<input 
								type="number" 
								id="rrp" 
								bind:value={quoteRow.rrp}
							/>
						</div>
						
						<div class="form-group">
							<label for="selectService">Select Service</label>
							<select id="selectService" bind:value={quoteRow.selectService}>
								<option value="Yes">Yes</option>
								<option value="No">No</option>
								<option value="Mandatory">Mandatory</option>
							</select>
						</div>
						
						<div class="form-group">
							<label for="qtyPerYr">Quantity per Year</label>
							<input 
								type="number" 
								id="qtyPerYr" 
								bind:value={quoteRow.qtyPerYr} 
								min="0"
							/>
						</div>
					</div>
				</div>
				
				<div class="form-actions">
					<button type="submit" class="btn-save" disabled={saving}>
						{#if saving}
							<div class="btn-spinner"></div>
							<span>Saving...</span>
						{:else}
							<span>Save Quote Row</span>
						{/if}
					</button>
				</div>
			</form>
		</div>
	{/if}
</div>

<style>
	.quote-container {
		max-width: 900px;
		margin: 0 auto;
		padding: 2rem;
		background-color: #ffffff;
		border-radius: 12px;
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 2rem;
		padding-bottom: 1rem;
		border-bottom: 1px solid #f0f0f0;
	}

	h1 {
		font-size: 1.8rem;
		font-weight: 600;
		color: #2c3e50;
		margin: 0;
	}

	.form-container {
		padding: 1rem 0;
	}

	.form-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 2rem;
	}

	.form-column {
		display: grid;
		grid-template-rows: repeat(auto-fill, minmax(80px, auto));
		gap: 1.25rem;
	}

	.form-group {
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}
	
	.part-field {
		background-color: #f8fafc;
		padding: 0.75rem;
		border-radius: 8px;
		border-left: 3px solid #3b82f6;
	}
	
	.price-field {
		background-color: #f0f9ff;
		padding: 0.75rem;
		border-radius: 8px;
		border-left: 3px solid #0ea5e9;
	}
	
	.input-with-prefix {
		position: relative;
		display: flex;
		align-items: center;
	}
	
	.currency-prefix {
		position: absolute;
		left: 0.75rem;
		color: #64748b;
		font-weight: 500;
	}
	
	.input-with-prefix input {
		padding-left: 1.75rem;
	}

	label {
		font-size: 0.9rem;
		font-weight: 500;
		color: #64748b;
	}

	input, select {
		padding: 0.75rem 1rem;
		border: 1px solid #e2e8f0;
		border-radius: 8px;
		font-size: 1rem;
		background-color: #f8fafc;
		transition: all 0.2s ease;
	}

	input:focus, select:focus {
		outline: none;
		border-color: #3b82f6;
		box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
		background-color: #ffffff;
	}

	input:hover, select:hover {
		border-color: #cbd5e1;
	}

	.read-only-field {
		background-color: #f0f9ff;
		padding: 0.75rem 1rem;
		border: 1px solid #e2e8f0;
		border-radius: 8px;
		font-size: 1rem;
		cursor: not-allowed;
	}

	.form-actions {
		display: flex;
		justify-content: flex-end;
		margin-top: 2.5rem;
		padding-top: 1.5rem;
		border-top: 1px solid #f0f0f0;
	}

	.btn-save {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 0.5rem;
		padding: 0.75rem 1.5rem;
		background-color: #3b82f6;
		color: white;
		border: none;
		border-radius: 8px;
		font-weight: 500;
		font-size: 1rem;
		cursor: pointer;
		transition: background-color 0.2s ease;
	}

	.btn-save:hover {
		background-color: #2563eb;
	}

	.btn-save:disabled {
		background-color: #93c5fd;
		cursor: not-allowed;
	}

	.btn-cancel {
		padding: 0.75rem 1.5rem;
		background-color: #f1f5f9;
		color: #64748b;
		border: none;
		border-radius: 8px;
		font-weight: 500;
		font-size: 1rem;
		cursor: pointer;
		transition: all 0.2s ease;
	}

	.btn-cancel:hover {
		background-color: #e2e8f0;
		color: #475569;
	}

	.loading-container, .error-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 3rem;
		text-align: center;
		gap: 1rem;
	}

	.loading-spinner, .btn-spinner {
		border: 3px solid rgba(59, 130, 246, 0.1);
		border-radius: 50%;
		border-top: 3px solid #3b82f6;
		width: 24px;
		height: 24px;
		animation: spin 1s linear infinite;
	}

	.btn-spinner {
		width: 16px;
		height: 16px;
		border-width: 2px;
		border-top: 2px solid white;
		border-color: rgba(255, 255, 255, 0.5);
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.error-container {
		color: #ef4444;
	}

	.error-container svg {
		stroke: #ef4444;
	}

	/* Responsive adjustments */
	@media (max-width: 768px) {
		.form-grid {
			grid-template-columns: 1fr;
			gap: 1rem;
		}

		.quote-container {
			padding: 1.5rem;
			border-radius: 8px;
		}
	}
</style>
