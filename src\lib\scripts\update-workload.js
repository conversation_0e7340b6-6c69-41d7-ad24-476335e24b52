const { MongoClient } = require('mongodb');

async function updateWorkloadActivity() {
    const uri = "mongodb://localhost:27017";
    const client = new MongoClient(uri);

    try {
        console.log('Connecting to MongoDB...');
        await client.connect();
        console.log('Connected successfully');

        const db = client.db("ServiceContracts");
        const Workload = db.collection("Workload");

        console.log('Updating Workload collection...');
        const result = await Workload.updateMany(
            {}, // match all documents
            { 
                $set: { 
                    activity: 'idle',
                    updatedAt: new Date()
                } 
            }
        );

        console.log(`Operation complete!`);
        console.log(`Matched ${result.matchedCount} documents`);
        console.log(`Modified ${result.modifiedCount} documents`);

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await client.close();
        console.log('Disconnected from MongoDB');
        process.exit(0);
    }
}

updateWorkloadActivity();
