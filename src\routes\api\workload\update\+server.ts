import { json } from '@sveltejs/kit';
import type { RequestEvent } from '@sveltejs/kit';
import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

export async function POST({ request }: RequestEvent) {
    try {
        const { computerId, year, month, hours, hasFixed, activity } = await request.json();
        
        if (!computerId || !year || !month || hours === undefined) {
            return json({ 
                success: false, 
                error: 'Missing required fields' 
            }, { status: 400 });
        }

        const db = client.db("ServiceContracts");
        const Workload = db.collection("Workload");
        const CustomerComputers = db.collection("CustomerComputers");

        // Validate computer exists
        const computer = await CustomerComputers.findOne({ _id: new ObjectId(computerId) });
        if (!computer) {
            return json({ 
                success: false, 
                error: 'Computer not found' 
            }, { status: 404 });
        }

        // Update workload entry
        const updateResult = await Workload.updateOne(
            { 
                computerId: new ObjectId(computerId),
                year: Number(year),
                month: Number(month)
            },
            {
                $set: {
                    hours: Number(hours),
                    hasFixed: Boolean(hasFixed),
                    activity: activity || null,
                    updatedAt: new Date()
                }
            },
            { upsert: true }
        );

        // Update activities in computer document
        if (activity) {
            const activities = computer.activities || {};
            activities[`${year}-${month}`] = activity;
            
            await CustomerComputers.updateOne(
                { _id: new ObjectId(computerId) },
                { 
                    $set: { 
                        activities: activities,
                        updatedAt: new Date()
                    } 
                }
            );
        }

        return json({
            success: true,
            message: 'Workload entry updated successfully',
            data: {
                year: Number(year),
                month: Number(month),
                hours: Number(hours),
                hasFixed: Boolean(hasFixed),
                activity: activity
            }
        });
    } catch (err) {
        return json({ 
            success: false, 
            error: 'Internal server error' 
        }, { status: 500 });
    } finally {
        await client.close();
    }
}
