<script>
  import { goto } from '$app/navigation';
  import { onMount } from 'svelte';
  import ComputerDetail from './computer-detail.svelte';

  /** @type {import('./$types').PageData} */
  export let data;

  // Define types
  /** @typedef {Object} CustomerType
   * @property {string} _id
   * @property {string} companyName
   * @property {string} [email]
   * @property {string} [phone]
   * @property {string} [address]
   * @property {string} [city]
   * @property {string} [country]
   * @property {string} [type]
   * @property {string} [division]
   */

  // Get item data from page props
  const itemId = data.itemId;
  const parentCollection = data.parentCollection || 'Customers';
  const parentId = data.parentId;
  let item = data.item;
  /** @type {CustomerType|null} */
  let customerDetails = null;
  let isLoading = false;
  let loadError = '';

  // Format dates for input fields
  /**
   * @param {string|Date|null|undefined} dateStr
   * @returns {string}
   */
  function formatDateForInput(dateStr) {
    if (!dateStr) return '';
    try {
      return new Date(dateStr).toISOString().split('T')[0];
    } catch (e) {
      return '';
    }
  }

  // Initialize form data with item data
  let formData = {
    _id: item?._id || '',
    customerId: item?.customerId || '',
    name: item?.name || '',
    type: item?.type || '',
    serialNumber: item?.serialNumber || '',
    model: item?.model || '',
    manufacturer: item?.manufacturer || '',
    operatingSystem: item?.operatingSystem || '',
    notes: item?.notes || '',
    status: item?.status || 'Active',

    // Additional fields from the example data
    ProductDesignation: item?.ProductDesignation || item?.productDesignation || '',
    ProductPartNumber: item?.ProductPartNumber || '',
    ProductValidityGroup: item?.ProductValidityGroup || '',
    ComputerCategory: item?.ComputerCategory || '',
    HoursContractStart: item?.HoursContractStart || 0,

    // Contract information
    contractNumber: item?.contractNumber || '',
    contractAgeLimit: item?.contractAgeLimit || 0,
    contractHoursLimit: item?.contractHoursLimit || 0,
    contractLengthYrs: item?.contractLengthYrs || 0,
    desiredContractLengthYears: item?.desiredContractLengthYears || 0,
    desiredContractLengthHrs: item?.desiredContractLengthHrs || 0,

    // Engine information
    engineAgeYrs: item?.engineAgeYrs || 0,
    engineAgeHrs: item?.engineAgeHrs || 0,
    estimatedUtilizationHrsYr: item?.estimatedUtilizationHrsYr || 0,

    // Dates - format for input fields
    purchaseDate: formatDateForInput(item?.purchaseDate),
    deliveryDate: formatDateForInput(item?.deliveryDate),
    warrantyEndDate: formatDateForInput(item?.warrantyEndDate),
    desiredContractStartDate: formatDateForInput(item?.desiredContractStartDate),
    contractStartDate: formatDateForInput(item?.contractStartDate),
    contractEndDate: formatDateForInput(item?.contractEndDate)
  };

  // State variables for editing
  let showForm = false;
  let showDeleteConfirm = false;
  let saveError = '';
  let saveSuccess = false;

  // Fetch the latest computer data from the API
  async function fetchComputerData() {
    isLoading = true;
    loadError = '';

    try {
      const response = await fetch(`/api/customer-computers/${itemId}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to fetch computer data: ${response.status}`);
      }

      const computerData = await response.json();

      // Update the item data
      item = computerData;

      // If there's customer data, store it separately
      if (item.customer) {
        customerDetails = item.customer;
      }

      // Update form data with the latest values
      updateFormData();

      return computerData;
    } catch (err) {
      console.error('Error fetching computer data:', err);
      loadError = err instanceof Error ? err.message : 'Failed to load computer data';
      return null;
    } finally {
      isLoading = false;
    }
  }

  // Update form data with the latest item values
  function updateFormData() {
    formData = {
      _id: item?._id || '',
      customerId: item?.customerId || '',
      name: item?.name || '',
      type: item?.type || '',
      serialNumber: item?.serialNumber || '',
      model: item?.model || '',
      manufacturer: item?.manufacturer || '',
      operatingSystem: item?.operatingSystem || '',
      notes: item?.notes || '',
      status: item?.status || 'Active',

      // Additional fields
      ProductDesignation: item?.ProductDesignation || item?.productDesignation || '',
      ProductPartNumber: item?.ProductPartNumber || '',
      ProductValidityGroup: item?.ProductValidityGroup || '',
      ComputerCategory: item?.ComputerCategory || '',
      HoursContractStart: item?.HoursContractStart || 0,

      // Contract information
      contractNumber: item?.contractNumber || '',
      contractAgeLimit: item?.contractAgeLimit || 0,
      contractHoursLimit: item?.contractHoursLimit || 0,
      contractLengthYrs: item?.contractLengthYrs || 0,
      desiredContractLengthYears: item?.desiredContractLengthYears || 0,
      desiredContractLengthHrs: item?.desiredContractLengthHrs || 0,

      // Engine information
      engineAgeYrs: item?.engineAgeYrs || 0,
      engineAgeHrs: item?.engineAgeHrs || 0,
      estimatedUtilizationHrsYr: item?.estimatedUtilizationHrsYr || 0,

      // Dates - format for input fields
      purchaseDate: formatDateForInput(item?.purchaseDate),
      deliveryDate: formatDateForInput(item?.deliveryDate),
      warrantyEndDate: formatDateForInput(item?.warrantyEndDate),
      desiredContractStartDate: formatDateForInput(item?.desiredContractStartDate),
      contractStartDate: formatDateForInput(item?.contractStartDate),
      contractEndDate: formatDateForInput(item?.contractEndDate)
    };
  }

  // Form handling
  function editItem() {
    updateFormData();
    showForm = true;
  }

  function cancelEdit() {
    showForm = false;
  }

  async function saveItem() {
    saveError = '';
    saveSuccess = false;

    try {
      const response = await fetch(`/api/customer-computers/${itemId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const result = await response.json();

      if (response.ok) {
        saveSuccess = true;
        showForm = false;
        // Refresh the data
        await fetchComputerData();
      } else {
        saveError = result.error || 'Failed to save changes';
      }
    } catch (err) {
      console.error('Error saving item:', err);
      saveError = err instanceof Error ? err.message : 'An error occurred while saving';
    }
  }

  // Handle delete
  async function handleDelete() {
    saveError = '';

    try {
      const response = await fetch(`/api/customer-computers/${itemId}`, {
        method: 'DELETE'
      });

      const result = await response.json();

      if (response.ok) {
        // Redirect back to parent item
        goto(`/liststd1-one-page?collection=${parentCollection}&itemId=${parentId}`);
      } else {
        saveError = result.error || 'Failed to delete computer';
        showDeleteConfirm = false;
      }
    } catch (err) {
      console.error('Error deleting computer:', err);
      saveError = err instanceof Error ? err.message : 'An error occurred while deleting';
      showDeleteConfirm = false;
    }
  }

  // Fetch data on component mount
  onMount(async () => {
    await fetchComputerData();
  });
</script>

<div class="one-page-container">
  <div class="header-container">
    <!-- Top row - Back to Computers button only -->
    <div class="header-row top-row">
      <button
        class="btn-secondary back-button"
        on:click={() => goto(`/liststd1-one-page?collection=${parentCollection}&itemId=${parentId}`)}
        aria-label="Back to computers"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="19" y1="12" x2="5" y2="12"></line>
          <polyline points="12 19 5 12 12 5"></polyline>
        </svg>
        Back to Computers
      </button>
    </div>

    <!-- Bottom row - Action buttons in specified order -->
    <div class="header-row bottom-row">
      {#if !showForm}
        <div class="action-buttons">
          <!-- Edit button -->
          <button
            class="btn-primary"
            on:click={editItem}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
            </svg>
            Edit
          </button>

          <!-- Delete button -->
          <button
            class="btn-danger"
            on:click={() => showDeleteConfirm = true}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="3 6 5 6 21 6"></polyline>
              <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
              <line x1="10" y1="11" x2="10" y2="17"></line>
              <line x1="14" y1="11" x2="14" y2="17"></line>
            </svg>
            Delete
          </button>

          <!-- Workload button -->
          <button
            class="btn-secondary"
            on:click={() => {
              // Get the product designation from the form data
              const productDesignation = formData.ProductDesignation || '';
              const productValidityGroup = formData.ProductValidityGroup || '';

              // Use either ProductValidityGroup or ProductDesignation, with ProductValidityGroup taking precedence
              const designationParam = productValidityGroup || productDesignation;

              // Navigate to workload display with both computerId and productDesignation
              goto(`/subliststd1-one-page/workload-display?computerId=${item._id}${designationParam ? `&productDesignation=${encodeURIComponent(designationParam)}` : ''}`);
            }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
              <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
            </svg>
            Workload
          </button>

          <!-- Workload Plan button -->
          <button
            class="btn-primary"
            on:click={() => goto(`/subliststd1-one-page/proposed-plan?computerId=${itemId}`)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14 2 14 8 20 8"></polyline>
              <line x1="16" y1="13" x2="8" y2="13"></line>
              <line x1="16" y1="17" x2="8" y2="17"></line>
              <polyline points="10 9 9 9 8 9"></polyline>
            </svg>
            Workload Plan
          </button>
        </div>
      {:else}
        <div class="edit-actions">
          <button
            class="btn-secondary"
            on:click={cancelEdit}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
            Cancel
          </button>
          <button
            type="submit"
            class="btn-primary"
            form="edit-form"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
              <polyline points="17 21 17 13 7 13 7 21"></polyline>
              <polyline points="7 3 7 8 15 8"></polyline>
            </svg>
            Save
          </button>
        </div>
      {/if}
    </div>
  </div>

  <!-- Computer ID and Name Display -->
  {#if item && !isLoading}
    <div class="computer-info-banner">
      <div class="computer-info-content">
        <div class="computer-id-section">
          <span class="computer-id-label">Computer ID:</span>
          <span class="computer-id-value">{item._id || itemId}</span>
        </div>
        <div class="computer-name-section">
          <span class="computer-name-label">Computer Name:</span>
          <span class="computer-name-value">{item.name || 'Unnamed Computer'}</span>
        </div>
      </div>
    </div>
  {/if}

  <div class="content-area">
    {#if isLoading}
      <div class="loading-indicator">
        <p>Loading data...</p>
      </div>
    {:else if loadError}
      <div class="error-message global-error">
        <p>{loadError}</p>
        <button class="btn-secondary" on:click={fetchComputerData}>Retry</button>
      </div>
    {:else if item}
      <!-- Customer Details Section -->
      {#if customerDetails}
        <div class="info-card">
          <div class="card-header">
            <h2>Customer Information</h2>
          </div>
          <div class="card-content">
            <div class="customer-details">
              <div class="customer-header">
                <h3>{customerDetails.companyName || 'N/A'}</h3>
                {#if customerDetails.type}
                  <span class="customer-type {customerDetails.type.toLowerCase().replace(' ', '-')}">
                    {customerDetails.type}
                  </span>
                {/if}
              </div>

              <div class="customer-info-grid">
                <div class="info-group">
                  <span class="info-label">Email</span>
                  <span class="info-value">{customerDetails.email || 'N/A'}</span>
                </div>

                <div class="info-group">
                  <span class="info-label">Phone</span>
                  <span class="info-value">{customerDetails.phone || 'N/A'}</span>
                </div>

                <div class="info-group">
                  <span class="info-label">Address</span>
                  <span class="info-value">
                    {customerDetails.address || 'N/A'}
                    {#if customerDetails.city || customerDetails.country}
                      <br>
                      {customerDetails.city || ''} {customerDetails.country ? `, ${customerDetails.country}` : ''}
                    {/if}
                  </span>
                </div>

                {#if customerDetails.division}
                  <div class="info-group">
                    <span class="info-label">Division</span>
                    <span class="info-value">{customerDetails.division}</span>
                  </div>
                {/if}
              </div>
            </div>
          </div>
        </div>
      {/if}

      <!-- Computer Details Section -->
      <div class="info-card">
        <div class="card-header">
          <h2>{showForm ? 'Edit Computer Details' : 'Computer Details'}</h2>
          {#if saveSuccess}
            <div class="success-message">Changes saved successfully!</div>
          {/if}
          {#if saveError}
            <div class="error-message">{saveError}</div>
          {/if}
        </div>
        <div class="card-content">
          <ComputerDetail
            item={item}
            showForm={showForm}
            formData={formData}
            onSave={saveItem}
            onCancel={cancelEdit}
          />
        </div>
      </div>
    {/if}
  </div>
</div>

<!-- Delete confirmation modal -->
{#if showDeleteConfirm}
  <div class="modal-overlay">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">Confirm Delete</h2>
        <button
          type="button"
          class="modal-close"
          on:click={() => showDeleteConfirm = false}
          aria-label="Close delete confirmation"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <p>Are you sure you want to delete this computer? This action cannot be undone.</p>
        <p class="warning">This will permanently remove the computer from the database.</p>
      </div>

      <div class="modal-actions">
        <button type="button" class="btn-secondary" on:click={() => showDeleteConfirm = false}>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
          Cancel
        </button>
        <button type="button" class="btn-danger" on:click={handleDelete}>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="3 6 5 6 21 6"></polyline>
            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
            <line x1="10" y1="11" x2="10" y2="17"></line>
            <line x1="14" y1="11" x2="14" y2="17"></line>
          </svg>
          Confirm Delete
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  /* Base styles */
  .one-page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: #f8fafc;
    min-height: calc(100vh - 100px);
  }

  .header-container {
    margin-bottom: 1.5rem;
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
    overflow: hidden;
  }

  .header-row {
    padding: 0.75rem 1.25rem;
    display: flex;
    align-items: center;
  }

  .top-row {
    justify-content: center;
    border-bottom: 1px solid #f1f5f9;
    background-color: #f8fafc;
  }

  .bottom-row {
    justify-content: center;
    padding: 1rem 1.25rem;
  }

  .action-buttons {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;
  }

  .page-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
    text-align: center;
    letter-spacing: -0.01em;
  }

  .edit-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
  }

  .success-message {
    background-color: #c6f6d5;
    color: #2f855a;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    margin-top: 0.5rem;
  }

  .error-message {
    background-color: #fed7d7;
    color: #c53030;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    margin-top: 0.5rem;
  }

  .global-error {
    margin: 1rem 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .loading-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .info-card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin-bottom: 1rem;
  }

  .card-header {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f1f5f9;
    background-color: #f8fafc;
  }

  .card-header h2 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #334155;
  }

  .card-content {
    padding: 1rem;
  }

  /* Customer details styles */
  .customer-details {
    padding: 0.5rem;
  }

  .customer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .customer-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
  }

  .customer-type {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
  }

  .customer-type.retail {
    background-color: #e0f2fe;
    color: #0369a1;
  }

  .customer-type.wholesale {
    background-color: #dcfce7;
    color: #15803d;
  }

  .customer-type.distributor {
    background-color: #fef3c7;
    color: #92400e;
  }

  .customer-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
  }

  .info-group {
    margin-bottom: 0.5rem;
  }

  .info-label {
    display: block;
    font-size: 0.75rem;
    font-weight: 500;
    color: #64748b;
    margin-bottom: 0.25rem;
  }

  .info-value {
    display: block;
    font-size: 0.875rem;
    color: #1e293b;
  }

  /* Computer Info Banner styles */
  .computer-info-banner {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    border-radius: 0.75rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .computer-info-content {
    padding: 1.25rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .computer-id-section,
  .computer-name-section {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .computer-id-label,
  .computer-name-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .computer-id-value,
  .computer-name-value {
    font-size: 1.125rem;
    font-weight: 600;
    color: white;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  }

  .computer-name-value {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  }

  /* Responsive design for computer info banner */
  @media (max-width: 640px) {
    .computer-info-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.75rem;
    }

    .computer-id-section,
    .computer-name-section {
      width: 100%;
    }
  }

  /* Modal styles */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(15, 23, 42, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 1rem;
    backdrop-filter: blur(2px);
  }

  .modal-content {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    border: 1px solid rgba(226, 232, 240, 0.8);
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid #f1f5f9;
    background-color: #f8fafc;
    border-top-left-radius: 0.75rem;
    border-top-right-radius: 0.75rem;
  }

  .modal-title {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    letter-spacing: -0.01em;
  }

  .modal-close {
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 9999px;
    transition: all 0.2s ease;
  }

  .modal-close:hover {
    background-color: #f1f5f9;
    color: #334155;
  }

  .modal-body {
    padding: 1.75rem 1.5rem;
    font-size: 0.95rem;
    color: #334155;
    line-height: 1.5;
  }

  .warning {
    color: #dc2626;
    margin-top: 0.75rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .warning::before {
    content: "⚠️";
  }

  .modal-actions {
    display: flex;
    justify-content: flex-end;
    padding: 1.25rem 1.5rem;
    gap: 1rem;
    border-top: 1px solid #f1f5f9;
    background-color: #f8fafc;
    border-bottom-left-radius: 0.75rem;
    border-bottom-right-radius: 0.75rem;
  }

  /* Button styles */
  .btn-primary, .btn-secondary, .btn-danger {
    padding: 0.5rem 1.25rem;
    border-radius: 0.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    letter-spacing: 0.01em;
    text-transform: uppercase;
    font-size: 0.75rem;
  }

  .btn-primary {
    background-color: #1e40af; /* Dark blue (blue-800) */
    color: white;
  }

  .btn-primary:hover {
    background-color: #1e3a8a; /* Darker blue (blue-900) */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
  }

  .btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .btn-secondary {
    background-color: #1e40af; /* Dark blue (blue-800) */
    color: white;
    border: none;
  }

  .btn-secondary:hover {
    background-color: #1e3a8a; /* Darker blue (blue-900) */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
  }

  .btn-secondary:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .btn-danger {
    background-color: #ef4444; /* Red 500 */
    color: white;
  }

  .btn-danger:hover {
    background-color: #dc2626; /* Red 600 */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
  }

  .btn-danger:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  /* Header action buttons */
  .header-container .btn-primary,
  .header-container .btn-secondary,
  .header-container .btn-danger {
    min-width: 100px;
    height: 36px;
    border-radius: 18px;
    font-weight: 600;
  }

  /* Back button specific style */
  .back-button {
    min-width: 80px;
    background-color: #1e40af; /* Dark blue (blue-800) */
    border: none;
    color: white;
    transition: all 0.2s ease;
  }

  .back-button:hover {
    background-color: #1e3a8a; /* Darker blue (blue-900) */
    color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  /* SVG icon styles */
  .h-5 {
    height: 1.25rem;
  }

  .w-5 {
    width: 1.25rem;
  }

  .h-6 {
    height: 1.5rem;
  }

  .w-6 {
    width: 1.5rem;
  }
</style>
