import { COLLECTIONS, serializeDocument, createObjectId, executeDbOperation } from './serviceContractDB.js';
import serviceContractDB from './serviceContractDB.js';

const db = serviceContractDB.db;
const collection = COLLECTIONS.PRICING_RULES;

/**
 * Create a new pricing rule
 * @param {Object} pricingRule - Pricing rule data
 * @returns {Promise<Object>} Inserted document with ID
 */
export async function createPricingRule(pricingRule) {
  // Required fields validation
  if (!pricingRule.code || !pricingRule.name) {
    throw new Error('Pricing rule code and name are required');
  }
  
  // Add timestamps
  const now = new Date();
  const documentToInsert = {
    ...pricingRule,
    active: pricingRule.active !== false, // Default to active if not specified
    createdAt: now,
    updatedAt: now
  };
  
  return executeDbOperation(async () => {
    // Check for duplicate code
    const existing = await db.collection(collection).findOne({ 
      code: pricingRule.code 
    });
    
    if (existing) {
      throw new Error(`Pricing rule with code ${pricingRule.code} already exists`);
    }
    
    const result = await db.collection(collection).insertOne(documentToInsert);
    if (!result.acknowledged) {
      throw new Error('Failed to create pricing rule');
    }
    
    return {
      ...serializeDocument(documentToInsert),
      _id: result.insertedId.toString()
    };
  }, 'Error creating pricing rule');
}

/**
 * Get all pricing rules with optional filtering
 * @param {Object} filter - MongoDB filter object
 * @param {Object} options - MongoDB options (sort, limit, etc.)
 * @returns {Promise<Array>} Array of pricing rules
 */
export async function getPricingRules(filter = {}, options = {}) {
  return executeDbOperation(async () => {
    const defaultOptions = {
      sort: { code: 1 },
      ...options
    };
    
    const cursor = db.collection(collection).find(filter, defaultOptions);
    const documents = await cursor.toArray();
    return documents.map(doc => serializeDocument(doc));
  }, 'Error retrieving pricing rules');
}

/**
 * Get a single pricing rule by ID
 * @param {string} id - Pricing rule ID
 * @returns {Promise<Object|null>} Pricing rule or null if not found
 */
export async function getPricingRuleById(id) {
  return executeDbOperation(async () => {
    const objectId = createObjectId(id);
    if (!objectId) return null;
    
    const document = await db.collection(collection).findOne({ _id: objectId });
    return document ? serializeDocument(document) : null;
  }, 'Error retrieving pricing rule');
}

/**
 * Get a pricing rule by code
 * @param {string} code - Pricing rule code
 * @returns {Promise<Object|null>} Pricing rule or null if not found
 */
export async function getPricingRuleByCode(code) {
  return executeDbOperation(async () => {
    if (!code) return null;
    
    const document = await db.collection(collection).findOne({ code });
    return document ? serializeDocument(document) : null;
  }, 'Error retrieving pricing rule by code');
}

/**
 * Update a pricing rule
 * @param {string} id - Pricing rule ID
 * @param {Object} updates - Fields to update
 * @returns {Promise<Object|null>} Updated pricing rule or null if not found
 */
export async function updatePricingRule(id, updates) {
  return executeDbOperation(async () => {
    const objectId = createObjectId(id);
    if (!objectId) return null;
    
    // Don't allow updating the _id field
    if (updates._id) delete updates._id;
    
    // Check if trying to update code to an existing one
    if (updates.code) {
      const existingWithCode = await db.collection(collection).findOne({
        code: updates.code,
        _id: { $ne: objectId }
      });
      
      if (existingWithCode) {
        throw new Error(`Pricing rule with code ${updates.code} already exists`);
      }
    }
    
    // Add updated timestamp
    const updateData = {
      ...updates,
      updatedAt: new Date()
    };
    
    const result = await db.collection(collection).findOneAndUpdate(
      { _id: objectId },
      { $set: updateData },
      { returnDocument: 'after' }
    );
    
    return result.value ? serializeDocument(result.value) : null;
  }, 'Error updating pricing rule');
}

/**
 * Delete a pricing rule
 * @param {string} id - Pricing rule ID
 * @returns {Promise<boolean>} True if deleted, false if not found
 */
export async function deletePricingRule(id) {
  return executeDbOperation(async () => {
    const objectId = createObjectId(id);
    if (!objectId) return false;
    
    // Check if pricing rule is in use
    const contractLinesUsingRule = await db.collection(COLLECTIONS.CONTRACT_LINES)
      .countDocuments({ pricingRuleId: objectId });
    
    if (contractLinesUsingRule > 0) {
      throw new Error(`Cannot delete pricing rule as it is used in ${contractLinesUsingRule} contract lines`);
    }
    
    const result = await db.collection(collection).deleteOne({ _id: objectId });
    return result.deletedCount > 0;
  }, 'Error deleting pricing rule');
}

/**
 * Get pricing rules applicable to a specific product category
 * @param {string} productCategory - Product category code
 * @param {boolean} activeOnly - If true, only return active rules
 * @returns {Promise<Array>} Array of applicable pricing rules
 */
export async function getPricingRulesByProductCategory(productCategory, activeOnly = true) {
  return executeDbOperation(async () => {
    if (!productCategory) return [];
    
    const filter = {
      $or: [
        { applicableCategories: { $in: [productCategory] } },
        { applicableCategories: { $exists: false } } // Global rules with no category restrictions
      ]
    };
    
    if (activeOnly) {
      filter.active = true;
    }
    
    const documents = await db.collection(collection)
      .find(filter)
      .sort({ priority: -1, code: 1 })
      .toArray();
    
    return documents.map(doc => serializeDocument(doc));
  }, 'Error retrieving pricing rules by product category');
}

/**
 * Calculate price based on a rule
 * @param {string} ruleId - Pricing rule ID
 * @param {Object} parameters - Parameters for price calculation
 * @returns {Promise<Object>} Calculated price details
 */
export async function calculatePrice(ruleId, parameters) {
  return executeDbOperation(async () => {
    const rule = await getPricingRuleById(ruleId);
    if (!rule) {
      throw new Error('Pricing rule not found');
    }
    
    // Simple implementation - in a real system this would be more complex
    // and would handle different rule types and formulas
    let basePrice = rule.basePrice || 0;
    
    // Apply multipliers based on parameters
    if (rule.hoursMultiplier && parameters.hours) {
      basePrice += parameters.hours * rule.hoursMultiplier;
    }
    
    if (rule.powerMultiplier && parameters.power) {
      basePrice += parameters.power * rule.powerMultiplier;
    }
    
    // Apply discounts
    let discount = 0;
    if (rule.volumeDiscounts && parameters.quantity) {
      // Find applicable volume discount
      const applicableDiscount = rule.volumeDiscounts.find(
        d => parameters.quantity >= d.minQuantity && 
             (!d.maxQuantity || parameters.quantity <= d.maxQuantity)
      );
      
      if (applicableDiscount) {
        discount = basePrice * (applicableDiscount.discountPercentage / 100);
      }
    }
    
    const netPrice = basePrice - discount;
    
    return {
      basePrice,
      discount,
      netPrice,
      currency: rule.currency || 'EUR',
      calculationDetails: {
        ruleApplied: rule.code,
        parameters: parameters
      }
    };
  }, 'Error calculating price');
}

export default {
  createPricingRule,
  getPricingRules,
  getPricingRuleById,
  getPricingRuleByCode,
  updatePricingRule,
  deletePricingRule,
  getPricingRulesByProductCategory,
  calculatePrice
};
