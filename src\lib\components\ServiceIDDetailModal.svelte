<script lang="ts">
  export let item = null;
  export let onClose = () => {};
  export let onSave = (updated) => {};
  export let onDelete = (item) => {};

  let editMode = false;
  let edited = {};

  $: if (item) {
    edited = { ...item };
  }

  function handleSave() {
    onSave(edited);
    editMode = false;
  }

  // Accessibility: close on Escape
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      onClose();
    }
  }

  // Workaround: ensure close events bubble and are handled
  function handleCloseClick(e) {
    e.stopPropagation();
    onClose();
  }
</script>

{#if item}
  <div
    class="modal-backdrop"
    role="presentation"
    aria-label="Close modal"
    tabindex="0"
    on:click={handleCloseClick}
    on:keydown={(e) => { if (e.key === 'Enter' || e.key === ' ') handleCloseClick(e); }}
  ></div>
  <div class="modal" role="dialog" aria-modal="true" tabindex="0" on:keydown={handleKeydown} autofocus>
    <div class="modal-header">
      <h2>ServiceID Details</h2>
      <button class="close-btn" aria-label="Close" type="button" on:click={handleCloseClick}>&times;</button>
    </div>
    <div class="modal-body">
      <div class="kv-grid">
        {#each Object.keys(item) as key}
          <div class="kv-row">
            <div class="kv-key">{key}</div>
            <div class="kv-value">
              {#if editMode && key !== '_id' && key !== 'createdAt' && key !== 'updatedAt'}
                <input type="text" bind:value={edited[key]} />
              {:else}
                {item[key]}
              {/if}
            </div>
          </div>
        {/each}
      </div>
    </div>
    <div class="modal-actions">
      {#if !editMode}
        <button class="btn edit" on:click={() => (editMode = true)}>Edit</button>
        <button class="btn delete" on:click={() => onDelete(item)}>Delete</button>
        <button class="btn close" type="button" on:click={handleCloseClick}>Close</button>
      {/if}
      {#if editMode}
        <button class="btn save" on:click={handleSave}>Save</button>
        <button class="btn cancel" on:click={() => (editMode = false)}>Cancel</button>
      {/if}
    </div>
  </div>
{/if}

<style>
.modal-backdrop {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  z-index: 1000;
}
.modal {
  position: fixed;
  top: 50%; left: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 16px rgba(0,0,0,0.15);
  z-index: 1001;
  min-width: 400px;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  outline: none;
}
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
}
.close-btn {
  background: none;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  line-height: 1;
  padding: 0 0.5rem;
}
.modal-body {
  padding: 1rem;
}
.kv-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 0.5rem 1.5rem;
}
.kv-row {
  display: contents;
}
.kv-key {
  font-weight: bold;
  color: #1976d2;
  text-align: right;
  padding-right: 0.5rem;
}
.kv-value {
  text-align: left;
}
.modal-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  padding: 1rem;
  border-top: 1px solid #eee;
}
.btn {
  padding: 0.4rem 1.2rem;
  border-radius: 4px;
  border: none;
  cursor: pointer;
}
.btn.edit {
  background: #1976d2;
  color: #fff;
}
.btn.save {
  background: #388e3c;
  color: #fff;
}
.btn.cancel {
  background: #bbb;
  color: #fff;
}
.btn.delete {
  background: #d32f2f;
  color: #fff;
}
.btn.close {
  background: #888;
  color: #fff;
}
</style>
