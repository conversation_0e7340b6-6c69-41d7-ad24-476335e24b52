<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  
  // Props
  export let months: string[] = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  export let isLoading: boolean = false;
  
  // Activity options - keep consistent with other components
  const activityOptions: string[] = [
    'Regular Service',
    'Contract Service',
    'Maintenance',
    'Repair',
    'Inspection',
    'Installation',
    'Remote Support',
    'Software Update',
    'Hardware Update',
    'Training',
    'Consultation'
  ];
  
  // Initialize with current year
  const currentYear: number = new Date().getFullYear();
  let startYear: number = currentYear;
  let startMonth: number = 1;
  let endYear: number = currentYear;
  let endMonth: number = 12;
  let hours: string = '1'; // Initialize with valid default as string for input
  let engineHours: string = '0'; // Initialize engine hours with default of 0
  let activity: string = 'Regular Service'; // Set default to ensure we always have a value
  let hasFixed: boolean = false;
  let showCustomActivity: boolean = false;
  let customActivity: string = '';
  
  // Event dispatcher
  const dispatch = createEventDispatcher<{
    submit: {
      startYear: number;
      startMonth: number;
      endYear: number;
      endMonth: number;
      hours: number;
      engineHours: number;
      activity: string;
      hasFixed: boolean;
    };
  }>();
  
  // Handler to ensure only whole numbers are allowed
  function handleHoursInput(event: Event) {
    const input = event.target as HTMLInputElement;
    // Remove any non-digit characters
    const value = input.value.replace(/[^0-9]/g, '');
    
    // Force integer value
    input.value = value;
    
    // Update the hours value - empty string or integer
    if (value === '') {
      hours = '1'; // Default to 1 if empty
    } else {
      const parsedValue = parseInt(value, 10);
      hours = parsedValue > 0 ? String(parsedValue) : '1'; // Ensure positive value
    }
  }
  
  // Handler to ensure only whole numbers are allowed for engine hours
  function handleEngineHoursInput(event: Event) {
    const input = event.target as HTMLInputElement;
    // Remove any non-digit characters
    const value = input.value.replace(/[^0-9]/g, '');
    
    // Force integer value
    input.value = value;
    
    // Update the engine hours value - empty string or integer
    if (value === '') {
      engineHours = '0'; // Default to 0 if empty
    } else {
      const parsedValue = parseInt(value, 10);
      engineHours = parsedValue >= 0 ? String(parsedValue) : '0'; // Ensure non-negative value
    }
  }
  
  // Prevent dots, commas and decimal input
  function handleHoursKeydown(event: KeyboardEvent) {
    // Block decimal points (period and comma) and NumPad decimal
    if (event.key === '.' || event.key === ',' || 
        event.code === 'Period' || event.code === 'Comma' || 
        event.code === 'NumpadDecimal') {
      event.preventDefault();
    }
  }
  
  // Prevent mouse wheel from changing the value when focused
  function handleWheel(event: WheelEvent) {
    event.currentTarget.blur();
  }
  
  function handleSubmit() {
    // Validate required fields
    const hoursValue = parseInt(hours, 10);
    const engineHoursValue = parseInt(engineHours, 10);
    
    if (isNaN(hoursValue) || hoursValue <= 0) {
      alert('Hours must be a positive whole number');
      return;
    }
    
    if (isNaN(engineHoursValue) || engineHoursValue < 0) {
      alert('Engine Hours must be a non-negative whole number');
      return;
    }
    
    // Ensure we have an activity
    if (!activity && !customActivity) {
      alert('Activity is required');
      return;
    }
    
    console.log('Submitting form with activity:', activity, 'hours:', hoursValue, 'engineHours:', engineHoursValue);
    
    dispatch('submit', {
      startYear,
      startMonth,
      endYear,
      endMonth,
      hours: hoursValue,
      engineHours: engineHoursValue,
      activity: showCustomActivity ? customActivity : activity,
      hasFixed
    });
  }
  
  function toggleCustomActivity() {
    showCustomActivity = !showCustomActivity;
    if (showCustomActivity) {
      activity = '';
    } else {
      customActivity = '';
    }
  }
</script>

<form class="bulk-hours-form" on:submit|preventDefault={handleSubmit}>
  <div class="form-grid">
    <div class="date-section">
      <h3>Date Range</h3>
      <div class="date-ranges">
        <div class="date-range">
          <div class="input-group">
            <label for="bulkStartYear">Start Year</label>
            <input id="bulkStartYear" type="number" min="2000" max="2100" bind:value={startYear} class="form-input" />
          </div>
          
          <div class="input-group">
            <label for="bulkStartMonth">Start Month</label>
            <select id="bulkStartMonth" bind:value={startMonth} class="form-select">
              {#each months as month, i}
                <option value={i + 1}>{month}</option>
              {/each}
            </select>
          </div>
        </div>
        
        <div class="date-range">
          <div class="input-group">
            <label for="bulkEndYear">End Year</label>
            <input id="bulkEndYear" type="number" min="2000" max="2100" bind:value={endYear} class="form-input" />
          </div>
          
          <div class="input-group">
            <label for="bulkEndMonth">End Month</label>
            <select id="bulkEndMonth" bind:value={endMonth} class="form-select">
              {#each months as month, i}
                <option value={i + 1}>{month}</option>
              {/each}
            </select>
          </div>
        </div>
      </div>
    </div>
    
    <div class="activities-section">
      <h3>Activity Details</h3>
      <div class="activities-grid">
        <div class="hours-input">
          <label for="bulkHours">Hours to Add</label>
          <div class="integer-input-container">
            <input 
              id="bulkHours" 
              type="text" 
              inputmode="numeric" 
              pattern="^[1-9][0-9]*$"
              bind:value={hours} 
              on:keypress={(e) => {
                // Only allow digits 0-9
                if (!/^\d$/.test(e.key) && e.key !== 'Backspace' && e.key !== 'Delete' && e.key !== 'ArrowLeft' && e.key !== 'ArrowRight') {
                  e.preventDefault();
                }
              }}
              on:input={(e) => {
                // Remove any non-digit characters
                const cleanValue = e.target.value.replace(/\D/g, '');
                e.target.value = cleanValue;
                
                // Convert to integer and ensure it's positive
                const parsedValue = parseInt(cleanValue, 10);
                if (isNaN(parsedValue) || parsedValue <= 0) {
                  hours = '1'; // Default to 1 if invalid
                } else {
                  hours = cleanValue;
                }
              }}
              class="form-input" 
              placeholder="Enter whole number"
              required 
            />
            <div class="integer-only-badge">INTEGER ONLY</div>
          </div>
          <small class="helper-text">Must be a positive whole number (no decimals)</small>
        </div>
        
        <div class="engine-hours-input">
          <label for="bulkEngineHours">Engine Hours</label>
          <div class="integer-input-container">
            <input 
              id="bulkEngineHours" 
              type="text" 
              inputmode="numeric" 
              pattern="^[0-9]*$"
              bind:value={engineHours} 
              on:input={handleEngineHoursInput}
              class="form-input" 
              placeholder="Enter whole number"
              required 
            />
            <div class="integer-only-badge">INTEGER ONLY</div>
          </div>
          <small class="helper-text">Must be a non-negative whole number (no decimals)</small>
        </div>
        
        <div class="activity-input">
          <label for="bulkActivity">Activity</label>
          <div class="activity-input-group">
            {#if !showCustomActivity}
              <select 
                id="bulkActivity" 
                bind:value={activity}
                class="form-select"
                required={!showCustomActivity}
              >
                <option value="">-- Select activity --</option>
                {#each activityOptions as option}
                  <option value={option}>{option}</option>
                {/each}
              </select>
            {:else}
              <input 
                type="text"
                id="bulkCustomActivity"
                bind:value={customActivity}
                placeholder="Enter custom activity"
                class="form-input custom-input"
                required={showCustomActivity}
              />
            {/if}
            
            <button 
              type="button" 
              class="toggle-button"
              on:click={toggleCustomActivity}
              title={showCustomActivity ? "Choose from options" : "Enter custom activity"}
            >
              {showCustomActivity ? 'Select' : 'Custom'}
            </button>
          </div>
        </div>
        
        <div class="fixed-checkbox">
          <label class="checkbox-label">
            <input type="checkbox" bind:checked={hasFixed} id="bulkHasFixed" />
            <span>Fixed Schedule</span>
          </label>
          <small>Activities with fixed schedule cannot be rescheduled</small>
        </div>
      </div>
    </div>
    
    <div class="form-actions">
      <button type="submit" class="primary-button" disabled={isLoading}>
        {isLoading ? 'Applying...' : 'Apply to Range'}
      </button>
    </div>
  </div>
</form>

<style>
  .bulk-hours-form {
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(8px);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    border: 1px solid rgba(51, 65, 85, 0.3);
  }
  
  .form-grid {
    display: grid;
    gap: 1.5rem;
  }
  
  h3 {
    color: #60a5fa;
    font-size: 1rem;
    margin: 0 0 0.75rem 0;
    position: relative;
    padding-left: 0.75rem;
  }
  
  h3::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(to bottom, #60a5fa, #3b82f6);
    border-radius: 3px;
  }
  
  .date-ranges {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.25rem;
  }
  
  .activities-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.25rem;
  }
  
  .fixed-checkbox {
    grid-column: span 2;
  }
  
  .date-range {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    align-items: end;
  }
  
  .input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  label {
    font-size: 0.875rem;
    color: #94a3b8;
    font-weight: 500;
  }
  
  .form-input, .form-select {
    background-color: rgba(15, 23, 42, 0.8);
    border: 1px solid #334155;
    border-radius: 8px;
    color: #e2e8f0;
    padding: 0.625rem 0.75rem;
    font-size: 0.95rem;
    transition: border-color 0.2s, box-shadow 0.2s;
  }
  
  .form-input:focus, .form-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  }
  
  .activity-input-group {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 0.5rem;
  }
  
  .toggle-button {
    background: linear-gradient(to right, #64748b, #475569);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.8rem;
    padding: 0 0.75rem;
    cursor: pointer;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }
  
  .toggle-button:hover {
    background: linear-gradient(to right, #7f8ea3, #566882);
  }
  
  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
  }
  
  .checkbox-label input {
    width: 1rem;
    height: 1rem;
    accent-color: #3b82f6;
  }
  
  small {
    font-size: 0.75rem;
    color: #94a3b8;
    display: block;
    margin-left: 1.5rem;
  }
  
  .helper-text {
    font-size: 0.75rem;
    color: #94a3b8;
    margin-top: 0.25rem;
    margin-left: 0;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 0.5rem;
  }
  
  .primary-button {
    background: linear-gradient(to right, #3b82f6, #2563eb);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.625rem 1.25rem;
    font-weight: 500;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
    box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.3);
  }
  
  .primary-button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 10px -1px rgba(37, 99, 235, 0.4);
  }
  
  .primary-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  .integer-input-container {
    position: relative;
    display: flex;
  }
  
  .integer-only-badge {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #3b82f6;
    color: white;
    font-size: 0.6rem;
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 4px;
    pointer-events: none;
  }
  
  @media (max-width: 768px) {
    .date-ranges, 
    .activities-grid {
      grid-template-columns: 1fr;
    }
    
    .fixed-checkbox {
      grid-column: 1;
    }
  }
</style>
