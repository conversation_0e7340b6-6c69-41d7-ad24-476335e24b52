<!-- ServiceActionEditor.svelte -->
<script lang="ts">
    import { createEventDispatcher } from 'svelte';
    
    const dispatch = createEventDispatcher();
    
    // Define interfaces
    interface ServiceAction {
        _id: string;
        title: string;
        description: string;
        demarcation: string;
        status: string;
        priority: string;
        assignedTo?: string;
        dueDate?: string | Date;
        createdAt?: string | Date;
        updatedAt?: string | Date;
    }
    
    // Props
    export let isOpen = false;
    export let action: ServiceAction = {
        _id: '',
        title: '',
        description: '',
        demarcation: '',
        status: 'Open',
        priority: 'Medium',
        assignedTo: '',
        dueDate: ''
    };
    
    // Local state for editing
    let editedAction: ServiceAction;
    
    // Reset form when action changes
    $: if (action) {
        editedAction = { ...action };
    }
    
    // Status options
    const statusOptions = ['Open', 'In Progress', 'Completed'];
    
    // Priority options
    const priorityOptions = ['High', 'Medium', 'Low'];
    
    // Handle save
    function handleSave() {
        // Validate form
        if (!editedAction.title) {
            alert('Title is required');
            return;
        }
        
        // Dispatch save event
        dispatch('save', editedAction);
    }
    
    // Handle cancel
    function handleCancel() {
        dispatch('cancel');
    }
</script>

<div class="service-action-editor-backdrop" class:visible={isOpen} on:click={handleCancel} role="dialog" aria-modal="true" tabindex="-1" on:keydown={(e) => e.key === 'Escape' && handleCancel()}>
    <div class="service-action-editor-modal" on:click|stopPropagation role="group" tabindex="-1">
        <div class="editor-header">
            <h3>{action._id ? 'Edit' : 'Add'} Service Action</h3>
            <button class="close-button" on:click={handleCancel}>&times;</button>
        </div>
        
        <div class="editor-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="title">Title*</label>
                    <input 
                        id="title" 
                        type="text" 
                        bind:value={editedAction.title} 
                        required
                        class="focus-field"
                    />
                </div>
                
                <div class="form-group">
                    <label for="demarcation">Demarcation</label>
                    <input 
                        id="demarcation" 
                        type="text" 
                        bind:value={editedAction.demarcation} 
                        readonly
                        class="readonly-field"
                    />
                </div>
            </div>
            
            <div class="form-row full-width">
                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea 
                        id="description" 
                        bind:value={editedAction.description} 
                        rows="3"
                    ></textarea>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="priority">Priority</label>
                    <select id="priority" bind:value={editedAction.priority}>
                        {#each priorityOptions as option}
                            <option value={option}>{option}</option>
                        {/each}
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="status">Status</label>
                    <select id="status" bind:value={editedAction.status}>
                        {#each statusOptions as option}
                            <option value={option}>{option}</option>
                        {/each}
                    </select>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="assignedTo">Assigned To</label>
                    <input 
                        id="assignedTo" 
                        type="text" 
                        bind:value={editedAction.assignedTo} 
                    />
                </div>
                
                <div class="form-group">
                    <label for="dueDate">Due Date</label>
                    <input 
                        id="dueDate" 
                        type="date" 
                        bind:value={editedAction.dueDate} 
                    />
                </div>
            </div>
            
            <div class="button-row">
                <button class="cancel-button" on:click={handleCancel}>Cancel</button>
                <button class="save-button" on:click={handleSave}>Save</button>
            </div>
        </div>
    </div>
</div>

<style>
    .service-action-editor-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.2s ease;
    }
    
    .service-action-editor-backdrop.visible {
        opacity: 1;
        pointer-events: auto;
    }
    
    .service-action-editor-modal {
        background-color: white;
        border-radius: 8px;
        width: 600px;
        max-width: 90%;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }
    
    .editor-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background-color: #102a54;
        color: white;
    }
    
    .editor-header h3 {
        margin: 0;
        font-size: 1.25rem;
    }
    
    .close-button {
        background: transparent;
        border: none;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
    }
    
    .editor-form {
        padding: 1.5rem;
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1rem;
    }
    
    .form-row.full-width {
        grid-template-columns: 1fr;
    }
    
    .form-group {
        display: flex;
        flex-direction: column;
    }
    
    .form-group label {
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #4b5563;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.5rem;
        border: 1px solid #d1d5db;
        border-radius: 4px;
        font-size: 1rem;
    }
    
    .form-group textarea {
        resize: vertical;
        min-height: 80px;
    }
    
    .readonly-field {
        background-color: #f3f4f6;
        cursor: not-allowed;
    }
    
    .focus-field {
        border-color: #1e40af;
        box-shadow: 0 0 0 2px rgba(30, 64, 175, 0.2);
    }
    
    .button-row {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        margin-top: 1.5rem;
    }
    
    .cancel-button,
    .save-button {
        padding: 0.5rem 1rem;
        border-radius: 4px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .cancel-button {
        background-color: #f3f4f6;
        border: 1px solid #d1d5db;
        color: #4b5563;
    }
    
    .save-button {
        background-color: #1e40af;
        border: 1px solid #1e3a8a;
        color: white;
    }
    
    .cancel-button:hover {
        background-color: #e5e7eb;
    }
    
    .save-button:hover {
        background-color: #1e3a8a;
    }
</style>
