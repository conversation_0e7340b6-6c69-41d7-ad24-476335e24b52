import { json } from '@sveltejs/kit';
import { getCollection } from '$lib/db/mongo';
import { ObjectId } from 'mongodb';

/**
 * Updates the CustomerComputers collection schema:
 * - Keeps ProductName field
 * - Adds SupplierProductName field
 */
export async function POST() {
    try {
        const collection = await getCollection('CustomerComputers');
        
        // Get all documents that don't have SupplierProductName field
        const computers = await collection.find({
            SupplierProductName: { $exists: false }
        }).toArray();
        
        console.log(`Found ${computers.length} computers to update`);
        
        // Update each document to add SupplierProductName field
        let updateCount = 0;
        for (const computer of computers) {
            // Initially set SupplierProductName to the same value as ProductName (if exists)
            // This keeps existing data intact until specific updates are made
            const updateResult = await collection.updateOne(
                { _id: new ObjectId(computer._id) },
                { $set: { 
                    SupplierProductName: computer.productName || computer.ProductName || ''
                }}
            );
            
            if (updateResult.modifiedCount > 0) {
                updateCount++;
            }
        }
        
        return json({
            success: true,
            message: `Updated ${updateCount} of ${computers.length} computers to include SupplierProductName field`,
            totalDocuments: computers.length,
            updatedDocuments: updateCount
        });
    } catch (err) {
        console.error('Error updating CustomerComputers schema:', err);
        return new Response(
            JSON.stringify({
                error: 'Internal Server Error',
                details: err instanceof Error ? err.message : 'Unknown error'
            }),
            {
                status: 500,
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );
    }
}
