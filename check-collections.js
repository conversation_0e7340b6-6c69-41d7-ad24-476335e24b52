import { MongoClient } from 'mongodb';

// Simple connection test
const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/**
 * Main function to connect to MongoDB and check collections.
 * 
 * @throws {MongoError} If there's an error connecting to MongoDB or accessing collections.
 */
async function main() {
    try {
        console.log('Attempting to connect to MongoDB...');
        await client.connect();
        console.log('Successfully connected to MongoDB');
        
        // From memory: database name is ServiceContracts
        // Following memory guidance on database and collection names
        const db = client.db('ServiceContracts');
        
        // Check BaseServices collection structure (from memory)
        console.log('\nChecking BaseServices collection...');
        const baseServicesCollection = db.collection('BaseServices');
        const baseServices = await baseServicesCollection
            .find({})
            .limit(1)
            .toArray();

        if (baseServices.length > 0) {
            console.log('BaseServices fields:');
            // Check exact field names from BaseServices collection
            console.log('- _id:', typeof baseServices[0]._id);
            console.log('- ServiceCode:', typeof baseServices[0].ServiceCode);
            console.log('- Service activity Label:', typeof baseServices[0]['Service activity Label']);
            console.log('- Activity purpose:', typeof baseServices[0]['Activity purpose']);
            console.log('- Product Validity Group:', typeof baseServices[0]['Product Validity Group']);
            console.log('- Internal No of Hours:', typeof baseServices[0]['Internal No of Hours']);
            console.log('- Internal No of Months:', typeof baseServices[0]['Internal No of Months']);
            console.log('- createdAt:', typeof baseServices[0].createdAt);
            console.log('- updatedAt:', typeof baseServices[0].updatedAt);
            console.log('\nSample ServiceCode:', baseServices[0].ServiceCode);
        } else {
            console.log('No documents found in BaseServices');
        }

        // Check PartNumbersServiceCodeAction collection structure (from memory)
        console.log('\nChecking PartNumbersServiceCodeAction collection...');
        const actionsCollection = db.collection('PartNumbersServiceCodeAction');
        const actions = await actionsCollection
            .find({})
            .limit(1)
            .toArray();

        if (actions.length > 0) {
            console.log('PartNumbersServiceCodeAction fields:');
            // Check exact field names from PartNumbersServiceCodeAction collection
            console.log('- _id:', typeof actions[0]._id);
            console.log('- ServiceCode:', typeof actions[0].ServiceCode);
            console.log('- PartNumber:', typeof actions[0].PartNumber);
            console.log('- ActionType:', typeof actions[0].ActionType);
            console.log('- Product Validity Group:', typeof actions[0]['Product Validity Group']);
            console.log('- Quantity:', typeof actions[0].Quantity);
            console.log('- Unit of Measure:', typeof actions[0]['Unit of Measure']);
            console.log('\nSample ServiceCode:', actions[0].ServiceCode);
        } else {
            console.log('No documents found in PartNumbersServiceCodeAction');
        }

        // Check available collections
        const collections = await db.listCollections().toArray();
        console.log('\nAvailable collections:', collections.map(c => c.name));
        
    } catch (error) {
        // Add proper error handling
        if (error instanceof Error) {
            console.error('Error:', error.message);
        } else {
            console.error('Unknown error:', error);
        }
    } finally {
        // Close the client to free up resources
        await client.close();
        console.log('\nConnection closed');
    }
}

// Run and catch any unhandled errors
main().catch(error => {
    // Add proper error handling for unhandled errors
    if (error instanceof Error) {
        console.error('Unhandled error:', error.message);
    } else {
        console.error('Unhandled unknown error:', error);
    }
});
