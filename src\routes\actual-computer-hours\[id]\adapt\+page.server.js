import { ObjectId } from 'mongodb';
import { json, fail } from '@sveltejs/kit';
import { getCollection, isValidObjectId, toObjectId } from '$lib/db/mongo.js';

/** @type {import('./$types').PageServerLoad} */
export async function load() {
  // No data needed for now; just return an empty object to fix the 500 error
  return {};
}

export async function POST({ params, request }) {
  const id = params.id;
  let data;
  try {
    data = await request.json();
  } catch (err) {
    console.error('Invalid JSON:', err);
    return fail(400, { error: 'Invalid JSON in request body.' });
  }

  // Basic validation (expand as needed)
  if (!data.computerId || !data.month || !data.year || !data.activity || typeof data.hours !== 'number') {
    return fail(400, { error: 'Missing required fields.' });
  }

  // Convert fields as needed
  const updateDoc = {
    computerId: isValidObjectId(data.computerId) ? toObjectId(data.computerId) : data.computerId,
    month: Number(data.month),
    year: Number(data.year),
    activity: data.activity,
    hasFixed: !!data.hasFixed,
    hours: Number(data.hours),
    isIdle: !!data.isIdle,
    ReportDateTime: data.ReportDateTime ? new Date(data.ReportDateTime) : null,
    updatedAt: new Date()
  };

  // Ensure ReportDateTime is present (combine ReportDate and ReportTime if needed)
  if (!updateDoc.ReportDateTime && updateDoc.ReportDate && updateDoc.ReportTime) {
    const dateStr = updateDoc.ReportDate;
    const timeStr = updateDoc.ReportTime.length === 5 ? updateDoc.ReportTime + ':00' : updateDoc.ReportTime;
    updateDoc.ReportDateTime = new Date(`${dateStr}T${timeStr}`);
  }

  // Calculate HoursPerMonth (hours per month since last entry)
  let hoursPerMonth = null;
  if (updateDoc.computerId && updateDoc.ReportDateTime) {
    const collection = await getCollection('ActualComputerHours');
    const prev = await collection.find({
      computerId: updateDoc.computerId,
      _id: { $ne: isValidObjectId(id) ? toObjectId(id) : id },
      ReportDateTime: { $lt: updateDoc.ReportDateTime }
    }).sort({ ReportDateTime: -1 }).limit(1).toArray();
    if (prev.length > 0) {
      const prevDoc = prev[0];
      const prevHours = parseFloat(prevDoc.hours) || 0;
      const prevDate = new Date(prevDoc.ReportDateTime);
      const currHours = parseFloat(updateDoc.hours) || 0;
      const currDate = new Date(updateDoc.ReportDateTime);
      const diffHours = currHours - prevHours;
      const monthsDiff = (currDate.getFullYear() - prevDate.getFullYear()) * 12 + (currDate.getMonth() - prevDate.getMonth()) || 1;
      hoursPerMonth = monthsDiff > 0 ? diffHours / monthsDiff : diffHours;
    } else {
      hoursPerMonth = parseFloat(updateDoc.hours) || 0;
    }
  }
  if (hoursPerMonth !== null && !isNaN(hoursPerMonth)) {
    updateDoc.HoursPerMonth = hoursPerMonth;
  }

  try {
    const collection = await getCollection('ActualComputerHours');
    const filter = isValidObjectId(id) ? { _id: toObjectId(id) } : { _id: id };
    const result = await collection.updateOne(
      filter,
      { $set: updateDoc }
    );
    if (result.modifiedCount === 1) {
      return json({ success: true });
    } else {
      console.error('No document updated:', { filter, updateDoc });
      return fail(404, { error: 'Document not found or not updated.' });
    }
  } catch (err) {
    console.error('DB error:', err);
    return fail(500, { error: 'Database error: ' + (err.message || err) });
  }
}
