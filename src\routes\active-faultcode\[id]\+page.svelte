<script lang="ts">
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  export let data: { faultcode: any };
  let editing = false;
  let editingFaultCode = { ...data.faultcode };
  let editError: string | null = null;
  let savingEdit = false;

  function startEdit() {
    editing = true;
    editingFaultCode = { ...data.faultcode };
    editError = null;
  }
  function cancelEdit() {
    editing = false;
    editError = null;
  }
  async function saveEdit() {
    savingEdit = true;
    editError = null;
    try {
      const res = await fetch(`/active-faultcode/${data.faultcode._id}/edit`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(editingFaultCode)
      });
      const result = await res.json();
      if (!result.success) {
        editError = result.error || 'Failed to update fault code.';
        savingEdit = false;
        return;
      }
      editing = false;
      goto(`/active-faultcode/${data.faultcode._id}`);
    } catch (e) {
      editError = 'Error: ' + (e?.message || e);
    }
    savingEdit = false;
  }
</script>

<div class="faultcode-page-container">
  <h1>Active Fault Code Details</h1>
  <button class="backbtn" on:click={() => history.length > 1 ? history.back() : goto('/computer-id/' + data.faultcode.computerId + '/service-activities/service-planning')}>Back</button>
  {#if !editing}
    <ul class="faultcode-details-list">
      {#each Object.entries(data.faultcode) as [key, value]}
        <li><strong>{key}:</strong> {typeof value === 'object' && value !== null ? JSON.stringify(value) : value?.toString()}</li>
      {/each}
    </ul>
    <button class="editbtn" on:click={startEdit}>Edit</button>
  {:else}
    <form class="edit-faultcode-form" on:submit|preventDefault={saveEdit}>
      {#each Object.entries(editingFaultCode) as [key, value]}
        <div class="edit-field">
          <label>{key}</label>
          {#if key === 'StartDateTime' || key === 'StopDateTime'}
            <input type="datetime-local" bind:value={editingFaultCode[key]} />
          {:else if key === '_id'}
            <input type="text" value={editingFaultCode[key]} disabled />
          {:else}
            <input type="text" bind:value={editingFaultCode[key]} />
          {/if}
        </div>
      {/each}
      {#if editError}
        <div class="edit-error">{editError}</div>
      {/if}
      <button type="submit" class="savebtn" disabled={savingEdit}>Save</button>
      <button type="button" class="cancelbtn" on:click={cancelEdit} disabled={savingEdit}>Cancel</button>
    </form>
  {/if}
</div>

<style>
.faultcode-page-container {
  max-width: 600px;
  margin: 2.5rem auto;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 16px rgba(0,0,0,0.07);
  padding: 2.5rem 2rem 2rem 2rem;
  position: relative;
}
.backbtn {
  position: absolute;
  left: 2rem;
  top: 2rem;
  background: #f3f3f3;
  color: #333;
  border: 1px solid #e3e7ee;
  border-radius: 6px;
  padding: 0.4em 1.2em;
  font-size: 1em;
  cursor: pointer;
  margin-bottom: 1em;
  transition: background 0.15s;
}
.backbtn:hover {
  background: #e8e8e8;
}
.faultcode-details-list {
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
}
.faultcode-details-list li {
  margin-bottom: 0.6em;
  font-size: 1.08em;
  color: #2d3a4a;
}
.edit-faultcode-form {
  display: flex;
  flex-direction: column;
  gap: 0.7em;
  margin-bottom: 1.2em;
}
.edit-field {
  display: flex;
  flex-direction: column;
  gap: 0.18em;
}
.edit-field label {
  font-size: 1em;
  color: #4a5a6a;
  font-weight: 500;
}
.edit-field input {
  font-size: 1em;
  padding: 0.45em 0.7em;
  border: 1px solid #d2d8e0;
  border-radius: 5px;
  background: #f9f9fb;
}
.editbtn, .savebtn, .cancelbtn {
  margin-top: 0.7em;
  padding: 0.4em 1.2em;
  border-radius: 6px;
  border: none;
  font-size: 1em;
  cursor: pointer;
}
.editbtn {
  background: #3763ff;
  color: #fff;
}
.savebtn {
  background: #059669;
  color: #fff;
}
.cancelbtn {
  background: #f3f3f3;
  color: #333;
  margin-left: 0.7em;
}
.edit-error {
  color: #dc2626;
  font-weight: 500;
  margin-bottom: 0.5em;
}
</style>
