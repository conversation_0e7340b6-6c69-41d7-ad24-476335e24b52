import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';
import type { RequestEvent } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/** @type {import('./$types').RequestHandler} */
export async function GET({ url }: RequestEvent) {
    try {
        await client.connect();
        const db = client.db('ServiceContracts');
        
        const computerId = url.searchParams.get('computerId');
        if (!computerId || !ObjectId.isValid(computerId)) {
            throw new Error('Valid computer ID is required');
        }

        // 1. Get Computer Details
        const computersCollection = db.collection('CustomerComputers');
        const computer = await computersCollection.findOne({ _id: new ObjectId(computerId) });
        if (!computer) {
            throw new Error('Computer not found');
        }

        // 2. Get Product Validity Group
        const pvgCollection = db.collection('ProductValidityGroupPartNumber');
        const pvgDoc = await pvgCollection.findOne({
            ProductPartNumber: parseInt(computer.ProductPartNumber)
        });

        // 3. Get Service Elements
        const serviceCodeCollection = db.collection('ServiceCodeAndActionType');
        const serviceElements = await serviceCodeCollection
            .find({ 
                "Product Validity Group": pvgDoc?.["Product Validity Group"] || '',
            })
            .toArray();

        // 4. Get Workload Data
        const workloadCollection = db.collection('Workload');
        const workloadData = await workloadCollection
            .find({ computerId: new ObjectId(computerId) })
            .toArray();

        // Log all data for debugging
        console.log('Computer:', computer);
        console.log('PVG Doc:', pvgDoc);
        console.log('Service Elements:', serviceElements);
        console.log('Workload Data:', workloadData);

        return json({
            success: true,
            data: {
                computer: {
                    _id: computer._id,
                    ProductPartNumber: computer.ProductPartNumber,
                    ProductDesignation: computer.ProductDesignation,
                    contractStartDate: computer.contractStartDate,
                    contractEndDate: computer.contractEndDate,
                    HoursContractStart: computer.HoursContractStart
                },
                productValidityGroup: pvgDoc ? {
                    _id: pvgDoc._id,
                    "Product Validity Group": pvgDoc["Product Validity Group"],
                    ProductPartNumber: pvgDoc.ProductPartNumber
                } : null,
                serviceElements: serviceElements.map(e => ({
                    _id: e._id,
                    ServiceCode: e.ServiceCode,
                    ServiceActivityLabel: e.ServiceActivityLabel,
                    "Product Validity Group": e["Product Validity Group"],
                    ActionType: e.ActionType,
                    Hrs: e.Hrs,
                    Months: e.Months
                })),
                workloadData: workloadData.map(w => ({
                    _id: w._id,
                    year: w.year,
                    month: w.month,
                    hours: w.hours,
                    hasFixed: w.hasFixed,
                    activity: w.activity
                }))
            }
        });

    } catch (error: unknown) {
        console.error('Error:', error);
        return json({
            success: false,
            message: error instanceof Error ? error.message : 'An unknown error occurred'
        }, { status: 500 });
    } finally {
        await client.close();
    }
}
