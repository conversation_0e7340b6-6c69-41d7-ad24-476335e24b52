<!-- QuotationListGrid.svelte -->
<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  
  // Optional props for customization
  export let gap: string = '0.5rem';
  export let padding: string = '1rem';
  export let backgroundColor: string = '#ffffff';
  export let className: string = '';
</script>

<BaseGrid 
  columns="1fr" 
  rows="auto"
  {gap}
  {padding}
  {backgroundColor}
  className="quotation-list-grid {className}">
  <slot />
</BaseGrid>

<style>
  /* Additional styling specific to this grid */
  :global(.quotation-list-grid) {
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    overflow: hidden;
  }
</style>
