<script>
  // Grid component that uses CSS Grid for layout
</script>

<div class="grid-container">
  <slot></slot>
</div>

<style>
  .grid-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    width: 100%;
  }
  
  @media (max-width: 1024px) {
    .grid-container {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media (max-width: 640px) {
    .grid-container {
      grid-template-columns: 1fr;
    }
  }
</style>
