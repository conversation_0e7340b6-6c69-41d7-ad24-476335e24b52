import { init } from '$lib/db';
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async () => {
    try {
        const db = await init();
        const Workload = db.collection("Workload");
        
        // Update ALL documents to set activity to 'idle'
        const result = await Workload.updateMany(
            {}, // update all documents
            { 
                $set: { 
                    activity: 'idle'
                } 
            }
        );

        return json({
            success: true,
            message: `Updated workload records with 'idle' activity`,
            matchedCount: result.matchedCount,
            modifiedCount: result.modifiedCount
        });
    } catch (error) {
        console.error('Error updating workload activity:', error);
        return json(
            { 
                success: false, 
                message: 'Failed to update workload activity',
                error: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
};
