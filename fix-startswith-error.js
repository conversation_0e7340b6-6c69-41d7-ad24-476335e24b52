/**
 * Fix script for "Cannot read properties of undefined (reading 'startsWith')" error
 * 
 * This script patches the common causes of this error by adding proper type checks:
 * 1. Add null/undefined checks before using string methods
 * 2. Use standardized field names (ProductValidityGroup in PascalCase)
 * 3. Add safety checks for URL parameters
 */

import fs from 'fs';
import path from 'path';

const baseDir = './src/routes/computer-id';

// Files to patch
const filesToPatch = [
  path.join(baseDir, '[id]/+page.server.js'),
  path.join(baseDir, '[id]/service-activities/service-planning/+page.server.js'),
  path.join(baseDir, '[id]/service-quotation/+page.server.js')
];

for (const filePath of filesToPatch) {
  try {
    console.log(`\nProcessing ${filePath}...`);
    
    if (!fs.existsSync(filePath)) {
      console.log(`File not found: ${filePath}`);
      continue;
    }
    
    // Read file content
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // 1. Add type checks for URL parameters (often causes startsWith errors)
    const paramCheckRegex = /(\w+Param)(\s*&&\s*)(\w+Param\.trim\(\))/g;
    if (paramCheckRegex.test(content)) {
      content = content.replace(paramCheckRegex, '$1 && typeof $1 === "string" $2$3');
      modified = true;
      console.log('✅ Added type checks for URL parameters');
    }
    
    // 2. Add null checks for accessing object properties
    const objAccessRegex = /(productDetails\[\d+\])\.(\w+)/g;
    if (objAccessRegex.test(content)) {
      content = content.replace(objAccessRegex, '$1 && $1.$2');
      modified = true;
      console.log('✅ Added null checks for array item access');
    }
    
    // 3. Fix "Product Validity Group" to standardized "ProductValidityGroup"
    if (content.includes('"Product Validity Group"')) {
      content = content.replace(/"Product Validity Group"/g, 'ProductValidityGroup');
      modified = true;
      console.log('✅ Standardized ProductValidityGroup field name');
    }
    
    // 4. Enhanced safety for default values in mapping
    const mapRegex = /(\w+)\.(\w+)(?!\s*\|\|)/g;
    if (mapRegex.test(content)) {
      // Only modify specific patterns to avoid breaking the code
      const safeReplacements = [
        { pattern: /(\w+)\.ProductValidityGroup(?!\s*\|\|)/g, replacement: '$1.ProductValidityGroup || ""' },
        { pattern: /(\w+)\.ProductDesignation(?!\s*\|\|)/g, replacement: '$1.ProductDesignation || ""' },
        { pattern: /(\w+)\.Category(?!\s*\|\|)/g, replacement: '$1.Category || ""' }
      ];
      
      for (const { pattern, replacement } of safeReplacements) {
        if (pattern.test(content)) {
          content = content.replace(pattern, replacement);
          modified = true;
          console.log(`✅ Added default values for ${pattern}`);
        }
      }
    }
    
    if (modified) {
      // Backup original file
      fs.writeFileSync(`${filePath}.backup`, fs.readFileSync(filePath));
      // Write updated content
      fs.writeFileSync(filePath, content);
      console.log(`✅ Successfully updated ${filePath}`);
    } else {
      console.log(`ℹ️ No changes needed for ${filePath}`);
    }
    
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error);
  }
}

console.log('\n✅ Finished checking and fixing files');
console.log('Please restart your server to apply the changes.');
