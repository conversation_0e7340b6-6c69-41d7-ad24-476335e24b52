<script lang="ts">
  import { formatCurrency } from '$lib/utils/formatters';

  export let totalRows: number;
  export let totalAmount: number = 0; // KCOS total
  export let totalSSP: number = 0;
  export let totalOemImporter: number = 0;
  export let totalFleetOwner: number = 0;
</script>

<div class="summary-grid">
  <div class="summary-item">
    <span class="label">Total Rows:</span>
    <span class="value">{totalRows}</span>
  </div>
  <div class="summary-item">
    <span class="label">Total KCOS:</span>
    <span class="value">{formatCurrency(totalAmount)}</span>
  </div>
  <div class="summary-item">
    <span class="label">Total OEM Importer:</span>
    <span class="value">{formatCurrency(totalOemImporter)}</span>
  </div>
  <div class="summary-item">
    <span class="label">Total Fleet Owner:</span>
    <span class="value">{formatCurrency(totalFleetOwner)}</span>
  </div>
  <div class="summary-item">
    <span class="label">Total SSP:</span>
    <span class="value">{formatCurrency(totalSSP)}</span>
  </div>
</div>

<style>
  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  }

  .summary-item {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 0.5rem;
    align-items: center;
    padding: 1rem;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  }

  .label {
    color: #6c757d;
    font-weight: 500;
  }

  .value {
    font-weight: 600;
    color: #2c3e50;
    text-align: right;
  }
</style>
