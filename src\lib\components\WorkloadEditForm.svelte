<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { fade } from 'svelte/transition';
  import { onMount } from 'svelte';

  export let workload: {
    id?: string;
    computerId: string;
    year: number;
    month: number;
    hours: number;
    activity: string;
    type: 'Fixed' | 'Soft';
    description: string;
  };

  export let isOpen = false;

  const dispatch = createEventDispatcher<{
    save: { workload: typeof workload };
    cancel: void;
  }>();

  const activityTypes = [
    'Idle',
    'Contract Service',
    'Emergency Service',
    'Preventive Maintenance',
    'Installation',
    'Consulting',
    'Other'
  ];

  function handleSubmit() {
    // Ensure all numeric values are properly converted
    const updatedWorkload = {
      ...workload,
      hours: Number(workload.hours) || 0,
      year: Number(workload.year) || new Date().getFullYear(),
      month: Number(workload.month) || 1
    };
    dispatch('save', { workload: updatedWorkload });
  }

  let modalElement: HTMLElement;

  function handleCancel() {
    dispatch('cancel');
  }

  // Focus the modal when it opens
  onMount(() => {
    if (modalElement) {
      modalElement.focus();
    }
  });
</script>

{#if isOpen}
  <div
    class="modal-overlay"
    on:click|self={handleCancel}
    on:keydown|stopPropagation={(e) => e.key === 'Escape' && handleCancel()}
    role="dialog"
    aria-modal="true"
    aria-labelledby="modal-title"
    tabindex="-1"
    transition:fade
    bind:this={modalElement}
    on:keydown|stopPropagation
  >
    <div class="modal-content">
      <div class="modal-header">
        <h2 id="modal-title">
          {workload.id ? 'Edit Workload' : 'Add Workload'}
        </h2>
        <button
          class="close-button"
          on:click={handleCancel}
          aria-label="Close dialog"
        >
          &times;
        </button>
      </div>

      <div class="modal-body">
        <div class="form-group">
          <label for="hours">Hours</label>
          <input
            id="hours"
            type="number"
            min="0"
            step="0.5"
            bind:value={workload.hours}
            class="form-input"
            on:input={(e) => {
              // Ensure we store the value as a number
              const target = e.target as HTMLInputElement;
              workload.hours = parseFloat(target.value) || 0;
            }}
          />
        </div>

        <div class="form-group">
          <label for="activity">Activity</label>
          <select
            id="activity"
            bind:value={workload.activity}
            class="form-input"
          >
            {#each activityTypes as type}
              <option value={type}>
                {type}
              </option>
            {/each}
          </select>
        </div>

        <div class="form-group">
          <label for="workload-type">Type</label>
          <div id="workload-type" class="radio-group">
            <span class="radio-option">
              <input
                id="type-fixed"
                type="radio"
                value="Fixed"
                bind:group={workload.type}
              />
              <label for="type-fixed" class="radio-label">Fixed</label>
            </span>
            <span class="radio-option">
              <input
                id="type-soft"
                type="radio"
                value="Soft"
                bind:group={workload.type}
              />
              <label for="type-soft" class="radio-label">Soft</label>
            </span>
          </div>
        </div>

        <div class="form-group">
          <label for="description">Description (Optional)</label>
          <textarea
            id="description"
            bind:value={workload.description}
            class="form-input"
            rows="3"
          ></textarea>
        </div>
      </div>

      <div class="modal-footer">
        <button
          type="button"
          class="btn-secondary"
          on:click={handleCancel}
        >
          Cancel
        </button>
        <button
          type="button"
          class="btn-primary"
          on:click={handleSubmit}
        >
          {workload.id ? 'Update' : 'Create'}
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(2px);
  }

  .modal-content {
    background: white;
    border-radius: 0.5rem;
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    animation: modalFadeIn 0.2s ease-out;
  }

  @keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .modal-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .modal-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1a202c;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 1.75rem;
    cursor: pointer;
    color: #718096;
    line-height: 1;
    padding: 0.25rem;
    margin: -0.25rem -0.5rem -0.25rem 0.5rem;
  }

  .close-button:hover {
    color: #2d3748;
  }

  .modal-body {
    padding: 1.5rem;
  }

  .form-group {
    margin-bottom: 1.25rem;
  }

  .form-group:last-child {
    margin-bottom: 0;
  }

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #4a5568;
    font-size: 0.875rem;
  }

  .form-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    font-size: 0.9375rem;
    transition: border-color 0.2s, box-shadow 0.2s;
  }

  .form-input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
  }

  textarea.form-input {
    min-height: 100px;
    resize: vertical;
  }

  .radio-group {
    display: flex;
    gap: 1.5rem;
    margin-top: 0.5rem;
  }

  .radio-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: normal;
  }

  .radio-option input[type="radio"] {
    margin-right: 0.5rem;
  }

  .radio-label {
    margin: 0;
    font-weight: normal;
  }

  .modal-footer {
    padding: 1.25rem 1.5rem;
    border-top: 1px solid #e2e8f0;
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
  }

  .btn-primary,
  .btn-secondary {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
  }

  .btn-primary {
    background-color: #4299e1;
    color: white;
    border: 1px solid #2b6cb0;
  }

  .btn-primary:hover {
    background-color: #3182ce;
  }

  .btn-secondary {
    background-color: #e2e8f0;
    color: #4a5568;
    border: 1px solid #cbd5e0;
  }

  .btn-secondary:hover {
    background-color: #cbd5e0;
  }
</style>
