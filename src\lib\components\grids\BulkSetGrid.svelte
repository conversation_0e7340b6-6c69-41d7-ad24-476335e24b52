<script lang="ts">
  import Modal from '$lib/components/Modal.svelte';

  export let activities: string[] = [];
  export let onSetValue: (data: {
    startYear: number;
    startMonth: number;
    endYear: number;
    endMonth: number;
    hours: number;
    activity: string;
  }) => void;
  export let onCancel: (() => void) | undefined = undefined;
  export let showModal = false;
  export let showInWindow = false;
  export let saving = false;

  let startYear = new Date().getFullYear();
  let startMonth = 1;
  let endYear = new Date().getFullYear();
  let endMonth = 12;
  let hours = 0;
  let activity = '';

  function handleSubmit() {
    onSetValue({
      startYear: parseInt(String(startYear)),
      startMonth: parseInt(String(startMonth)),
      endYear: parseInt(String(endYear)),
      endMonth: parseInt(String(endMonth)),
      hours: parseInt(String(hours)),
      activity
    });
  }

  function handleCancel() {
    if (showInWindow && onCancel) {
      onCancel();
    } else {
      showModal = false;
    }
  }

  function resetForm() {
    startYear = new Date().getFullYear();
    startMonth = 1;
    endYear = new Date().getFullYear();
    endMonth = 12;
    hours = 0;
    activity = '';
  }

  $: if (showModal && !showInWindow) {
    resetForm();
  }
</script>

{#if showInWindow}
  <div class="bulk-set-grid">
    <div class="form-content">
      <div class="date-range">
        <div class="date-group">
          <h4>Start Date</h4>
          <div class="input-group">
            <div class="field">
              <label for="startYear">Year:</label>
              <input
                type="number"
                id="startYear"
                bind:value={startYear}
                min="2000"
                max="2100"
              />
            </div>
            <div class="field">
              <label for="startMonth">Month:</label>
              <input
                type="number"
                id="startMonth"
                bind:value={startMonth}
                min="1"
                max="12"
              />
            </div>
          </div>
        </div>

        <div class="date-group">
          <h4>End Date</h4>
          <div class="input-group">
            <div class="field">
              <label for="endYear">Year:</label>
              <input
                type="number"
                id="endYear"
                bind:value={endYear}
                min="2000"
                max="2100"
              />
            </div>
            <div class="field">
              <label for="endMonth">Month:</label>
              <input
                type="number"
                id="endMonth"
                bind:value={endMonth}
                min="1"
                max="12"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="value-group">
        <div class="field">
          <label for="hours">Hours:</label>
          <input
            type="number"
            id="hours"
            bind:value={hours}
            min="0"
            max="999"
          />
        </div>

        <div class="field">
          <label for="activity">Activity:</label>
          <select 
            id="activity"
            bind:value={activity}
          >
            <option value="">None</option>
            {#each activities as act}
              <option value={act}>{act}</option>
            {/each}
          </select>
        </div>
      </div>

      <div class="button-group">
        <button class="cancel-btn" on:click={handleCancel}>
          Cancel
        </button>
        <button 
          class="set-value-btn" 
          on:click={handleSubmit}
          disabled={!activity || hours < 0 || saving}
        >
          {saving ? 'Setting Values...' : 'Set Value'}
        </button>
      </div>
    </div>
  </div>
{:else}
  <Modal bind:showModal title="Set Value Range">
    <div class="bulk-set-grid">
      <div class="form-content">
        <div class="date-range">
          <div class="date-group">
            <h4>Start Date</h4>
            <div class="input-group">
              <div class="field">
                <label for="startYear">Year:</label>
                <input
                  type="number"
                  id="startYear"
                  bind:value={startYear}
                  min="2000"
                  max="2100"
                />
              </div>
              <div class="field">
                <label for="startMonth">Month:</label>
                <input
                  type="number"
                  id="startMonth"
                  bind:value={startMonth}
                  min="1"
                  max="12"
                />
              </div>
            </div>
          </div>

          <div class="date-group">
            <h4>End Date</h4>
            <div class="input-group">
              <div class="field">
                <label for="endYear">Year:</label>
                <input
                  type="number"
                  id="endYear"
                  bind:value={endYear}
                  min="2000"
                  max="2100"
                />
              </div>
              <div class="field">
                <label for="endMonth">Month:</label>
                <input
                  type="number"
                  id="endMonth"
                  bind:value={endMonth}
                  min="1"
                  max="12"
                />
              </div>
            </div>
          </div>
        </div>

        <div class="value-group">
          <div class="field">
            <label for="hours">Hours:</label>
            <input
              type="number"
              id="hours"
              bind:value={hours}
              min="0"
              max="999"
            />
          </div>

          <div class="field">
            <label for="activity">Activity:</label>
            <select 
              id="activity"
              bind:value={activity}
            >
              <option value="">None</option>
              {#each activities as act}
                <option value={act}>{act}</option>
              {/each}
            </select>
          </div>
        </div>

        <div class="button-group">
          <button class="cancel-btn" on:click={handleCancel}>
            Cancel
          </button>
          <button 
            class="set-value-btn" 
            on:click={handleSubmit}
            disabled={!activity || hours < 0 || saving}
          >
            {saving ? 'Setting Values...' : 'Set Value'}
          </button>
        </div>
      </div>
    </div>
  </Modal>
{/if}

<style>
  .bulk-set-grid {
    width: 100%;
  }

  .form-content {
    display: grid;
    gap: 1.5rem;
  }

  .date-range {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }

  .date-group {
    display: grid;
    gap: 0.5rem;
  }

  h4 {
    margin: 0;
    color: #666;
    font-size: 1rem;
  }

  .input-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .value-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
  }

  .field {
    display: grid;
    gap: 0.25rem;
  }

  label {
    font-size: 0.9rem;
    color: #666;
  }

  input,
  select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    width: 100%;
  }

  .button-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
  }

  button {
    padding: 0.75rem;
    border: none;
    border-radius: 4px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .cancel-btn {
    background: #e0e0e0;
    color: #333;
  }

  .cancel-btn:hover {
    background: #d0d0d0;
  }

  .set-value-btn {
    background: #2196f3;
    color: white;
  }

  .set-value-btn:hover:not(:disabled) {
    background: #1976d2;
  }

  .set-value-btn:disabled {
    background: #cccccc;
    cursor: not-allowed;
  }
</style>
