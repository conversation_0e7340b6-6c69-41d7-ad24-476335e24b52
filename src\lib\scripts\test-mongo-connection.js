import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function testConnection() {
    try {
        await client.connect();
        const db = client.db('ServiceContracts');
        
        // Test Computers collection
        const computersCollection = db.collection('Computers');
        const computerCount = await computersCollection.countDocuments();
        console.log('Number of computers:', computerCount);
        
        // Test LabourTime collection
        const labourTimeCollection = db.collection('LabourTime');
        const labourTimeCount = await labourTimeCollection.countDocuments();
        console.log('Number of labour time records:', labourTimeCount);
        
        // Test a specific computer query
        const computer = await computersCollection.findOne();
        if (computer) {
            console.log('Sample computer:', {
                id: computer._id,
                category: computer.Category,
                computerCategory: computer.ComputerCathegory
            });
            
            // Test labour time query for this computer
            const labourTime = await labourTimeCollection.findOne({ 
                ComputerCathegory: computer.Category 
            });
            console.log('Labour time for this computer:', labourTime);
        }

    } catch (err) {
        console.error('Connection error:', err);
    } finally {
        await client.close();
    }
}

testConnection();
