<!-- QuotationDetailGrid.svelte -->
<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  
  // Props for grid configuration
  export let gap: string = '1.5rem';
  export let padding: string = '1.5rem';
  export let backgroundColor: string = '#f9f9f9';
  export let className: string = '';
</script>

<BaseGrid 
  columns="1fr" 
  rows="auto" 
  {gap} 
  {padding} 
  {backgroundColor}
  className="quotation-detail-grid {className}"
>
  <slot />
</BaseGrid>

<style>
  :global(.quotation-detail-grid) {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
  }
  
  /* Section styles */
  :global(.section-header) {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: #f0f0f0;
    border-radius: 6px;
    margin-bottom: 1rem;
    font-weight: 600;
  }
  
  :global(.row-type-section) {
    margin-bottom: 2rem;
  }
  
  :global(.row-grid) {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
  }
  
  :global(.row-card) {
    background-color: white;
    border-radius: 6px;
    padding: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  :global(.field-group) {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
  }
  
  :global(.field) {
    margin-bottom: 0.5rem;
  }
  
  :global(.field-label) {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 0.25rem;
  }
  
  :global(.field-value) {
    font-weight: 500;
  }
  
  :global(.checkbox-field) {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  :global(.checkbox-icon) {
    width: 18px;
    height: 18px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    color: white;
  }
  
  :global(.checkbox-true) {
    background-color: #4caf50;
  }
  
  :global(.checkbox-false) {
    background-color: #f44336;
  }
  
  @media (max-width: 768px) {
    :global(.field-group) {
      grid-template-columns: 1fr;
    }
  }
</style>
