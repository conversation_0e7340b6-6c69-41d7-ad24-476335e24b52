import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/**
 * Serializes MongoDB documents by converting ObjectId and Date instances to string format
 * @param {Object} doc - Document to serialize
 * @returns {Object} Serialized document
 */
function serializeDocument(doc) {
    if (!doc) return {};
    
    const serialized = {};
    for (const [key, value] of Object.entries(doc)) {
        if (value instanceof ObjectId) {
            serialized[key] = value.toString();
        } else if (value instanceof Date) {
            serialized[key] = value.toISOString();
        } else if (Array.isArray(value)) {
            serialized[key] = value.map(item => 
                item instanceof ObjectId ? item.toString() : item
            );
        } else if (value && typeof value === 'object') {
            serialized[key] = serializeDocument(value);
        } else {
            serialized[key] = value;
        }
    }
    return serialized;
}

/** @type {import('./$types').PageServerLoad} */
export async function load({ params, url }) {
    try {
        const db = client.db('ServiceContracts');
        const computerId = params.id;
        
        // Get URL parameters with type safety
        const productValidityGroupParam = url.searchParams.get('productValidityGroup') || '';
        const categoryParam = url.searchParams.get('category') || '';
        const hoursAtContractStartParam = url.searchParams.get('hoursAtContractStart') || '';
        
        console.log('=== Loading Service Planning Data ===');
        console.log(`Computer ID: ${computerId}`);
        
        // Log URL parameters safely
        const urlParams = {
            productValidityGroup: productValidityGroupParam,
            category: categoryParam,
            hoursAtContractStart: hoursAtContractStartParam
        };
        console.log('URL Parameters:', urlParams);
        
        // Step 1: Get computer info with proper error handling
        const computer = await db.collection('CustomerComputers')
            .findOne({ _id: new ObjectId(computerId) });

        if (!computer) {
            throw new Error('Computer not found');
        }

        // --- Ensure workload holds all date events from CustomerComputers ---
        // Collect all date fields relevant for event visualization
        const eventFields = [
            { field: 'PurchaseDate', label: 'purchase' },
            { field: 'DeliveryDate', label: 'delivery' },
            { field: 'WarrantyEndDate', label: 'warranty' },
            { field: 'ServiceContractStart', label: 'contractStart' },
            { field: 'ServiceContractEnd', label: 'contractEnd' }
        ];

        // Load workload data for the computer
        let workloadData = await db.collection('Workload')
            .find({ computerId: new ObjectId(computerId) })
            .toArray();

        // For each event, ensure there is a workload entry for that year/month
        const updates = [];
        for (const ev of eventFields) {
            const dateStr = computer[ev.field];
            if (!dateStr) continue;
            const d = new Date(dateStr);
            if (isNaN(d)) continue;
            const year = d.getFullYear();
            const month = d.getMonth() + 1;
            // Check if workload already has this entry
            if (!workloadData.some(w => w.year === year && w.month === month)) {
                // Insert a minimal workload record for this event
                updates.push({
                    computerId: new ObjectId(computerId),
                    year,
                    month,
                    hours: 0,
                    activity: ev.label.charAt(0).toUpperCase() + ev.label.slice(1),
                    hasFixed: false,
                    isIdle: true,
                    createdAt: new Date(),
                    updatedAt: new Date()
                });
            }
        }
        if (updates.length > 0) {
            await db.collection('Workload').insertMany(updates);
            // Reload workload data
            workloadData = await db.collection('Workload')
                .find({ computerId: new ObjectId(computerId) })
                .toArray();
        }
        
        console.log(`Found ${workloadData.length} workload records in database`);
        
        // If no workload data exists, don't generate sample data - only use what's in the database
        if (workloadData.length === 0) {
            console.log('No workload data found in database');
            workloadData = [];
        }
        
        // Step 2: Get ActiveFaultCodes for this computer
        const activeFaultCodes = await db.collection('ActiveFaultCodes')
            .find({ computerId: new ObjectId(computerId) })
            .sort({ createdAt: -1 })
            .toArray();
            
        // Add StartDateTime and StopDatetime if missing
        for (const code of activeFaultCodes) {
          if (!('StartDateTime' in code)) code.StartDateTime = null;
          if (!('StopDatetime' in code)) code.StopDatetime = null;
        }
        
        // Log workload data info
        console.log(`Found ${workloadData.length} workload records`);
        console.log(`Found ${activeFaultCodes.length} active fault codes`);
        
        // Serialize the data for the client
        const serializedWorkload = workloadData.map(item => serializeDocument(item));
        
        // Step 3: Load actual computer hours related to this computer
        const actualComputerHours = await db.collection('ActualComputerHours')
            .find({ computerId: new ObjectId(computerId) })
            .sort({ ReportDate: -1, ReportTime: -1 })
            .toArray();
            
        console.log(`Found ${actualComputerHours.length} actual computer hours records`);

        // Get service code types where ProductValidityGroup matches the computer's ProductDesignation value
        let serviceCodeTypes = [];
        
        // Get ProductDesignation from computer's data
        const productDesignation = computer.productDesignation || 
                                  computer.ProductDesignation || 
                                  '';
        
        console.log("Using ProductDesignation value:", productDesignation);
        
        if (productDesignation) {
            // Filter ServiceCodeAndActionType by ProductValidityGroup that matches the computer's ProductDesignation
            serviceCodeTypes = await db.collection('ServiceCodeAndActionType')
                .find({ ProductValidityGroup: productDesignation })
                .toArray();
            
            console.log(`Found ${serviceCodeTypes.length} service code types where ProductValidityGroup matches computer's ProductDesignation value: ${productDesignation}`);
        }

        return {
            computer: serializeDocument(computer),
            workload: workloadData.map(serializeDocument),
            activeFaultCodes: activeFaultCodes.map(serializeDocument),
            actualComputerHours: actualComputerHours.map(serializeDocument),
            serviceCodeTypes: serviceCodeTypes.map(serializeDocument),
        };
    } catch (error) {
        console.error('Error loading service planning data:', error);
        return {
            error: error instanceof Error ? error.message : 'Failed to load service planning data',
            success: false
        };
    }
}

/**
 * Generates sample workload data for a given year
 * @param {string} computerId - Computer ID
 * @param {number} year - Year to generate data for
 * @returns {Array<Object>} Sample workload data
 */
function generateSampleWorkloadData(computerId, year) {
    console.log(`Generating sample workload data for ${year}`);
    const sampleData = [];
    
    for (let month = 1; month <= 12; month++) {
        sampleData.push({
            _id: `sample_${year}_${month}`,
            computerId: computerId,
            year: year,
            month: month,
            hours: 0,
            activity: month === 1 ? 'Contract Start' : '',
            hasFixed: false,
            isIdle: true,
            createdAt: new Date(),
            updatedAt: new Date()
        });
    }
    
    return sampleData;
}

/** @type {import('./$types').Actions} */
export const actions = {
    updateWorkload: async ({ request, params }) => {
        try {
            console.log('Received updateWorkload request');
            const formData = await request.formData();
            const dataStr = formData.get('data');
            
            if (!dataStr || typeof dataStr !== 'string') {
                console.error('Invalid data format:', dataStr);
                throw new Error('Invalid workload data format');
            }
            
            const workloadData = JSON.parse(dataStr);
            console.log('Parsed workload data:', workloadData);
            
            // Validate computerId
            if (!workloadData.computerId || typeof workloadData.computerId !== 'string') {
                console.error('Invalid computerId:', workloadData.computerId);
                throw new Error('Invalid or missing computerId');
            }

            // Initialize database connection
            const db = client.db('ServiceContracts');
            
            // Convert computerId to ObjectId
            const computerId = new ObjectId(workloadData.computerId);
            
            // Handle new record creation (no _id provided)
            if (!workloadData._id) {
                console.log('Creating new workload record');
                
                // Validate required fields
                if (!workloadData.year || !workloadData.month) {
                    console.error('Missing required fields:', workloadData);
                    throw new Error('Missing required fields for new workload record');
                }
                
                // Check if a record already exists for this computer, year, and month
                const existingRecord = await db.collection('Workload').findOne({
                    computerId,
                    year: Number(workloadData.year),
                    month: Number(workloadData.month)
                });
                
                if (existingRecord) {
                    console.log(`Found existing record for year ${workloadData.year}, month ${workloadData.month}`);
                    
                    // Update the existing record instead of creating a new one
                    const updateResult = await db.collection('Workload').updateOne(
                        { _id: existingRecord._id },
                        { 
                            $set: {
                                hours: Number(workloadData.hours) || 0,
                                activity: workloadData.activity || '',
                                hasFixed: !!workloadData.hasFixed,
                                isIdle: false,
                                updatedAt: new Date()
                            }
                        }
                    );
                    
                    console.log('Updated existing record:', updateResult);
                    return { 
                        success: true, 
                        action: 'updated_existing', 
                        id: existingRecord._id.toString(),
                        modifiedCount: updateResult.modifiedCount 
                    };
                }
                
                // Create new record
                const newRecord = {
                    computerId,
                    year: Number(workloadData.year) || 0,
                    month: Number(workloadData.month) || 0,
                    hours: Number(workloadData.hours) || 0,
                    activity: workloadData.activity || '',
                    hasFixed: !!workloadData.hasFixed,
                    isIdle: false,
                    createdAt: new Date(),
                    updatedAt: new Date()
                };
                
                // Insert the new record
                const result = await db.collection('Workload').insertOne(newRecord);
                console.log('Created new record:', result);
                
                return { success: true, action: 'created', id: result.insertedId.toString() };
            }
            
            // Handle existing record update
            console.log(`Updating workload record: ${workloadData._id}`);
            
            // Ensure we're using a proper ObjectId for filtering
            const isValidObjectId = workloadData._id && 
                typeof workloadData._id === 'string' && 
                workloadData._id.match(/^[0-9a-fA-F]{24}$/);
                
            if (!isValidObjectId) {
                console.error('Invalid record ID:', workloadData._id);
                throw new Error('Invalid record ID format');
            }
            
            // Create a specific filter using the ObjectId
            const filter = { _id: new ObjectId(workloadData._id) };
            
            // Update existing record with specific fields
            const updateResult = await db.collection('Workload').updateOne(
                filter,
                { 
                    $set: {
                        activity: workloadData.activity || '',
                        hours: Number(workloadData.hours) || 0,
                        hasFixed: !!workloadData.hasFixed,
                        isIdle: false,
                        updatedAt: new Date()
                    }
                }
            );

            if (updateResult.matchedCount === 0) {
                console.error('No matching record found:', filter);
                throw new Error('No matching record found with the provided ID');
            }

            console.log('Update result:', updateResult);
            return { 
                success: true, 
                action: 'updated', 
                modifiedCount: updateResult.modifiedCount 
            };
        } catch (error) {
            console.error('Error in updateWorkload:', error);
            return { 
                success: false, 
                error: error instanceof Error ? error.message : 'Failed to update workload data'
            };
        }
    },
    addHoursToMonths: async ({ request, params }) => {
        try {
            const db = client.db('ServiceContracts');
            const computerId = params.id;
            const formData = await request.formData();
            const year = Number(formData.get('year'));
            const endYear = Number(formData.get('endYear'));
            const startMonth = Number(formData.get('startMonth'));
            const endMonth = Number(formData.get('endMonth'));
            const hours = Number(formData.get('hours'));
            console.log('addHoursToMonths params:', { computerId, year, endYear, startMonth, endMonth, hours });

            if (!computerId || isNaN(year) || isNaN(endYear) || isNaN(startMonth) || isNaN(endMonth) || isNaN(hours)) {
                const errMsg = `Invalid input: computerId=${computerId}, year=${year}, endYear=${endYear}, startMonth=${startMonth}, endMonth=${endMonth}, hours=${hours}`;
                console.error(errMsg);
                return { success: false, error: errMsg };
            }

            let y = year;
            let m = startMonth;
            while (y < endYear || (y === endYear && m <= endMonth)) {
                const filter = { computerId: new ObjectId(computerId), year: y, month: m };
                const update = {
                    $set: {
                        hours,
                        updatedAt: new Date()
                    },
                    $setOnInsert: {
                        activity: '',
                        hasFixed: false,
                        isIdle: false,
                        createdAt: new Date()
                    }
                };
                await db.collection('Workload').updateOne(filter, update, { upsert: true });
                m++;
                if (m > 12) {
                    m = 1;
                    y++;
                }
            }
            return { success: true };
        } catch (error) {
            console.error('addHoursToMonths error:', error);
            return { success: false, error: error.message || 'Unknown error' };
        }
    },
    addHoursBatch: async ({ request, params }) => {
        try {
            const db = client.db('ServiceContracts');
            const computerId = params.id;
            const formData = await request.formData();
            const startYear = Number(formData.get('startYear'));
            const startMonth = Number(formData.get('startMonth'));
            const endYear = Number(formData.get('endYear'));
            const endMonth = Number(formData.get('endMonth'));
            const hours = Number(formData.get('hours'));

            if (!computerId || isNaN(startYear) || isNaN(endYear) || isNaN(startMonth) || isNaN(endMonth) || isNaN(hours)) {
                return { success: false, error: 'Invalid input' };
            }

            let y = startYear;
            let m = startMonth;
            while (y < endYear || (y === endYear && m <= endMonth)) {
                const filter = { computerId: new ObjectId(computerId), year: y, month: m };
                const existing = await db.collection('Workload').findOne(filter);
                const now = new Date();
                if (existing) {
                    await db.collection('Workload').updateOne(filter, {
                        $set: {
                            hours,
                            hasFixed: false,
                            updatedAt: now
                        }
                    });
                } else {
                    await db.collection('Workload').insertOne({
                        computerId: new ObjectId(computerId),
                        year: y,
                        month: m,
                        hours,
                        activity: '',
                        hasFixed: false,
                        isIdle: false,
                        createdAt: now,
                        updatedAt: now
                    });
                }
                m++;
                if (m > 12) { m = 1; y++; }
            }
            return { success: true };
        } catch (error) {
            return { success: false, error: error.message || 'Unknown error' };
        }
    },
};
