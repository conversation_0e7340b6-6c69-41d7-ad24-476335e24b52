import { json } from '@sveltejs/kit';
import { getCollection, toClientDocument } from '$lib/db/mongo';

/**
 * GET handler for fetching collection data with filtering capability
 * @type {import('./$types').RequestHandler}
 */
export async function GET({ url }) {
  try {
    // Get parameters from URL
    const collectionName = url.searchParams.get('collection');
    const filterField = url.searchParams.get('filterlist');
    const filterValue = url.searchParams.get('filterValue') || '';
    
    if (!collectionName) {
      return json({ error: 'Collection parameter is required' }, { status: 400 });
    }
    
    // For security, only allow certain collections
    const allowedCollections = ['Customers', 'CustomerComputers', 'Sites', 'Regions'];
    
    // Convert to PascalCase for MongoDB (as per project convention)
    const pascalCaseCollection = collectionName.charAt(0).toUpperCase() + collectionName.slice(1);
    
    if (!allowedCollections.includes(pascalCaseCollection)) {
      return json({ error: 'Invalid collection' }, { status: 400 });
    }
    
    // Get collection from MongoDB
    const collection = await getCollection(pascalCaseCollection);
    
    let query = {};
    let pipeline = [];
    
    // For Customers, build a more complex query with computer counts
    if (pascalCaseCollection === 'Customers') {
      pipeline = [
        {
          $lookup: {
            from: 'CustomerComputers',
            localField: '_id',
            foreignField: 'customerId',
            as: 'computers'
          }
        },
        {
          $addFields: {
            computerCount: { $size: '$computers' }
          }
        }
      ];
      
      // Add filter if provided
      if (filterField && filterValue) {
        // Add a $match stage at the beginning of the pipeline
        pipeline.unshift({
          $match: {
            [filterField]: { $regex: filterValue, $options: 'i' }
          }
        });
      }
      
      const items = await collection.aggregate(pipeline).toArray();
      // Convert ObjectIds to strings for client-side use
      return json(items.map(item => toClientDocument(item)));
    }
    
    // For other collections, use a simpler approach
    if (filterField && filterValue) {
      query = {
        [filterField]: { $regex: filterValue, $options: 'i' }
      };
    }
    
    const items = await collection.find(query).toArray();
    // Convert ObjectIds to strings for client-side use
    return json(items.map(item => toClientDocument(item)));
    
  } catch (err) {
    console.error('Error loading collection data:', err);
    return json({ error: err instanceof Error ? err.message : 'Unknown error' }, { status: 500 });
  }
}
