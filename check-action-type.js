import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function checkActionTypeField() {
    const client = new MongoClient(uri);
    
    try {
        await client.connect();
        console.log('Connected to MongoDB');
        
        const db = client.db(dbName);
        const collection = db.collection('PartNumbersServiceCodeAction');
        
        // Get a sample document to see its structure
        const sample = await collection.findOne({});
        console.log('\nSample document structure from PartNumbersServiceCodeAction:');
        if (sample) {
            console.log('Fields in document:');
            Object.keys(sample).forEach(key => {
                console.log(`- ${key}: ${typeof sample[key]} = ${JSON.stringify(sample[key])}`);
            });
        }

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await client.close();
        console.log('\nConnection closed');
    }
}

checkActionTypeField().catch(console.error);
