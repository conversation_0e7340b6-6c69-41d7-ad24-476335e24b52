import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function updateCustomerComputersProductInfo() {
    const client = new MongoClient(uri);
    
    try {
        await client.connect();
        console.log('Connected to MongoDB');
        
        const db = client.db(dbName);
        const customerComputersCollection = db.collection('CustomerComputers');
        const productValidityGroupCollection = db.collection('ProductValidityGroupPartNumber');

        // Get all computers
        const computers = await customerComputersCollection.find({}).toArray();
        console.log(`Found ${computers.length} computers to update`);

        // Update each computer
        for (const computer of computers) {
            // Default values from the image
            const defaultProductDesignation = 'TAD1381-85VE';
            
            // Find matching product in ProductValidityGroupPartNumber collection
            const productInfo = await productValidityGroupCollection.findOne({
                ProductDesignation: computer.productDesignation || defaultProductDesignation
            });

            const updates = {
                ProductDesignation: computer.productDesignation || defaultProductDesignation,
                ProductPartNumber: productInfo?.ProductPartNumber || null
            };

            // Update the computer document
            const result = await customerComputersCollection.updateOne(
                { _id: computer._id },
                { $set: updates }
            );

            console.log(`Updated computer ${computer._id}:`, updates);
        }

        console.log('Finished updating computers');
        
        // Verify updates
        const sampleComputer = await customerComputersCollection.findOne({});
        console.log('Sample computer after update:', sampleComputer);
        
    } catch (error) {
        console.error('Error updating computers:', error);
    } finally {
        await client.close();
        console.log('Disconnected from MongoDB');
    }
}

// Run the update
updateCustomerComputersProductInfo().catch(console.error);
