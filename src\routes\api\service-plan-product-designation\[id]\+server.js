import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

/** @type {import('./$types').RequestHandler} */
export async function DELETE({ params }) {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db(dbName);
    
    // Delete the item
    const result = await db.collection('ServicePlanProductDesignation').deleteOne({
      _id: new ObjectId(params.id)
    });
    
    if (result.deletedCount === 0) {
      return json({ error: 'Service plan item not found' }, { status: 404 });
    }
    
    return json({
      success: true,
      message: 'Service plan item deleted successfully'
    });
    
  } catch (err) {
    console.error('Error deleting service plan item:', err);
    return json({ error: 'Failed to delete service plan item' }, { status: 500 });
  } finally {
    await client.close();
  }
}
