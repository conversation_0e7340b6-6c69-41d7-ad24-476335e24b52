import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function removeYearMonthFields() {
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db('ServiceContracts');
    const collection = db.collection('ActualComputerHours');
    
    // Get all records
    const records = await collection.find({}).toArray();
    console.log(`Found ${records.length} records to update`);
    
    let updatedCount = 0;
    
    // Update each record to remove the year and month fields
    for (const record of records) {
      try {
        const result = await collection.updateOne(
          { _id: record._id },
          { $unset: { year: "", month: "" } }
        );
        
        if (result.modifiedCount > 0) {
          updatedCount++;
        }
      } catch (err) {
        console.error(`Error updating record ${record._id}:`, err);
      }
    }
    
    console.log(`Updated ${updatedCount} records - removed year and month fields`);
    
  } catch (err) {
    console.error('Error:', err);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

removeYearMonthFields();
