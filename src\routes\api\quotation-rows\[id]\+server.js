import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/**
 * @param {import('@sveltejs/kit').RequestEvent} event
 */
export async function GET(event) {
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('QuotationRows');
    
    const row = await collection.findOne({ 
      _id: new ObjectId(event.params.id)
    });
    
    if (!row) {
      return new Response(JSON.stringify({ error: 'Row not found' }), {
        status: 404
      });
    }
    
    return json(row);
  } catch (error) {
    console.error('Error fetching quotation row:', error);
    return new Response(JSON.stringify({ error: 'Failed to fetch quotation row' }), {
      status: 500
    });
  } finally {
    await client.close();
  }
}

/**
 * @param {import('@sveltejs/kit').RequestEvent} event
 */
export async function PUT(event) {
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('QuotationRows');
    
    const rowData = await event.request.json();
    const rowId = event.params.id;

    // Get the existing document first to preserve IDs
    const existingRow = await collection.findOne({ _id: new ObjectId(rowId) });
    if (!existingRow) {
      return new Response(JSON.stringify({ error: 'Row not found' }), {
        status: 404
      });
    }

    // Preserve all ID fields from the existing document
    const preservedFields = {
      _id: existingRow._id,
      QuoteId: existingRow.QuoteId,
      QuotationId: existingRow.QuotationId
    };

    // Only update non-ID fields that were changed
    const updateFields = {
      RowType: rowData.RowType,
      RowOrder: parseInt(rowData.RowOrder) || 1,
      ServiceActivity: rowData.ServiceActivity,
      Cost: parseFloat(rowData.Cost) || 0,
      OemImporter: parseFloat(rowData.OemImporter) || 0,
      FleetOwner: parseFloat(rowData.FleetOwner) || 0,
      SSP: parseFloat(rowData.SSP) || 0,
      CustomerSpecific: !!rowData.CustomerSpecific,
      Required: !!rowData.Required,
      IncludeInOffer: rowData.IncludeInOffer !== false,
      UpdatedAt: new Date()
    };

    // Update only the fields that have changed
    const result = await collection.updateOne(
      { _id: new ObjectId(rowId) },
      { 
        $set: {
          ...updateFields
        }
      }
    );
    
    if (result.matchedCount === 0) {
      return new Response(JSON.stringify({ error: 'Failed to update row' }), {
        status: 500
      });
    }
    
    return json({ 
      success: true,
      message: 'Row updated successfully'
    });
  } catch (error) {
    console.error('Error updating quotation row:', error);
    return new Response(JSON.stringify({ error: 'Failed to update quotation row' }), {
      status: 500
    });
  } finally {
    await client.close();
  }
}
