<script lang="ts">
  import { goto } from '$app/navigation';
  
  export let data: any = null;
  export let title: string = 'JSON Data';
  
  function viewJson() {
    try {
      // Store the data in sessionStorage
      sessionStorage.setItem('viewJsonData', JSON.stringify(data));
      sessionStorage.setItem('viewJsonTitle', title);
      
      // Navigate to the JSON view page
      goto('/services/json-view');
    } catch (err) {
      console.error('Error storing JSON data:', err);
    }
  }
</script>

<button class="view-json-button" on:click={viewJson}>
  Show {title}
</button>

<style>
  .view-json-button {
    background: #4a5568;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background 0.2s;
    margin: 0.5rem 0;
  }
  
  .view-json-button:hover {
    background: #2d3748;
  }
</style>
