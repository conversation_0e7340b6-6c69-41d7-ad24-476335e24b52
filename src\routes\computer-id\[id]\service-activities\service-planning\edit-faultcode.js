import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

export async function POST({ request }) {
  try {
    const body = await request.json();
    if (!body._id) {
      return new Response(JSON.stringify({ success: false, error: 'Missing _id' }), { status: 400 });
    }
    const db = client.db('ServiceContracts');
    const { _id, ...fields } = body;
    // Remove _id from update fields
    // Convert string id to ObjectId
    const objectId = typeof _id === 'string' ? new ObjectId(_id) : _id;
    // Ensure no undefined values
    Object.keys(fields).forEach(key => {
      if (fields[key] === undefined) delete fields[key];
    });
    // Update document
    const result = await db.collection('ActiveFaultCodes').updateOne(
      { _id: objectId },
      { $set: fields }
    );
    if (result.modifiedCount === 1) {
      return new Response(JSON.stringify({ success: true }), { status: 200 });
    } else {
      return new Response(JSON.stringify({ success: false, error: 'Not found or not modified' }), { status: 404 });
    }
  } catch (e) {
    return new Response(JSON.stringify({ success: false, error: e.message || e }), { status: 500 });
  }
}
