<script lang="ts">
    import { enhance } from '$app/forms';

    interface ServiceCodeAction {
        _id: string;
        ProductValidityGroup: string;
        ActivityPurpose: string;
        ServiceActivityLabel: string;
        ServiceCode: string;
        ActionType: string;
        PartNumber: number;
        UnitOfMeasure: string;
        Quantity: number;
        InternalNoOfHours: number;
        InternalNoOfMonths: number | null;
    }

    interface ActionFormData {
        productDesignation: string;
        productPartNumber: string;
    }

    interface ActionResponse {
        status: number;
        data: {
            items: ServiceCodeAction[];
            formData: ActionFormData;
            error?: string;
        };
    }

    export let data: { items: ServiceCodeAction[], formData?: ActionFormData };
    export let form: ActionResponse | null;

    // Initialize with form response data if available, otherwise use data or empty string
    $: productDesignation = form?.data?.formData?.productDesignation || data.formData?.productDesignation || '';
    $: productPartNumber = form?.data?.formData?.productPartNumber || data.formData?.productPartNumber || '';

    // Debug logs
    $: console.log('Data received:', data);
    $: console.log('Form data:', form);
    $: console.log('Items length:', data.items?.length);
    $: console.log('Current form values:', { productDesignation, productPartNumber });

    // Search inputs for each column
    let filters = {
        validityGroup: '',
        activityPurpose: '',
        activityLabel: '',
        serviceCode: '',
        actionType: '',
        partNumber: '',
        unit: '',
        quantity: '',
        hours: '',
        months: ''
    };

    // Get items from form response if available, otherwise from data
    $: displayItems = form?.data?.items || data.items;

    // Filter the items based on all filters
    $: filteredItems = displayItems?.filter(item => {
        const validityGroupMatch = item.ProductValidityGroup.toLowerCase().includes(filters.validityGroup.toLowerCase());
        const activityPurposeMatch = item.ActivityPurpose.toLowerCase().includes(filters.activityPurpose.toLowerCase());
        const activityLabelMatch = item.ServiceActivityLabel.toLowerCase().includes(filters.activityLabel.toLowerCase());
        const serviceCodeMatch = item.ServiceCode.toLowerCase().includes(filters.serviceCode.toLowerCase());
        const actionTypeMatch = item.ActionType.toLowerCase().includes(filters.actionType.toLowerCase());
        const partNumberMatch = item.PartNumber.toString().includes(filters.partNumber);
        const unitMatch = item.UnitOfMeasure.toLowerCase().includes(filters.unit.toLowerCase());
        const quantityMatch = item.Quantity.toString().includes(filters.quantity);
        const hoursMatch = item.InternalNoOfHours.toString().includes(filters.hours);
        const monthsMatch = item.InternalNoOfMonths?.toString().includes(filters.months) ?? false;

        return validityGroupMatch && activityPurposeMatch && activityLabelMatch && 
               serviceCodeMatch && actionTypeMatch && partNumberMatch && 
               unitMatch && quantityMatch && hoursMatch && monthsMatch;
    }) || [];

    // Handle form submission
    function handleSubmit(event: SubmitEvent) {
        return async ({ result, update }) => {
            await update();
        };
    }
</script>

<div class="p-4">
    <h1 class="text-2xl font-bold mb-4">Services</h1>
    
    <form method="POST" use:enhance>
        <div class="flex gap-4 mb-4">
            <div>
                <label for="productDesignation" class="block text-sm text-gray-600 mb-1">Product Designation</label>
                <input
                    type="text"
                    id="productDesignation"
                    name="productDesignation"
                    bind:value={productDesignation}
                    class="border p-2 rounded"
                >
            </div>
            <div>
                <label for="productPartNumber" class="block text-sm text-gray-600 mb-1">Product Part Number</label>
                <input
                    type="text"
                    id="productPartNumber"
                    name="productPartNumber"
                    bind:value={productPartNumber}
                    class="border p-2 rounded"
                >
            </div>
            <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">Search</button>
        </div>
    </form>

    <div class="text-sm text-gray-600 mb-4">
        Example: Try searching with Product Designation "D1-13" and Part Number "868975"
    </div>

    {#if form?.data?.error}
        <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
            {form.data.error}
        </div>
    {:else if filteredItems?.length > 0}
        <div class="overflow-x-auto">
            <table class="min-w-full border-collapse">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="border px-2 py-1">
                            <input
                                type="text"
                                bind:value={filters.validityGroup}
                                placeholder="Product Validity Group"
                                class="w-full p-1 text-sm border rounded"
                            />
                        </th>
                        <th class="border px-2 py-1">
                            <input
                                type="text"
                                bind:value={filters.activityPurpose}
                                placeholder="Activity Purpose"
                                class="w-full p-1 text-sm border rounded"
                            />
                        </th>
                        <th class="border px-2 py-1">
                            <input
                                type="text"
                                bind:value={filters.activityLabel}
                                placeholder="Activity Label"
                                class="w-full p-1 text-sm border rounded"
                            />
                        </th>
                        <th class="border px-2 py-1">
                            <input
                                type="text"
                                bind:value={filters.serviceCode}
                                placeholder="Service Code"
                                class="w-full p-1 text-sm border rounded"
                            />
                        </th>
                        <th class="border px-2 py-1">
                            <input
                                type="text"
                                bind:value={filters.actionType}
                                placeholder="Action Type"
                                class="w-full p-1 text-sm border rounded"
                            />
                        </th>
                        <th class="border px-2 py-1">
                            <input
                                type="text"
                                bind:value={filters.partNumber}
                                placeholder="Part Number"
                                class="w-full p-1 text-sm border rounded"
                            />
                        </th>
                        <th class="border px-2 py-1">
                            <input
                                type="text"
                                bind:value={filters.unit}
                                placeholder="Unit of Measure"
                                class="w-full p-1 text-sm border rounded"
                            />
                        </th>
                        <th class="border px-2 py-1">
                            <input
                                type="text"
                                bind:value={filters.quantity}
                                placeholder="Quantity"
                                class="w-full p-1 text-sm border rounded"
                            />
                        </th>
                        <th class="border px-2 py-1">
                            <input
                                type="text"
                                bind:value={filters.hours}
                                placeholder="Hours"
                                class="w-full p-1 text-sm border rounded"
                            />
                        </th>
                        <th class="border px-2 py-1">
                            <input
                                type="text"
                                bind:value={filters.months}
                                placeholder="Months"
                                class="w-full p-1 text-sm border rounded"
                            />
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {#each filteredItems as item}
                        <tr class="hover:bg-gray-50">
                            <td class="border px-2 py-1">{item.ProductValidityGroup}</td>
                            <td class="border px-2 py-1">{item.ActivityPurpose}</td>
                            <td class="border px-2 py-1">{item.ServiceActivityLabel}</td>
                            <td class="border px-2 py-1">{item.ServiceCode}</td>
                            <td class="border px-2 py-1">{item.ActionType}</td>
                            <td class="border px-2 py-1">{item.PartNumber}</td>
                            <td class="border px-2 py-1">{item.UnitOfMeasure}</td>
                            <td class="border px-2 py-1">{item.Quantity}</td>
                            <td class="border px-2 py-1">{item.InternalNoOfHours}</td>
                            <td class="border px-2 py-1">{item.InternalNoOfMonths ?? '-'}</td>
                        </tr>
                    {/each}
                </tbody>
            </table>
        </div>
    {:else}
        <div class="mb-6 p-4 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded">
            No hit!
        </div>
    {/if}
</div>
