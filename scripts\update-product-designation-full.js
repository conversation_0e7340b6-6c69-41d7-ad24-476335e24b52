// Script to update ProductValidityGroup collection to add ProductDesignationFull field
// This script adds a new field ProductDesignationFull with the same value as ProductDesignation

import { MongoClient } from 'mongodb';

// Connection URL and Database name
const url = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';
const collectionName = 'ProductValidityGroup';

async function updateProductDesignationFull() {
  let client;

  try {
    // Connect to MongoDB
    client = new MongoClient(url);
    await client.connect();
    console.log('Connected to MongoDB server');

    // Get database and collection
    const db = client.db(dbName);
    const collection = db.collection(collectionName);

    // Count documents before update
    const totalDocuments = await collection.countDocuments();
    console.log(`Total documents in ${collectionName}: ${totalDocuments}`);

    // Update all documents to add ProductDesignationFull field
    const updateResult = await collection.updateMany(
      {}, // Match all documents
      [
        { 
          $set: { 
            // Set ProductDesignationFull to ProductDesignation value, or null if it doesn't exist
            ProductDesignationFull: { $ifNull: ["$ProductDesignation", null] }
          } 
        }
      ]
    );

    console.log(`Updated ${updateResult.modifiedCount} documents with ProductDesignationFull field`);

    // Verify update by checking a few documents
    const sampleDocs = await collection.find({}).limit(5).toArray();
    console.log('Sample documents after update:');
    sampleDocs.forEach(doc => {
      console.log(`ID: ${doc._id}, ProductDesignation: ${doc.ProductDesignation}, ProductDesignationFull: ${doc.ProductDesignationFull}`);
    });

  } catch (err) {
    console.error('Error updating documents:', err);
  } finally {
    // Close the connection
    if (client) {
      await client.close();
      console.log('MongoDB connection closed');
    }
  }
}

// Run the update function
updateProductDesignationFull().catch(console.error);
