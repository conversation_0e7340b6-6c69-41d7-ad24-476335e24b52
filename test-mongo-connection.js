import { MongoClient } from 'mongodb';

async function checkDatabase() {
  try {
    // Connection URI and database name from mongo.js
    const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017';
    const dbName = process.env.MONGODB_DB_NAME || 'ssund';
    
    console.log(`Checking MongoDB connection at: ${uri}`);
    console.log(`Using database: ${dbName}`);
    
    // Create a new MongoClient
    const client = new MongoClient(uri);
    
    // Connect to the MongoDB server
    await client.connect();
    console.log('Successfully connected to MongoDB server!');
    
    // Get reference to the database
    const db = client.db(dbName);
    
    // List all collections in the database
    const collections = await db.listCollections().toArray();
    console.log(`\nAvailable collections in ${dbName} database:`);
    if (collections.length === 0) {
      console.log('  No collections found.');
    } else {
      collections.forEach(coll => console.log(`  - ${coll.name}`));
    }
    
    // Check for specific collections we expect in our application
    const expectedCollections = ['Customers', 'CustomerComputers', 'Sites'];
    console.log('\nChecking for expected collections:');
    for (const collName of expectedCollections) {
      const exists = collections.some(coll => coll.name === collName);
      console.log(`  - ${collName}: ${exists ? 'Found' : 'Not found'}`);
      
      // If collection exists, count documents
      if (exists) {
        const count = await db.collection(collName).countDocuments();
        console.log(`    Document count: ${count}`);
        
        // Sample a single document to check structure (without modifying anything)
        if (count > 0) {
          const sampleDoc = await db.collection(collName).findOne({});
          console.log(`    Sample document fields: ${Object.keys(sampleDoc).join(', ')}`);
          
          // Check if _id is ObjectId
          if (sampleDoc._id) {
            console.log(`    _id type: ${sampleDoc._id.constructor.name}`);
          }
          
          // Check for foreign key fields
          if (sampleDoc.customerId) {
            console.log(`    customerId type: ${sampleDoc.customerId.constructor.name}`);
          }
          
          if (sampleDoc.siteId) {
            console.log(`    siteId type: ${sampleDoc.siteId.constructor.name}`);
          }
        }
      }
    }
    
    // Close the connection
    await client.close();
    console.log('\nMongoDB connection closed');
  } catch (err) {
    console.error('Error checking database:', err);
  }
}

// Run the check
checkDatabase();
