// API endpoint for fetching service code types by demarcation
import { json } from '@sveltejs/kit';
import { client } from '$lib/db';

const dbName = 'ServiceContracts';

// Get service code types by demarcation
export async function GET({ url }) {
  try {
    const db = client.db(dbName);
    
    // Get query parameters
    const productDemarcation = url.searchParams.get('productDemarcation');
    
    // Validate required parameters
    if (!productDemarcation) {
      return json({ error: 'Missing required parameter: productDemarcation' }, { status: 400 });
    }
    
    // Build query
    const query = { ProductDemarcation: productDemarcation };
    
    // Execute query
    const serviceCodeTypes = await db.collection('ServiceCodeAndActionType').find(query).toArray();
    
    // Return results
    return json(serviceCodeTypes);
  } catch (error) {
    console.error('Error fetching service code types by demarcation:', error);
    return json({ error: String(error) }, { status: 500 });
  }
}
