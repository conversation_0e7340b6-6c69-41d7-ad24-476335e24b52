// Script to fix CalculatedHoursPerMonth with the correct calculation logic:
// 1. Count months since last entry
// 2. Divide hours by number of months
// 3. Cap at 744

import { MongoClient, ObjectId } from 'mongodb';

async function fixCalculatedHoursField() {
  // Connection URI
  const uri = 'mongodb://localhost:27017';
  const client = new MongoClient(uri);

  const MAX_HOURS_PER_MONTH = 744; // Maximum hours in a month

  try {
    // Connect to MongoDB
    await client.connect();
    console.log('Connected to MongoDB');

    // Access the ServiceContracts database and ActualComputerHours collection
    const db = client.db('ServiceContracts');
    const collection = db.collection('ActualComputerHours');

    // Find all documents
    const docs = await collection.find({}).toArray();
    console.log(`Found ${docs.length} documents in ActualComputerHours collection`);

    // Group documents by computerId for correct calculation
    const computerGroups = {};
    
    // Group documents by computerId
    docs.forEach(doc => {
      const computerIdStr = doc.computerId.toString();
      if (!computerGroups[computerIdStr]) {
        computerGroups[computerIdStr] = [];
      }
      computerGroups[computerIdStr].push(doc);
    });

    // Process each computer group
    for (const [computerIdStr, computerDocs] of Object.entries(computerGroups)) {
      console.log(`Processing ${computerDocs.length} documents for computer ${computerIdStr}`);
      
      // Sort documents by year and month
      computerDocs.sort((a, b) => {
        if (a.year !== b.year) return a.year - b.year;
        return a.month - b.month;
      });
      
      // Process each document in chronological order
      let previousDoc = null;
      for (const doc of computerDocs) {
        // Ensure hours is an integer
        const hours = parseInt(doc.hours || 0);
        
        // Calculate months since previous entry
        let calculatedHours;
        if (!previousDoc) {
          // First entry - just use hours
          calculatedHours = hours;
        } else {
          // Calculate months between entries
          const currentDate = new Date(doc.year, doc.month - 1, 1);
          const previousDate = new Date(previousDoc.year, previousDoc.month - 1, 1);
          
          // Calculate months difference
          const monthsDiff = (currentDate.getFullYear() - previousDate.getFullYear()) * 12 + 
                            (currentDate.getMonth() - previousDate.getMonth());
          
          // Divide hours by number of months (or use hours if monthsDiff is 0)
          calculatedHours = monthsDiff > 0 ? Math.floor(hours / monthsDiff) : hours;
        }
        
        // Cap at maximum hours per month
        calculatedHours = Math.min(calculatedHours, MAX_HOURS_PER_MONTH);
        
        // Update document with calculated value
        await collection.updateOne(
          { _id: doc._id },
          { $set: { 
              hours: hours,
              CalculatedHoursPerMonth: calculatedHours 
            }
          }
        );
        
        console.log(`Updated document ${doc._id}: set hours=${hours}, CalculatedHoursPerMonth=${calculatedHours}, date=${doc.month}/${doc.year}`);
        
        // Store current document as previous for next iteration
        previousDoc = doc;
      }
    }

    // Verify some updated documents
    const updatedSamples = await collection.find({}).limit(5).toArray();
    console.log('\nSample documents after update:');
    updatedSamples.forEach(doc => {
      console.log(
        `ID: ${doc._id}, ` +
        `Hours: ${doc.hours}, ` + 
        `CalculatedHoursPerMonth: ${doc.CalculatedHoursPerMonth} ` +
        `(Month: ${doc.month}/${doc.year})`
      );
    });

  } catch (error) {
    console.error('Error updating documents:', error);
  } finally {
    // Close the connection
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the function
fixCalculatedHoursField().catch(console.error);
