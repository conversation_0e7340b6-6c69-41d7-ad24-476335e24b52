<script>
  import { createEventDispatcher } from 'svelte';

  const dispatch = createEventDispatcher();

  // Props
  export let services = [];
  export let isPackageOffering = false;
  export let title = '';

  // Handle service toggle
  function toggleService(serviceId) {
    dispatch('toggleService', { id: serviceId });
  }

  // Handle service selection from dropdown
  function selectService(serviceId, serviceCode) {
    dispatch('selectService', { id: serviceId, serviceCode });
  }

  // Format currency display
  function formatCurrency(amount) {
    if (!amount || amount === 0) return '0';
    if (typeof amount === 'string' && (amount.includes('#') || amount.includes('REF') || amount.includes('VALUE') || amount.includes('N/A'))) {
      return amount;
    }
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(amount);
  }

  // Get cell background color based on status - matching the image colors
  function getCellColor(service, column) {
    if (column === 'includedInPackage') {
      return service.includedInPackage ? '#90EE90' : '#FFD700'; // Green if included, yellow if not
    }
    if (column === 'required') {
      return service.required ? '#90EE90' : '#FFD700'; // Green if required, yellow if not
    }
    if (column === 'includeInOffer') {
      return service.includeInOffer ? '#90EE90' : '#FFD700'; // Green if included, yellow if not
    }
    if (column === 'cost') {
      if (service.cost === 0) return '#90EE90'; // Green for zero cost
      if (typeof service.cost === 'string' && service.cost.includes('#')) return '#FFD700'; // Yellow for error values
      if (service.cost > 0) return '#87CEEB'; // Light blue for positive cost
      return '#FFD700'; // Yellow for other cases
    }
    return 'transparent';
  }

  // Get package level color for package offering
  function getPackageLevelColor(level) {
    switch(level?.toUpperCase()) {
      case 'A': return '#90EE90'; // Light green
      case 'B': return '#90EE90'; // Light green
      case 'C': return '#90EE90'; // Light green
      default: return '#f5f5f5';
    }
  }

  // Group services by package name
  function groupServicesByPackage(services) {
    const groups = {};
    services.forEach(service => {
      const packageName = service.packageName || 'Other';
      if (!groups[packageName]) {
        groups[packageName] = [];
      }
      groups[packageName].push(service);
    });
    return groups;
  }

  $: groupedServices = groupServicesByPackage(services);
</script>

<div class="service-offering-table">
  {#if title}
    <div class="table-title">{title}</div>
  {/if}

  {#if isPackageOffering}
    <!-- Package Offering Table -->
    <table class="package-table">
      <thead>
        <tr>
          <th>Level</th>
          <th></th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        {#each services as service}
          <tr style="background-color: {getPackageLevelColor(service.level)}">
            <td class="level-cell">{service.level}</td>
            <td class="package-cell">{service.name || 'N/A'}</td>
            <td class="status-cell">{service.status || 'N/A'}</td>
          </tr>
        {/each}
      </tbody>
    </table>
  {:else}
    <!-- Individual Service Table -->
    <table class="service-table">
      <thead>
        <tr>
          <th>Level</th>
          <th>Package</th>
          <th>Service ID</th>
          <th>Service</th>
          <th>Included in package cost</th>
          <th>Required</th>
          <th>Select Service (Dropdown)</th>
          <th>Include cost in quote</th>
          <th>Cost</th>
        </tr>
      </thead>
      <tbody>
        {#each Object.entries(groupedServices) as [packageName, packageServices]}
          {#each packageServices as service, index}
            <tr class="service-row">
              <td class="level-cell">{service.level}</td>
              <td class="package-cell">{service.packageName}</td>
              <td class="service-id-cell">{service.serviceId}</td>
              <td class="service-name-cell">{service.ServiceActivity}</td>
              <td class="included-cell" style="background-color: {getCellColor(service, 'includedInPackage')}">
                {service.includedInPackage ? 'Yes' : ''}
              </td>
              <td class="required-cell" style="background-color: {getCellColor(service, 'required')}">
                {service.required ? 'Yes' : ''}
              </td>
              <td class="dropdown-cell">
                <select on:change={(e) => selectService(service.id, e.target.value)}>
                  <option value="">Select Service</option>
                  <option value="parts-supply">Parts Supply on Request</option>
                  <option value="monitoring">Monitoring</option>
                  <option value="inspections">Inspections</option>
                  <option value="repair-parts">Repair Parts</option>
                  <option value="preventive">Preventive</option>
                  <option value="engine-health">Engine Health</option>
                  <option value="adjustments">Adjustments</option>
                  <option value="eats-mot">EATS MOT</option>
                  <option value="overhaul">Overhaul Service</option>
                  <option value="oil-sampling">Oil Sampling</option>
                  <option value="def-sampling">DEF Sampling Program</option>
                  <option value="technical-support">Technical Support</option>
                  <option value="full-service">Full Service & Repair</option>
                  <option value="replacement">Engine Replacement</option>
                </select>
              </td>
              <td class="include-offer-cell" style="background-color: {getCellColor(service, 'includeInOffer')}">
                {service.includeInOffer ? 'Yes' : ''}
              </td>
              <td class="cost-cell" style="background-color: {getCellColor(service, 'cost')}">
                {formatCurrency(service.cost)}
              </td>
            </tr>
          {/each}
        {/each}
      </tbody>
    </table>
  {/if}
</div>

<style>
  .service-offering-table {
    width: 100%;
    margin-bottom: 1rem;
    border: 2px solid #333;
  }

  .table-title {
    background-color: #f5f5f5;
    padding: 0.5rem;
    font-weight: bold;
    border-bottom: 1px solid #333;
  }

  .package-table,
  .service-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
  }

  .package-table th,
  .service-table th {
    background-color: #f5f5f5;
    padding: 0.5rem;
    text-align: center;
    border: 1px solid #333;
    font-weight: bold;
  }

  .package-table td,
  .service-table td {
    padding: 0.4rem;
    text-align: center;
    border: 1px solid #333;
    vertical-align: middle;
  }

  .level-cell {
    width: 60px;
    font-weight: bold;
  }

  .package-cell {
    min-width: 150px;
    text-align: left;
  }

  .service-id-cell {
    width: 80px;
  }

  .service-name-cell {
    min-width: 200px;
    text-align: left;
  }

  .included-cell,
  .required-cell,
  .include-offer-cell {
    width: 100px;
  }

  .dropdown-cell {
    min-width: 150px;
  }

  .dropdown-cell select {
    width: 100%;
    padding: 0.25rem;
    border: 1px solid #ccc;
    border-radius: 3px;
  }

  .cost-cell {
    width: 100px;
    text-align: right;
    font-weight: bold;
  }

  .service-row:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  /* Responsive design */
  @media (max-width: 1200px) {
    .dropdown-cell {
      display: none;
    }
  }

  @media (max-width: 768px) {
    .service-id-cell,
    .included-cell,
    .required-cell {
      display: none;
    }
  }
</style>
