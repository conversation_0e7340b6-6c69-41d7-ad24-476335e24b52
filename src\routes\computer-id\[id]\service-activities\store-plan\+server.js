import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

interface ServiceActivity {
    ServiceCode: string;
    ActionType: string;
    ActivityPurpose: string;
    ServiceActivityLabel: string;
    PartNumber: string;
    Quantity: number;
    UoM: string;
    Hrs: number;
    Months: number;
}

/** @type {import('./$types').RequestHandler} */
export async function POST({ params, request }) {
    try {
        await client.connect();
        const db = client.db('ServiceContracts');
        const activitiesCollection = db.collection('ServiceActivities');
        
        const { activities, startDate, endDate } = await request.json();
        const computerId = params.id;

        // Delete any existing planned activities
        await activitiesCollection.deleteMany({
            computerId: new ObjectId(computerId),
            isPlanned: true
        });

        // Calculate dates for each activity
        const start = new Date(startDate);
        const end = new Date(endDate);
        const contractDurationMonths = (end.getFullYear() - start.getFullYear()) * 12 + end.getMonth() - start.getMonth();

        const plannedActivities = activities.map((activity: ServiceActivity) => {
            const months = activity.Months || 12; // Default to yearly if not specified
            const numberOfOccurrences = Math.floor(contractDurationMonths / months);
            
            const instances = [];
            for (let i = 0; i < numberOfOccurrences; i++) {
                const plannedDate = new Date(start);
                plannedDate.setMonth(plannedDate.getMonth() + (i * months));
                
                instances.push({
                    ...activity,
                    _id: new ObjectId(),
                    computerId: new ObjectId(computerId),
                    plannedDate,
                    isPlanned: true,
                    status: 'Planned'
                });
            }
            return instances;
        }).flat();

        if (plannedActivities.length > 0) {
            await activitiesCollection.insertMany(plannedActivities);
        }

        return json({
            success: true,
            message: `Created ${plannedActivities.length} planned activities`,
            activities: plannedActivities
        });
    } catch (error: unknown) {
        console.error('Error:', error);
        return json({
            success: false,
            message: error instanceof Error ? error.message : 'An unknown error occurred'
        }, { status: 500 });
    } finally {
        await client.close();
    }
}
