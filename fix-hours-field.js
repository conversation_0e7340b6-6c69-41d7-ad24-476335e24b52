const { MongoClient } = require('mongodb');

async function updateHoursField() {
  const uri = 'mongodb://localhost:27017';
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db('ServiceContracts');
    const collection = db.collection('ActualComputerHours');
    
    // Find all documents
    const documents = await collection.find({}).toArray();
    console.log(`Found ${documents.length} documents in ActualComputerHours collection`);
    
    // Update each document to ensure hours is an integer
    let updateCount = 0;
    for (const doc of documents) {
      if (doc.hours !== undefined) {
        const hoursAsInt = parseInt(doc.hours);
        if (!isNaN(hoursAsInt)) {
          // Only update if needed
          if (typeof doc.hours !== 'number' || Math.floor(doc.hours) !== doc.hours) {
            await collection.updateOne(
              { _id: doc._id },
              { $set: { hours: hoursAsInt } }
            );
            updateCount++;
          }
        }
      }
    }
    
    console.log(`Updated hours field to integer in ${updateCount} documents`);
    
    // Verify a few documents
    const updatedDocs = await collection.find({}).limit(3).toArray();
    console.log('Sample documents after update:');
    updatedDocs.forEach(doc => {
      console.log(`ID: ${doc._id}, Hours: ${doc.hours} (type: ${typeof doc.hours})`);
    });
    
  } catch (err) {
    console.error('Error updating hours field:', err);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

updateHoursField().catch(console.error);
