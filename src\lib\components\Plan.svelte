<script>
    export let title = '';
    export let subtitle = '';
    export let hasHeader = true;
</script>

<div class="plan">
    {#if hasHeader}
        <div class="plan-header">
            <div class="flex items-center">
                <div class="w-2 h-6 bg-blue-600 mr-3 rounded"></div>
                <h2 class="text-xl font-bold text-blue-700">{title}</h2>
            </div>
            {#if subtitle}
                <p class="text-blue-600 text-sm mt-1">{subtitle}</p>
            {/if}
        </div>
    {/if}
    <div class="plan-content">
        <slot />
    </div>
</div>

<style>
    .plan {
        background-color: #f0f7ff;  /* Very light blue */
        padding: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .plan-header {
        border-bottom: 2px solid #3b82f6;
        padding-bottom: 0.5rem;
        margin-bottom: 1rem;
    }

    .plan-content {
        background-color: #f8fafc;  /* Almost white blue tint */
        border-radius: 0.375rem;
        padding: 1rem;
    }
</style>
