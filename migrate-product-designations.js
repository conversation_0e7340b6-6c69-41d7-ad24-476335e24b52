import { MongoClient } from 'mongodb';

// From memory: use localhost and ServiceContracts database
const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);
const dbName = 'ServiceContracts';

async function main() {
    try {
        console.log('Connecting to MongoDB...');
        await client.connect();
        console.log('Connected successfully to MongoDB');

        const db = client.db(dbName);

        // From memory: use PascalCase for collection names
        const productValidityGroupCollection = db.collection('ProductValidityGroup');
        const productDesignationCollection = db.collection('ProductDesignation');

        // Drop existing collection
        console.log('Dropping existing ProductDesignation collection...');
        try {
            await db.collection('ProductDesignation').drop();
            console.log('ProductDesignation collection dropped successfully');
        } catch (error) {
            if (error.codeName === 'NamespaceNotFound') {
                console.log('Collection did not exist, continuing...');
            } else {
                throw error;
            }
        }

        console.log('Creating indexes...');
        try {
            await productDesignationCollection.createIndexes([
                { key: { ProductDesignation: 1 }, unique: true }
            ]);
            console.log('Indexes created successfully');
        } catch (error) {
            console.error('Error creating indexes:', error);
            throw error;
        }

        console.log('Getting product designations from ProductValidityGroup...');
        const productValidityGroups = await productValidityGroupCollection
            .find({})
            .project({
                ProductDesignation: 1
            })
            .toArray();

        const designations = new Map();

        for (const group of productValidityGroups) {
            if (group.ProductDesignation) {
                designations.set(group.ProductDesignation, {
                    ProductDesignation: group.ProductDesignation,
                    CreatedAt: new Date(),
                    UpdatedAt: new Date()
                });
            }
        }

        // Insert all unique designations
        if (designations.size > 0) {
            console.log(`Inserting ${designations.size} product designations...`);
            try {
                const result = await productDesignationCollection.insertMany(
                    Array.from(designations.values())
                );
                console.log(`Successfully inserted ${result.insertedCount} product designations`);

                // Log some sample data
                console.log('\nSample product designations:');
                const samples = await productDesignationCollection
                    .find({})
                    .limit(5)
                    .toArray();
                console.log(JSON.stringify(samples, null, 2));

                // Log field names from a sample document
                if (samples.length > 0) {
                    console.log('\nField names in ProductDesignation collection:');
                    console.log(Object.keys(samples[0]).sort());
                }
            } catch (error) {
                console.error('Error inserting product designations:', error);
                throw error;
            }
        } else {
            console.log('No product designations found to insert');
        }

    } catch (error) {
        console.error('Error:', error);
    } finally {
        try {
            await client.close();
            console.log('\nConnection closed');
        } catch (error) {
            console.error('Error closing connection:', error);
        }
    }
}

main().catch(console.error);
