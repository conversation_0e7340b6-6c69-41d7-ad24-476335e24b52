<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import BaseGrid from './grids/BaseGrid.svelte';
  import type { Activity } from '$lib/types/activity';

  // Props
  export let activity: Activity | null = null;
  export let show = false;

  // Predefined activity options
  const activityOptions = [
    'Regular Service',
    'Contract Service',
    'Maintenance',
    'Repair',
    'Inspection',
    'Installation',
    'Remote Support',
    'Software Update',
    'Hardware Update',
    'Training',
    'Consultation'
  ];

  // Local state
  let valueInput: number | string = activity?.value || '';
  let activityValue: string = activity?.activity || '';
  let hasFixedValue: boolean = activity?.hasFixed || false;
  let isSubmitting = false;
  let errorMessage = '';

  // Event dispatcher
  const dispatch = createEventDispatcher<{
    close: void;
    updated: Activity;
  }>();

  // Reset form when activity changes
  $: if (activity) {
    valueInput = activity.value || '';
    activityValue = activity.activity || '';
    hasFixedValue = activity.hasFixed || false;
    errorMessage = '';
  }

  // Format month and year for display
  $: monthName = activity ? getMonthName(activity.month) : '';
  $: modalTitle = activity ? `Edit Activity for ${monthName} ${activity.year}` : 'Edit Activity';

  function getMonthName(monthNumber: number): string {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[(monthNumber - 1) % 12];
  }

  // Close modal
  function closeModal() {
    dispatch('close');
  }

  // Save changes
  async function saveChanges() {
    if (!activity) return;
    
    isSubmitting = true;
    errorMessage = '';

    // Validate input
    if (valueInput === '') {
      errorMessage = 'Hours value is required';
      isSubmitting = false;
      return;
    }

    try {
      const response = await fetch('/api/service-activities/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          id: activity._id,
          value: valueInput,
          activity: activityValue,
          hasFixed: hasFixedValue
        })
      });

      const result = await response.json();

      if (response.ok) {
        // Success
        dispatch('updated', {
          ...activity,
          value: valueInput,
          activity: activityValue,
          hasFixed: hasFixedValue
        });
        closeModal();
      } else {
        // Error
        errorMessage = result.message || 'Failed to update activity';
      }
    } catch (error: unknown) {
      const errorMsg = error instanceof Error ? error.message : 'An unexpected error occurred';
      errorMessage = errorMsg;
    } finally {
      isSubmitting = false;
    }
  }
</script>

{#if show}
<div class="modal-overlay">
  <div class="modal-container">
    <BaseGrid>
      <div class="modal-grid">
        <div class="modal-header">
          <h2 class="modal-title">{modalTitle}</h2>
          <button 
            type="button" 
            class="close-button" 
            on:click={closeModal}
            aria-label="Close modal"
          >
            &times;
          </button>
        </div>
        
        <div class="modal-body">
          <div class="form-grid">
            <div class="form-group">
              <label for="hours">Hours</label>
              <input 
                type="number" 
                id="hours" 
                bind:value={valueInput} 
                placeholder="Enter hours"
                step="0.1"
                min="0"
                required
              />
              <small>Enter the number of hours for this activity</small>
            </div>
            
            <div class="form-group">
              <label for="activity-input">Activity</label>
              <select 
                id="activity-input" 
                class="form-control"
                bind:value={activityValue}
                required
              >
                <option value="">Select an activity</option>
                {#each activityOptions as option}
                  <option value={option}>{option}</option>
                {/each}
                <option value="Other">Other</option>
              </select>
              
              {#if activityValue === 'Other'}
                <input 
                  type="text" 
                  class="custom-activity" 
                  placeholder="Enter custom activity"
                  bind:value={activityValue}
                />
              {/if}
            </div>
            
            <div class="form-group checkbox-group">
              <label class="checkbox-label">
                <input 
                  type="checkbox" 
                  bind:checked={hasFixedValue} 
                />
                <span>Has Fixed Schedule</span>
              </label>
              <small>Check if this is a fixed schedule activity</small>
            </div>
            
            {#if errorMessage}
              <div class="error-message">
                {errorMessage}
              </div>
            {/if}
          </div>
        </div>
        
        <div class="modal-footer">
          <button 
            type="button" 
            class="button secondary-button" 
            on:click={closeModal}
          >
            Cancel
          </button>
          <button 
            type="submit" 
            class="button primary-button" 
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>
    </BaseGrid>
  </div>
</div>
{/if}

<style>
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  
  .modal-container {
    background-color: #1e293b;
    border-radius: 8px;
    max-width: 500px;
    width: 100%;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  }
  
  .modal-header {
    padding: 1.25rem;
    border-bottom: 1px solid #334155;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .modal-title {
    color: #60a5fa;
    margin: 0;
    font-size: 1.25rem;
  }
  
  .close-button {
    background: transparent;
    border: none;
    color: #94a3b8;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    line-height: 1;
  }
  
  .close-button:hover {
    color: white;
  }
  
  .modal-body {
    padding: 1.5rem;
  }
  
  .form-grid {
    display: grid;
    gap: 1.25rem;
  }
  
  .form-group {
    display: grid;
    gap: 0.5rem;
  }
  
  label {
    color: #e2e8f0;
    font-weight: 500;
    font-size: 0.95rem;
  }
  
  input, select {
    background-color: #0f172a;
    border: 1px solid #334155;
    border-radius: 6px;
    color: #e2e8f0;
    padding: 0.75rem;
    font-size: 1rem;
    transition: border-color 0.2s;
    width: 100%;
  }
  
  input:focus, select:focus {
    border-color: #60a5fa;
    outline: none;
  }

  select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%2360a5fa' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1em;
    padding-right: 2.5rem;
  }
  
  select option {
    background-color: #0f172a;
    color: #e2e8f0;
  }
  
  .custom-activity {
    margin-top: 0.5rem;
    border-top-color: #3b82f6;
  }
  
  small {
    color: #94a3b8;
    font-size: 0.8rem;
  }
  
  .checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }
  
  .checkbox-group input {
    width: auto;
    margin: 0;
  }
  
  .modal-footer {
    padding: 1.25rem;
    border-top: 1px solid #334155;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
  }
  
  .button {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
  }
  
  .primary-button {
    background-color: #3b82f6;
    color: white;
  }
  
  .primary-button:hover:not(:disabled) {
    background-color: #2563eb;
  }
  
  .primary-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  .secondary-button {
    background-color: transparent;
    border: 1px solid #475569;
    color: #e2e8f0;
  }
  
  .secondary-button:hover {
    background-color: rgba(71, 85, 105, 0.2);
  }
  
  .error-message {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.5rem;
  }
</style>
