import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';
import type { RequestEvent } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/** @type {import('./$types').RequestHandler} */
export async function POST({ url }: RequestEvent) {
    try {
        await client.connect();
        const db = client.db('ServiceContracts');

        const computerId = url.searchParams.get('computerId');
        if (!computerId || !ObjectId.isValid(computerId)) {
            throw new Error('Valid computer ID is required');
        }

        // 1. Get computer
        const computersCollection = db.collection('CustomerComputers');
        const computer = await computersCollection.findOne({ _id: new ObjectId(computerId) });
        if (!computer) {
            throw new Error('Computer not found');
        }

        // 2. Get Product Validity Group from part number
        const pvgCollection = db.collection('ProductValidityGroupPartNumber');
        const pvgDoc = await pvgCollection.findOne({
            ProductPartNumber: parseInt(computer.ProductPartNumber)
        });

        if (!pvgDoc || !pvgDoc["Product Validity Group"]) {
            throw new Error(`No Product Validity Group found for part number: ${computer.ProductPartNumber}`);
        }

        // 3. Update computer with Product Validity Group
        await computersCollection.updateOne(
            { _id: new ObjectId(computerId) },
            { 
                $set: { 
                    "Product Validity Group": pvgDoc["Product Validity Group"],
                    lastUpdated: new Date()
                } 
            }
        );

        // 4. Get service elements count for this PVG
        const serviceCodeCollection = db.collection('ServiceCodeAndActionType');
        const serviceElementCount = await serviceCodeCollection.countDocuments({
            "Product Validity Group": pvgDoc["Product Validity Group"]
        });

        return json({
            success: true,
            message: 'Updated computer Product Validity Group',
            data: {
                partNumber: computer.ProductPartNumber,
                oldPvg: computer["Product Validity Group"],
                newPvg: pvgDoc["Product Validity Group"],
                serviceElementCount
            }
        });

    } catch (error: unknown) {
        console.error('Error:', error);
        return json({
            success: false,
            message: error instanceof Error ? error.message : 'An unknown error occurred'
        }, { status: 500 });
    } finally {
        await client.close();
    }
}
