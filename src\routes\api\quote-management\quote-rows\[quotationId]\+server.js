import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/**
 * GET handler for fetching quote rows by quotation ID
 * @type {import('@sveltejs/kit').RequestHandler}
 */
export async function GET({ params }) {
  try {
    const db = client.db('ServiceContracts');
    const quotationId = params.quotationId;
    
    // Validate ObjectId format
    if (!ObjectId.isValid(quotationId)) {
      return json({ success: false, message: 'Invalid quotation ID format' }, { status: 400 });
    }
    
    console.log(`Fetching quote rows for quotationId: ${quotationId}`);
    
    // Query the QuotationRows collection (using PascalCase per convention)
    const quoteRows = await db.collection('QuotationRows')
      .find({ quotationId: new ObjectId(quotationId) })
      .sort({ rowType: 1, rowOrder: 1 })
      .toArray();
    
    console.log(`Found ${quoteRows.length} quote rows`);
    
    // Transform ObjectIds to strings for JSON serialization
    const transformedRows = quoteRows.map(row => ({
      ...row,
      _id: row._id.toString(),
      quotationId: row.quotationId.toString()
    }));
    
    return json(transformedRows);
  } catch (error) {
    console.error('Error fetching quote rows:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return json({ success: false, message: errorMessage }, { status: 500 });
  }
}
