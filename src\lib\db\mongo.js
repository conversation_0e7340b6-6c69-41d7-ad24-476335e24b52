import { MongoClient, ObjectId } from 'mongodb';

/** @type {string} */
const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017';
/** @type {string} */
const dbName = process.env.MONGODB_DB_NAME || 'ServiceContracts';

// Create a new MongoClient
const client = new MongoClient(uri);
/** @type {import('mongodb').Db | null} */
let db = null;

/**
 * Connect to MongoDB
 * @returns {Promise<import('mongodb').Db>}
 */
async function connectToDatabase() {
  if (db) return db;
  
  try {
    // Connect the client to the server
    await client.connect();
    console.log('Connected successfully to MongoDB server');
    
    // Get reference to the database
    db = client.db(dbName);
    console.log(`Using database: ${dbName}`);
    return db;
  } catch (error) {
    console.error('MongoDB connection error:', error instanceof Error ? error.message : 'Unknown error');
    throw error;
  }
}

/**
 * Get a collection from the database
 * @param {string} collectionName - The name of the collection
 * @returns {Promise<import('mongodb').Collection>}
 */
export async function getCollection(collectionName) {
  try {
    const database = await connectToDatabase();
    return database.collection(collectionName);
  } catch (error) {
    console.error(`Error getting collection ${collectionName}:`, error instanceof Error ? error.message : 'Unknown error');
    throw error;
  }
}

/**
 * Serialize a MongoDB document to make it JSON-safe
 * @param {Object} doc - The MongoDB document to serialize
 * @returns {Object} The serialized document
 */
export function serializeDocument(doc) {
  if (!doc) return null;
  
  const result = { ...doc };
  
  // Convert ObjectId to string
  if (result._id instanceof ObjectId) {
    result._id = result._id.toString();
  }
  
  // Convert other ObjectIds to strings
  Object.keys(result).forEach(key => {
    if (result[key] instanceof ObjectId) {
      result[key] = result[key].toString();
    }
  });
  
  return result;
}

/**
 * Serialize an array of MongoDB documents to make them JSON-safe
 * @param {Array<Object>} docs - The MongoDB documents to serialize
 * @returns {Array<Object>} The serialized documents
 */
export function serializeDocuments(docs) {
  if (!docs || !Array.isArray(docs)) return [];
  return docs.map(serializeDocument);
}

/**
 * Check if a string is a valid MongoDB ObjectId
 * @param {string} id - The ID to validate
 * @returns {boolean}
 */
export function isValidObjectId(id) {
  if (!id || typeof id !== 'string') return false;
  return ObjectId.isValid(id);
}

/**
 * Convert a string ID to MongoDB ObjectId
 * @param {string} id - The ID to convert
 * @returns {ObjectId}
 * @throws {Error} If the ID is invalid
 */
export function toObjectId(id) {
  if (!isValidObjectId(id)) {
    throw new Error('Invalid ObjectId format');
  }
  return new ObjectId(id);
}

/**
 * Convert MongoDB document for client-side use
 * @template {Record<string, any>} T
 * @param {T} doc - The document to convert
 * @returns {T} Document with string IDs
 */
export function toClientDocument(doc) {
  if (!doc) return doc;

  /** @type {Record<string, any>} */
  const result = { ...doc };

  // Convert _id to string if it's an ObjectId
  if (result._id instanceof ObjectId) {
    result._id = result._id.toString();
  }

  // Convert any other ObjectId fields to strings
  Object.entries(result).forEach(([key, value]) => {
    if (value instanceof ObjectId) {
      result[key] = value.toString();
    }
  });

  return /** @type {T} */ (result);
}

/**
 * Convert document for database use
 * @template {Record<string, any>} T
 * @param {T} doc - The document to convert
 * @returns {T} Document with ObjectIds
 */
export function toDatabaseDocument(doc) {
  if (!doc) return doc;

  /** @type {Record<string, any>} */
  const result = { ...doc };

  // Create new ObjectId for _id if not present
  if (!result._id) {
    result._id = new ObjectId();
  } else if (typeof result._id === 'string' && isValidObjectId(result._id)) {
    result._id = new ObjectId(result._id);
  }

  // Convert any ID fields that might be present
  Object.entries(result).forEach(([key, value]) => {
    if (key.endsWith('Id') && 
        key !== '_id' && 
        typeof value === 'string' && 
        isValidObjectId(value)) {
      result[key] = new ObjectId(value);
    }
  });

  return /** @type {T} */ (result);
}

/**
 * Close the MongoDB connection
 * @returns {Promise<void>}
 */
export async function closeConnection() {
  if (client) {
    await client.close();
    db = null;
    console.log('MongoDB connection closed');
  }
}

export { ObjectId };
