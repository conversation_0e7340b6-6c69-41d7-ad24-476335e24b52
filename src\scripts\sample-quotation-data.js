/**
 * Sample Quotation Data Structure
 * 
 * This file contains sample data structures for the QuotationHeader and QuotationRows collections
 * in the ServiceContracts database. These samples follow the exact field naming conventions
 * used in the application.
 */

const { MongoClient, ObjectId } = require('mongodb');

// Sample data for QuotationHeader collection
const sampleQuotationHeader = {
  _id: new ObjectId(), // MongoDB will generate this
  customerId: new ObjectId("60a2b8e9f237a81c0c9e1111"), // Reference to a customer
  computerId: new ObjectId("60a2b8e9f237a81c0c9e2222"), // Reference to a computer
  QuotationNumber: "Q-2025-0042",
  QuotationDate: new Date("2025-04-09T10:00:00Z"),
  QuotationStatus: "Active", // Active, Approved, Rejected, Expired
  ValidUntil: new Date("2025-05-09T10:00:00Z"),
  CreatedBy: "John Doe",
  ApprovedBy: null,
  ApprovalDate: null,
  TotalAmount: 2450.75,
  Currency: "GBP",
  Notes: "Annual maintenance quote for server hardware and support",
  CustomerName: "Acme Corporation",
  CustomerContact: "<PERSON>",
  CustomerEmail: "<EMAIL>",
  CustomerPhone: "+44 20 1234 5678",
  TermsAndConditions: "Standard terms apply. Valid for 30 days.",
  DiscountPercentage: 5,
  TaxRate: 20,
  PaymentTerms: "Net 30",
  DeliveryTerms: "On-site",
  QuotationType: "Service Contract"
};

// Sample data for QuotationRows collection
const sampleQuotationRows = [
  // Labour row - Base Contract
  {
    _id: new ObjectId(), // MongoDB will generate this
    quotationId: sampleQuotationHeader._id, // Reference to the quotation header
    level: 0,
    package: "Base Contract Offering [Fixed]",
    serviceId: "SVC-001",
    service: "Basic System Maintenance",
    included: "Yes",
    required: "Yes",
    scos: 150.00,
    oemImport: 0,
    fleetOwner: 0,
    rrp: 200.00,
    selectService: "Yes",
    qtyPerYr: 4,
    rowOrder: 1,
    rowType: "service",
    QuoteRowType: "Labour",
    CustomerPrice: 180.00,
    DealerPrice: 160.00,
    InternalCost: 120.00
  },
  
  // Labour row - Support
  {
    _id: new ObjectId(),
    quotationId: sampleQuotationHeader._id,
    level: 1,
    package: "Support (Dealer) [Fixed]",
    serviceId: "SVC-002",
    service: "Remote System Support",
    included: "Yes",
    required: "Yes",
    scos: 300.00,
    oemImport: 0,
    fleetOwner: 0,
    rrp: 350.00,
    selectService: "Yes",
    qtyPerYr: 12,
    rowOrder: 2,
    rowType: "service",
    QuoteRowType: "Labour",
    CustomerPrice: 330.00,
    DealerPrice: 310.00,
    InternalCost: 280.00
  },
  
  // Labour row - Variable Support
  {
    _id: new ObjectId(),
    quotationId: sampleQuotationHeader._id,
    level: 1,
    package: "Support (Dealer) [Variable/hr]",
    serviceId: "SVC-003",
    service: "Emergency Support",
    included: "No",
    required: "No",
    scos: 100.00,
    oemImport: 0,
    fleetOwner: 0,
    rrp: 150.00,
    selectService: "No",
    qtyPerYr: 0,
    rowOrder: 3,
    rowType: "service",
    QuoteRowType: "Labour",
    CustomerPrice: 140.00,
    DealerPrice: 130.00,
    InternalCost: 110.00
  },
  
  // Part row
  {
    _id: new ObjectId(),
    quotationId: sampleQuotationHeader._id,
    level: 2,
    package: "Dealer Add-Ons [Fixed]",
    serviceId: "SVC-004", // We still keep serviceId for reference even though it's a part
    service: "Hardware Components", // We still keep service for reference
    included: "Yes",
    required: "Yes",
    scos: 0,
    oemImport: 250.00,
    fleetOwner: 0,
    rrp: 300.00,
    selectService: "Yes",
    qtyPerYr: 1,
    rowOrder: 4,
    rowType: "part",
    QuoteRowType: "Part",
    PartNumber: "HW-RAM-16GB",
    PartName: "16GB DDR4 RAM Module",
    PartQty: 2,
    CustomerPrice: 280.00,
    DealerPrice: 260.00,
    InternalCost: 220.00
  },
  
  // Another Part row
  {
    _id: new ObjectId(),
    quotationId: sampleQuotationHeader._id,
    level: 2,
    package: "Dealer Add-Ons [Fixed]",
    serviceId: "SVC-005",
    service: "Storage Components",
    included: "Yes",
    required: "No",
    scos: 0,
    oemImport: 400.00,
    fleetOwner: 0,
    rrp: 450.00,
    selectService: "Yes",
    qtyPerYr: 1,
    rowOrder: 5,
    rowType: "part",
    QuoteRowType: "Part",
    PartNumber: "HW-SSD-1TB",
    PartName: "1TB SSD Drive",
    PartQty: 1,
    CustomerPrice: 420.00,
    DealerPrice: 390.00,
    InternalCost: 350.00
  }
];

// Function to insert sample data (for demonstration purposes)
async function insertSampleData() {
  try {
    const uri = "mongodb://localhost:27017";
    const client = new MongoClient(uri);
    
    await client.connect();
    console.log("Connected to MongoDB");
    
    const database = client.db("ServiceContracts");
    const quotationHeaderCollection = database.collection("QuotationHeader");
    const quotationRowsCollection = database.collection("QuotationRows");
    
    // Insert header
    const headerResult = await quotationHeaderCollection.insertOne(sampleQuotationHeader);
    console.log(`Inserted header with ID: ${headerResult.insertedId}`);
    
    // Insert rows
    const rowsResult = await quotationRowsCollection.insertMany(sampleQuotationRows);
    console.log(`Inserted ${rowsResult.insertedCount} quote rows`);
    
    await client.close();
    console.log("Connection closed");
    
  } catch (error) {
    console.error("Error:", error);
  }
}

// To execute this script, uncomment the line below:
// insertSampleData();

// Export the sample data for reference
module.exports = {
  sampleQuotationHeader,
  sampleQuotationRows
};
