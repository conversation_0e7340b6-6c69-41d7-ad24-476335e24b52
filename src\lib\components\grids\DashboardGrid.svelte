<!-- DashboardGrid.svelte -->
<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  
  // You can add specific props for the dashboard layout
  export let showSidebar: boolean = true;
</script>

<BaseGrid columns={showSidebar ? "250px 1fr" : "1fr"} gap="1.5rem" padding="1rem" minHeight="100vh">
  {#if showSidebar}
    <div class="sidebar">
      <slot name="sidebar" />
    </div>
  {/if}
  
  <div class="main-content">
    <slot />
  </div>
</BaseGrid>

<style>
  .sidebar {
    background-color: var(--sidebar-bg, #f5f5f5);
    border-right: 1px solid var(--border-color, #e0e0e0);
    height: 100%;
  }

  .main-content {
    height: 100%;
    overflow-y: auto;
  }
</style>
