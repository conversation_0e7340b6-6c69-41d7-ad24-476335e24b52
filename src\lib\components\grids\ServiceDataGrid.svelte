<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  
  // Define column layout for grid
  export let columnWidths: string[] = [];
  export let columnNames: string[] = [];
</script>

<BaseGrid>
  <div class="service-data-grid" style="--grid-columns: {columnWidths.join(' ')}">
    <slot name="filter-row" />
    <slot name="header-row" />
    <slot name="data-rows" />
  </div>
</BaseGrid>

<style>
  .service-data-grid {
    display: grid;
    grid-template-columns: var(--grid-columns);
    width: 100%;
    gap: 0;
    overflow-x: auto;
  }
</style>
