import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function updateCustomerComputers() {
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db('ServiceContracts');
    const collection = db.collection('CustomerComputers');
    
    // First, check if any documents have the old fields and rename them
    const oldFieldsResult = await collection.updateMany(
      { 
        $or: [
          { 'Hours Contract Start': { $exists: true } },
          { hoursContractStart: { $exists: true } },
          { HoursAtServiceStart: { $exists: true } }
        ]
      },
      [
        { 
          $set: { 
            // Set HoursAtContractStart to the first non-null value from the old fields
            HoursAtContractStart: { 
              $ifNull: [
                '$HoursAtServiceStart', 
                { $ifNull: ['$hoursContractStart', { $ifNull: ['$Hours Contract Start', 1000] }] }
              ]
            }
          }
        },
        {
          $unset: ['Hours Contract Start', 'hoursContractStart', 'HoursAtServiceStart']
        }
      ]
    );
    
    console.log(`Updated ${oldFieldsResult.modifiedCount} documents with old fields`);
    
    // Then, make sure all documents have HoursAtContractStart field with value 1000
    const updateResult = await collection.updateMany(
      { HoursAtContractStart: { $exists: false } },
      { $set: { HoursAtContractStart: 1000 } }
    );
    
    console.log(`Added field to ${updateResult.modifiedCount} additional documents`);
    
  } catch (error) {
    console.error('Error updating CustomerComputers:', error);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

updateCustomerComputers();
