<!-- @component
A form component for editing quotation rows
-->
<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import type { QuotationRow } from '$lib/types';
  
  export let row: QuotationRow | null = null;
  export let currentQuoteId: string | undefined = undefined;
  
  const dispatch = createEventDispatcher<{
    save: QuotationRow;
    cancel: void;
  }>();
  
  // Default form data - simplified to only include the essential fields
  const defaultFormData: Partial<QuotationRow> = {
    _id: '',
    RowType: '',
    RowOrder: 0,
    ServiceActivity: '',
    Cost: 0,
    SSP: 0,
    OemImporter: 0,
    FleetOwner: 0,
    Required: false,
    IncludeInOffer: true,
    CreatedAt: new Date().toISOString(),
    UpdatedAt: new Date().toISOString()
  };
  
  // Initialize form data from row or default
  let formData: Partial<QuotationRow> = row 
    ? { ...defaultFormData, ...row } 
    : { ...defaultFormData };
  
  // Row type options
  const rowTypeOptions = [
    'BaseContract',
    'DealerAddOns',
    'Support',
    'RetirementPlan',
    'Other'
  ];
  
  function handleSubmit() {
    // Validate required fields
    if (!formData.ServiceActivity) {
      alert('Service activity is required');
      return;
    }
    
    // Update timestamps
    formData.UpdatedAt = new Date().toISOString();
    if (!formData._id) {
      formData.CreatedAt = formData.UpdatedAt;
    }
    
    // Set QuoteId and QuotationId to current quote ID
    if (currentQuoteId) {
      formData.QuoteId = currentQuoteId;
      formData.QuotationId = currentQuoteId;
    }
    
    dispatch('save', formData as QuotationRow);
  }
  
  function handleCancel() {
    dispatch('cancel');
  }
</script>

<div class="modal-backdrop">
  <div class="modal-content">
    <div class="modal-header">
      <h2>{row ? 'Edit' : 'Add'} Quotation Row</h2>
      <button class="close-button" on:click={handleCancel}>×</button>
    </div>
    
    <form on:submit|preventDefault={handleSubmit}>
      <div class="form-grid">
        <!-- Basic Information Section -->
        <div class="form-section">
          <h3>Service Information</h3>
          
          <div class="form-row">
            <label for="rowType">Row Type</label>
            <select id="rowType" bind:value={formData.RowType}>
              <option value="">Select Type</option>
              {#each rowTypeOptions as option}
                <option value={option}>{option}</option>
              {/each}
            </select>
          </div>
          
          <div class="form-row">
            <label for="rowOrder">Service ID</label>
            <input 
              type="number" 
              id="rowOrder" 
              bind:value={formData.RowOrder} 
              min="0"
            />
          </div>
          
          <div class="form-row">
            <label for="serviceActivity">Service Activity</label>
            <input 
              type="text" 
              id="serviceActivity" 
              bind:value={formData.ServiceActivity} 
              required
            />
          </div>
          
          <div class="form-row">
            <label for="cost">Cost (KCOS)</label>
            <input 
              type="number" 
              id="cost" 
              bind:value={formData.Cost} 
              min="0"
              step="0.01"
            />
          </div>
          
          <div class="form-row">
            <label for="oemImporter">OEM Importer</label>
            <input 
              type="number" 
              id="oemImporter" 
              bind:value={formData.OemImporter} 
              min="0"
              step="0.01"
            />
          </div>
          
          <div class="form-row">
            <label for="fleetOwner">Fleet Owner</label>
            <input 
              type="number" 
              id="fleetOwner" 
              bind:value={formData.FleetOwner} 
              min="0"
              step="0.01"
            />
          </div>
          
          <div class="form-row">
            <label for="ssp">SSP</label>
            <input 
              type="number" 
              id="ssp" 
              bind:value={formData.SSP} 
              min="0"
              step="0.01"
            />
          </div>
          
          <div class="form-row">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                bind:checked={formData.Required}
              />
              Required
            </label>
          </div>
          
          <div class="form-row">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                bind:checked={formData.IncludeInOffer}
              />
              Include in Package Pricing
            </label>
          </div>
        </div>
      </div>
      
      <div class="form-actions">
        <button type="button" class="cancel-button" on:click={handleCancel}>
          Cancel
        </button>
        <button type="submit" class="save-button">
          Save
        </button>
      </div>
    </form>
  </div>
</div>

<style>
  .modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: grid;
    place-items: center;
    z-index: 1000;
  }

  .modal-content {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
  }

  .modal-header {
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
  }

  .modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: #2c3e50;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6c757d;
    cursor: pointer;
    padding: 0.5rem;
  }

  .close-button:hover {
    color: #343a40;
  }

  .form-grid {
    display: grid;
    gap: 2rem;
    padding: 1rem;
  }

  .form-section {
    display: grid;
    gap: 1rem;
  }

  .form-section h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.25rem;
  }

  .form-row {
    display: grid;
    gap: 0.5rem;
  }

  label {
    color: #495057;
    font-weight: 500;
  }

  input[type="text"],
  input[type="number"],
  select {
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 1rem;
  }

  input[type="text"]:focus,
  input[type="number"]:focus,
  select:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  .checkbox-label {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 0.5rem;
    align-items: center;
  }

  .form-actions {
    display: grid;
    grid-template-columns: auto auto;
    justify-content: end;
    gap: 1rem;
    padding: 1rem;
    border-top: 1px solid #dee2e6;
  }

  .cancel-button {
    padding: 0.5rem 1rem;
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  .save-button {
    padding: 0.5rem 1rem;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  .cancel-button:hover {
    background-color: #5a6268;
  }

  .save-button:hover {
    background-color: #218838;
  }
</style>
