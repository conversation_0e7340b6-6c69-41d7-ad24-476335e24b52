import { json } from '@sveltejs/kit';
import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/** @type {import('./$types').RequestHandler} */
export async function GET({ url }) {
  try {
    console.log('API: Connecting to MongoDB...');
    await client.connect();
    console.log('API: Connected to MongoDB');
    
    const db = client.db('ServiceContracts');
    console.log('API: Using ServiceContracts database');
    
    // List all collections to verify ServiceCodeHeader exists
    const collections = await db.listCollections().toArray();
    console.log('API: Available collections:', collections.map(c => c.name));
    
    const collection = db.collection('ServiceCodeHeader');
    console.log('API: Using ServiceCodeHeader collection');
    
    // Parse query parameters
    const page = parseInt(url.searchParams.get('page') || '1');
    // Set a very high limit by default to show all items
    const limit = parseInt(url.searchParams.get('limit') || '2500');
    const skip = (page - 1) * limit;
    
    // Build filter based on query parameters
    const filter = {};
    
    // Define the mapping of URL parameter names to actual field names
    const fieldMapping = {
      'ServiceCode': 'ServiceCode',
      'Service_activity_Label': 'Service activity Label',
      'Activity_purpose': 'Activity purpose',
      'Product_Validity_Group': 'Product Validity Group',
      'Internal_No_of_Hours': 'Internal No of Hours',
      'Internal_No_of_Months': 'Internal No of Months'
    };
    
    // Add filters for each field if provided in the query
    for (const [key, value] of url.searchParams.entries()) {
      // Skip pagination parameters
      if (['page', 'limit'].includes(key)) continue;
      
      if (value) {
        // Map the URL parameter name to the actual field name
        const fieldName = fieldMapping[key] || key;
        
        // Use regex for string fields to enable partial matching
        filter[fieldName] = { $regex: value, $options: 'i' };
      }
    }
    
    console.log('API: Filter:', filter);
    
    // Get total count for pagination
    const totalItems = await collection.countDocuments(filter);
    console.log(`API: Total items in collection with filter: ${totalItems}`);
    
    // Get a sample document to verify field names
    const sampleDoc = await collection.findOne({});
    console.log('API: Sample document:', sampleDoc);
    
    // Get items with pagination and sorting
    const items = await collection.find(filter)
      .sort({ ServiceCode: 1 }) // Sort by ServiceCode for better readability
      .skip(skip)
      .limit(limit)
      .toArray();
    
    console.log(`API: Found ${items.length} items out of ${totalItems} total`);
    
    // Convert ObjectId to string to avoid serialization issues
    const serializedItems = items.map(item => {
      return {
        ...item,
        _id: item._id.toString()
      };
    });
    
    const response = {
      items: serializedItems,
      totalItems,
      page,
      limit,
      totalPages: Math.ceil(totalItems / limit)
    };
    
    console.log(`API: Sending response with ${serializedItems.length} items`);
    
    return json(response);
  } catch (error) {
    console.error('API Error fetching data:', error);
    return json({ error: 'Failed to fetch data: ' + error.message }, { status: 500 });
  } finally {
    // Don't close the client connection here as it might be reused
  }
}

/** @type {import('./$types').RequestHandler} */
export async function POST({ request }) {
  try {
    const data = await request.json();
    
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('ServiceCodeHeader');
    
    // Add timestamps
    data.createdAt = new Date();
    data.updatedAt = new Date();
    
    const result = await collection.insertOne(data);
    
    return json({ 
      success: true, 
      id: result.insertedId,
      message: 'Item created successfully' 
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating item:', error);
    return json({ error: 'Failed to create item' }, { status: 500 });
  }
}
