import { json } from '@sveltejs/kit';
import { ObjectId } from 'mongodb';
import { getCollection } from '$lib/db/mongo';

/**
 * GET handler for fetching service contracts
 * @param {import('@sveltejs/kit').RequestEvent} event
 */
export async function GET({ url }) {
  try {
    // Get query parameters
    const customerId = url.searchParams.get('customerId');
    
    // Build filter
    const filter = {};
    
    // Add customer filter if provided
    if (customerId && ObjectId.isValid(customerId)) {
      filter.customerId = new ObjectId(customerId);
    }
    
    // Get collection and fetch contracts
    const collection = await getCollection('ServiceContracts');
    const contracts = await collection.find(filter)
      .sort({ createdAt: -1 })
      .toArray();
    
    // Convert ObjectIds to strings for client-side use
    const formattedContracts = contracts.map(contract => ({
      ...contract,
      _id: contract._id.toString(),
      customerId: contract.customerId.toString()
    }));
    
    return json(formattedContracts);
  } catch (err) {
    console.error('Error fetching service contracts:', err);
    return new Response(
      JSON.stringify({ 
        error: 'Internal Server Error', 
        details: err instanceof Error ? err.message : 'Unknown error'
      }), 
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}

/**
 * POST handler for creating a new service contract
 * @param {import('@sveltejs/kit').RequestEvent} event
 */
export async function POST({ request }) {
  try {
    // Get contract data from request
    const contractData = await request.json();
    
    // Validate required fields
    if (!contractData.customerId || !contractData.name || !contractData.startDate) {
      return new Response(
        JSON.stringify({ 
          error: 'Missing required fields', 
          details: 'Customer ID, name, and start date are required'
        }), 
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }
    
    // Validate ObjectId format
    if (!ObjectId.isValid(contractData.customerId)) {
      return new Response(
        JSON.stringify({ 
          error: 'Invalid customer ID format'
        }), 
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }
    
    // Convert string ID to ObjectId
    contractData.customerId = new ObjectId(contractData.customerId);
    
    // Add timestamps
    const now = new Date();
    contractData.createdAt = now;
    contractData.updatedAt = now;
    
    // Get collection and insert contract
    const collection = await getCollection('ServiceContracts');
    const result = await collection.insertOne(contractData);
    
    if (!result.acknowledged) {
      return new Response(
        JSON.stringify({ 
          error: 'Failed to create service contract'
        }), 
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }
    
    return json({
      success: true,
      _id: result.insertedId.toString(),
      message: 'Service contract created successfully'
    }, { status: 201 });
  } catch (err) {
    console.error('Error creating service contract:', err);
    return new Response(
      JSON.stringify({ 
        error: 'Internal Server Error', 
        details: err instanceof Error ? err.message : 'Unknown error'
      }), 
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}
