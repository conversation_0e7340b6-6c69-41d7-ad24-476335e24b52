import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function fixLabourTimeData() {
    const client = new MongoClient(uri);
    
    try {
        await client.connect();
        console.log('Connected to MongoDB');
        
        const db = client.db(dbName);
        const labourTimeCollection = db.collection('LabourTime');

        // Drop the existing collection to start fresh
        await labourTimeCollection.drop().catch(err => {
            if (err.code !== 26) { // Ignore "ns not found" error
                throw err;
            }
        });
        console.log('Cleared existing collection');

        // Sample data to insert
        const sampleData = [
            {
                productDesignation: 'TAD1381-85VE',
                laborHours: 4.5,
                category: 'Engine Maintenance',
                description: 'Standard engine maintenance procedure',
                createdAt: new Date(),
                updatedAt: new Date()
            },
            {
                productDesignation: 'TAD1382-87VE',
                laborHours: 6.0,
                category: 'Major Repair',
                description: 'Complete engine overhaul',
                createdAt: new Date(),
                updatedAt: new Date()
            },
            {
                productDesignation: 'TAD1383-88VE',
                laborHours: 2.5,
                category: 'Inspection',
                description: 'Routine inspection and diagnostics',
                createdAt: new Date(),
                updatedAt: new Date()
            }
        ];

        // Insert new sample data
        const insertResult = await labourTimeCollection.insertMany(sampleData);
        console.log(`Inserted ${insertResult.insertedCount} new documents`);

        // Verify the fix
        const count = await labourTimeCollection.countDocuments();
        console.log(`\nTotal documents after fix: ${count}`);
        
        const samples = await labourTimeCollection.find({}).toArray();
        console.log('\nSample documents after fix:');
        samples.forEach(doc => {
            console.log('\nDocument:', {
                _id: doc._id.toString(),
                productDesignation: doc.productDesignation,
                laborHours: doc.laborHours,
                category: doc.category,
                description: doc.description
            });
        });
        
    } catch (error) {
        console.error('Error fixing data:', error);
    } finally {
        await client.close();
        console.log('\nDisconnected from MongoDB');
    }
}

// Run the fix
fixLabourTimeData().catch(console.error);
