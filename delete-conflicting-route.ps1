# PowerShell script to delete the conflicting route
# This will remove the [rowId] route and keep only the [id] route

# Set the path to the conflicting route
$conflictingRoutePath = "C:\SvelteProjects\Svelte-Mongo3\src\routes\api\quote-rows\update\[rowId]"

# Check if the directory exists
if (Test-Path $conflictingRoutePath) {
    Write-Host "Removing conflicting route: $conflictingRoutePath"
    
    # Remove the directory and all its contents
    Remove-Item -Path $conflictingRoutePath -Recurse -Force
    
    Write-Host "Conflicting route removed successfully!"
} else {
    Write-Host "Conflicting route directory not found: $conflictingRoutePath"
}

Write-Host "Route conflict resolution complete."
Write-Host "The application is now using only the /api/quote-rows/update/[id] route."
