<script lang="ts">
  export let data: any = null;
  export let title: string = 'JSON Data';
  let isVisible = false;

  function toggleVisibility() {
    isVisible = !isVisible;
  }
</script>

<div class="json-viewer">
  <button class="toggle-button" on:click={toggleVisibility}>
    {isVisible ? 'Hide' : 'Show'} {title}
  </button>
  
  {#if isVisible}
    <div class="json-content">
      <pre>{JSON.stringify(data, null, 2)}</pre>
    </div>
  {/if}
</div>

<style>
  .json-viewer {
    margin: 1rem 0;
    width: 100%;
  }
  
  .toggle-button {
    background: #4a5568;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background 0.2s;
  }
  
  .toggle-button:hover {
    background: #2d3748;
  }
  
  .json-content {
    background: #f8f9fa;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 1rem;
    margin-top: 0.5rem;
    overflow-x: auto;
    max-height: 500px;
    overflow-y: auto;
  }
  
  pre {
    margin: 0;
    font-family: monospace;
    font-size: 0.9rem;
    white-space: pre-wrap;
    word-break: break-word;
  }
</style>
