<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  
  // Props
  export let months: string[] = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  export let years: number[] = [];
  export let data: any[] = [];
  export let title: string = "Computer Monthly Activities";
  export let showYearTotals: boolean = true;
  
  // Convert data to a map for easy lookup
  $: dataMap = data.reduce((acc, item) => {
    const key = `${item.year}-${item.month}`;
    acc[key] = item;
    return acc;
  }, {});
  
  // Get data for a specific year and month
  function getDataForYearMonth(year: number, month: number) {
    const key = `${year}-${month}`;
    return dataMap[key] || null;
  }
  
  // Calculate yearly totals
  $: yearTotals = years.reduce((acc, year) => {
    let total = 0;
    for (let i = 1; i <= 12; i++) {
      const item = getDataForYearMonth(year, i);
      total += item?.hours || 0;
    }
    acc[year] = total;
    return acc;
  }, {});
</script>

<BaseGrid>
  <div class="computer-months-grid">
    <header class="grid-header">
      <h2>{title}</h2>
      <slot name="header-actions"></slot>
    </header>
    
    <div class="grid-content">
      <div class="months-table">
        <div class="table-header">
          <div class="cell header-cell year-header">Year</div>
          {#each months as month, i}
            <div class="cell header-cell month-header">{month}</div>
          {/each}
          {#if showYearTotals}
            <div class="cell header-cell total-header">Total</div>
          {/if}
        </div>
        
        <div class="table-body">
          {#each years as year}
            <div class="table-row">
              <div class="cell year-cell">{year}</div>
              
              {#each Array(12).fill(0) as _, monthIndex}
                {@const monthData = getDataForYearMonth(year, monthIndex + 1)}
                <div 
                  class="cell month-cell" 
                  class:has-data={!!monthData}
                  class:has-fixed={monthData?.hasFixed}
                  on:click={() => {
                    if (typeof onCellClick === 'function') {
                      onCellClick(year, monthIndex);
                    }
                  }}
                  on:keydown|preventDefault={(e) => {
                    if (e.key === 'Enter' && typeof onCellClick === 'function') {
                      onCellClick(year, monthIndex);
                    }
                  }}
                  tabindex="0"
                  role="button"
                >
                  <slot name="cell" year={year} month={monthIndex + 1} data={monthData}>
                    <div class="default-cell-content">
                      <span class="hours-value">{monthData?.hours || 0}</span>
                      {#if monthData?.activity}
                        <span class="activity-label">{monthData.activity}</span>
                      {/if}
                    </div>
                  </slot>
                </div>
              {/each}
              
              {#if showYearTotals}
                <div class="cell total-cell">
                  {yearTotals[year]?.toFixed(1) || '0'}
                </div>
              {/if}
            </div>
          {/each}
        </div>
      </div>
    </div>
    
    <footer class="grid-footer">
      <slot name="footer"></slot>
    </footer>
  </div>
</BaseGrid>

<style>
  .computer-months-grid {
    display: grid;
    grid-template-rows: auto 1fr auto;
    height: 100%;
    width: 100%;
  }
  
  .grid-header {
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #334155;
  }
  
  .grid-header h2 {
    color: #60a5fa;
    margin: 0;
    font-size: 1.25rem;
  }
  
  .grid-content {
    overflow: auto;
    padding: 0.5rem;
  }
  
  .months-table {
    display: grid;
    grid-template-rows: auto 1fr;
    width: 100%;
    min-width: min-content;
  }
  
  .table-header {
    display: grid;
    grid-template-columns: 100px repeat(12, minmax(80px, 1fr)) auto;
    position: sticky;
    top: 0;
    z-index: 2;
    background: #0f172a;
  }
  
  .table-body {
    display: grid;
    grid-auto-rows: minmax(60px, auto);
  }
  
  .table-row {
    display: grid;
    grid-template-columns: 100px repeat(12, minmax(80px, 1fr)) auto;
    border-bottom: 1px solid #334155;
  }
  
  .cell {
    padding: 0.75rem;
    border-right: 1px solid #334155;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .header-cell {
    font-weight: 500;
    color: #94a3b8;
    background: #1e293b;
    text-align: center;
    position: sticky;
    top: 0;
    z-index: 1;
  }
  
  .year-header, .year-cell {
    position: sticky;
    left: 0;
    z-index: 2;
    background: #1e293b;
  }
  
  .year-cell {
    color: #94a3b8;
    font-weight: 500;
  }
  
  .month-cell {
    background: #0f172a;
    transition: all 0.2s;
    cursor: pointer;
  }
  
  .month-cell:hover {
    background: #1e293b;
  }
  
  .has-data {
    background-color: rgba(59, 130, 246, 0.1);
  }
  
  .has-fixed {
    background-color: rgba(59, 130, 246, 0.2);
  }
  
  .total-header, .total-cell {
    background: #1e293b;
    font-weight: 500;
  }
  
  .total-cell {
    color: #60a5fa;
  }
  
  .default-cell-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
  }
  
  .hours-value {
    font-weight: 500;
  }
  
  .activity-label {
    font-size: 0.75rem;
    color: #94a3b8;
    max-width: 90%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .grid-footer {
    padding: 1rem;
    border-top: 1px solid #334155;
  }
</style>
