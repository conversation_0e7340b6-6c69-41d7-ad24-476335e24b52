import { MongoClient } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

export async function GET() {
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('ProductValidityGroup');
    
    const items = await collection.find({}).sort({
      ProductGroupId: 1,    // Primary sort: Group related products
      Id: 1,               // Secondary sort: Within same group
      ProductPartNumber: 1  // Final sort: Unique identifier
    }).toArray();
    
    return json({ items });
  } catch (error) {
    console.error('Error fetching product designations:', error);
    return json({ error: 'Failed to fetch product designations' }, { status: 500 });
  } finally {
    await client.close();
  }
}
