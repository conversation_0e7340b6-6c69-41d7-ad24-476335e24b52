# Script to remove the conflicting update route

Write-Host "Removing the conflicting /api/quote-rows/update route..."

# Define path to the conflicting directory
$updatePath = "C:\SvelteProjects\Svelte-Mongo3\src\routes\api\quote-rows\update"

# Check if directory exists
$directoryExists = Test-Path $updatePath

if ($directoryExists) {
    Write-Host "Found update directory at: $updatePath"
    
    # List contents before removal for verification
    $files = Get-ChildItem -Path $updatePath -Recurse
    Write-Host "Directory contains the following items:"
    foreach ($file in $files) {
        Write-Host "  - $($file.FullName.Replace($updatePath, ''))"
    }
    
    # Remove the directory
    Write-Host "Removing the update directory and all its contents..."
    Remove-Item -Path $updatePath -Recurse -Force
    
    # Verify removal
    if (Test-Path $updatePath) {
        Write-Host "Failed to remove the update directory" -ForegroundColor Red
    } else {
        Write-Host "Successfully removed the update directory" -ForegroundColor Green
    }
} else {
    Write-Host "update directory not found at: $updatePath" -ForegroundColor Yellow
}

Write-Host "Operation complete. Please restart your development server."
