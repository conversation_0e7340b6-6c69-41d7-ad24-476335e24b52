import { MongoClient, ObjectId } from 'mongodb';
import { error, json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/**
 * Serializes MongoDB documents by converting ObjectId and Date instances to string format
 * @param {Object} doc - Document to serialize
 * @return {Object} Serialized document
 */
function serializeDocument(doc) {
    if (!doc) return {};
    
    const serialized = {};
    for (const [key, value] of Object.entries(doc)) {
        if (value instanceof ObjectId) {
            serialized[key] = value.toString();
        } else if (value instanceof Date) {
            serialized[key] = value.toISOString();
        } else if (Array.isArray(value)) {
            serialized[key] = value.map(item => 
                item instanceof ObjectId ? item.toString() : item
            );
        } else if (value && typeof value === 'object') {
            serialized[key] = serializeDocument(value);
        } else {
            serialized[key] = value;
        }
    }
    return serialized;
}

/**
 * Handles server-side loading of the page
 * @param {Object} params - Page parameters
 * @return {Object} Loaded data
 */
/** @type {import('./$types').PageServerLoad} */
export async function load({ params }) {
    try {
        const db = client.db('ServiceContracts');
        const computerId = params.id;

        // Validate ObjectId
        if (!ObjectId.isValid(computerId)) {
            throw error(400, 'Invalid computer ID');
        }

        // Get computer details from CustomerComputers
        const computer = await db.collection('CustomerComputers')
            .findOne({ _id: new ObjectId(computerId) });

        if (!computer) {
            throw error(404, 'Computer not found');
        }

        // Build $or array for query
        const orArray = [];
        if (computer.ProductDesignation) {
            orArray.push({ ProductDesignation: computer.ProductDesignation });
        }
        if (computer.ProductPartNumber) {
            orArray.push({ ProductPartNumber: parseInt(computer.ProductPartNumber) });
        }
        let productValidityGroups = [];
        if (orArray.length > 0) {
            productValidityGroups = await db.collection('ProductValidityGroup')
                .find({ $or: orArray })
                .sort({ ProductPartNumber: 1 })
                .toArray();
        }

        // Map field names to match the expected format
        const mappedComputer = {
            ...computer,
            'Product Designation': computer.ProductDesignation || "",
            'Product Part Number': computer.ProductPartNumber,
            'Product Validity Group': computer.ProductValidityGroup || "",
            'Product Name': computer.ProductName,
            'Supplier Product Name': computer.SupplierProductName,
            'Operating System': computer.OperatingSystem,
            'Hours Contract Start': computer.HoursContractStart,
            // Add any missing fields with their original values
            name: computer.name,
            model: computer.model,
            manufacturer: computer.manufacturer,
            serialNumber: computer.serialNumber,
            type: computer.type,
            computerCategory: computer.ComputerCategory
        };

        return {
            computer: serializeDocument(mappedComputer),
            productValidityGroups: productValidityGroups.map(group => serializeDocument(group)),
            productValidityGroup: computer.ProductValidityGroup || (productValidityGroups[0] ? productValidityGroups[0].ProductValidityGroup : ''),
            success: true
        };

    } catch (err) {
        console.error('Error loading computer:', err);
        throw error(500, err instanceof Error ? err.message : 'Internal server error');
    }
}
