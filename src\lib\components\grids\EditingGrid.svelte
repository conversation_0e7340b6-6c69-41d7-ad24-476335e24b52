<script lang="ts">
  export let selectedCell: { year: number; month: number; hours: number; activity: string; hasFixed: boolean } | null = null;
  export let editingHours = '';
  export let editingActivity = '';
  export let editingHasFixed = false;
  export let saving = false;
  export let activities: string[] = [];
  
  export function handleCancel() {
    selectedCell = null;
  }
</script>

<div class="editing-grid">
  {#if selectedCell}
    <div class="edit-form">
      <div class="form-item cell-info">Cell: {selectedCell.year}-{selectedCell.month}</div>
      <div class="form-item">
        <input
          type="number"
          bind:value={editingHours}
          min="0"
          max="999"
          class="hours-input"
          placeholder="Hours"
        />
      </div>
      <div class="form-item">
        <select 
          bind:value={editingActivity} 
          class="activity-select"
        >
          <option value="">None</option>
          {#each activities as activity}
            <option value={activity}>{activity}</option>
          {/each}
        </select>
      </div>
      <div class="form-item fixed-container">
        <label class="fixed-label">
          <input
            type="checkbox"
            bind:checked={editingHasFixed}
          />
          <span>Fixed</span>
        </label>
      </div>
      <div class="form-item actions">
        <button class="cancel-btn" on:click={handleCancel}>Cancel</button>
        <button 
          class="save-btn" 
          on:click
          disabled={saving}
        >
          {saving ? 'Saving...' : 'Save'}
        </button>
      </div>
    </div>
  {/if}
</div>

<style>
  .editing-grid {
    margin-top: 0.5rem;
  }

  .edit-form {
    display: flex;
    align-items: stretch;
    gap: 0.5rem;
    padding: 0.5rem;
    background: #f5f5f5;
    border-radius: 4px;
  }

  .form-item {
    min-width: 100px;
    display: flex;
    align-items: center;
    height: 32px;
  }

  .cell-info {
    font-size: 0.9rem;
    color: #666;
    white-space: nowrap;
    padding: 0 0.5rem;
  }

  .hours-input,
  .activity-select {
    width: 100%;
    height: 100%;
    padding: 0 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    background: white;
  }

  .fixed-container {
    min-width: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
  }

  .fixed-label {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.9rem;
    color: #666;
    white-space: nowrap;
    cursor: pointer;
    height: 100%;
    padding: 0 0.5rem;
  }

  .fixed-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
  }

  .actions {
    display: flex;
    gap: 0.5rem;
    margin-left: auto;
    min-width: 160px;
    justify-content: flex-end;
  }

  button {
    height: 100%;
    padding: 0 1rem;
    border: none;
    border-radius: 4px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: background-color 0.2s;
    min-width: 70px;
  }

  .cancel-btn {
    background: #e0e0e0;
    color: #333;
  }

  .cancel-btn:hover {
    background: #d0d0d0;
  }

  .save-btn {
    background: #4caf50;
    color: white;
  }

  .save-btn:hover:not(:disabled) {
    background: #45a049;
  }

  .save-btn:disabled {
    background: #cccccc;
    cursor: not-allowed;
  }
</style>
