<script lang="ts">
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import BulkSetGrid from '$lib/components/grids/BulkSetGrid.svelte';

  const activities = ['Contract', 'Delivery', 'Purchase', 'Running'];
  const computerId = $page.params.id;
  let saving = false;

  async function handleBulkSet(data: {
    startYear: number;
    startMonth: number;
    endYear: number;
    endMonth: number;
    hours: number;
    activity: string;
  }) {
    try {
      saving = true;
      const response = await fetch(`/computer-id/${computerId}/service-activities/service-planning`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...data,
          computerId
        })
      });

      if (!response.ok) {
        throw new Error('Failed to set bulk values');
      }

      const result = await response.json();
      if (result.success) {
        // Go back to service planning
        goto(`/computer-id/${computerId}/service-activities/service-planning`);
      } else {
        throw new Error(result.error || 'Failed to set bulk values');
      }
    } catch (error) {
      console.error('Failed to set bulk values:', error);
      alert('Failed to set bulk values. Please try again.');
    } finally {
      saving = false;
    }
  }

  function handleCancel() {
    goto(`/computer-id/${computerId}/service-activities/service-planning`);
  }
</script>

<div class="bulk-set-window">
  <header class="header">
    <h1>Set Value Range</h1>
    <button class="close-btn" on:click={handleCancel}>&times;</button>
  </header>

  <div class="content">
    <BulkSetGrid 
      {activities}
      showInWindow={true}
      {saving}
      onSetValue={handleBulkSet}
      onCancel={handleCancel}
    />
  </div>
</div>

<style>
  .bulk-set-window {
    max-width: 800px;
    margin: 2rem auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: #2196f3;
    color: white;
  }

  h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: normal;
  }

  .close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    line-height: 1;
  }

  .close-btn:hover {
    opacity: 0.8;
  }

  .content {
    padding: 1.5rem;
  }
</style>
