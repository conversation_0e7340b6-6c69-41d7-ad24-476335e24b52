// Script to update QuotationRows collection with financial fields
const { MongoClient } = require('mongodb');

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';
const collectionName = 'QuotationRows';

async function updateQuotationRowsSchema() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(dbName);
    const collection = db.collection(collectionName);
    
    // Get all existing rows
    const rows = await collection.find({}).toArray();
    console.log(`Found ${rows.length} rows in QuotationRows collection`);
    
    // Define financial fields to add with default values
    const financialFields = {
      // Cost fields
      ListPrice: 0,
      DiscountPercentage: 0,
      DiscountAmount: 0,
      NetPrice: 0,
      
      // Tax fields
      TaxRate: 0,
      TaxAmount: 0,
      
      // Margin fields
      CostPrice: 0,
      MarginPercentage: 0,
      MarginAmount: 0,
      
      // Currency fields
      Currency: 'GBP',
      ExchangeRate: 1,
      
      // Billing fields
      BillingFrequency: 'Monthly', // Monthly, Quarterly, Annually, One-time
      BillingCycle: 12, // Number of billing cycles
      
      // Totals
      TotalPrice: 0, // NetPrice + TaxAmount
      
      // Flags
      IsRecurring: false,
      IsTaxable: true
    };
    
    // Update each row with new fields if they don't exist
    const updatePromises = rows.map(async (row) => {
      const updateFields = {};
      
      // Only add fields that don't already exist
      Object.entries(financialFields).forEach(([field, defaultValue]) => {
        if (row[field] === undefined) {
          updateFields[field] = defaultValue;
        }
      });
      
      // If there are fields to update
      if (Object.keys(updateFields).length > 0) {
        return collection.updateOne(
          { _id: row._id },
          { $set: updateFields }
        );
      }
      
      return Promise.resolve();
    });
    
    const results = await Promise.all(updatePromises);
    const modifiedCount = results.reduce((count, result) => {
      return count + (result?.modifiedCount || 0);
    }, 0);
    
    console.log(`Updated ${modifiedCount} rows with new financial fields`);
    
  } catch (error) {
    console.error('Error updating QuotationRows schema:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

// Run the update function
updateQuotationRowsSchema()
  .then(() => console.log('Schema update completed'))
  .catch(err => console.error('Schema update failed:', err));
