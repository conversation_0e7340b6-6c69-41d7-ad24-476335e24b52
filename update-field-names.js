import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function updateFieldNames() {
    try {
        await client.connect();
        const db = client.db('ServiceContracts');
        
        // List of collections to update
        const collectionsToUpdate = [
            'BaseServices',
            'PartNumbersServiceCodeAction',
            'ProductValidityGroup',
            'ServiceCodeHeader'
        ];

        for (const collectionName of collectionsToUpdate) {
            console.log(`\nProcessing collection: ${collectionName}`);
            const collection = db.collection(collectionName);

            // First check if the collection has any documents with the old field name
            const sampleDoc = await collection.findOne({ 'Product Validity Group': { $exists: true } });
            
            if (sampleDoc) {
                console.log(`Updating documents in ${collectionName}...`);
                
                // Update all documents in the collection
                const result = await collection.updateMany(
                    { 'Product Validity Group': { $exists: true } },
                    [
                        {
                            $addFields: {
                                'ProductValidityGroup': '$Product Validity Group'
                            }
                        },
                        {
                            $project: {
                                'Product Validity Group': 0
                            }
                        }
                    ]
                );

                console.log(`Modified ${result.modifiedCount} documents in ${collectionName}`);
            } else {
                console.log(`No documents with 'Product Validity Group' field found in ${collectionName}`);
            }
        }

        // Verify the updates
        console.log('\nVerifying updates...');
        for (const collectionName of collectionsToUpdate) {
            const collection = db.collection(collectionName);
            
            // Check for any remaining documents with old field name
            const oldFieldCount = await collection.countDocuments({ 'Product Validity Group': { $exists: true } });
            
            // Check for documents with new field name
            const newFieldCount = await collection.countDocuments({ 'ProductValidityGroup': { $exists: true } });
            
            console.log(`\n${collectionName} verification:`);
            console.log(`- Documents with old field name: ${oldFieldCount}`);
            console.log(`- Documents with new field name: ${newFieldCount}`);
        }

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await client.close();
    }
}

updateFieldNames();
