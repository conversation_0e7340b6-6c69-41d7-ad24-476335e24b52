<script>
  import { fade, fly } from 'svelte/transition';

  /**
   * @typedef {Object} ProductValidityGroup
   * @property {string} Id
   * @property {string} ProductGroupId
   * @property {string} ProductPartNumber
   * @property {string} ProductDesignation
   * @property {string} ProductName
   * @property {string} Description
   */

  /** @type {ProductValidityGroup[]} */
  export let products = [];

  let expandedGroups = new Set();

  // Group products by ProductDesignation
  $: groupedProducts = products.reduce((groups, product) => {
    const designation = product.ProductDesignation || 'No Designation';
    if (!groups[designation]) {
      groups[designation] = [];
    }
    groups[designation].push(product);
    return groups;
  }, /** @type {Record<string, ProductValidityGroup[]>} */ ({}));

  // Sort groups by ProductDesignation alphabetically
  $: sortedGroups = Object.entries(groupedProducts).sort(([a], [b]) =>
    a.localeCompare(b)
  );

  function toggleGroup(groupId) {
    if (expandedGroups.has(groupId)) {
      expandedGroups.delete(groupId);
    } else {
      expandedGroups.add(groupId);
    }
    expandedGroups = expandedGroups; // Trigger reactivity
  }

  function expandAll() {
    expandedGroups = new Set(Object.keys(groupedProducts));
  }

  function collapseAll() {
    expandedGroups = new Set();
  }

  function viewProductDetails(product) {
    // You can implement navigation to a detail page here
    console.log('View details for product:', product);
  }

  function handleKeydown(event, groupId) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      toggleGroup(groupId);
    }
  }
</script>

{#if sortedGroups.length === 0}
  <div class="empty-state" transition:fade>
    <div class="empty-icon">📦</div>
    <h3>No product groups found</h3>
    <p>Try adjusting your filters to see more results.</p>
  </div>
{:else}
  <!-- Control Buttons -->
  <div class="controls" transition:fade>
    <div class="controls-left">
      <span class="group-count">
        {sortedGroups.length} group{sortedGroups.length !== 1 ? 's' : ''} • {products.length} product{products.length !== 1 ? 's' : ''}
      </span>
    </div>
    <div class="controls-right">
      <button class="control-btn" on:click={expandAll}>
        📂 Expand All
      </button>
      <button class="control-btn" on:click={collapseAll}>
        📁 Collapse All
      </button>
    </div>
  </div>

  <!-- Product Groups Grid -->
  <div class="product-groups-grid">
    {#each sortedGroups as [designation, designationProducts], index}
      <div class="product-group-card" in:fly={{ y: 20, duration: 300, delay: index * 50 }}>
        <!-- Group Header -->
        <div class="group-header" on:click={() => toggleGroup(designation)} on:keydown={(e) => handleKeydown(e, designation)} role="button" tabindex="0">
          <div class="group-info">
            <h2>
              <span class="group-icon">🏷️</span>
              {designation === 'No Designation' ? 'No Designation' : `Designation: ${designation}`}
            </h2>
            <div class="group-meta">
              <span class="product-count">{designationProducts.length} product{designationProducts.length !== 1 ? 's' : ''}</span>
              {#if designationProducts[0]?.ProductGroupId}
                <span class="group-badge">Group {designationProducts[0].ProductGroupId}</span>
              {/if}
            </div>
          </div>
          <div class="expand-icon" class:expanded={expandedGroups.has(designation)}>
            {expandedGroups.has(designation) ? '▼' : '▶'}
          </div>
        </div>

        <!-- Group Content -->
        {#if expandedGroups.has(designation)}
          <div class="group-content" transition:fly={{ y: -10, duration: 200 }}>
            <div class="product-table-container">
              <table>
                <thead>
                  <tr>
                    <th>ID</th>
                    <th>Part Number</th>
                    <th>Product Name</th>
                    <th>Group ID</th>
                    <th>Description</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {#each designationProducts as product, productIndex}
                    <tr in:fade={{ duration: 200, delay: productIndex * 30 }}>
                      <td>
                        <span class="id-badge">{product.Id}</span>
                      </td>
                      <td>
                        <span class="part-number">{product.ProductPartNumber || 'N/A'}</span>
                      </td>
                      <td>
                        <span class="product-name">{product.ProductName || 'N/A'}</span>
                      </td>
                      <td>
                        {#if product.ProductGroupId}
                          <span class="group-tag">{product.ProductGroupId}</span>
                        {:else}
                          <span class="na-text">N/A</span>
                        {/if}
                      </td>
                      <td>
                        <span class="description">{product.Description || 'No description'}</span>
                      </td>
                      <td>
                        <button
                          class="view-button"
                          on:click={() => viewProductDetails(product)}
                          title="View product details"
                        >
                          👁️ View
                        </button>
                      </td>
                    </tr>
                  {/each}
                </tbody>
              </table>
            </div>
          </div>
        {/if}
      </div>
    {/each}
  </div>
{/if}

<style>
  /* Empty State */
  .empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #718096;
  }

  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.6;
  }

  .empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #4a5568;
  }

  .empty-state p {
    font-size: 1.1rem;
    margin: 0;
  }

  /* Controls */
  .controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    backdrop-filter: blur(10px);
  }

  .controls-left {
    display: flex;
    align-items: center;
  }

  .group-count {
    font-weight: 600;
    color: #4a5568;
    font-size: 1rem;
  }

  .controls-right {
    display: flex;
    gap: 0.75rem;
  }

  .control-btn {
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    font-size: 0.9rem;
  }

  .control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  }

  /* Product Groups Grid */
  .product-groups-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(600px, 1fr));
    gap: 1.5rem;
    width: 100%;
  }

  @media (max-width: 767px) {
    .product-groups-grid {
      grid-template-columns: 1fr;
    }

    .controls {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;
    }

    .controls-right {
      justify-content: center;
    }
  }

  /* Product Group Card */
  .product-group-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .product-group-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  /* Group Header */
  .group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    cursor: pointer;
    transition: background 0.3s ease;
  }

  .group-header:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a42a0);
  }

  .group-info h2 {
    margin: 0 0 0.5rem 0;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .group-icon {
    font-size: 1.2rem;
  }

  .group-meta {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
  }

  .product-count {
    font-size: 0.9rem;
    opacity: 0.9;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
  }

  .group-badge {
    background: rgba(255, 255, 255, 0.9);
    color: #667eea;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
  }

  .expand-icon {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
    opacity: 0.8;
  }

  .expand-icon.expanded {
    transform: rotate(0deg);
  }

  /* Group Content */
  .group-content {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
  }

  .product-table-container {
    padding: 0;
    overflow-x: auto;
  }

  /* Table Styles */
  table {
    width: 100%;
    border-collapse: collapse;
  }

  th, td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
  }

  th {
    background: #f8fafc;
    font-weight: 600;
    color: #4a5568;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  tbody tr {
    transition: background-color 0.2s ease;
  }

  tbody tr:hover {
    background-color: #f7fafc;
  }

  tbody tr:last-child td {
    border-bottom: none;
  }

  /* Table Cell Content */
  .id-badge {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
  }

  .part-number {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #2d3748;
  }

  .product-name {
    font-weight: 500;
    color: #4a5568;
  }

  .group-tag {
    background: #fff5f5;
    color: #c53030;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid #fed7d7;
  }

  .na-text {
    color: #a0aec0;
    font-style: italic;
  }

  .description {
    color: #718096;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* View Button */
  .view-button {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .view-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
  }

  /* Responsive Table */
  @media (max-width: 1024px) {
    .product-table-container {
      font-size: 0.9rem;
    }

    th, td {
      padding: 0.75rem 0.5rem;
    }

    .description {
      max-width: 150px;
    }
  }

  @media (max-width: 768px) {
    .group-meta {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }

    .product-table-container {
      font-size: 0.8rem;
    }

    th, td {
      padding: 0.5rem 0.25rem;
    }

    .description {
      max-width: 100px;
    }

    .view-button {
      padding: 0.4rem 0.8rem;
      font-size: 0.8rem;
    }
  }
</style>
