import { json } from '@sveltejs/kit';
import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/** @type {import('./$types').RequestHandler} */
export async function GET({ url }) {
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('ServiceCodeHeader');
    
    // Parse query parameters
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const skip = (page - 1) * limit;
    
    // Build filter based on query parameters
    const filter = {};
    
    // Add filters for each field if provided in the query
    for (const [key, value] of url.searchParams.entries()) {
      // Skip pagination parameters
      if (['page', 'limit'].includes(key)) continue;
      
      if (value) {
        // Use regex for string fields to enable partial matching
        filter[key] = { $regex: value, $options: 'i' };
      }
    }
    
    console.log('Filter:', filter);
    
    // Get total count for pagination
    const totalItems = await collection.countDocuments(filter);
    
    // Get items with pagination and sorting
    const items = await collection.find(filter)
      .sort({ _id: 1 })
      .skip(skip)
      .limit(limit)
      .toArray();
    
    console.log(`Found ${items.length} items out of ${totalItems} total`);
    
    return json({
      items,
      totalItems,
      page,
      limit,
      totalPages: Math.ceil(totalItems / limit)
    });
  } catch (error) {
    console.error('Error fetching data:', error);
    return json({ error: 'Failed to fetch data' }, { status: 500 });
  }
}

/** @type {import('./$types').RequestHandler} */
export async function POST({ request }) {
  try {
    const data = await request.json();
    
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('ServiceCodeHeader');
    
    // Add timestamps
    data.createdAt = new Date();
    data.updatedAt = new Date();
    
    const result = await collection.insertOne(data);
    
    return json({ 
      success: true, 
      id: result.insertedId,
      message: 'Item created successfully' 
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating item:', error);
    return json({ error: 'Failed to create item' }, { status: 500 });
  }
}
