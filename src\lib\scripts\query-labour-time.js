import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function queryLabourTime(computerCategory) {
    try {
        await client.connect();
        const db = client.db('ServiceContracts');
        const collection = db.collection('LabourTime');

        // Query for labour time records matching the computer category
        const records = await collection.find({
            ComputerCategory: computerCategory
        }).sort({
            'Service Code': 1  // Sort by Service Code
        }).toArray();

        if (records.length === 0) {
            console.log(`No labour time records found for computer category ${computerCategory}`);
            return;
        }

        console.log(`\nLabour Time Records for Computer Category ${computerCategory}:`);
        console.log('----------------------------------------');
        records.forEach(record => {
            console.log(`
Service Code: ${record['Service Code']}
Service Description: ${record['Service Description']}
VST Code: ${record['VST Code']}
VST Hours: ${record['VST Hours']}
Service Phase: ${record.ServicePhase}
----------------------------------------`);
        });

    } catch (error) {
        console.error('Error querying labour time records:', error);
    } finally {
        await client.close();
    }
}

// Query for D11 category
queryLabourTime('D11');
