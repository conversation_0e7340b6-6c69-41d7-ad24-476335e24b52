<script lang="ts">
  import { goto } from '$app/navigation';
  let form = {
    computerId: '',
    month: '',
    year: '',
    activity: '',
    hasFixed: false,
    hours: '',
    isIdle: false,
    reportDateTime: ''
  };
  let errorMsg = '';
  let saving = false;

  async function saveNew() {
    saving = true;
    errorMsg = '';
    const hex24 = /^[0-9a-fA-F]{24}$/;
    if (!form.computerId || !hex24.test(form.computerId)) {
      errorMsg = 'Please enter a valid computerId (24 hex chars)';
      saving = false;
      return;
    }
    if (!form.reportDateTime) {
      errorMsg = 'Report Date/Time is required.';
      saving = false;
      return;
    }
    const payload = {};
    Object.entries(form).forEach(([k, v]) => {
      if (v !== '' && v !== null && v !== undefined) {
        if (k.toLowerCase().includes('date') && typeof v === 'string' && v) {
          payload[k] = new Date(v).toISOString();
        } else {
          payload[k] = v;
        }
      }
    });
    try {
      const res = await fetch('/actual-computer-hours', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });
      const result = await res.json();
      if (!res.ok) {
        errorMsg = result.error || 'Failed to create';
        saving = false;
        return;
      }
      if (!result.insertedId) throw new Error('Failed to create');
      // Reset form for adding more
      form = {
        computerId: '',
        month: '',
        year: '',
        activity: '',
        hasFixed: false,
        hours: '',
        isIdle: false,
        reportDateTime: ''
      };
      errorMsg = 'Item added! You can add another.';
    } catch (e) {
      errorMsg = e.message;
    } finally {
      saving = false;
    }
  }
</script>

<div class="ach-add-card">
  <h1>Add Actual Computer Hours</h1>
  <form on:submit|preventDefault={saveNew} class="ach-form">
    <label>Computer ID
      <input type="text" bind:value={form.computerId} required placeholder="24-char ObjectId" maxlength="24" minlength="24" />
    </label>
    <label>Month <input type="number" bind:value={form.month} min="1" max="12" required /></label>
    <label>Year <input type="number" bind:value={form.year} required /></label>
    <label>Activity <input type="text" bind:value={form.activity} /></label>
    <label>Has Fixed <input type="checkbox" bind:checked={form.hasFixed} /></label>
    <label>Hours <input type="number" bind:value={form.hours} required /></label>
    <label>Is Idle <input type="checkbox" bind:checked={form.isIdle} /></label>
    <label>Report Date/Time <input type="datetime-local" bind:value={form.reportDateTime} required /></label>
    <div class="ach-form-actions">
      <button type="submit" disabled={saving}>Add</button>
    </div>
    {#if errorMsg}
      <div class="ach-error">{errorMsg}</div>
    {/if}
  </form>
</div>

<style>
.ach-add-card {
  max-width: 520px;
  margin: 3em auto;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 18px rgba(0,0,0,0.08);
  padding: 2.5em 2em 2em 2em;
}
.ach-form {
  display: flex;
  flex-direction: column;
  gap: 1.2em;
}
.ach-form label {
  display: flex;
  flex-direction: column;
  font-weight: 500;
  color: #2d3a4a;
  gap: 0.3em;
}
.ach-form input, .ach-form select {
  font-size: 1em;
  padding: 0.45em 0.7em;
  border: 1px solid #d2d8e0;
  border-radius: 5px;
  background: #f9f9fb;
}
.ach-form-actions {
  margin-top: 1.5em;
  display: flex;
  gap: 1em;
}
.ach-form button {
  background: #059669;
  color: #fff;
  border: none;
  border-radius: 5px;
  font-weight: 600;
  padding: 0.55em 1.3em;
  font-size: 1.06em;
  cursor: pointer;
  transition: background 0.15s;
}
.ach-form button:disabled {
  background: #c0c0c0;
  cursor: not-allowed;
}
.ach-error {
  color: #d32f2f;
  margin-top: 1em;
}
</style>
