import { MongoClient } from 'mongodb';

// From memory: all collections are in database "ServiceContracts"
const url = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function removeDuplicates() {
    const client = await MongoClient.connect(url);
    try {
        const db = client.db(dbName);
        // From memory: use ProductValidityGroup collection in PascalCase
        const collection = db.collection('ProductValidityGroup');

        // Get all documents grouped by ProductDesignation
        const pipeline = [
            {
                $group: {
                    _id: '$ProductDesignation',
                    firstDoc: { $first: '$$ROOT' },
                    count: { $sum: 1 }
                }
            }
        ];

        const groups = await collection.aggregate(pipeline).toArray();
        console.log(`Found ${groups.length} unique product designations`);

        // Keep track of duplicates removed
        let totalDuplicates = 0;

        // Process each group
        for (const group of groups) {
            if (group.count > 1) {
                // Delete all documents with this ProductDesignation except the first one
                const result = await collection.deleteMany({
                    ProductDesignation: group._id,
                    _id: { $ne: group.firstDoc._id }
                });

                console.log(`Removed ${result.deletedCount} duplicates for ProductDesignation: ${group._id}`);
                totalDuplicates += result.deletedCount;
            }
        }

        console.log(`\nSummary:`);
        console.log(`Total unique product designations: ${groups.length}`);
        console.log(`Total duplicates removed: ${totalDuplicates}`);

    } catch (err) {
        console.error('Error removing duplicates:', err);
    } finally {
        await client.close();
    }
}

// Run the script
removeDuplicates().catch(console.error);
