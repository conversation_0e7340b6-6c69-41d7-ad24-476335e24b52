import { ObjectId } from 'mongodb';

/**
 * Recursively serializes a MongoDB document to make it JSON-safe
 * Converts ObjectIds to strings, Dates to ISO strings, and handles nested objects and arrays
 * @param {Object|null} doc - The document to serialize
 * @returns {Object|null} - The serialized document
 */
export function serializeDocument(doc) {
    if (!doc) return null;
    
    /** @type {Record<string, any>} */
    const serialized = {};
    for (const [key, value] of Object.entries(doc)) {
        if (value instanceof ObjectId) {
            serialized[key] = value.toString();
        } else if (value instanceof Date) {
            serialized[key] = value.toISOString();
        } else if (Array.isArray(value)) {
            serialized[key] = value.map(item => 
                item instanceof ObjectId ? item.toString() : 
                (typeof item === 'object' && item !== null) ? serializeDocument(item) : item
            );
        } else if (typeof value === 'object' && value !== null) {
            serialized[key] = serializeDocument(value);
        } else {
            serialized[key] = value;
        }
    }
    return serialized;
}

/**
 * Serialize an array of documents
 * @param {Array<any>|null|undefined} docs - Array of documents to serialize
 * @returns {Array<any>} - Array of serialized documents
 */
export function serializeDocuments(docs) {
    if (!docs || !Array.isArray(docs)) return [];
    return docs.map(doc => serializeDocument(doc));
}
