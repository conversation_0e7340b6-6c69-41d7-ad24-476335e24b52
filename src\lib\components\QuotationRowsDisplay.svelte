<script lang="ts">
  import QuotationDetailGrid from '$lib/components/grids/QuotationDetailGrid.svelte';
  import { onMount } from 'svelte';
  
  // Props with proper TypeScript typing
  export let quotationRows: Array<{
    _id: string;
    quotationId: string;
    rowType: string;
    rowOrder: number;
    packageId: number;
    packageName: string;
    activity?: string;
    Activity?: string; // Handle both casing possibilities
    cost: number;
    oemImporter: boolean;
    fleetOwner: boolean;
    customerSpecific: boolean;
    required: boolean;
    includeInOffer: boolean;
    createdAt: string;
    updatedAt: string;
    [key: string]: any;
  }> = [];
  export let quotationId: string | null = null;
  export let refreshData: () => Promise<void> = async () => {};
  
  // Local state for editing
  let editingActivity: { [key: string]: boolean } = {};
  let activityValues: { [key: string]: string } = {};
  let isUpdating: { [key: string]: boolean } = {};
  let updateError: string | null = null;
  let updateSuccess: string | null = null;
  
  // Helper function to get activity value from row (handles both casing)
  function getActivityValue(row: any): string {
    // Try lowercase first, then uppercase if not found
    return row.activity || row.Activity || '';
  }
  
  // Initialize activity values from quotationRows
  $: {
    quotationRows.forEach(row => {
      if (!activityValues[row._id] || !editingActivity[row._id]) {
        activityValues[row._id] = getActivityValue(row);
      }
    });
  }
  
  // Group rows by rowType
  $: groupedRows = quotationRows.reduce<Record<string, typeof quotationRows>>((acc, row) => {
    const rowType = row.rowType || 'Other';
    if (!acc[rowType]) {
      acc[rowType] = [];
    }
    acc[rowType].push(row);
    return acc;
  }, {});
  
  // Get all row types
  $: rowTypes = Object.keys(groupedRows);
  
  // Format date for display
  function formatDate(dateString: string | null | undefined): string {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  }
  
  // Format currency
  function formatCurrency(value: number | null | undefined): string {
    if (value === undefined || value === null) return '0.00';
    return Number(value).toFixed(2);
  }
  
  // Toggle editing mode for activity
  function toggleEditActivity(rowId: string): void {
    editingActivity[rowId] = !editingActivity[rowId];
    // Reset to original value if canceling edit
    if (!editingActivity[rowId]) {
      const row = quotationRows.find(r => r._id === rowId);
      if (row) {
        activityValues[rowId] = getActivityValue(row);
      }
    }
  }
  
  // Clear messages after a delay
  function clearMessagesAfterDelay(delay = 5000): void {
    setTimeout(() => {
      updateError = null;
      updateSuccess = null;
    }, delay);
  }
  
  // Save activity value
  async function saveActivity(row: any): Promise<void> {
    try {
      const newActivity = activityValues[row._id] || ''; // Ensure it's not undefined
      const currentActivity = getActivityValue(row);
      
      isUpdating[row._id] = true;
      updateError = null;
      updateSuccess = null;
      
      console.log(`Updating activity for row ${row._id} from "${currentActivity}" to "${newActivity}"`);
      
      // Send update to server
      const response = await fetch('/api/quote-rows/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          rowId: row._id,
          field: 'activity', // Always use lowercase for consistency
          value: newActivity,
          quotationId
        })
      });
      
      const result = await response.json();
      console.log('Update result:', result);
      
      if (!response.ok) {
        throw new Error(result.message || 'Failed to update activity');
      }
      
      if (!result.success) {
        throw new Error(result.message || 'Server reported update failure');
      }
      
      // Update local row data - update both casing variants
      row.activity = newActivity;
      row.Activity = newActivity;
      
      // Exit edit mode
      editingActivity[row._id] = false;
      
      updateSuccess = `Activity updated successfully for ${row.packageName}`;
      clearMessagesAfterDelay();
      
      // Refresh data to ensure we have the latest state
      await refreshData();
    } catch (error) {
      console.error('Error updating activity:', error);
      updateError = error instanceof Error ? error.message : 'Failed to update activity. Please try again.';
      clearMessagesAfterDelay(10000); // Show error for longer
    } finally {
      isUpdating[row._id] = false;
    }
  }
  
  // Handle checkbox toggle
  async function toggleCheckbox(row: any, field: string): Promise<void> {
    try {
      // Toggle the value locally for immediate feedback
      const originalValue = row[field];
      row[field] = !row[field];
      
      isUpdating[row._id] = true;
      updateError = null;
      updateSuccess = null;
      
      console.log(`Toggling ${field} for row ${row._id} from ${originalValue} to ${row[field]}`);
      
      // Send update to server
      const response = await fetch('/api/quote-rows/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          rowId: row._id,
          field,
          value: row[field],
          quotationId // Include quotationId in the request for tracking/logging
        })
      });
      
      const result = await response.json();
      console.log('Update result:', result);
      
      if (!response.ok) {
        throw new Error(result.message || 'Failed to update quote row');
      }
      
      if (!result.success) {
        throw new Error(result.message || 'Server reported update failure');
      }
      
      updateSuccess = `${field} updated successfully for ${row.packageName}`;
      clearMessagesAfterDelay();
      
      // Refresh data to ensure we have the latest state
      await refreshData();
    } catch (error) {
      console.error('Error updating quote row:', error);
      // Revert the local change if the server update failed
      row[field] = !row[field];
      updateError = error instanceof Error ? error.message : 'Failed to update. Please try again.';
      clearMessagesAfterDelay(10000); // Show error for longer
    } finally {
      isUpdating[row._id] = false;
    }
  }
</script>

<div class="quotation-rows-container">
  {#if quotationRows.length === 0}
    <div class="empty-message">No quotation rows found.</div>
  {:else}
    <div class="quotation-header">
      <h2>Quotation ID: {quotationId || 'N/A'}</h2>
      <button type="button" class="refresh-button" on:click={refreshData}>
        Refresh Data
      </button>
    </div>
    
    {#if updateError}
      <div class="error-message">
        <span>Error: {updateError}</span>
        <button type="button" class="close-button" on:click={() => updateError = null}>×</button>
      </div>
    {/if}
    
    {#if updateSuccess}
      <div class="success-message">
        <span>{updateSuccess}</span>
        <button type="button" class="close-button" on:click={() => updateSuccess = null}>×</button>
      </div>
    {/if}
    
    {#each rowTypes as rowType}
      <div class="row-type-section">
        <div class="section-header">
          <h3>{rowType}</h3>
          <span class="count-badge">{groupedRows[rowType].length} items</span>
        </div>
        
        <QuotationDetailGrid>
          <div class="row-grid">
            {#each groupedRows[rowType] as row}
              <div class="row-card">
                <div class="row-header">
                  <h4>Package: {row.packageName || 'N/A'}</h4>
                  <span class="order-badge">Order: {row.rowOrder || 'N/A'}</span>
                </div>
                
                <div class="row-content">
                  <div class="field activity-field">
                    <div class="field-label">Activity</div>
                    {#if editingActivity[row._id]}
                      <div class="activity-edit">
                        <textarea 
                          bind:value={activityValues[row._id]} 
                          class="activity-input"
                          placeholder="Enter activity"
                          rows="3"
                        ></textarea>
                        <div class="activity-buttons">
                          <button 
                            type="button" 
                            class="save-button" 
                            on:click={() => saveActivity(row)}
                            aria-label="Save activity"
                            disabled={isUpdating[row._id]}
                          >
                            {isUpdating[row._id] ? 'Saving...' : 'Save'}
                          </button>
                          <button 
                            type="button" 
                            class="cancel-button" 
                            on:click={() => toggleEditActivity(row._id)}
                            aria-label="Cancel editing"
                            disabled={isUpdating[row._id]}
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    {:else}
                      <div class="activity-display">
                        <div class="field-value activity-text">
                          {#if getActivityValue(row)}
                            {getActivityValue(row)}
                          {:else}
                            <span class="empty-activity">No activity specified</span>
                          {/if}
                        </div>
                        <button 
                          type="button" 
                          class="edit-button" 
                          on:click={() => toggleEditActivity(row._id)}
                          aria-label="Edit activity"
                          disabled={isUpdating[row._id]}
                        >
                          Edit
                        </button>
                      </div>
                    {/if}
                  </div>
                  
                  <div class="field">
                    <div class="field-label">Cost</div>
                    <div class="field-value">{formatCurrency(row.cost)}</div>
                  </div>
                  
                  <div class="field-group">
                    <div class="checkbox-field">
                      <button 
                        type="button"
                        class="checkbox-button" 
                        aria-label="Toggle OEM Importer {row.oemImporter ? 'off' : 'on'}"
                        on:click={() => toggleCheckbox(row, 'oemImporter')}
                        on:keydown={(e) => e.key === 'Enter' && toggleCheckbox(row, 'oemImporter')}
                        disabled={isUpdating[row._id]}
                      >
                        <span class="checkbox-icon checkbox-{row.oemImporter}">
                          {row.oemImporter ? '✓' : '✗'}
                        </span>
                      </button>
                      <span>OEM Importer</span>
                    </div>
                    
                    <div class="checkbox-field">
                      <button 
                        type="button"
                        class="checkbox-button" 
                        aria-label="Toggle Fleet Owner {row.fleetOwner ? 'off' : 'on'}"
                        on:click={() => toggleCheckbox(row, 'fleetOwner')}
                        on:keydown={(e) => e.key === 'Enter' && toggleCheckbox(row, 'fleetOwner')}
                        disabled={isUpdating[row._id]}
                      >
                        <span class="checkbox-icon checkbox-{row.fleetOwner}">
                          {row.fleetOwner ? '✓' : '✗'}
                        </span>
                      </button>
                      <span>Fleet Owner</span>
                    </div>
                  </div>
                  
                  <div class="field-group">
                    <div class="checkbox-field">
                      <button 
                        type="button"
                        class="checkbox-button" 
                        aria-label="Toggle Customer Specific {row.customerSpecific ? 'off' : 'on'}"
                        on:click={() => toggleCheckbox(row, 'customerSpecific')}
                        on:keydown={(e) => e.key === 'Enter' && toggleCheckbox(row, 'customerSpecific')}
                        disabled={isUpdating[row._id]}
                      >
                        <span class="checkbox-icon checkbox-{row.customerSpecific}">
                          {row.customerSpecific ? '✓' : '✗'}
                        </span>
                      </button>
                      <span>Customer Specific</span>
                    </div>
                    
                    <div class="checkbox-field">
                      <button 
                        type="button"
                        class="checkbox-button" 
                        aria-label="Toggle Required {row.required ? 'off' : 'on'}"
                        on:click={() => toggleCheckbox(row, 'required')}
                        on:keydown={(e) => e.key === 'Enter' && toggleCheckbox(row, 'required')}
                        disabled={isUpdating[row._id]}
                      >
                        <span class="checkbox-icon checkbox-{row.required}">
                          {row.required ? '✓' : '✗'}
                        </span>
                      </button>
                      <span>Required</span>
                    </div>
                  </div>
                  
                  <div class="checkbox-field">
                    <button 
                      type="button"
                      class="checkbox-button" 
                      aria-label="Toggle Include In Offer {row.includeInOffer ? 'off' : 'on'}"
                      on:click={() => toggleCheckbox(row, 'includeInOffer')}
                      on:keydown={(e) => e.key === 'Enter' && toggleCheckbox(row, 'includeInOffer')}
                      disabled={isUpdating[row._id]}
                    >
                      <span class="checkbox-icon checkbox-{row.includeInOffer}">
                        {row.includeInOffer ? '✓' : '✗'}
                      </span>
                    </button>
                    <span>Include In Offer</span>
                  </div>
                  
                  <div class="field">
                    <div class="field-label">Created</div>
                    <div class="field-value">{formatDate(row.createdAt)}</div>
                  </div>
                </div>
              </div>
            {/each}
          </div>
        </QuotationDetailGrid>
      </div>
    {/each}
  {/if}
</div>

<style>
  .quotation-rows-container {
    width: 100%;
  }
  
  .quotation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e0e0e0;
  }
  
  .quotation-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: #333;
  }
  
  .refresh-button {
    background-color: #2196f3;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
  }
  
  .refresh-button:hover {
    background-color: #1976d2;
  }
  
  .error-message {
    background-color: #ffebee;
    color: #d32f2f;
    padding: 0.75rem 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .success-message {
    background-color: #e8f5e9;
    color: #2e7d32;
    padding: 0.75rem 1rem;
    border-radius: 4px;
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .close-button {
    background: none;
    border: none;
    color: inherit;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    line-height: 1;
  }
  
  .empty-message {
    padding: 2rem;
    text-align: center;
    background-color: #f5f5f5;
    border-radius: 8px;
    color: #666;
  }
  
  .count-badge {
    background-color: #e0e0e0;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
  }
  
  .row-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
  }
  
  .row-header h4 {
    margin: 0;
    font-size: 1.1rem;
    color: #333;
  }
  
  .order-badge {
    background-color: #2196f3;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
  }
  
  .row-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .activity-field {
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px dashed #e0e0e0;
  }
  
  .activity-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .activity-text {
    font-weight: 500;
    color: #333;
    word-break: break-word;
  }
  
  .empty-activity {
    color: #999;
    font-style: italic;
  }
  
  .activity-edit {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .activity-input {
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 0.9rem;
    width: 100%;
    resize: vertical;
  }
  
  .activity-buttons {
    display: flex;
    gap: 0.5rem;
  }
  
  .edit-button, .save-button, .cancel-button {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    border: none;
    font-size: 0.8rem;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .edit-button {
    background-color: #f5f5f5;
    color: #333;
  }
  
  .edit-button:hover {
    background-color: #e0e0e0;
  }
  
  .save-button {
    background-color: #4caf50;
    color: white;
  }
  
  .save-button:hover {
    background-color: #388e3c;
  }
  
  .cancel-button {
    background-color: #f44336;
    color: white;
  }
  
  .cancel-button:hover {
    background-color: #d32f2f;
  }
  
  button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  .checkbox-button {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .checkbox-icon {
    width: 18px;
    height: 18px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    color: white;
    transition: all 0.2s ease;
  }
  
  .checkbox-button:hover:not(:disabled) .checkbox-icon {
    transform: scale(1.1);
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  }
  
  .checkbox-button:focus {
    outline: 2px solid #2196f3;
    border-radius: 4px;
  }
  
  .checkbox-true {
    background-color: #4caf50;
  }
  
  .checkbox-false {
    background-color: #f44336;
  }
</style>
