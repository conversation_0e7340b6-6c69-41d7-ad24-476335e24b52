<script lang="ts">
    export let show = false;
    export let hours = 0;
    export let month = '';
    export let year = '';
    export let onSave = (hours: number) => {};
    export let onClose = () => {};

    let editedHours = hours;
    let inputElement: HTMLInputElement;

    // Keep editedHours in sync with hours prop
    $: editedHours = hours;

    function handleSubmit() {
        onSave(parseFloat(editedHours) || 0);  // Ensure we pass a valid number
        onClose();
    }

    function handleKeydown(event: KeyboardEvent) {
        if (event.key === 'Escape') {
            onClose();
        }
    }

    $: if (show && inputElement) {
        inputElement.focus();
    }
</script>

{#if show}
<div 
    class="modal-backdrop" 
    on:click={onClose}
    on:keydown={handleKeydown}
    role="dialog"
    aria-modal="true"
    aria-labelledby="modal-title"
    tabindex="-1"
>
    <div 
        class="modal-content" 
        on:click|stopPropagation
        role="document"
    >
        <div class="modal-header">
            <h3 id="modal-title">Edit Hours for {month} {year}</h3>
            <button type="button" class="close-btn" on:click={onClose} aria-label="Close modal">&times;</button>
        </div>
        <form on:submit|preventDefault={handleSubmit}>
            <div class="form-group">
                <label for="hours">Hours:</label>
                <input 
                    type="number" 
                    id="hours" 
                    bind:value={editedHours}
                    bind:this={inputElement}
                    min="0"
                    step="0.1"
                    required
                />
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" on:click={onClose}>Cancel</button>
                <button type="submit" class="btn-primary">Save</button>
            </div>
        </form>
    </div>
</div>
{/if}

<svelte:window on:keydown={handleKeydown}/>

<style>
    .modal-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }

    .modal-content {
        background: #2a2a3e;
        padding: 1.5rem;
        border-radius: 8px;
        width: 90%;
        max-width: 400px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        color: #fff;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    h3 {
        margin: 0;
        font-size: 1.25rem;
        color: #60a5fa;
    }

    .close-btn {
        background: none;
        border: none;
        color: #fff;
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0.25rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    label {
        display: block;
        margin-bottom: 0.5rem;
        color: #e2e8f0;
    }

    input {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid #4b5563;
        border-radius: 4px;
        background: #1f2937;
        color: #fff;
    }

    input:focus {
        outline: 2px solid #60a5fa;
        border-color: #60a5fa;
    }

    .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        margin-top: 2rem;
    }

    .btn-primary, .btn-secondary {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 4px;
        font-weight: 500;
        cursor: pointer;
    }

    .btn-primary {
        background: #2563eb;
        color: #fff;
    }

    .btn-primary:hover {
        background: #1d4ed8;
    }

    .btn-secondary {
        background: #4b5563;
        color: #fff;
    }

    .btn-secondary:hover {
        background: #374151;
    }
</style>
