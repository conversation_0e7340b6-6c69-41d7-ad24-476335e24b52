<script lang="ts">
    import EditActivityModal from '$lib/components/EditActivityModal.svelte';
    import ServiceActivityGrid from '$lib/components/grids/ServiceActivityGrid.svelte';
    import type { Activity, Computer } from '$lib/types/activity';
    
    interface PageData {
        activities: Activity[];
        computer: Computer | null;
        contract: any | null;
    }
    
    export let data: PageData;
    let { activities, computer } = data;
    
    // Modal state
    let showModal = false;
    let selectedActivity: Activity | null = null;

    // Get purchase date and calculate year range
    let purchaseYear = 2025;
    if (computer && computer.purchaseDate) {
        purchaseYear = new Date(computer.purchaseDate).getFullYear();
    }
    const startYear = purchaseYear - 2;
    const endYear = purchaseYear + 10;
    const years: number[] = [];
    for (let y = startYear; y <= endYear; y++) years.push(y);
    const months = [
        'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];

    // Helper: find activity for a cell
    function getCell(year: number, monthIdx: number): Activity | undefined {
        return activities.find((a: Activity) => a.year === year && a.month === monthIdx + 1);
    }

    // Open edit modal for an activity
    function editActivity(activity: Activity): void {
        selectedActivity = activity;
        showModal = true;
    }

    // Handle activity update
    function handleActivityUpdated(event: CustomEvent<Activity>): void {
        const updatedActivity = event.detail;
        
        // Update the activities array with the updated activity
        const index = activities.findIndex((a: Activity) => a._id === updatedActivity._id);
        if (index !== -1) {
            // Create a new array with the updated activity
            const updatedActivities = [...activities];
            updatedActivities[index] = updatedActivity;
            activities = updatedActivities; // This is fine since we're using let
        }
        
        // Close the modal
        showModal = false;
        selectedActivity = null;
    }
</script>

<div class="page-container">
    <h1 class="page-title">Service Activity Schedule</h1>
    
    <div class="computer-info">
        {#if computer}
            <p><strong>Computer:</strong> {computer.name || 'No name'}</p>
            {#if computer.purchaseDate}
                <p><strong>Purchase Date:</strong> {new Date(computer.purchaseDate).toLocaleDateString()}</p>
            {/if}
        {:else}
            <p>Computer information not available</p>
        {/if}
    </div>

    <ServiceActivityGrid {years} {months}>
        <svelte:fragment slot="cell" let:year let:monthIndex>
            {@const cell = getCell(year, monthIndex)}
            {#if cell}
                <div 
                    class="activity-cell {cell.hasFixed ? 'fixed' : ''}"
                    on:click={() => editActivity(cell)}
                    on:keydown={(e) => e.key === 'Enter' && editActivity(cell)}
                    tabindex="0"
                    role="button"
                    aria-label="Edit activity for {months[monthIndex]} {year}"
                >
                    <div class="cell-content">
                        <span class="value">{cell.value || 0}</span>
                        {#if cell.activity}
                            <span class="activity">{cell.activity}</span>
                        {/if}
                        {#if cell.hasFixed}
                            <span class="fixed-indicator">*</span>
                        {/if}
                    </div>
                </div>
            {:else}
                <div class="empty-cell">-</div>
            {/if}
        </svelte:fragment>
    </ServiceActivityGrid>
</div>

<EditActivityModal 
    activity={selectedActivity}
    show={showModal}
    on:close={() => { showModal = false; selectedActivity = null; }}
    on:updated={handleActivityUpdated}
/>

<style>
.page-container {
    max-width: 100%;
    margin: 2rem auto;
    padding: 0 1rem;
}

.page-title {
    color: #60a5fa;
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
}

.computer-info {
    background: #1e293b;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.computer-info p {
    margin: 0.5rem 0;
    color: #e2e8f0;
}

.computer-info strong {
    color: #60a5fa;
}

.activity-cell {
    background: #222b3a;
    color: #eaf1fb;
    text-align: center;
    min-height: 40px;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
    padding: 0.5rem 0.25rem;
}

.activity-cell.fixed {
    background: #2563eb;
    color: #fff;
}

.activity-cell:hover {
    background: #2d3748;
}

.activity-cell.fixed:hover {
    background: #1d4ed8;
}

.cell-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.value {
    font-size: 1.1rem;
    font-weight: 500;
}

.activity {
    font-size: 0.75rem;
    color: #94a3b8;
    margin-top: 0.2rem;
    max-width: 58px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.fixed-indicator {
    position: absolute;
    top: 2px;
    right: 4px;
    font-size: 0.85rem;
    color: #a5e0ff;
}

.empty-cell {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #1a202c;
    width: 100%;
    height: 100%;
    border-radius: 4px;
    color: #4b5563;
}
</style>
