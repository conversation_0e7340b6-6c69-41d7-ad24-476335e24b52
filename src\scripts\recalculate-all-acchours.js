// Script to recalculate AccHours for all computers in the Workload collection
import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function recalculateAccHoursForComputer(db, computerId) {
    console.log(`🔄 Recalculating AccHours for computer: ${computerId}`);
    
    // Get all workload entries for this computer, sorted chronologically
    const workloadEntries = await db.collection('Workload')
        .find({ computerId })
        .sort({ year: 1, month: 1 })
        .toArray();
    
    if (workloadEntries.length === 0) {
        console.log(`  ⚠️ No workload entries found for computer ${computerId}`);
        return 0;
    }
    
    let accumulatedHours = 0;
    const updates = [];
    
    // Calculate accumulated hours for each entry
    for (const entry of workloadEntries) {
        accumulatedHours += entry.hours || 0;
        
        // Prepare update operation
        updates.push({
            updateOne: {
                filter: { _id: entry._id },
                update: { 
                    $set: { 
                        accHours: accumulatedHours,
                        updatedAt: new Date()
                    }
                }
            }
        });
    }
    
    // Execute bulk update if we have updates
    if (updates.length > 0) {
        await db.collection('Workload').bulkWrite(updates);
        console.log(`  ✅ Updated AccHours for ${updates.length} workload entries (Total: ${accumulatedHours} hours)`);
    }
    
    return accumulatedHours;
}

async function recalculateAllAccHours() {
    const client = new MongoClient(uri);
    
    try {
        console.log('🚀 Starting AccHours recalculation for all computers...');
        await client.connect();
        const db = client.db(dbName);
        
        // Get all unique computer IDs from the Workload collection
        const uniqueComputerIds = await db.collection('Workload').distinct('computerId');
        console.log(`📊 Found ${uniqueComputerIds.length} unique computers with workload data`);
        
        let totalComputers = 0;
        let totalHours = 0;
        
        for (const computerId of uniqueComputerIds) {
            try {
                const computerHours = await recalculateAccHoursForComputer(db, computerId);
                totalComputers++;
                totalHours += computerHours;
            } catch (error) {
                console.error(`❌ Error processing computer ${computerId}:`, error);
            }
        }
        
        console.log('\n📈 Recalculation Summary:');
        console.log(`  ✅ Processed: ${totalComputers} computers`);
        console.log(`  ⏱️ Total accumulated hours across all computers: ${totalHours.toLocaleString()}`);
        console.log(`  📅 Completed at: ${new Date().toISOString()}`);
        
        // Verify the results by checking a few random entries
        console.log('\n🔍 Verification - Sample AccHours values:');
        const sampleEntries = await db.collection('Workload')
            .find({ accHours: { $exists: true } })
            .sort({ accHours: -1 })
            .limit(5)
            .toArray();
            
        sampleEntries.forEach((entry, index) => {
            console.log(`  ${index + 1}. Computer ${entry.computerId}: ${entry.year}/${entry.month} - ${entry.hours}h (AccHours: ${entry.accHours})`);
        });
        
    } catch (error) {
        console.error('❌ Error during AccHours recalculation:', error);
    } finally {
        await client.close();
        console.log('\n🔌 Database connection closed');
    }
}

// Run the script
console.log('🎯 AccHours Recalculation Script');
console.log('================================');
recalculateAllAccHours()
    .then(() => {
        console.log('\n✅ Script completed successfully!');
        process.exit(0);
    })
    .catch((error) => {
        console.error('\n❌ Script failed:', error);
        process.exit(1);
    });
