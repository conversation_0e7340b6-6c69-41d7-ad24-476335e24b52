import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/**
 * @param {string | null} partNumber
 * @param {string | null} designation
 */
async function queryProductValidityGroup(partNumber = null, designation = null) {
    try {
        await client.connect();
        const db = client.db('ServiceContracts');
        const collection = db.collection('ProductValidityGroupPartNumber');

        /** @type {{ProductPartNumber?: number, ProductDesignation?: string}} */
        const query = {};
        if (partNumber) {
            query.ProductPartNumber = parseInt(partNumber);
        }
        if (designation) {
            query.ProductDesignation = designation;
        }

        const items = await collection.find(query)
            .sort({
                ProductGroupId: 1,    // Primary sort
                Id: 1,               // Secondary sort
                ProductPartNumber: 1  // Final sort - unique per product
            })
            .toArray();

        console.log(`Found ${items.length} items:`);
        items.forEach(item => {
            console.log(`\nProduct: ${item.ProductName}`);
            console.log(`Part Number: ${item.ProductPartNumber}`);
            console.log(`Designation: ${item.ProductDesignation}`);
            console.log(`Group ID: ${item.ProductGroupId}`);
            console.log(`ID: ${item.Id}`);
            console.log(`Validity Group: ${item.ProductValidityGroup}`);
            console.log('------------------------');
        });

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await client.close();
    }
}

// Get command line arguments
const partNumber = process.argv[2] || null;
const designation = process.argv[3] || null;

queryProductValidityGroup(partNumber, designation);
