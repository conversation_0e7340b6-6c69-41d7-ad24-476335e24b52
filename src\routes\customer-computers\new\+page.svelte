<script lang="ts">
  import { enhance } from '$app/forms';
  import { goto } from '$app/navigation';
  
  // Get data from server
  export let data;
  
  // Component state
  let customers = data.customers || [];
  let selectedCustomer = data.selectedCustomer;
  let selectedCustomerId = selectedCustomer?._id || '';
  let isSubmitting = false;
  let formError = '';
  
  // Form data
  let formData = {
    name: '',
    serialNumber: '',
    model: '',
    productType: '',
    productDesignation: '',
    engineType: '',
    operatingHours: 0,
    installationDate: '',
    manufactureDate: '',
    isActive: true,
    notes: ''
  };
  
  // Handle form submission result
  function handleFormResult(result: any) {
    isSubmitting = false;
    
    if (!result.success) {
      formError = result.error || 'An error occurred';
    }
  }
  
  // Handle customer selection
  function handleCustomerChange() {
    // Update URL with selected customer ID
    const url = new URL(window.location.href);
    if (selectedCustomerId) {
      url.searchParams.set('customerId', selectedCustomerId);
      history.pushState({}, '', url);
      
      // Reload the page to get the selected customer data
      window.location.reload();
    }
  }
</script>

<div class="container">
  <header>
    <div class="header-content">
      <h1>Add New Computer</h1>
      {#if selectedCustomer}
        <div class="customer-info">
          <span class="customer-name">{selectedCustomer.companyName || selectedCustomer.name}</span>
          <span class="customer-type {selectedCustomer.type.toLowerCase().replace(' ', '-')}">
            {selectedCustomer.type}
          </span>
        </div>
      {/if}
    </div>
    
    <div class="header-actions">
      <a href="/customer-computers" class="back-button">
        ← Back to List
      </a>
    </div>
  </header>
  
  {#if formError}
    <div class="alert error">
      {formError}
      <button class="close-alert" on:click={() => formError = ''}>×</button>
    </div>
  {/if}
  
  {#if !selectedCustomer}
    <div class="customer-selection">
      <h2>Select a Customer</h2>
      <p>Please select a customer to add a computer to:</p>
      
      <div class="form-group">
        <label for="customerId">Customer</label>
        <select 
          id="customerId"
          bind:value={selectedCustomerId}
          on:change={handleCustomerChange}
          class="customer-select"
        >
          <option value="">-- Select a Customer --</option>
          {#each customers as customer}
            <option value={customer._id}>
              {customer.companyName || customer.name} ({customer.type || 'Retail'})
            </option>
          {/each}
        </select>
      </div>
      
      {#if customers.length === 0}
        <div class="empty-state">
          <p>No customers found. Please add a customer first.</p>
          <a href="/customers" class="btn-primary">Go to Customers</a>
        </div>
      {/if}
    </div>
  {:else}
    <div class="edit-form">
      <form 
        method="POST" 
        action="?/addComputer"
        use:enhance={() => {
          isSubmitting = true;
          return async ({ result }) => {
            handleFormResult(result.data);
          };
        }}
      >
        <input type="hidden" name="customerId" value={selectedCustomerId} />
        
        <div class="form-grid">
          <div class="form-group">
            <label for="name">Name</label>
            <input 
              type="text"
              id="name"
              name="name"
              bind:value={formData.name}
              placeholder="Generated from model and serial if empty"
            />
          </div>
          
          <div class="form-group">
            <label for="serialNumber">Serial Number*</label>
            <input 
              type="text"
              id="serialNumber"
              name="serialNumber"
              bind:value={formData.serialNumber}
              required
            />
          </div>
          
          <div class="form-group">
            <label for="model">Model*</label>
            <input 
              type="text"
              id="model"
              name="model"
              bind:value={formData.model}
              required
            />
          </div>
          
          <div class="form-group">
            <label for="productType">Product Type*</label>
            <input 
              type="text"
              id="productType"
              name="productType"
              bind:value={formData.productType}
              required
            />
          </div>
          
          <div class="form-group">
            <label for="productDesignation">Product Designation</label>
            <input 
              type="text"
              id="productDesignation"
              name="productDesignation"
              bind:value={formData.productDesignation}
            />
          </div>
          
          <div class="form-group">
            <label for="engineType">Engine Type</label>
            <input 
              type="text"
              id="engineType"
              name="engineType"
              bind:value={formData.engineType}
            />
          </div>
          
          <div class="form-group">
            <label for="operatingHours">Operating Hours*</label>
            <input 
              type="number"
              id="operatingHours"
              name="operatingHours"
              bind:value={formData.operatingHours}
              min="0"
              required
            />
          </div>
          
          <div class="form-group">
            <label for="installationDate">Installation Date</label>
            <input 
              type="date"
              id="installationDate"
              name="installationDate"
              bind:value={formData.installationDate}
            />
          </div>
          
          <div class="form-group">
            <label for="manufactureDate">Manufacture Date</label>
            <input 
              type="date"
              id="manufactureDate"
              name="manufactureDate"
              bind:value={formData.manufactureDate}
            />
          </div>
          
          <div class="form-group">
            <label class="checkbox-label">
              <input 
                type="checkbox"
                name="isActive"
                bind:checked={formData.isActive}
              />
              Active
            </label>
          </div>
          
          <div class="form-group full-width">
            <label for="notes">Notes</label>
            <textarea 
              id="notes"
              name="notes"
              bind:value={formData.notes}
              rows="3"
            ></textarea>
          </div>
        </div>
        
        <div class="form-actions">
          <a href="/customer-computers" class="btn-secondary">Cancel</a>
          <button type="submit" class="btn-primary" disabled={isSubmitting}>
            {isSubmitting ? 'Creating...' : 'Create Computer'}
          </button>
        </div>
      </form>
    </div>
  {/if}
</div>

<style>
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
  }
  
  header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .header-content h1 {
    margin: 0 0 0.5rem;
    font-size: 1.8rem;
    color: #2d3748;
  }
  
  .customer-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }
  
  .customer-name {
    font-size: 1rem;
    color: #4a5568;
  }
  
  .customer-type {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    background-color: #ebf8ff;
    color: #2b6cb0;
  }
  
  .header-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
  }
  
  .back-button {
    padding: 0.5rem 1rem;
    background: none;
    border: none;
    color: #4299e1;
    cursor: pointer;
    font-size: 1rem;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .back-button:hover {
    text-decoration: underline;
  }
  
  .alert {
    padding: 0.75rem 1rem;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .alert.error {
    background-color: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
  }
  
  .close-alert {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: inherit;
  }
  
  .customer-selection {
    background-color: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .customer-selection h2 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.25rem;
    color: #2d3748;
  }
  
  .customer-select {
    width: 100%;
    max-width: 500px;
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    font-size: 0.9rem;
  }
  
  .empty-state {
    margin-top: 1.5rem;
    text-align: center;
    color: #718096;
  }
  
  .edit-form {
    background-color: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }
  
  .form-group {
    margin-bottom: 1rem;
  }
  
  .form-group.full-width {
    grid-column: 1 / -1;
  }
  
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #4a5568;
  }
  
  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
  }
  
  input[type="text"],
  input[type="number"],
  input[type="date"],
  select,
  textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    font-size: 0.875rem;
  }
  
  input[type="checkbox"] {
    margin: 0;
  }
  
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    margin-top: 1.5rem;
  }
  
  .btn-primary,
  .btn-secondary {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.25rem;
    cursor: pointer;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    text-align: center;
  }
  
  .btn-primary {
    background-color: #4299e1;
    color: white;
  }
  
  .btn-primary:hover {
    background-color: #3182ce;
  }
  
  .btn-primary:disabled {
    background-color: #a0aec0;
    cursor: not-allowed;
  }
  
  .btn-secondary {
    background-color: #e2e8f0;
    color: #4a5568;
  }
  
  .btn-secondary:hover {
    background-color: #cbd5e0;
  }
</style>
