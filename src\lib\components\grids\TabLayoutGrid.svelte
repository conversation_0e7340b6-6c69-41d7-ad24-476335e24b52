<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  import { createEventDispatcher } from 'svelte';
  
  // Props
  export let tabs: { id: string; label: string }[] = [];
  export let activeTab: string = '';
  
  // Event dispatcher
  const dispatch = createEventDispatcher<{
    tabChange: string;
  }>();
  
  // Handle tab change
  function changeTab(tabId: string) {
    activeTab = tabId;
    dispatch('tabChange', tabId);
  }
</script>

<BaseGrid>
  <div class="tab-layout-grid">
    <header class="tab-header">
      <div class="tab-list">
        {#each tabs as tab}
          <button 
            class="tab-button {tab.id === activeTab ? 'active' : ''}" 
            on:click={() => changeTab(tab.id)}
          >
            {tab.label}
          </button>
        {/each}
      </div>
    </header>
    
    <main class="tab-content">
      {#each tabs as tab}
        {#if tab.id === activeTab}
          <div class="tab-panel">
            <slot name={tab.id}></slot>
          </div>
        {/if}
      {/each}
    </main>
  </div>
</BaseGrid>

<style>
  .tab-layout-grid {
    display: grid;
    grid-template-rows: auto 1fr;
    height: 100%;
    width: 100%;
  }
  
  .tab-header {
    background: #1e293b;
    border-bottom: 1px solid #334155;
    padding: 0 1rem;
  }
  
  .tab-list {
    display: flex;
    gap: 0.5rem;
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: #475569 #1e293b;
  }
  
  .tab-list::-webkit-scrollbar {
    height: 6px;
  }
  
  .tab-list::-webkit-scrollbar-track {
    background: #1e293b;
  }
  
  .tab-list::-webkit-scrollbar-thumb {
    background-color: #475569;
    border-radius: 6px;
  }
  
  .tab-button {
    background: transparent;
    color: #94a3b8;
    border: none;
    padding: 1rem 1.25rem;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
  }
  
  .tab-button:hover {
    color: #e2e8f0;
  }
  
  .tab-button.active {
    color: #60a5fa;
  }
  
  .tab-button.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: #60a5fa;
  }
  
  .tab-content {
    background: #0f172a;
    overflow: auto;
  }
  
  .tab-panel {
    height: 100%;
    padding: 1.5rem;
  }
</style>
