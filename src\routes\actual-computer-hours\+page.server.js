import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);
const dbName = 'ServiceContracts';
const collectionName = 'ActualComputerHours';

// Add a logging utility
function logData(label, data) {
  console.log(`=== ${label} ===`);
  if (Array.isArray(data)) {
    console.log(`Array with ${data.length} items:`);
    console.log(JSON.stringify(data.map(item => {
      const copy = { ...item };
      // Hide large or sensitive fields for cleaner logs
      if (copy._id) copy._id = copy._id.toString ? copy._id.toString() : copy._id;
      if (copy.computerId) copy.computerId = copy.computerId.toString ? copy.computerId.toString() : copy.computerId;
      return copy;
    }), null, 2));
  } else if (data && typeof data === 'object') {
    const copy = { ...data };
    if (copy._id) copy._id = copy._id.toString ? copy._id.toString() : copy._id;
    if (copy.computerId) copy.computerId = copy.computerId.toString ? copy.computerId.toString() : copy.computerId;
    console.log(JSON.stringify(copy, null, 2));
  } else {
    console.log(data);
  }
  console.log(`=== END ${label} ===`);
}

export async function GET({ url }) {
  console.log(`[${new Date().toISOString()}] Starting GET for ActualComputerHours`);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(dbName);
    const collection = db.collection(collectionName);

    // Optional: filter by computerId if provided
    const computerId = url.searchParams.get('computerId');
    let query = {};
    if (computerId && ObjectId.isValid(computerId)) {
      query.computerId = new ObjectId(computerId);
      console.log(`Filtering by computerId: ${computerId}`);
    }

    console.log(`Executing find query on ${collectionName} with filter:`, query);
    const hours = await collection.find(query).sort({ _id: -1 }).toArray();
    console.log(`Found ${hours.length} records`);
    
    // Log raw data from database
    logData('Raw Database Records', hours);
    
    // Get current date/time for ensuring fields exist
    const now = new Date();
    const formattedDate = now.toISOString().split('T')[0]; // YYYY-MM-DD
    const formattedTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
    
    console.log(`Current date/time: ${formattedDate} ${formattedTime}`);
    
    // Convert ObjectIds to strings for client and ensure ReportDate and ReportTime exist
    hours.forEach((doc) => {
      doc._id = doc._id.toString();
      if (doc.computerId) doc.computerId = doc.computerId.toString();
      
      // Ensure ReportDate and ReportTime fields exist
      if (!doc.ReportDate) {
        console.log(`Adding missing ReportDate for document ${doc._id}`);
        doc.ReportDate = formattedDate;
      }
      
      if (!doc.ReportTime) {
        console.log(`Adding missing ReportTime for document ${doc._id}`);
        doc.ReportTime = formattedTime;
      }
      
      // Remove legacy field if it exists
      if (doc.ReportDateTime) {
        console.log(`Removing legacy ReportDateTime field for document ${doc._id}`);
        delete doc.ReportDateTime;
      }
    });
    
    // Log processed data
    logData('Processed Records for Client', hours);
    
    // Update the documents in the database to ensure the fields are persistent
    console.log(`Updating ${hours.length} documents in database to persist ReportDate and ReportTime`);
    let updatedCount = 0;
    for (const doc of hours) {
      const result = await collection.updateOne(
        { _id: new ObjectId(doc._id) },
        { 
          $set: { 
            ReportDate: doc.ReportDate,
            ReportTime: doc.ReportTime
          },
          $unset: { ReportDateTime: "" }
        }
      );
      if (result.modifiedCount > 0) {
        updatedCount++;
      }
    }
    console.log(`Updated ${updatedCount} documents in database`);
    
    console.log(`[${new Date().toISOString()}] Completed GET for ActualComputerHours successfully`);
    return json(hours);
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error in GET ActualComputerHours:`, error);
    return json({ error: 'Failed to fetch data' }, { status: 500 });
  }
}

export async function POST({ request }) {
  console.log(`[${new Date().toISOString()}] Starting POST for ActualComputerHours`);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(dbName);
    const collection = db.collection(collectionName);
    const data = await request.json();
    console.log('Received data:', data);
    
    // Validate and require computerId
    if (!data.computerId || !ObjectId.isValid(data.computerId)) {
      console.error('Invalid computerId');
      return json({ error: 'computerId is required and must be a valid ObjectId.' }, { status: 400 });
    }
    data.computerId = new ObjectId(data.computerId);
    console.log('Validated computerId:', data.computerId);
    
    // Ensure reportDateTime is set (default to now if missing)
    if (!data.reportDateTime) {
      data.reportDateTime = new Date();
    }
    console.log('Ensured reportDateTime:', data.reportDateTime);
    
    const result = await collection.insertOne(data);
    console.log('Inserted document with ID:', result.insertedId);
    
    console.log(`[${new Date().toISOString()}] Completed POST for ActualComputerHours successfully`);
    return json({ insertedId: result.insertedId.toString() });
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error in POST ActualComputerHours:`, error);
    return json({ error: 'Failed to insert data' }, { status: 500 });
  }
}

export async function PUT({ request }) {
  console.log(`[${new Date().toISOString()}] Starting PUT for ActualComputerHours`);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(dbName);
    const collection = db.collection(collectionName);
    const data = await request.json();
    console.log('Received data:', data);
    
    if (!data._id || !ObjectId.isValid(data._id)) {
      console.error('Invalid ID');
      return json({ error: 'Invalid ID' }, { status: 400 });
    }
    const _id = new ObjectId(data._id);
    delete data._id;
    console.log('Validated ID:', _id);
    
    if (data.computerId && ObjectId.isValid(data.computerId)) {
      data.computerId = new ObjectId(data.computerId);
    }
    console.log('Updated data:', data);
    
    const result = await collection.updateOne({ _id }, { $set: data });
    console.log('Updated document with ID:', _id);
    
    console.log(`[${new Date().toISOString()}] Completed PUT for ActualComputerHours successfully`);
    return json({ modifiedCount: result.modifiedCount });
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error in PUT ActualComputerHours:`, error);
    return json({ error: 'Failed to update data' }, { status: 500 });
  }
}

export async function DELETE({ url }) {
  console.log(`[${new Date().toISOString()}] Starting DELETE for ActualComputerHours`);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(dbName);
    const collection = db.collection(collectionName);
    const id = url.searchParams.get('id');
    console.log('Received ID:', id);
    
    if (!id || !ObjectId.isValid(id)) {
      console.error('Invalid ID');
      return json({ error: 'Invalid ID' }, { status: 400 });
    }
    const _id = new ObjectId(id);
    console.log('Validated ID:', _id);
    
    const result = await collection.deleteOne({ _id });
    console.log('Deleted document with ID:', _id);
    
    console.log(`[${new Date().toISOString()}] Completed DELETE for ActualComputerHours successfully`);
    return json({ deletedCount: result.deletedCount });
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error in DELETE ActualComputerHours:`, error);
    return json({ error: 'Failed to delete data' }, { status: 500 });
  }
}
