import { MongoClient } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);
const dbName = 'ServiceContracts';

export async function GET() {
    try {
        await client.connect();
        const db = client.db(dbName);
        const collection = db.collection('ProductValidityGroupPartNumber');
        
        const documents = await collection.find({})
            .sort({ 
                ProductGroupId: 1,    // Primary sort
                Id: 1,               // Secondary sort
                ProductPartNumber: 1  // Final sort - unique per product
            })
            .toArray();
            
        return json(documents);
    } catch (error) {
        console.error('Error fetching product validity groups:', error);
        return new Response(JSON.stringify({ error: error instanceof Error ? error.message : 'Unknown error' }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    } finally {
        await client.close();
    }
}
