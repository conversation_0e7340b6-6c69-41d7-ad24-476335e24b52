import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/** @type {import('./$types').PageServerLoad} */
export async function load() {
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    const collection = db.collection('PartNumbersServiceCodeAction');
    
    // Get all items with sorting
    const items = await collection.find({})
      .sort({ ServiceCode: 1, PartNumber: 1 })
      .toArray();
    
    // Convert ObjectId to string for serialization
    const serializedItems = items.map(item => ({
      ...item,
      _id: item._id.toString()
    }));
    
    return {
      items: serializedItems
    };
  } catch (error) {
    console.error('Error loading part numbers service code actions:', error);
    return {
      items: [],
      error: 'Failed to load data'
    };
  } finally {
    await client.close();
  }
}
