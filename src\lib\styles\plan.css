:global(.plan) {
    background-color: #e6f3ff;  /* Light blue background */
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:global(.plan-header) {
    border-bottom: 2px solid #3b82f6;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

:global(.plan-content) {
    background-color: #fff3e6;  /* Light orange background */
    border-radius: 0.375rem;
    padding: 1rem;
}

:global(.plan-grid) {
    background-color: #ffedd5;  /* Slightly darker orange for the grid */
    border-radius: 0.375rem;
    overflow-x: auto;
}

:global(.plan-grid) table {
    width: 100%;
    border-collapse: collapse;
    background-color: #fff7ed;  /* Even lighter orange for table background */
}

:global(.plan-grid) th {
    background-color: #fed7aa;  /* Orange tint for header cells */
    border: 1px solid #fdba74;
}
