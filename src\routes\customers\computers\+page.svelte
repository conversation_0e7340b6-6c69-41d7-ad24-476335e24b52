<script lang="ts">
  import { enhance } from '$app/forms';
  import ComputerForm from '$lib/components/ComputerForm.svelte';

  interface Customer {
    _id: string;
    companyName?: string;
    name?: string;
    type: string;
    city?: string;
    country?: string;
  }

  interface Computer {
    _id: string;
    customerId: string;
    name: string;
    serialNumber?: string;
    type?: string;
    model?: string;
    manufacturer?: string;
    productType?: string;
    productDesignation?: string;
    engineType?: string;
    operatingHours?: number;
    installationDate?: string | null;
    manufactureDate?: string | null;
    isActive?: boolean;
    notes?: string;
    purchaseDate?: Date | null;
    warrantyEndDate?: Date | null;
    createdAt: Date;
  }

  interface PageData {
    customers: Customer[];
    computers: Computer[];
    selectedCustomer: Customer | null;
  }

  export let data: PageData;
  
  let { customers, computers, selectedCustomer } = data;
  let selectedCustomerId = selectedCustomer?._id || '';
  
  // Add Computer form state
  let showAddComputerForm = false;
  let editingComputerId = '';
  
  // Computer form data
  let computerFormData = {
    serialNumber: '',
    model: '',
    productType: '',
    productDesignation: '',
    engineType: '',
    operatingHours: 0,
    installationDate: '',
    manufactureDate: '',
    isActive: true,
    notes: ''
  };
  
  // Reset form
  function resetComputerForm() {
    computerFormData = {
      serialNumber: '',
      model: '',
      productType: '',
      productDesignation: '',
      engineType: '',
      operatingHours: 0,
      installationDate: '',
      manufactureDate: '',
      isActive: true,
      notes: ''
    };
    showAddComputerForm = false;
    editingComputerId = '';
  }
  
  /** 
   * Edit computer
   * @param {Object} computer - Computer to edit
   */
  function editComputer(computer: Computer) {
    editingComputerId = computer._id;
    computerFormData = {
      serialNumber: computer.serialNumber || '',
      model: computer.model || '',
      productType: computer.productType || '',
      productDesignation: computer.productDesignation || '',
      engineType: computer.engineType || '',
      operatingHours: computer.operatingHours || 0,
      installationDate: computer.installationDate ? computer.installationDate.substring(0, 10) : '',
      manufactureDate: computer.manufactureDate ? computer.manufactureDate.substring(0, 10) : '',
      isActive: computer.isActive === undefined ? true : computer.isActive,
      notes: computer.notes || ''
    };
    showAddComputerForm = true;
  }
  
  // Filtered computers based on selected customer
  $: filteredComputers = selectedCustomerId 
    ? computers.filter(c => c.customerId === selectedCustomerId)
    : computers;

  function getCustomerName(customerId: string): string {
    const customer = customers.find(c => c._id === customerId);
    return customer ? (customer.companyName || customer.name || '') : 'Unknown Customer';
  }

  function getCustomerType(customerId: string): string {
    const customer = customers.find(c => c._id === customerId);
    return customer ? customer.type || 'Retail' : 'Retail';
  }

  function getWarrantyStatus(warrantyEndDate: Date) {
    const today = new Date();
    const warrantyDate = new Date(warrantyEndDate);
    const timeDiff = warrantyDate.getTime() - today.getTime();
    const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
    
    if (daysDiff < 0) {
      return { status: 'Expired', class: 'expired' };
    } else if (daysDiff < 30) {
      return { status: 'Expiring soon', class: 'expiring' };
    } else {
      return { status: 'Active', class: 'active' };
    }
  }
</script>

<svelte:head>
  <title>Customer Computers Overview</title>
</svelte:head>

<div class="container">
  <header>
    <div class="header-content">
      <h1>Customer Computers Overview</h1>
      <p>View and manage all customer computers in one place</p>
    </div>
    {#if selectedCustomer}
      <div class="customer-details">
        <div class="customer-card">
          <h2>{selectedCustomer.companyName || selectedCustomer.name}</h2>
          <div class="customer-info">
            <span class="customer-type {selectedCustomer.type.toLowerCase().replace(' ', '-')}">
              {selectedCustomer.type}
            </span>
            {#if selectedCustomer.country}
              <span class="customer-location">
                {selectedCustomer.city}, {selectedCustomer.country}
              </span>
            {/if}
          </div>
          <div class="computer-count">
            Computers: {filteredComputers.length}
          </div>
        </div>
      </div>
    {/if}
    <div class="header-actions">
      <div class="filter-section">
        <select 
          bind:value={selectedCustomerId}
          class="customer-filter"
        >
          <option value="">All Customers</option>
          {#each customers as customer}
            <option value={customer._id} class="customer-option {customer.type.toLowerCase().replace(' ', '-')}">
              {customer.companyName || customer.name} ({customer.type || 'Retail'})
            </option>
          {/each}
        </select>
      </div>
      
      {#if selectedCustomerId}
        <button class="add-button" on:click={() => showAddComputerForm = true}>
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M8 0a1 1 0 0 1 1 1v6h6a1 1 0 1 1 0 2H9v6a1 1 0 1 1-2 0V9H1a1 1 0 0 1 0-2h6V1a1 1 0 0 1 1-1z"/>
          </svg>
          Add Computer
        </button>
      {/if}
    </div>
  </header>

  {#if showAddComputerForm && selectedCustomerId}
    <ComputerForm
      customerId={selectedCustomerId}
      editingComputerId={editingComputerId}
      formData={computerFormData}
      on:cancel={resetComputerForm}
    />
  {/if}

  {#if filteredComputers.length > 0}
    <div class="table-container">
      <table>
        <thead>
          <tr>
            <th>Customer</th>
            <th>Name</th>
            <th>Type</th>
            <th>Model</th>
            <th>Warranty</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {#each filteredComputers as computer (computer._id)}
            <tr>
              <td>
                <div class="customer-info">
                  <span class="customer-name">{getCustomerName(computer.customerId)}</span>
                  <span class="customer-type {getCustomerType(computer.customerId).toLowerCase().replace(' ', '-')}">
                    {getCustomerType(computer.customerId)}
                  </span>
                </div>
              </td>
              <td>
                <div class="computer-info">
                  <span class="computer-name">{computer.name}</span>
                  {#if computer.model}
                    <div class="model-info">
                      <span class="model">{computer.model}</span>
                      {#if computer.manufacturer}
                        <span class="manufacturer">by {computer.manufacturer}</span>
                      {/if}
                    </div>
                  {/if}
                </div>
              </td>
              <td>{computer.type || 'Unknown'}</td>
              <td>{computer.model || 'N/A'}</td>
              <td>
                {#if computer.warrantyEndDate}
                  {@const status = getWarrantyStatus(computer.warrantyEndDate)}
                  <span class="warranty-status {status.class}">
                    {status.status}
                  </span>
                {:else}
                  <span class="warranty-status unknown">Unknown</span>
                {/if}
              </td>
              <td class="actions-cell">
                <div class="actions-grid">
                  <button class="btn-edit" on:click={() => editComputer(computer)} title="Edit computer">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
                    </svg>
                  </button>
                  <form action="?/deleteComputer" method="POST" use:enhance>
                    <input type="hidden" name="_id" value={computer._id} />
                    <input type="hidden" name="customerId" value={computer.customerId} />
                    <button class="btn-delete" type="submit" title="Delete computer">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                        <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                      </svg>
                    </button>
                  </form>
                  <a href="/customer-computers/{computer._id}" class="btn-view" title="View details">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                      <path d="M10.5 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0z"/>
                      <path d="M0 8s3-5.5 8-5.5S16 8 16 8s-3 5.5-8 5.5S0 8 0 8zm8 3.5a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7z"/>
                    </svg>
                  </a>
                </div>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  {:else}
    <div class="no-results">
      <p>No computers found for the selected customer.</p>
    </div>
  {/if}
</div>

<style>
  .container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
  }

  header {
    margin-bottom: 2rem;
  }

  .header-content {
    margin-bottom: 1.5rem;
  }

  h1 {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
  }

  .customer-details {
    margin-bottom: 2rem;
  }

  .customer-card {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .customer-card h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }

  .customer-info {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .customer-type {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .customer-type.retail {
    background-color: #e9ecef;
    color: #495057;
  }

  .customer-type.oem-importer {
    background-color: #cff4fc;
    color: #055160;
  }

  .customer-type.fleet-owner {
    background-color: #d1e7dd;
    color: #0f5132;
  }

  .customer-location {
    color: #6c757d;
    font-size: 0.875rem;
  }

  .computer-count {
    margin-top: 1rem;
    font-size: 0.875rem;
    color: #6c757d;
  }

  .header-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .customer-filter {
    min-width: 200px;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background-color: white;
  }

  .table-container {
    overflow-x: auto;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
  }

  th, td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
  }

  th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
  }

  .computer-info {
    display: flex;
    flex-direction: column;
  }

  .computer-name {
    font-weight: 500;
  }

  .model-info {
    font-size: 0.875rem;
    color: #6c757d;
  }

  .manufacturer {
    color: #6c757d;
  }

  .warranty-status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .warranty-status.active {
    background-color: #d1e7dd;
    color: #0f5132;
  }

  .warranty-status.expiring {
    background-color: #fff3cd;
    color: #664d03;
  }

  .warranty-status.expired {
    background-color: #f8d7da;
    color: #842029;
  }

  .warranty-status.unknown {
    background-color: #e9ecef;
    color: #495057;
  }

  .actions-cell {
    padding: 0.5rem !important;
  }
  
  .actions-grid {
    display: grid;
    grid-template-columns: repeat(3, auto);
    gap: 0.5rem;
    justify-content: start;
  }
  
  .btn-view, .btn-edit, .btn-delete {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 0.25rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .btn-view {
    background-color: #2563eb;
    color: white;
    text-decoration: none;
  }
  
  .btn-view:hover {
    background-color: #1d4ed8;
  }
  
  .btn-edit {
    background-color: #0ea5e9;
    color: white;
  }
  
  .btn-edit:hover {
    background-color: #0284c7;
  }
  
  .btn-delete {
    background-color: #ef4444;
    color: white;
  }
  
  .btn-delete:hover {
    background-color: #dc2626;
  }

  .no-results {
    text-align: center;
    padding: 2rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    color: #6c757d;
  }
</style>
