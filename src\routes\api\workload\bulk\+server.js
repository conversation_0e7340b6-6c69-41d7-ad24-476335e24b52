import { json } from '@sveltejs/kit';
import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';

/**
 * Handles bulk creation/update of workload entries
 * @type {import('./$types').RequestHandler}
 */
export async function POST({ request }) {
    const client = new MongoClient(uri);
    
    try {
        await client.connect();
        const db = client.db('ServiceContracts');
        const workloadCollection = db.collection('Workload');
        
        // Parse request body
        const data = await request.json();
        const { 
            computerId, 
            startYear, 
            startMonth, 
            endYear, 
            endMonth, 
            hours, 
            activity = 'Regular Service',
            hasFixed = false
        } = data;
        
        console.log('Bulk workload operation received:', data);
        
        // Validate required fields
        if (!computerId || !ObjectId.isValid(computerId)) {
            return json({ success: false, message: 'Valid computer ID is required' }, { status: 400 });
        }
        
        if (!validateDateRange(startYear, startMonth, endYear, endMonth)) {
            return json({ success: false, message: 'Valid date range is required' }, { status: 400 });
        }
        
        // Basic validation that hours is provided and is a positive number
        if (typeof hours !== 'number' || hours <= 0) {
            return json({ success: false, message: 'Hours must be a positive number' }, { status: 400 });
        }
        
        // Generate all months in the specified range
        const monthsInRange = generateMonthsInRange(startYear, startMonth, endYear, endMonth);
        console.log(`Processing ${monthsInRange.length} months in range`);
        
        // Process each month in the range
        const results = await processWorkload(workloadCollection, computerId, monthsInRange, hours, activity, hasFixed);
        
        return json({
            success: true,
            message: `Successfully processed ${results.operations.length} workload entries (${results.created.length} created, ${results.updated.length} updated)`,
            results
        });
        
    } catch (error) {
        console.error('Error handling bulk workload operation:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        return json({ success: false, message: errorMessage }, { status: 500 });
    } finally {
        await client.close();
    }
}

/**
 * Validates that the provided date range is valid
 * @param {number} startYear - Start year
 * @param {number} startMonth - Start month
 * @param {number} endYear - End year
 * @param {number} endMonth - End month
 * @returns {boolean} Whether the date range is valid
 */
function validateDateRange(startYear, startMonth, endYear, endMonth) {
    // Convert to numbers
    const start = {
        year: Number(startYear),
        month: Number(startMonth)
    };
    
    const end = {
        year: Number(endYear),
        month: Number(endMonth)
    };
    
    // Basic validation
    if (
        isNaN(start.year) || isNaN(start.month) || 
        isNaN(end.year) || isNaN(end.month) ||
        start.month < 1 || start.month > 12 ||
        end.month < 1 || end.month > 12
    ) {
        return false;
    }
    
    // Check that end date is not before start date
    if (end.year < start.year || (end.year === start.year && end.month < start.month)) {
        return false;
    }
    
    return true;
}

/**
 * Generates an array of all months in the specified range
 * @param {number} startYear - Start year
 * @param {number} startMonth - Start month 
 * @param {number} endYear - End year
 * @param {number} endMonth - End month
 * @returns {Array<{year: number, month: number}>} Array of year-month objects
 */
function generateMonthsInRange(startYear, startMonth, endYear, endMonth) {
    // Convert to numbers
    const start = {
        year: Number(startYear),
        month: Number(startMonth)
    };
    
    const end = {
        year: Number(endYear),
        month: Number(endMonth)
    };
    
    const months = /** @type {Array<{year: number, month: number}>} */ [];
    
    let currentYear = start.year;
    let currentMonth = start.month;
    
    while (
        currentYear < end.year || 
        (currentYear === end.year && currentMonth <= end.month)
    ) {
        months.push({
            year: currentYear,
            month: currentMonth
        });
        
        currentMonth++;
        if (currentMonth > 12) {
            currentMonth = 1;
            currentYear++;
        }
    }
    
    return months;
}

/**
 * Process workload entries for the month range
 * @param {import('mongodb').Collection} collection - MongoDB Workload collection
 * @param {string} computerId - Computer ID string
 * @param {Array<{year: number, month: number}>} monthsInRange - Array of months to process
 * @param {number} hours - Number of hours to set
 * @param {string} activity - Activity description
 * @param {boolean} hasFixed - Fixed schedule flag
 * @returns {Promise<{operations: Array<{type: string, year: number, month: number, success: boolean}>, created: Array<{year: number, month: number}>, updated: Array<{year: number, month: number}>}>}
 */
async function processWorkload(collection, computerId, monthsInRange, hours, activity, hasFixed) {
    /** @type {{operations: Array<{type: string, year: number, month: number, success: boolean}>, created: Array<{year: number, month: number}>, updated: Array<{year: number, month: number}>}} */
    const results = {
        operations: [],
        created: [],
        updated: []
    };

    for (const { year, month } of monthsInRange) {
        // Check if workload exists
        const existingWorkload = await collection.findOne({
            computerId: new ObjectId(computerId),
            year,
            month
        });

        const workloadData = {
            computerId: new ObjectId(computerId),
            year,
            month,
            hours,
            activity,
            type: hasFixed ? 'Fixed' : 'Soft',
            updatedAt: new Date()
        };

        if (existingWorkload) {
            // Update existing workload
            console.log(`Updating workload for ${year}-${month}`);
            const updateResult = await collection.updateOne(
                { _id: existingWorkload._id },
                { $set: workloadData }
            );
            
            const success = updateResult.modifiedCount > 0;
            if (success) {
                results.updated.push({ year, month });
            }
            
            results.operations.push({
                type: 'update',
                year,
                month,
                success
            });
        } else {
            // Create new workload entry
            console.log(`Creating new workload for ${year}-${month}`);
            const newWorkload = {
                ...workloadData,
                createdAt: new Date()
            };
            
            const insertResult = await collection.insertOne(newWorkload);
            const success = insertResult.acknowledged;
            
            if (success) {
                results.created.push({ year, month });
            }
            
            results.operations.push({
                type: 'create',
                year,
                month,
                success
            });
        }
    }
    
    return results;
}
