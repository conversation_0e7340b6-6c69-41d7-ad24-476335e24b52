import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

/** @type {import('./$types').PageServerLoad} */
export async function load({ params }) {
  const db = client.db('ServiceContracts');
  const id = params.id;
  const faultcode = await db.collection('ActiveFaultCodes').findOne({ _id: new ObjectId(id) });
  // Serialize ObjectId and Dates
  function serialize(doc) {
    if (!doc) return {};
    const out = {};
    for (const [k, v] of Object.entries(doc)) {
      if (v instanceof ObjectId) out[k] = v.toString();
      else if (v instanceof Date) out[k] = v.toISOString();
      else out[k] = v;
    }
    return out;
  }
  return { faultcode: serialize(faultcode) };
}

/** @type {import('./$types').Actions} */
export const actions = {
  default: async ({ request, params }) => {
    const db = client.db('ServiceContracts');
    const id = params.id;
    const body = await request.json();
    const { _id, ...fields } = body;
    Object.keys(fields).forEach(key => {
      if (fields[key] === undefined) delete fields[key];
    });
    const result = await db.collection('ActiveFaultCodes').updateOne(
      { _id: new ObjectId(id) },
      { $set: fields }
    );
    if (result.modifiedCount === 1) {
      return { success: true };
    } else {
      return { success: false, error: 'Not found or not modified' };
    }
  }
};
