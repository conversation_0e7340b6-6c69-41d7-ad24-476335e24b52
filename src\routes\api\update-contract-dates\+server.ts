import { ObjectId } from 'mongodb';
import { collections } from '$lib/server/database';
import { json } from '@sveltejs/kit';

export async function POST() {
    try {
        const CustomerComputers = collections.CustomerComputers;
        
        // Get all customer computers
        const computers = await CustomerComputers.find({}).toArray();
        
        for (const computer of computers) {
            // Skip if no desired contract length
            if (!computer.desiredContractLengthYears) continue;

            // Calculate desired contract start date
            // If purchaseDate exists, use that as base, otherwise use current date
            const baseDate = computer.purchaseDate ? new Date(computer.purchaseDate) : new Date();
            
            // Create the desired contract start date
            const desiredContractStartDate = new Date(baseDate);
            
            // Update the document
            await CustomerComputers.updateOne(
                { _id: new ObjectId(computer._id) },
                { 
                    $set: { 
                        desiredContractStartDate: desiredContractStartDate,
                        updatedAt: new Date()
                    } 
                }
            );
        }

        return json({ success: true, message: 'Contract dates updated successfully' });
    } catch (error) {
        console.error('Error updating contract dates:', error);
        return json(
            { success: false, message: 'Failed to update contract dates' },
            { status: 500 }
        );
    }
}
