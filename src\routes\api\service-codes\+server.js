import { MongoClient } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);
const dbName = 'ServiceContracts';

export async function GET() {
    try {
        await client.connect();
        const db = client.db(dbName);
        const collection = db.collection('ServiceCodes');
        
        const documents = await collection.find({})
            .sort({ servicecode: 1 })
            .limit(100)  // Limit results as per memory guidance
            .toArray();
            
        return json(documents);
    } catch (err) {
        return new Response(JSON.stringify({ error: err.message }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    } finally {
        await client.close();
    }
}
