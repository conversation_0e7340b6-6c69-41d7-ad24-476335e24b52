<script>
  // Component for displaying quotation rows in a condensed grid format
  export let quoteRows = [];
  export let loading = false;
  export let errorMessage = '';
  export let hasParts = false;

  // Handlers for row actions (passed as props from parent)
  export let onUpdateIncluded = row => {};
  export let onUpdateQuoteRowType = row => {};
  export let onEdit = row => {};
  export let onDuplicate = row => {};
  export let onDelete = row => {};
  
  // Helper for formatting dollar values
  const formatDollar = (value) => {
    if (value === undefined || value === null) return '$0.00';
    return `$${parseFloat(value).toFixed(2)}`;
  };
</script>

{#if loading}
  <div class="loading">Loading quote data...</div>
{:else if errorMessage}
  <div class="error">Error: {errorMessage}</div>
{:else if quoteRows.length === 0}
  <div class="no-data">No quote rows found for this quotation.</div>
{:else}
  <div class="quotation-rows-grid">
    <div class="header" class:has-parts={hasParts}>
      <div class="cell">Level</div>
      <div class="cell">Package</div>
      <div class="cell">Type</div>
      {#if hasParts}
        <div class="cell part-column">Part#</div>
        <div class="cell part-column">Name</div>
        <div class="cell part-column">Qty</div>
      {/if}
      <div class="cell">SvcID</div>
      <div class="cell">Service</div>
      <div class="cell">Incl</div>
      <div class="cell">Req</div>
      <div class="cell">SCOS</div>
      <div class="cell">OEM</div>
      <div class="cell">Fleet</div>
      <div class="cell">RRP</div>
      <div class="cell dollar-column">Cust$</div>
      <div class="cell dollar-column">Dlr$</div>
      <div class="cell dollar-column">Int$</div>
      <div class="cell">Select</div>
      <div class="cell">Qty/Yr</div>
      <div class="cell actions-cell">Acts</div>
    </div>
    
    <div class="body">
      {#each quoteRows as row}
        <div class="row" class:has-parts={hasParts}>
          <div class="cell">{row.level}</div>
          <div class="cell pkg-cell" title={row.package}>{row.package}</div>
          <div class="cell type-cell">
            <select 
              value={row.QuoteRowType || 'Labour'} 
              on:change={(e) => onUpdateQuoteRowType(row, e.target.value)}
              class="compact-select"
            >
              <option value="Labour">Labour</option>
              <option value="Part">Part</option>
            </select>
          </div>
          {#if hasParts}
            <div class="cell part-column">{row.QuoteRowType === 'Part' ? row.PartNumber || '-' : '-'}</div>
            <div class="cell part-column">{row.QuoteRowType === 'Part' ? row.PartName || '-' : '-'}</div>
            <div class="cell part-column">{row.QuoteRowType === 'Part' ? row.PartQty || '1' : '-'}</div>
          {/if}
          <div class="cell">{row.serviceId}</div>
          <div class="cell service-cell" title={row.service}>{row.service}</div>
          <div class="cell included-cell">
            <select 
              value={row.included} 
              on:change={(e) => onUpdateIncluded(row, e.target.value)}
              class="compact-select"
            >
              <option value="Yes">Y</option>
              <option value="No">N</option>
            </select>
          </div>
          <div class="cell">{row.required}</div>
          <div class="cell">{row.scos}</div>
          <div class="cell">{row.oemImport}</div>
          <div class="cell">{row.fleetOwner}</div>
          <div class="cell">{row.rrp}</div>
          <div class="cell dollar-column">{formatDollar(row.CustomerPrice)}</div>
          <div class="cell dollar-column">{formatDollar(row.DealerPrice)}</div>
          <div class="cell dollar-column">{formatDollar(row.InternalCost)}</div>
          <div class="cell">{row.selectService}</div>
          <div class="cell">{row.qtyPerYear}</div>
          <div class="cell actions-cell">
            <div class="action-buttons">
              <button 
                class="btn-icon edit" 
                title="Edit" 
                on:click={() => onEdit(row)}
              >✏️</button>
              <button 
                class="btn-icon duplicate" 
                title="Duplicate" 
                on:click={() => onDuplicate(row)}
              >🔄</button>
              <button 
                class="btn-icon delete" 
                title="Delete" 
                on:click={() => onDelete(row)}
              >🗑️</button>
            </div>
          </div>
        </div>
      {/each}
    </div>
  </div>
{/if}

<style>
  .quotation-rows-grid {
    width: 100%;
    font-size: 0.75rem;
    overflow-x: auto;
  }
  
  .header, .row {
    display: grid;
    grid-template-columns: 
      2rem      /* Level */
      minmax(5rem, 8%)   /* Package */ 
      3rem      /* Row Type */
      3rem      /* Service ID */
      minmax(8rem, 12%)  /* Service */
      3rem      /* Included */
      3rem      /* Required */
      3rem      /* SCOS */
      3rem      /* OEM Import */
      3rem      /* Fleet Owner */
      3rem      /* RRP */
      4rem      /* Customer $ */
      4rem      /* Dealer $ */
      4rem      /* Internal $ */
      3rem      /* Select Service */
      3rem      /* Qty per Yr */
      5rem;     /* Actions */
    gap: 0.2rem;
    padding: 0.2rem;
    align-items: center;
    border-bottom: 1px solid #e2e8f0;
  }
  
  .header.has-parts, .row.has-parts {
    grid-template-columns: 
      2rem      /* Level */
      minmax(5rem, 8%)   /* Package */ 
      3rem      /* Row Type */
      5rem      /* Part Number */
      5rem      /* Part Name */
      2rem      /* Part Qty */
      3rem      /* Service ID */
      minmax(8rem, 12%)  /* Service */
      3rem      /* Included */
      3rem      /* Required */
      3rem      /* SCOS */
      3rem      /* OEM Import */
      3rem      /* Fleet Owner */
      3rem      /* RRP */
      4rem      /* Customer $ */
      4rem      /* Dealer $ */
      4rem      /* Internal $ */
      3rem      /* Select Service */
      3rem      /* Qty per Yr */
      5rem;     /* Actions */
  }
  
  .cell {
    padding: 0.15rem;
    font-size: 0.75rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
  }
  
  .service-cell, .pkg-cell {
    text-align: left;
  }
  
  .header {
    position: sticky;
    top: 0;
    z-index: 10;
  }
  
  .header .cell {
    font-weight: 600;
    color: #475569;
    background-color: #f1f5f9;
  }
  
  .body {
    max-height: 70vh;
    overflow-y: auto;
  }
  
  .row:nth-child(even) {
    background-color: #f8fafc;
  }
  
  .row:hover {
    background-color: #e9f5ff;
  }
  
  .compact-select {
    width: 100%;
    padding: 0.1rem;
    font-size: 0.7rem;
    border-radius: 0.15rem;
    border: 1px solid #cbd5e1;
  }
  
  .dollar-column {
    color: #0369a1;
    font-weight: 500;
  }
  
  .part-column {
    background-color: #f0f9ff;
  }
  
  .action-buttons {
    display: flex;
    gap: 0.15rem;
    justify-content: center;
  }
  
  .btn-icon {
    padding: 0.15rem;
    border-radius: 0.2rem;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 0.7rem;
  }
  
  .btn-icon:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
  
  .loading, .error, .no-data {
    padding: 1rem;
    text-align: center;
  }
  
  .error {
    color: #e74c3c;
  }
</style>
