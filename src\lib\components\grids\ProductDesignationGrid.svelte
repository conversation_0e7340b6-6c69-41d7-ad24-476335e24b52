<script>
    import BaseGrid from './BaseGrid.svelte';
    import { createEventDispatcher } from 'svelte';
    
    /**
     * @type {Array<{
     *   _id: string,
     *   ProductDesignation: string,
     *   Caption: string
     * }>}
     */
    export let designations = [];
    export let filterText = '';
    
    const dispatch = createEventDispatcher();
    
    $: filteredDesignations = filterText 
        ? designations.filter(d => 
            d.ProductDesignation.toLowerCase().includes(filterText.toLowerCase()) ||
            d.Caption.toLowerCase().includes(filterText.toLowerCase())
          )
        : designations;
        
    function handleEdit(designation) {
        dispatch('edit', designation);
    }
    
    function handleDelete(designation) {
        dispatch('delete', designation);
    }
    
    function handleView(designation) {
        dispatch('view', designation);
    }
</script>

<BaseGrid>
    <div class="product-designation-grid">
        <div class="filter-section">
            <input
                type="text"
                bind:value={filterText}
                placeholder="Filter by designation or caption..."
                class="filter-input"
            />
        </div>
        
        <div class="grid-header">
            <div class="header-item">Product Designation</div>
            <div class="header-item">Caption</div>
            <div class="header-item actions">Actions</div>
        </div>
        
        {#if filteredDesignations.length > 0}
            {#each filteredDesignations as designation}
                <div class="grid-row">
                    <div class="grid-cell">{designation.ProductDesignation}</div>
                    <div class="grid-cell">{designation.Caption}</div>
                    <div class="grid-cell actions">
                        <div class="action-buttons">
                            <button 
                                class="action-btn view-btn"
                                on:click={() => handleView(designation)}
                            >
                                View
                            </button>
                            <button 
                                class="action-btn edit-btn"
                                on:click={() => handleEdit(designation)}
                            >
                                Edit
                            </button>
                            <button 
                                class="action-btn delete-btn"
                                on:click={() => handleDelete(designation)}
                            >
                                Delete
                            </button>
                        </div>
                    </div>
                </div>
            {/each}
        {:else}
            <div class="empty-message">
                {filterText ? 'No matching designations found' : 'No product designations found'}
            </div>
        {/if}
    </div>
</BaseGrid>

<style>
    .product-designation-grid {
        display: grid;
        width: 100%;
    }
    
    .filter-section {
        margin-bottom: 1rem;
        padding: 0.5rem 0;
    }
    
    .filter-input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 1px solid #e2e8f0;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        transition: border-color 0.2s;
    }
    
    .filter-input:focus {
        outline: none;
        border-color: #14b8a6;
        box-shadow: 0 0 0 2px rgba(20, 184, 166, 0.2);
    }
    
    .grid-header {
        display: grid;
        grid-template-columns: 1fr 1fr 200px;
        background-color: #f8f9fa;
        font-weight: 500;
        border-bottom: 1px solid #e2e8f0;
    }
    
    .header-item {
        padding: 0.75rem 1rem;
        text-align: left;
        border-right: 1px solid #e2e8f0;
    }
    
    .grid-row {
        display: grid;
        grid-template-columns: 1fr 1fr 200px;
        border-bottom: 1px solid #e2e8f0;
        transition: background-color 0.2s;
    }
    
    .grid-row:hover {
        background-color: #f8f9fa;
    }
    
    .grid-cell {
        padding: 0.75rem 1rem;
        border-right: 1px solid #e2e8f0;
    }
    
    .actions {
        text-align: center;
    }
    
    .action-buttons {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
    }
    
    .action-btn {
        padding: 0.25rem 0.75rem;
        border: none;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .view-btn {
        background-color: #14b8a6;
        color: white;
    }
    
    .view-btn:hover {
        background-color: #0d9488;
    }
    
    .edit-btn {
        background-color: #3b82f6;
        color: white;
    }
    
    .edit-btn:hover {
        background-color: #2563eb;
    }
    
    .delete-btn {
        background-color: #ef4444;
        color: white;
    }
    
    .delete-btn:hover {
        background-color: #dc2626;
    }
    
    .empty-message {
        grid-column: 1 / -1;
        padding: 2rem;
        text-align: center;
        color: #6b7280;
    }
</style>
