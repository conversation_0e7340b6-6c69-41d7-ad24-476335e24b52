<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import WorkloadEditForm from '$lib/components/WorkloadEditForm.svelte';
  
  // Types
  type WorkloadType = 'Fixed' | 'Soft';
  
  interface WorkloadEntry {
    id?: string;
    computerId: string;
    year: number;
    month: number;
    hours: number;
    activity: string;
    type: WorkloadType;
    description: string;
    createdAt?: Date | string;
    updatedAt?: Date | string;
  }
  
  interface GridData {
    year: number;
    months: (WorkloadEntry | null)[];
    accumulatedHours?: number[];
  }
  
  // Component state
  const computerId = $page.url.searchParams.get('computerId') || '';
  let rowOrder = ($page.url.searchParams.get('rowOrder') || 'FirstLast') as 'FirstLast' | 'LastFirst';
  let workloadData: WorkloadEntry[] = [];
  let gridData: GridData[] = [];
  let isLoading = true;
  let error: string | null = null;
  
  // Edit modal state
  let isEditModalOpen = false;
  let currentWorkload: WorkloadEntry | null = null;
  let isSaving = false;
  
  // Open edit modal for a cell
  function openEditModal(year: number, month: number, existingData: WorkloadEntry | null = null) {
    currentWorkload = existingData || {
      computerId,
      year,
      month,
      hours: 0,
      activity: 'Contract Service',
      type: 'Soft',
      description: ''
    };
    isEditModalOpen = true;
  }
  
  // Close edit modal
  function closeEditModal() {
    isEditModalOpen = false;
    currentWorkload = null;
  }
  
  // Save workload data
  async function saveWorkload(workload: WorkloadEntry) {
    try {
      isSaving = true;
      error = null;
      
      // Prepare the data to send with proper type conversion
      const dataToSend = {
        computerId: workload.computerId || computerId,
        year: Number(workload.year) || new Date().getFullYear(),
        month: Number(workload.month) || 1,
        hours: Number(workload.hours) || 0,
        activity: workload.activity || 'Contract Service',
        type: workload.type || 'Soft',
        description: workload.description || '',
        isFixed: workload.type === 'Fixed'
      };
      
      const url = '/api/workload';
      
      console.log('Saving workload data:', JSON.stringify(dataToSend, null, 2));
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dataToSend)
      });
      
      console.log('Response status:', response.status, response.statusText);
      
      let result;
      const responseText = await response.text();
      
      try {
        result = responseText ? JSON.parse(responseText) : {};
        console.log('API Response:', result);
      } catch (jsonError) {
        console.error('Error parsing JSON response:', jsonError);
        throw new Error('Invalid response from server');
      }
      
      if (!response.ok) {
        const errorMsg = result?.error || result?.details || `HTTP ${response.status} - ${response.statusText}`;
        console.error('API Error:', {
          status: response.status,
          error: errorMsg,
          details: result?.details
        });
        
        // Format error message for display
        let errorMessage = 'Failed to save workload';
        if (Array.isArray(errorMsg)) {
          errorMessage = errorMsg.join('\n');
        } else if (typeof errorMsg === 'string') {
          errorMessage = errorMsg;
        }
        
        throw new Error(errorMessage);
      }
      
      if (result.success && result.data) {
        console.log(`Workload ${result.action || 'saved'} successfully:`, result);
        
        // Update the local data with the saved workload
        const updatedWorkload = result.data;
        const workloadIndex = workloadData.findIndex(w => 
          w.year === updatedWorkload.year && 
          w.month === updatedWorkload.month
        );
        
        if (workloadIndex !== -1) {
          // Update existing entry
          workloadData[workloadIndex] = {
            ...workloadData[workloadIndex],
            ...updatedWorkload
          };
        } else {
          // Add new entry
          workloadData = [...workloadData, updatedWorkload];
        }
        
        // Update the grid data
        updateGridData();
        
        // Show success message
        const actionMessage = result.action === 'created' ? 'created' : 'updated';
        alert(`Workload ${actionMessage} successfully`);
        
        // Close the modal
        closeEditModal();
      } else {
        console.error('API reported failure:', result);
        throw new Error(result.error || 'Failed to save workload');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('Error saving workload:', errorMessage, err);
      error = errorMessage;
      alert(`Error: ${errorMessage}`);
    } finally {
      isSaving = false;
    }
  }

  // Function to fetch workload data
  async function fetchWorkloadData() {
    try {
      isLoading = true;
      error = null;
      
      const response = await fetch(`/api/workload?computerId=${computerId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch workload data: ${response.status} ${response.statusText}`);
      }
      
      const result = await response.json();
      console.log('API Response:', result);
      
      if (result.success) {
        // Ensure dates are properly parsed
        workloadData = result.data.map((item: any) => ({
          ...item,
          createdAt: item.createdAt ? new Date(item.createdAt) : null,
          updatedAt: item.updatedAt ? new Date(item.updatedAt) : null
        }));
      } else {
        throw new Error(result.error || 'Failed to load workload data');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('Error fetching workload data:', errorMessage, err);
      error = errorMessage;
    } finally {
      isLoading = false;
    }
  }

  // Update grid data based on workloadData
  function updateGridData() {
    // Group by year
    const years = new Set(workloadData.map(entry => entry.year));
    
    // Sort years in chronological order for accumulated calculation
    const sortedYears = Array.from(years).sort((a, b) => a - b);
    
    // Calculate total accumulated hours across all years
    let totalAccumulatedHours = 0;
    const yearToAccumulatedMap = new Map();
    
    // First pass: calculate accumulated hours across all years in chronological order
    sortedYears.forEach(year => {
      const monthlyAccumulated = Array(12).fill(0);
      
      for (let month = 0; month < 12; month++) {
        const entry = workloadData.find(d => d.year === year && d.month === month + 1);
        if (entry) {
          totalAccumulatedHours += entry.hours;
        }
        monthlyAccumulated[month] = totalAccumulatedHours;
      }
      
      yearToAccumulatedMap.set(year, monthlyAccumulated);
    });
    
    // Create grid data array with the display order (newest first)
    const newGridData = sortedYears.sort((a, b) => b - a).map(year => {
      const yearEntries = workloadData.filter(entry => entry.year === year);
      const months = Array(12).fill(null);
      
      yearEntries.forEach(entry => {
        months[entry.month - 1] = entry;
      });
      
      return {
        year,
        months,
        accumulatedHours: yearToAccumulatedMap.get(year)
      };
    });
    
    gridData = newGridData;
  }

  // Update grid data when workloadData changes
  $: if (workloadData.length > 0) {
    updateGridData();
  }

  // Fetch data when component mounts
  onMount(() => {
    if (!computerId) {
      error = 'Computer ID is required';
      isLoading = false;
      return;
    }
    fetchWorkloadData();
    
    // Cleanup function
    return () => {
      // Any cleanup if needed
    };
  });

  // Get month names for display
  const monthNames = [
    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
  ];

  // Precompute the grid data
  $: {
    if (workloadData) {
      console.log('Raw workloadData:', workloadData);
      if (workloadData.length === 0) {
        gridData = [];
      } else {
        // Get unique years and sort based on rowOrder
        const years = [...new Set(workloadData.map(item => item.year))];
        
        // Sort years based on the selected order
        const sortedYears = years.sort((a, b) => {
          return rowOrder === 'FirstLast' ? a - b : b - a;
        });
        
        // First calculate accumulated hours across all years in chronological order
        let totalAccumulatedHours = 0;
        const yearToAccumulatedMap = new Map();
        
        // Process years in chronological order for accumulated calculation
        const chronologicalYears = [...years].sort((a, b) => a - b);
        
        chronologicalYears.forEach(year => {
          const monthlyAccumulated = Array(12).fill(0);
          
          for (let month = 0; month < 12; month++) {
            const entry = workloadData.find(d => d.year === year && d.month === month + 1);
            if (entry) {
              totalAccumulatedHours += entry.hours;
            }
            monthlyAccumulated[month] = totalAccumulatedHours;
          }
          
          yearToAccumulatedMap.set(year, monthlyAccumulated);
        });
        
        // Now create the grid data with the display order
        gridData = sortedYears.map(year => {
          const months = Array(12).fill(0).map((_, i) => 
            workloadData.find(d => d.year === year && d.month === i + 1) || null
          );
          
          return {
            year,
            months,
            accumulatedHours: yearToAccumulatedMap.get(year)
          };
        });
        
        console.log(`Computed gridData (${rowOrder}):`, gridData);
      }
    }
  }
</script>

<div class="container">
  <div class="header">
    <button 
      class="back-button" 
      on:click={() => history.back()}
    >
      ← Back
    </button>
    <h1>Workload Overview</h1>
    
    <div class="sort-controls">
      <span>Sort Order:</span>
      <button 
        class:active={rowOrder === 'FirstLast'}
        on:click={() => {
          rowOrder = 'FirstLast';
          // Update URL
          const url = new URL(window.location.href);
          url.searchParams.set('rowOrder', 'FirstLast');
          window.history.pushState({}, '', url);
        }}
      >
        First to Last
      </button>
      <button 
        class:active={rowOrder === 'LastFirst'}
        on:click={() => {
          rowOrder = 'LastFirst';
          // Update URL
          const url = new URL(window.location.href);
          url.searchParams.set('rowOrder', 'LastFirst');
          window.history.pushState({}, '', url);
        }}
      >
        Last to First
      </button>
    </div>
  </div>

  {#if error}
    <div class="error-message">
      Error: {error}
    </div>
  {:else if isLoading}
    <div class="loading">Loading workload data for computer ID: {computerId}...</div>
  {:else if !workloadData || workloadData.length === 0}
    <div class="no-data">No workload data available for this computer</div>
  {:else}
    <div class="workload-grid">
      <div class="debug-info" style="display: none;">
        Computer ID: {computerId}<br>
        Data points: {workloadData.length}
      </div>
      <div class="grid-header">Year</div>
      {#each Array(12) as _, i}
        <div class="month-header">{monthNames[i]}</div>
      {/each}
      
      {#each gridData as { year, months, accumulatedHours }}
        <div class="year-cell">{year}</div>
        {#each months as monthData, i}
          <div class="data-cell {monthData ? 'has-data' : ''}">
            {#if monthData}
              <div class="hours">{monthData.hours}h</div>
              <div class="accumulated-hours">({accumulatedHours[i]}h)</div>
              <div class="activity" title={monthData.activity}>
                {monthData.activity}
              </div>
              <div class="type-badge {monthData.type.toLowerCase()}">
                {monthData.type}
              </div>
              <button 
                class="edit-button" 
                on:click|stopPropagation={() => openEditModal(year, i + 1, monthData)}
                aria-label="Edit workload"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                </svg>
              </button>
            {:else}
              <button 
                type="button"
                class="empty-cell" 
                on:click|stopPropagation={() => openEditModal(year, i + 1)}
                aria-label="Add workload for ${monthNames[i]} ${year}"
              >
                <span>+ Add</span>
              </button>
            {/if}
          </div>
        {/each}
      {/each}
    </div>
  {/if}
  
  <!-- Edit Modal -->
  {#if currentWorkload}
    <WorkloadEditForm
      bind:isOpen={isEditModalOpen}
      workload={currentWorkload}
      on:save={async (e) => {
        await saveWorkload(e.detail.workload);
      }}
      on:cancel={closeEditModal}
    />
  {/if}
</div>

<style>
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
  }
  
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 2rem;
  }
  
  .sort-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-left: auto;
  }
  
  .sort-controls button {
    padding: 0.5rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    background: white;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .sort-controls button:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
  }
  
  .sort-controls button.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
  }
  
  .sort-controls button:focus {
    outline: 2px solid #93c5fd;
    outline-offset: 2px;
  }
  
  @media (max-width: 640px) {
    .header {
      flex-direction: column;
      align-items: flex-start;
    }
    
    .sort-controls {
      margin-left: 0;
      width: 100%;
      justify-content: flex-end;
    }
  }
  
  .back-button {
    background: none;
    border: none;
    color: #3b82f6;
    cursor: pointer;
    font-size: 1rem;
    margin-right: 1rem;
    padding: 0.5rem 0;
  }
  
  .back-button:hover {
    text-decoration: underline;
  }
  
  h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
  }
  
  .workload-grid {
    display: grid;
    grid-template-columns: repeat(13, minmax(80px, 1fr));
    gap: 1px;
    background-color: #e5e7eb;
    border: 1px solid #e5e7eb;
    width: 100%;
    font-size: 0.85rem;
  }
  
  @media (max-width: 1200px) {
    .workload-grid {
      grid-template-columns: repeat(13, minmax(70px, 1fr));
      font-size: 0.8rem;
    }
  }
  
  @media (max-width: 1024px) {
    .workload-grid {
      grid-template-columns: repeat(13, minmax(60px, 1fr));
      font-size: 0.75rem;
    }
  }
  
  .grid-header,
  .month-header,
  .year-cell,
  .data-cell {
    background-color: white;
    padding: 0.5rem 0.25rem;
    text-align: center;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .grid-header,
  .month-header {
    font-weight: 600;
    background-color: #f3f4f6;
  }
  
  .month-header {
    padding: 0.5rem 0.25rem;
    font-weight: 500;
  }
  
  .year-cell {
    font-weight: 600;
    background-color: #f9fafb;
  }
  
  .accumulated-hours {
    color: #0066cc;
    font-weight: 600;
    font-size: 0.8rem;
    margin-top: 0.1rem;
  }
  
  .data-cell {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 70px;
    padding: 0.25rem;
    position: relative;
    transition: background-color 0.2s ease;
    cursor: pointer;
    
    &:hover {
      background-color: #f8fafc;
    }
    
    .empty-cell {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      color: #718096;
      font-size: 0.8rem;
      opacity: 0.7;
      transition: all 0.2s;
      background: none;
      border: none;
      padding: 0;
      cursor: pointer;
      font-family: inherit;
      
      &:hover {
        color: #4299e1;
        opacity: 1;
      }
    }
    
    .edit-button {
      position: absolute;
      top: 0.1rem;
      right: 0.1rem;
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid #e2e8f0;
      border-radius: 0.25rem;
      width: 1.25rem;
      height: 1.25rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      opacity: 0;
      transition: all 0.2s;
      color: #718096;
      
      svg {
        width: 0.75rem;
        height: 0.75rem;
      }
      
      &:hover {
        background: #fff;
        color: #4299e1;
        border-color: #cbd5e0;
      }
    }
    
    &:hover .edit-button {
      opacity: 1;
    }
    
    &.has-data {
      background-color: #f0f9ff;
      
      &:hover {
        background-color: #e0f2fe;
      }
    }
    
    .hours {
      font-weight: 600;
      font-size: 0.9rem;
      color: #0369a1;
      margin-bottom: 0.1rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
      text-align: center;
    }
    
    .activity {
      font-size: 0.7rem;
      color: #334155;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
      margin-bottom: 0.1rem;
    }
    
    .type-badge {
      font-size: 0.6rem;
      padding: 0.1rem 0.25rem;
      border-radius: 0.2rem;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.02em;
      margin-top: 0.1rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
      
      &.fixed {
        background-color: #dcfce7;
        color: #166534;
      }
      
      &.soft {
        background-color: #dbeafe;
        color: #1e40af;
      }
    }
  }
  
  .error-message {
    color: #ef4444;
    padding: 1rem;
    background-color: #fef2f2;
    border-radius: 0.375rem;
    margin-top: 1rem;
  }
  
  .loading,
  .no-data {
    text-align: center;
    padding: 2rem;
    color: #6b7280;
  }
</style>
