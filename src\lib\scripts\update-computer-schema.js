import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function updateComputerSchema() {
    const client = new MongoClient(uri);
    
    try {
        await client.connect();
        console.log('Connected to MongoDB');
        
        const db = client.db(dbName);
        const collection = db.collection('CustomerComputers');
        
        // Define the new fields with their types and defaults
        const updateSchema = {
            $set: {
                'desiredContractLengthYears': { $ifNull: ['$desiredContractLengthYears', 3] },
                'desiredContractLengthHrs': { $ifNull: ['$desiredContractLengthHrs', 4453] },
                'desiredContractStartDate': { $ifNull: ['$desiredContractStartDate', new Date('2025-01-01')] },
                'contractNumber': { $ifNull: ['$contractNumber', ''] },
                'contractAgeLimit': { $ifNull: ['$contractAgeLimit', 5] },
                'serialNumber': { $ifNull: ['$serialNumber', ''] },
                'contractHoursLimit': { $ifNull: ['$contractHoursLimit', 10000] },
                'productDesignation': { $ifNull: ['$productDesignation', ''] },
                'estimatedUtilizationHrsYr': { $ifNull: ['$estimatedUtilizationHrsYr', 1484.4498] },
                'deliveryDate': { $ifNull: ['$deliveryDate', new Date('2024-06-06')] },
                'engineAgeYrs': { $ifNull: ['$engineAgeYrs', 1.00] },
                'engineAgeHrs': { $ifNull: ['$engineAgeHrs', 850] },
                'contractLengthYrs': { $ifNull: ['$contractLengthYrs', 3.00] }
            }
        };

        // Update all documents in the collection
        const result = await collection.updateMany(
            {}, // Match all documents
            [{ // Use aggregation pipeline for complex updates
                $set: {
                    desiredContractLengthYears: updateSchema.$set.desiredContractLengthYears,
                    desiredContractLengthHrs: updateSchema.$set.desiredContractLengthHrs,
                    desiredContractStartDate: updateSchema.$set.desiredContractStartDate,
                    contractNumber: updateSchema.$set.contractNumber,
                    contractAgeLimit: updateSchema.$set.contractAgeLimit,
                    serialNumber: updateSchema.$set.serialNumber,
                    contractHoursLimit: updateSchema.$set.contractHoursLimit,
                    productDesignation: updateSchema.$set.productDesignation,
                    estimatedUtilizationHrsYr: updateSchema.$set.estimatedUtilizationHrsYr,
                    deliveryDate: updateSchema.$set.deliveryDate,
                    engineAgeYrs: updateSchema.$set.engineAgeYrs,
                    engineAgeHrs: updateSchema.$set.engineAgeHrs,
                    contractLengthYrs: updateSchema.$set.contractLengthYrs
                }
            }]
        );

        console.log(`Updated ${result.modifiedCount} documents`);
        
        // Verify the schema update
        const sampleComputer = await collection.findOne({});
        console.log('Sample computer document after update:', sampleComputer);
        
    } catch (error) {
        console.error('Error updating computer schema:', error);
    } finally {
        await client.close();
        console.log('Disconnected from MongoDB');
    }
}

// Run the update
updateComputerSchema().catch(console.error);
