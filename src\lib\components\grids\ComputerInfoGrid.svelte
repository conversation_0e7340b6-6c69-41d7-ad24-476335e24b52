<script lang="ts">
  export let computer: any;
  export let isEditing = false;
  export let editedComputer: any = null;

  const basicInfo = [
    { label: 'Name', field: 'name' },
    { label: 'Model', field: 'model' },
    { label: 'Manufacturer', field: 'manufacturer' },
    { label: 'Serial Number', field: 'serialNumber' },
    { label: 'Type', field: 'type' },
    { label: 'Operating System', field: ['operatingSystem', 'Operating System'] },
    { label: 'Computer Category', field: ['computerCategory', 'Computer Category', 'ComputerCategory'] }
  ];

  const productInfo = [
    { label: 'Product Name', field: ['productName', 'Product Name'] },
    { label: 'Product Designation', field: ['Product Designation'] },
    { label: 'Product Part Number', field: ['Product Part Number'] },
    { label: 'Product Validity Group', field: ['Product Validity Group'] },
    { label: 'Hours Contract Start', field: ['Hours Contract Start', 'hoursContractStart'] }
  ];

  function getValue(item: { field: string | string[] }) {
    if (!isEditing) {
      if (Array.isArray(item.field)) {
        for (const field of item.field) {
          if (computer[field] !== undefined && computer[field] !== null) {
            return computer[field];
          }
        }
        return 'N/A';
      }
      return computer[item.field] || 'N/A';
    } else {
      if (Array.isArray(item.field)) {
        const field = item.field[0]; // Use first field name for editing
        return editedComputer[field];
      }
      return editedComputer[item.field];
    }
  }

  function setValue(item: { field: string | string[] }, value: string) {
    if (isEditing && editedComputer) {
      if (Array.isArray(item.field)) {
        const field = item.field[0]; // Use first field name for editing
        editedComputer[field] = value;
      } else {
        editedComputer[item.field] = value;
      }
    }
  }
</script>

<div class="computer-info-grid">
  <section class="info-section">
    <h3>Basic Information</h3>
    <div class="info-grid">
      {#each basicInfo as item}
        <div class="info-item">
          <span class="label">{item.label}:</span>
          {#if isEditing}
            <input 
              type="text" 
              class="edit-input"
              value={getValue(item)}
              on:input={(e) => setValue(item, e.currentTarget.value)}
            />
          {:else}
            <span class="value">{getValue(item)}</span>
          {/if}
        </div>
      {/each}
    </div>
  </section>

  <section class="info-section">
    <h3>Product Information</h3>
    <div class="info-grid">
      {#each productInfo as item}
        <div class="info-item {item.label.includes('Product') ? 'highlight' : ''}">
          <span class="label">{item.label}:</span>
          {#if isEditing}
            {#if item.label === 'Hours Contract Start'}
              <input 
                type="number" 
                class="edit-input"
                value={getValue(item)}
                min="0"
                step="1"
                on:input={(e) => setValue(item, e.currentTarget.value)}
              />
            {:else}
              <input 
                type="text" 
                class="edit-input"
                value={getValue(item)}
                on:input={(e) => setValue(item, e.currentTarget.value)}
              />
            {/if}
          {:else}
            <span class="value">{getValue(item)}</span>
          {/if}
        </div>
      {/each}
    </div>
  </section>
</div>

<style>
  .computer-info-grid {
    display: grid;
    gap: 2rem;
  }

  .info-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  h3 {
    color: #2196f3;
    margin: 0 0 1rem 0;
    font-size: 1.2rem;
    font-weight: 500;
  }

  .info-grid {
    display: grid;
    gap: 1rem;
  }

  .info-item {
    display: grid;
    grid-template-columns: 180px 1fr;
    gap: 1rem;
    align-items: center;
    padding: 0.5rem;
    border-radius: 4px;
  }

  .info-item.highlight {
    background: #f5f5f5;
    border-left: 3px solid #2196f3;
  }

  .label {
    color: #666;
    font-weight: 500;
  }

  .value {
    color: #333;
  }

  .edit-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
  }

  .edit-input:focus {
    outline: none;
    border-color: #2196f3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
  }

  @media (max-width: 768px) {
    .info-item {
      grid-template-columns: 1fr;
      gap: 0.25rem;
    }

    .label {
      font-size: 0.9rem;
    }
  }
</style>
