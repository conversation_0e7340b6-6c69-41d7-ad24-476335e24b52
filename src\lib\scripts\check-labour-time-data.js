import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function checkLabourTimeData() {
    const client = new MongoClient(uri);
    
    try {
        await client.connect();
        console.log('Connected to MongoDB');
        
        const db = client.db(dbName);
        
        // List all collections
        const collections = await db.listCollections().toArray();
        console.log('\nCollections in database:');
        collections.forEach(col => console.log(`- ${col.name}`));
        
        // Check LabourTime collection
        const labourTimeCollection = db.collection('LabourTime');
        const count = await labourTimeCollection.countDocuments();
        console.log(`\nNumber of documents in LabourTime collection: ${count}`);
        
        if (count > 0) {
            const documents = await labourTimeCollection.find({}).toArray();
            console.log('\nSample documents:');
            documents.forEach(doc => {
                console.log('\nDocument:', {
                    _id: doc._id.toString(),
                    productDesignation: doc.productDesignation,
                    laborHours: doc.laborHours,
                    category: doc.category,
                    description: doc.description
                });
            });
        }
        
    } catch (error) {
        console.error('Error checking data:', error);
    } finally {
        await client.close();
        console.log('\nDisconnected from MongoDB');
    }
}

// Run the check
checkLabourTimeData().catch(console.error);
