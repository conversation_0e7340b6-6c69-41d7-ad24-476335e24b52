import { MongoClient } from 'mongodb';
import { json } from '@sveltejs/kit';

/** @type {import('./$types').RequestHandler} */
export async function GET() {
  const uri = 'mongodb://localhost:27017';
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    
    const result = {
      labourTime: [],
      serviceCodeHeader: [],
      partNumbersServiceCodeAction: [],
      counts: {}
    };
    
    // Get LabourTime data
    const labourTimeCollection = db.collection('LabourTime');
    result.labourTime = await labourTimeCollection.find({})
      .limit(10)
      .toArray();
    
    // Get ServiceCodeHeader data
    const serviceCodeHeaderCollection = db.collection('ServiceCodeHeader');
    result.serviceCodeHeader = await serviceCodeHeaderCollection.find({})
      .limit(10)
      .toArray();
    
    // Get PartNumbersServiceCodeAction data
    const partNumbersCollection = db.collection('PartNumbersServiceCodeAction');
    result.partNumbersServiceCodeAction = await partNumbersCollection.find({})
      .limit(10)
      .toArray();
    
    // Get counts for each collection
    result.counts = {
      labourTime: await labourTimeCollection.countDocuments(),
      serviceCodeHeader: await serviceCodeHeaderCollection.countDocuments(),
      partNumbersServiceCodeAction: await partNumbersCollection.countDocuments()
    };
    
    return json(result);
  } catch (error) {
    console.error('Error fetching workload data:', error);
    return json({ error: 'Failed to fetch workload data' }, { status: 500 });
  } finally {
    await client.close();
  }
}
