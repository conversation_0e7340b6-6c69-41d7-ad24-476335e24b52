// Define shared activity type
export interface Activity {
    _id: string;
    computerId: string;
    year: number;
    month: number;
    value?: number | string;
    activity?: string;
    hasFixed?: boolean;
    [key: string]: any;
}

export interface Computer {
    _id: string;
    name?: string;
    purchaseDate?: string;
    customerId?: string;
    [key: string]: any;
}

export interface ServiceContract {
    _id: string;
    customerId: string;
    startDate?: string;
    endDate?: string;
    [key: string]: any;
}
