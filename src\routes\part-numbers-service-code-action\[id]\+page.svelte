<script lang="ts">
    import { enhance } from '$app/forms';
    import { goto } from '$app/navigation';
    import { page } from '$app/stores';

    interface PartNumbersServiceCodeAction {
        _id: string;
        ServiceCode: string;
        PartNumber: string;
        ActionType: string;
        'Product Validity Group': string;
        Quantity: number;
        'Unit of Measure': string;
        createdAt?: string;
        updatedAt?: string;
    }

    export let data: { 
        item: PartNumbersServiceCodeAction | null, 
        isNew: boolean 
    };
    export let form: any;

    let isSubmitting = false;
    let isDeleting = false;

    // Form data
    let formData = {
        ServiceCode: data.item?.ServiceCode || '',
        PartNumber: data.item?.PartNumber || '',
        ActionType: data.item?.ActionType || '',
        ProductValidityGroup: data.item?.['Product Validity Group'] || '',
        Quantity: data.item?.Quantity || 0,
        UnitOfMeasure: data.item?.['Unit of Measure'] || ''
    };

    // Handle form responses
    $: if (form?.success && form?.id && data.isNew) {
        // Redirect to the created item's detail page
        goto(`/part-numbers-service-code-action/${form.id}`);
    }

    $: if (form?.success && form?.message === 'Item deleted successfully') {
        // Redirect back to list after successful deletion
        goto('/part-numbers-service-code-action');
    }

    function goBack() {
        goto('/part-numbers-service-code-action');
    }

    async function handleDelete() {
        if (!confirm('Are you sure you want to delete this item?')) {
            return;
        }
        isDeleting = true;
    }

    // Common action types for dropdown
    const actionTypes = ['Replace', 'Install', 'Remove', 'Repair', 'Inspect', 'Clean'];
    
    // Common units of measure for dropdown
    const unitsOfMeasure = ['Pieces', 'Hours', 'Liters', 'Kilograms', 'Meters', 'Each'];
</script>

<div class="container">
    <div class="header">
        <button class="btn btn-secondary" on:click={goBack}>
            ← Back to List
        </button>
        <h1>
            {data.isNew ? 'Add New' : 'Edit'} Part Numbers Service Code Action
        </h1>
    </div>

    {#if form?.error}
        <div class="alert alert-error">
            {form.error}
        </div>
    {/if}

    {#if form?.success && form?.message}
        <div class="alert alert-success">
            {form.message}
        </div>
    {/if}

    <div class="form-container">
        <form 
            method="POST" 
            action="?/save"
            use:enhance={() => {
                isSubmitting = true;
                return async ({ update }) => {
                    await update();
                    isSubmitting = false;
                };
            }}
        >
            <div class="form-grid">
                <div class="form-group">
                    <label for="ServiceCode">Service Code *</label>
                    <input 
                        type="text" 
                        id="ServiceCode" 
                        name="ServiceCode" 
                        bind:value={formData.ServiceCode}
                        required 
                        placeholder="e.g., S-D13"
                    />
                </div>

                <div class="form-group">
                    <label for="PartNumber">Part Number *</label>
                    <input 
                        type="text" 
                        id="PartNumber" 
                        name="PartNumber" 
                        bind:value={formData.PartNumber}
                        required 
                        placeholder="e.g., 22677426"
                    />
                </div>

                <div class="form-group">
                    <label for="ActionType">Action Type *</label>
                    <select 
                        id="ActionType" 
                        name="ActionType" 
                        bind:value={formData.ActionType}
                        required
                    >
                        <option value="">Select Action Type</option>
                        {#each actionTypes as actionType}
                            <option value={actionType}>{actionType}</option>
                        {/each}
                    </select>
                </div>

                <div class="form-group">
                    <label for="ProductValidityGroup">Product Validity Group *</label>
                    <input 
                        type="text" 
                        id="ProductValidityGroup" 
                        name="ProductValidityGroup" 
                        bind:value={formData.ProductValidityGroup}
                        required 
                        placeholder="e.g., D13"
                    />
                </div>

                <div class="form-group">
                    <label for="Quantity">Quantity *</label>
                    <input 
                        type="number" 
                        id="Quantity" 
                        name="Quantity" 
                        bind:value={formData.Quantity}
                        required 
                        min="0"
                        step="1"
                    />
                </div>

                <div class="form-group">
                    <label for="UnitOfMeasure">Unit of Measure *</label>
                    <select 
                        id="UnitOfMeasure" 
                        name="UnitOfMeasure" 
                        bind:value={formData.UnitOfMeasure}
                        required
                    >
                        <option value="">Select Unit</option>
                        {#each unitsOfMeasure as unit}
                            <option value={unit}>{unit}</option>
                        {/each}
                    </select>
                </div>
            </div>

            {#if !data.isNew && data.item}
                <div class="metadata">
                    <div class="metadata-item">
                        <strong>ID:</strong> {data.item._id}
                    </div>
                    {#if data.item.createdAt}
                        <div class="metadata-item">
                            <strong>Created:</strong> {new Date(data.item.createdAt).toLocaleString()}
                        </div>
                    {/if}
                    {#if data.item.updatedAt}
                        <div class="metadata-item">
                            <strong>Updated:</strong> {new Date(data.item.updatedAt).toLocaleString()}
                        </div>
                    {/if}
                </div>
            {/if}

            <div class="form-actions">
                <button 
                    type="submit" 
                    class="btn btn-primary"
                    disabled={isSubmitting}
                >
                    {isSubmitting ? 'Saving...' : (data.isNew ? 'Create' : 'Update')}
                </button>

                {#if !data.isNew}
                    <form 
                        method="POST" 
                        action="?/delete" 
                        style="display: inline;"
                        use:enhance={() => {
                            isDeleting = true;
                            return async ({ update }) => {
                                await update();
                                isDeleting = false;
                            };
                        }}
                    >
                        <button 
                            type="submit" 
                            class="btn btn-danger"
                            disabled={isDeleting}
                            on:click={handleDelete}
                        >
                            {isDeleting ? 'Deleting...' : 'Delete'}
                        </button>
                    </form>
                {/if}

                <button 
                    type="button" 
                    class="btn btn-secondary" 
                    on:click={goBack}
                >
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<style>
    .container {
        padding: 1rem;
        max-width: 800px;
        margin: 0 auto;
    }

    .header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .header h1 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 600;
    }

    .alert {
        padding: 1rem;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
    }

    .alert-error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .alert-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .form-container {
        background-color: white;
        padding: 2rem;
        border-radius: 0.5rem;
        border: 1px solid #dee2e6;
    }

    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }

    .form-group label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #374151;
    }

    .form-group input,
    .form-group select {
        padding: 0.75rem;
        border: 1px solid #d1d5db;
        border-radius: 0.375rem;
        font-size: 1rem;
    }

    .form-group input:focus,
    .form-group select:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .metadata {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.375rem;
        margin-bottom: 2rem;
    }

    .metadata-item {
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
        color: #6c757d;
    }

    .metadata-item:last-child {
        margin-bottom: 0;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 0.375rem;
        cursor: pointer;
        font-size: 1rem;
        font-weight: 500;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        transition: all 0.2s;
    }

    .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    .btn-primary {
        background-color: #007bff;
        color: white;
    }

    .btn-primary:hover:not(:disabled) {
        background-color: #0056b3;
    }

    .btn-secondary {
        background-color: #6c757d;
        color: white;
    }

    .btn-secondary:hover:not(:disabled) {
        background-color: #545b62;
    }

    .btn-danger {
        background-color: #dc3545;
        color: white;
    }

    .btn-danger:hover:not(:disabled) {
        background-color: #c82333;
    }

    @media (max-width: 768px) {
        .form-grid {
            grid-template-columns: 1fr;
        }
        
        .form-actions {
            flex-direction: column;
        }
        
        .btn {
            width: 100%;
            justify-content: center;
        }
    }
</style>
