import { MongoClient, ObjectId } from 'mongodb';
import { error } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const DB_NAME = 'ServiceContracts';

const COLLECTIONS = {
  QUOTATION_ROWS: 'QuotationRows',
  QUOTATION_HEADER: 'QuotationHeader'
};

/** @type {import('./$types').PageServerLoad} */
export async function load({ params }) {
  const client = new MongoClient(uri);
  
  try {
    console.log('Connecting to MongoDB...');
    await client.connect();
    const db = client.db(DB_NAME);
    
    // Get the quote ID from URL params
    const { quoteId } = params;
    console.log('Quote ID:', quoteId);
    
    if (!quoteId || !ObjectId.isValid(quoteId)) {
      throw error(400, 'Invalid quote ID');
    }

    // Get the specific quote first
    const quotesCollection = db.collection(COLLECTIONS.QUOTATION_HEADER);
    const quote = await quotesCollection.findOne({ _id: new ObjectId(quoteId) });
    if (!quote) {
      throw error(404, 'Quote not found');
    }
    console.log('Quote found:', quote._id.toString());

    // Get all rows for this specific quote - check both field names
    const rowsCollection = db.collection(COLLECTIONS.QUOTATION_ROWS);
    const query = {
      $or: [
        { QuoteId: new ObjectId(quoteId) },
        { QuotationId: new ObjectId(quoteId) }
      ]
    };
    console.log('Searching for rows with query:', JSON.stringify(query));
    
    // Fetch rows and sort them by RowType and RowOrder
    const rawRows = await rowsCollection
      .find(query)
      .sort({ RowType: 1, RowOrder: 1 })
      .toArray();
    
    console.log(`Found ${rawRows.length} rows`);
    
    if (rawRows.length > 0) {
      console.log('Sample row:', JSON.stringify(rawRows[0]));
    }

    // Convert raw rows to client format with null checks
    const rowsData = rawRows.map(row => ({
      _id: row._id?.toString() || '',
      RowType: row.RowType || '',
      RowOrder: Number(row.RowOrder) || 0,
      PackageName: row.PackageName || '',
      ServiceActivity: row.ServiceActivity || '',
      Cost: Number(row.Cost) || 0,
      OemImporter: Number(row.OemImporter) || 0,
      FleetOwner: Number(row.FleetOwner) || 0,
      SSP: Number(row.SSP) || 0,
      CustomerSpecific: !!row.CustomerSpecific,
      Required: !!row.Required,
      IncludeInOffer: row.IncludeInOffer !== false,
      QuoteId: row.QuoteId?.toString() || '',
      QuotationId: row.QuotationId?.toString() || '',
      QuoteNumber: quote.QuoteNumber || '',
      CreatedAt: row.CreatedAt?.toISOString() || new Date().toISOString(),
      UpdatedAt: row.UpdatedAt?.toISOString() || new Date().toISOString()
    }));

    // Calculate totals
    const totalAmount = rowsData.reduce((sum, row) => sum + (row.Cost || 0), 0);
    const totalSSP = rowsData.reduce((sum, row) => sum + (row.SSP || 0), 0);
    const totalOemImporter = rowsData.reduce((sum, row) => sum + (row.OemImporter || 0), 0);
    const totalFleetOwner = rowsData.reduce((sum, row) => sum + (row.FleetOwner || 0), 0);

    // Return the data with proper formatting
    return {
      rows: rowsData,
      currentQuote: {
        ...quote,
        _id: quote._id.toString(),
        QuoteNumber: quote.QuoteNumber || 'N/A'
      },
      totalAmount,
      totalSSP,
      totalOemImporter,
      totalFleetOwner
    };
  } catch (err) {
    console.error('Error loading quotation rows:', err);
    if (err && typeof err === 'object' && 'status' in err) {
      throw err;
    }
    throw error(500, 'Failed to load quotation rows');
  } finally {
    await client.close();
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  saveRow: async ({ request, params }) => {
    const client = new MongoClient(uri);
    
    try {
      const formData = await request.formData();
      const rowData = JSON.parse(formData.get('rowData')?.toString() || '{}');
      
      console.log('=== QuotationRows Save Operation ===');
      console.log('Input data:', rowData);
      console.log('Quote ID from params:', params.quoteId);
      
      // Validate required fields
      if (!rowData.ServiceActivity) {
        return {
          success: false,
          error: 'Service activity is required'
        };
      }

      await client.connect();
      const db = client.db(DB_NAME);
      const rowsCollection = db.collection(COLLECTIONS.QUOTATION_ROWS);
      
      // Prepare the document for MongoDB
      const now = new Date();
      const baseDoc = {
        RowType: rowData.RowType || '',
        RowOrder: parseInt(rowData.RowOrder) || 0,
        ServiceActivity: rowData.ServiceActivity,
        Cost: parseFloat(rowData.Cost) || 0,
        OemImporter: parseInt(rowData.OemImporter) || 0,
        FleetOwner: parseInt(rowData.FleetOwner) || 0,
        SSP: parseInt(rowData.SSP) || 0,
        CustomerSpecific: !!rowData.CustomerSpecific,
        Required: !!rowData.Required,
        IncludeInOffer: !!rowData.IncludeInOffer,
        QuoteId: new ObjectId(params.quoteId), // Always use the quote ID from the URL
        QuotationId: new ObjectId(params.quoteId), // Always use the quote ID from the URL
        UpdatedAt: now
      };

      console.log('\nPrepared base document:');
      console.log(JSON.stringify(baseDoc, null, 2));

      let savedDoc;
      
      if (rowData._id) {
        console.log('\nUpdating existing row:', rowData._id);
        // Update existing row
        const updateResult = await rowsCollection.findOneAndUpdate(
          { 
            _id: new ObjectId(rowData._id),
            QuoteId: new ObjectId(params.quoteId) // Ensure we only update rows belonging to this quote
          },
          { 
            $set: baseDoc
          },
          { 
            returnDocument: 'after'
          }
        );

        if (!updateResult?.value) {
          throw new Error('Failed to update quotation row');
        }

        savedDoc = updateResult.value;
        console.log('\nUpdated document:');
        console.log(JSON.stringify(savedDoc, null, 2));
      } else {
        console.log('\nInserting new row');
        // Insert new row
        const insertDoc = {
          ...baseDoc,
          CreatedAt: now
        };

        console.log('\nDocument to insert:');
        console.log(JSON.stringify(insertDoc, null, 2));

        const insertResult = await rowsCollection.insertOne(insertDoc);
        
        if (!insertResult?.acknowledged) {
          throw new Error('Failed to insert quotation row');
        }

        console.log('\nInsert result:', {
          acknowledged: insertResult.acknowledged,
          insertedId: insertResult.insertedId.toString()
        });

        const newDoc = await rowsCollection.findOne({ _id: insertResult.insertedId });
        
        if (!newDoc) {
          throw new Error('Failed to retrieve inserted row');
        }

        savedDoc = newDoc;
      }

      // Verify the write by reading it back
      const verifiedRow = await rowsCollection.findOne({ 
        _id: savedDoc._id,
        QuoteId: new ObjectId(params.quoteId)
      });

      if (!verifiedRow) {
        throw new Error('Failed to verify row write');
      }

      console.log('\nVerified document in database:');
      console.log(JSON.stringify(verifiedRow, null, 2));

      // Convert ObjectIds to strings for client
      const responseRow = {
        ...verifiedRow,
        _id: verifiedRow._id.toString(),
        QuoteId: verifiedRow.QuoteId.toString()
      };

      console.log('\nResponse to client:');
      console.log(JSON.stringify(responseRow, null, 2));
      console.log('\n=== End QuotationRows Save Operation ===\n');

      return { 
        success: true,
        row: responseRow
      };
    } catch (err) {
      console.error('\nError saving quotation row:', err);
      return {
        success: false,
        error: err instanceof Error ? err.message : 'Failed to save quotation row'
      };
    } finally {
      await client.close();
    }
  }
};
