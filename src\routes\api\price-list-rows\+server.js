import { MongoClient } from 'mongodb';
import { json } from '@sveltejs/kit';

// MongoDB connection URI
const uri = 'mongodb://localhost:27017';

// GET: Fetch all price list rows
export async function GET() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    
    // Using PascalCase for collection name per project convention
    const priceList = db.collection('PriceList');
    
    // Fetch all items from the PriceList collection
    // Sorting by Service Category and Service ID
    const items = await priceList.find({}).sort({ 
      "Service Category": 1, 
      "Service ID": 1 
    }).toArray();
    
    // Return the items as JSON
    return json(items);
    
  } catch (error) {
    console.error('Error fetching price list rows:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  } finally {
    await client.close();
  }
}
