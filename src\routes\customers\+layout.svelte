<script>
  import { page } from '$app/stores';
</script>

<div class="customers-layout">
  <div class="subnav">
    <a 
      href="/customers" 
      class="subnav-item" 
      class:active={$page.url.pathname === '/customers'}
    >
      Customer List
    </a>
    <a 
      href="/customers/computers" 
      class="subnav-item" 
      class:active={$page.url.pathname === '/customers/computers'}
    >
      All Computers
    </a>
  </div>

  <slot />
</div>

<style>
  .customers-layout {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .subnav {
    display: flex;
    gap: 0.5rem;
    padding: 0.5rem;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  }

  .subnav-item {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    text-decoration: none;
    color: #4b5563;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s;
  }

  .subnav-item:hover {
    background: #f3f4f6;
  }

  .subnav-item.active {
    background: #2563eb;
    color: white;
  }
</style>
