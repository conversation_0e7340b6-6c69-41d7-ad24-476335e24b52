import { json } from '@sveltejs/kit';
import { MongoClient, ObjectId } from 'mongodb';

// Function to recalculate AccHours for a specific computer
async function recalculateAccHours(db, computerId) {
    console.log(`🔄 Recalculating AccHours for computer: ${computerId}`);

    // Get all workload entries for this computer, sorted chronologically
    const workloadEntries = await db.collection('Workload')
        .find({ computerId: new ObjectId(computerId) })
        .sort({ year: 1, month: 1 })
        .toArray();

    let accumulatedHours = 0;
    const updates = [];

    // Calculate accumulated hours for each entry
    for (const entry of workloadEntries) {
        accumulatedHours += entry.hours || 0;

        // Prepare update operation
        updates.push({
            updateOne: {
                filter: { _id: entry._id },
                update: {
                    $set: {
                        accHours: accumulatedHours,
                        updatedAt: new Date()
                    }
                }
            }
        });
    }

    // Execute bulk update if we have updates
    if (updates.length > 0) {
        await db.collection('Workload').bulkWrite(updates);
        console.log(`✅ Updated AccHours for ${updates.length} workload entries`);
    }

    return accumulatedHours;
}

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

// Main endpoint handler
export const POST = async (event) => {
    try {
        const formData = await event.request.formData();
        const dataStr = formData.get('data');
        if (!dataStr || typeof dataStr !== 'string') {
            throw new Error('No data provided');
        }

        const data = JSON.parse(dataStr);
        const db = client.db('ServiceContracts');

        // Handle single cell update
        if (data.computerId && data.year && data.month) {
            const newRecord = {
                computerId: new ObjectId(data.computerId),
                year: Number(data.year),
                month: Number(data.month),
                hours: Number(data.hours) || 0,
                activity: data.activity || '',
                hasFixed: !!data.hasFixed,
                isIdle: false,
                createdAt: new Date(),
                updatedAt: new Date()
            };

            const result = await db.collection('Workload').insertOne(newRecord);

            // Recalculate AccHours for this computer
            await recalculateAccHours(db, data.computerId);

            return json({
                success: true,
                id: result.insertedId.toString(),
                action: 'created'
            });
        }

        // Handle bulk update
        if (data.startYear && data.startMonth && data.endYear && data.endMonth && data.hours !== undefined && data.computerId) {
            return handleBulkOperation(data, db);
        }

        throw new Error('Invalid request format');
    } catch (error) {
        console.error('POST Error:', error);
        const message = error instanceof Error ? error.message : 'Unknown error';
        return json({ success: false, error: message }, { status: 400 });
    }
};

// PUT endpoint for updating existing records
export async function PUT({ request }) {
    try {
        const formData = await request.formData();
        const dataStr = formData.get('data');
        if (!dataStr || typeof dataStr !== 'string') {
            throw new Error('No data provided');
        }

        const data = JSON.parse(dataStr);
        if (!data.computerId || !data.year || !data.month) {
            throw new Error('Missing required fields: computerId, year, month');
        }

        const db = client.db('ServiceContracts');

        // Update existing record
        const filter = {
            computerId: new ObjectId(data.computerId),
            year: Number(data.year),
            month: Number(data.month)
        };

        const updateResult = await db.collection('Workload').updateOne(
            filter,
            {
                $set: {
                    hours: Number(data.hours) || 0,
                    activity: data.activity || '',
                    hasFixed: !!data.hasFixed,
                    isIdle: false,
                    updatedAt: new Date()
                }
            },
            { upsert: true }
        );

        // Recalculate AccHours for this computer
        await recalculateAccHours(db, data.computerId);

        return json({
            success: true,
            modifiedCount: updateResult.modifiedCount,
            upsertedId: updateResult.upsertedId?.toString(),
            action: updateResult.upsertedId ? 'created' : 'updated'
        });
    } catch (error) {
        console.error('PUT Error:', error);
        const message = error instanceof Error ? error.message : 'Unknown error';
        return json({ success: false, error: message }, { status: 400 });
    }
}

async function handleBulkOperation(data, db) {
    try {
        const startYear = Number(data.startYear);
        const startMonth = Number(data.startMonth);
        const endYear = Number(data.endYear);
        const endMonth = Number(data.endMonth);
        const hours = Number(data.hours);
        const computerId = new ObjectId(data.computerId);

        // Generate all year-month combinations in the range
        const combinations = [];
        let currentYear = startYear;
        let currentMonth = startMonth;

        while (currentYear < endYear || (currentYear === endYear && currentMonth <= endMonth)) {
            combinations.push({ year: currentYear, month: currentMonth });
            currentMonth++;
            if (currentMonth > 12) {
                currentMonth = 1;
                currentYear++;
            }
        }

        // Prepare bulk operations
        const bulkOps = combinations.map(({ year, month }) => ({
            updateOne: {
                filter: {
                    computerId,
                    year,
                    month
                },
                update: {
                    $set: {
                        hours,
                        activity: data.activity || '',
                        hasFixed: false,
                        isIdle: false,
                        updatedAt: new Date()
                    },
                    $setOnInsert: {
                        createdAt: new Date()
                    }
                },
                upsert: true
            }
        }));

        const result = await db.collection('Workload').bulkWrite(bulkOps);

        // Recalculate AccHours for this computer after bulk operation
        await recalculateAccHours(db, data.computerId);

        return json({
            success: true,
            modifiedCount: result.modifiedCount,
            upsertedCount: result.upsertedCount,
            action: 'bulk_updated'
        });
    } catch (error) {
        console.error('Bulk operation error:', error);
        const message = error instanceof Error ? error.message : 'Unknown error';
        return json({ success: false, error: message }, { status: 400 });
    }
}
