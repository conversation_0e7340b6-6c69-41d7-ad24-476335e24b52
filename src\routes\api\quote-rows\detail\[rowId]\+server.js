import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

// MongoDB connection URI
const uri = 'mongodb://localhost:27017';

// GET: Fetch a specific quote row by ID
export async function GET({ params }) {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    
    // Get the QuoteRows collection
    const quoteRows = db.collection('QuoteRows');
    
    // Convert string ID to ObjectId for database query
    const rowId = new ObjectId(params.rowId);
    
    // Fetch the quote row
    const row = await quoteRows.findOne({ _id: rowId });
    
    if (!row) {
      return new Response(JSON.stringify({ error: 'Quote row not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Convert ObjectId to string for client-side use
    const formattedRow = {
      ...row,
      _id: row._id.toString(),
      quotationId: row.quotationId.toString()
    };
    
    return json(formattedRow);
  } catch (error) {
    console.error('Error fetching quote row details:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  } finally {
    await client.close();
  }
}
