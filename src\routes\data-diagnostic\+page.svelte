<script>
  /** @type {import('./$types').PageData} */
  export let data;

  $: diagnostic = data.diagnostic;
</script>

<svelte:head>
  <title>Data Diagnostic Report</title>
</svelte:head>

<div class="diagnostic-container">
  <div class="header">
    <button class="back-button" on:click={() => history.back()}>
      ← Back
    </button>
    <h1>Data Diagnostic Report</h1>
  </div>

  {#if data.error}
    <div class="error-section">
      <h2>❌ Error</h2>
      <p>{data.error}</p>
    </div>
  {:else if diagnostic}

    <!-- Search Criteria -->
    <div class="section">
      <h2>🔍 Search Criteria</h2>
      <div class="info-grid">
        <div><strong>Computer ID:</strong> {diagnostic.searchCriteria.computerId}</div>
        <div><strong>Product Designation:</strong> {diagnostic.searchCriteria.productDesignation}</div>
        <div><strong>Timestamp:</strong> {new Date(diagnostic.timestamp).toLocaleString()}</div>
      </div>
    </div>

    <!-- Issues Summary -->
    {#if diagnostic.issues.length > 0}
      <div class="section issues">
        <h2>⚠️ Issues Found ({diagnostic.issues.length})</h2>
        <ul>
          {#each diagnostic.issues as issue}
            <li>{issue}</li>
          {/each}
        </ul>
      </div>
    {:else}
      <div class="section success">
        <h2>✅ No Issues Found</h2>
        <p>All required data appears to be available.</p>
      </div>
    {/if}

    <!-- Collections Overview -->
    <div class="section">
      <h2>📚 Database Collections</h2>
      <div class="info-grid">
        <div><strong>Total Collections:</strong> {diagnostic.collections.total}</div>
        <div><strong>Available Collections:</strong></div>
      </div>
      <div class="collections-list">
        {#each diagnostic.collections.available as collection}
          <span class="collection-tag">{collection}</span>
        {/each}
      </div>
    </div>

    <!-- Computer Data -->
    <div class="section">
      <h2>💻 Computer Data</h2>
      <div class="info-grid">
        <div><strong>Total Computers:</strong> {diagnostic.computer.totalComputers.toLocaleString()}</div>
        <div><strong>Computer Found:</strong> {diagnostic.computer.found ? '✅ Yes' : '❌ No'}</div>
        <div><strong>Search Method:</strong>
          {diagnostic.computer.searchedAsObjectId ? 'ObjectId' : ''}
          {diagnostic.computer.searchedAsString ? 'String' : ''}
        </div>
      </div>

      {#if diagnostic.computer.computerData}
        <div class="computer-details">
          <h3>Computer Details:</h3>
          <pre>{JSON.stringify(diagnostic.computer.computerData, null, 2)}</pre>
        </div>
      {/if}
    </div>

    <!-- Workload Data -->
    <div class="section">
      <h2>📊 Workload Data</h2>
      <div class="info-grid">
        <div><strong>Total Workload Records:</strong> {diagnostic.workload.totalRecords.toLocaleString()}</div>
        <div><strong>Records for This Computer:</strong> {diagnostic.workload.recordsForComputer.toLocaleString()}</div>
        <div><strong>Has AccHours Field:</strong> {diagnostic.workload.hasAccHours ? '✅ Yes' : '❌ No'}</div>
      </div>

      {#if diagnostic.workload.sampleData.length > 0}
        <div class="sample-data">
          <h3>Sample Workload Data (First 10 records):</h3>
          <div class="table-container">
            <table>
              <thead>
                <tr>
                  <th>Year</th>
                  <th>Month</th>
                  <th>Hours</th>
                  <th>Activity</th>
                  <th>AccHours</th>
                </tr>
              </thead>
              <tbody>
                {#each diagnostic.workload.sampleData as record}
                  <tr>
                    <td>{record.year}</td>
                    <td>{record.month}</td>
                    <td>{record.hours}</td>
                    <td>{record.activity}</td>
                    <td class:missing={record.accHours === 'NOT SET'}>{record.accHours}</td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        </div>
      {:else}
        <div class="no-data">No workload data found for this computer.</div>
      {/if}
    </div>

    <!-- Service Code and Action Type Data -->
    <div class="section">
      <h2>🔧 Service Code and Action Type Data</h2>
      <div class="info-grid">
        <div><strong>Total ServiceCodeAndActionType Records:</strong> {diagnostic.serviceCodeAndActionType.totalRecords.toLocaleString()}</div>
        <div><strong>Records for This Product:</strong> {diagnostic.serviceCodeAndActionType.recordsForProduct.toLocaleString()}</div>
        <div><strong>Available Product Validity Groups:</strong> {diagnostic.serviceCodeAndActionType.availableProducts.toLocaleString()}</div>
      </div>

      <div class="product-designations">
        <h3>Available Product Validity Groups (First 20):</h3>
        <div class="tags-container">
          {#each diagnostic.serviceCodeAndActionType.productValidityGroups as product}
            <span class="product-tag" class:current={product === diagnostic.searchCriteria.productDesignation}>
              {product}
            </span>
          {/each}
        </div>
      </div>

      {#if diagnostic.serviceCodeAndActionType.sampleData.length > 0}
        <div class="sample-data">
          <h3>Sample ServiceCodeAndActionType Data (First 10 records):</h3>
          <div class="table-container">
            <table>
              <thead>
                <tr>
                  <th>Service Code</th>
                  <th>Action Type</th>
                  <th>Hours Frequency</th>
                  <th>Activity Purpose</th>
                  <th>Part Number</th>
                </tr>
              </thead>
              <tbody>
                {#each diagnostic.serviceCodeAndActionType.sampleData as record}
                  <tr>
                    <td>{record.serviceCode}</td>
                    <td>{record.actionType}</td>
                    <td>{record.internalNoOfHours}</td>
                    <td>{record.activityPurpose || 'N/A'}</td>
                    <td>{record.partNumber || 'N/A'}</td>
                  </tr>
                {/each}
              </tbody>
            </table>
          </div>
        </div>
      {:else}
        <div class="no-data">No ServiceCodeAndActionType data found for this product designation.</div>
      {/if}
    </div>

    <!-- Service Plan Data (Legacy) -->
    <div class="section">
      <h2>📋 Service Plan Data (Legacy)</h2>
      <div class="info-grid">
        <div><strong>Total ServicePlanProductDesignation Records:</strong> {diagnostic.servicePlan.totalRecords.toLocaleString()}</div>
        <div><strong>Records for This Product:</strong> {diagnostic.servicePlan.recordsForProduct.toLocaleString()}</div>
        <div><strong>Note:</strong> {diagnostic.servicePlan.note}</div>
      </div>
    </div>

  {/if}
</div>

<style>
  .diagnostic-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e2e8f0;
  }

  .header h1 {
    margin: 0;
    color: #1e293b;
    font-size: 2rem;
    font-weight: 700;
  }

  .back-button {
    padding: 0.5rem 1rem;
    background: #1e40af;
    color: white;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
  }

  .back-button:hover {
    background: #1e3a8a;
  }

  .section {
    margin-bottom: 2rem;
    background: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
  }

  .section.issues {
    border-color: #f59e0b;
    background: #fffbeb;
  }

  .section.success {
    border-color: #10b981;
    background: #f0fdf4;
  }

  .section h2 {
    margin: 0 0 1rem 0;
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 600;
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .info-grid div {
    padding: 0.5rem;
    background: #f8fafc;
    border-radius: 0.25rem;
  }

  .collections-list, .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
  }

  .collection-tag, .product-tag {
    background: #e2e8f0;
    color: #475569;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .product-tag.current {
    background: #1e40af;
    color: white;
  }

  .computer-details, .sample-data {
    margin-top: 1rem;
  }

  .computer-details h3, .sample-data h3 {
    margin: 0 0 0.5rem 0;
    color: #374151;
    font-size: 1.125rem;
  }

  .computer-details pre {
    background: #f1f5f9;
    padding: 1rem;
    border-radius: 0.375rem;
    overflow-x: auto;
    font-size: 0.875rem;
  }

  .table-container {
    overflow-x: auto;
    border-radius: 0.375rem;
    border: 1px solid #e2e8f0;
  }

  table {
    width: 100%;
    border-collapse: collapse;
  }

  th, td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
  }

  th {
    background: #f8fafc;
    font-weight: 600;
    color: #374151;
  }

  td.missing {
    background: #fef2f2;
    color: #dc2626;
    font-weight: 500;
  }

  .no-data {
    text-align: center;
    padding: 2rem;
    color: #64748b;
    font-style: italic;
  }

  .error-section {
    background: #fef2f2;
    border: 1px solid #fca5a5;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .error-section h2 {
    color: #dc2626;
    margin: 0 0 1rem 0;
  }

  ul {
    margin: 0;
    padding-left: 1.5rem;
  }

  li {
    margin-bottom: 0.5rem;
  }
</style>
