<!-- ServiceCodeTypesGrid.svelte -->
<script lang="ts">
    import BaseGrid from '$lib/components/grids/BaseGrid.svelte';
    import { createEventDispatcher } from 'svelte';
    
    // Define interface for service code type
    interface ServiceCodeType {
        _id?: string;
        ServiceCode?: string;
        ActionType?: string;
        Description?: string;
        ProductGroup?: string;
        ProductDemarcation?: string;
        ProductValidityGroup?: string;
        ExpectedResponse?: number;
        [key: string]: any; // Allow for additional properties
    }
    
    export let serviceCodeTypes: ServiceCodeType[] = [];
    export let productDemarcation: string = '';
    
    const dispatch = createEventDispatcher<{
        select: ServiceCodeType;
    }>();
    
    // Filter state
    let filterText = '';
    let filterColumn = 'all';
    
    // Define filter options
    const filterOptions = [
        { value: 'all', label: 'All Columns' },
        { value: 'ServiceCode', label: 'Service Code' },
        { value: 'ActionType', label: 'Action Type' },
        { value: 'Description', label: 'Description' },
        { value: 'ProductGroup', label: 'Product Group' },
        { value: 'ProductValidityGroup', label: 'Product Validity Group' }
    ];
    
    // Filter function
    $: filteredServiceCodeTypes = serviceCodeTypes.filter(item => {
        if (!filterText.trim()) return true;
        
        const searchTerm = filterText.toLowerCase().trim();
        
        // Search in all columns
        if (filterColumn === 'all') {
            return Object.keys(item).some(key => {
                if (typeof item[key] === 'string') {
                    return item[key].toLowerCase().includes(searchTerm);
                }
                return false;
            });
        }
        
        // Search in specific column
        if (typeof item[filterColumn] === 'string') {
            return item[filterColumn].toLowerCase().includes(searchTerm);
        }
        
        return false;
    });
    
    // Sort service code types for better display
    $: sortedServiceCodeTypes = [...filteredServiceCodeTypes].sort((a, b) => {
        // First sort by ServiceCode
        const serviceCodeA = a.ServiceCode || '';
        const serviceCodeB = b.ServiceCode || '';
        const codeComp = serviceCodeA.localeCompare(serviceCodeB);
        
        if (codeComp !== 0) return codeComp;
        
        // Then by ActionType
        const actionTypeA = a.ActionType || '';
        const actionTypeB = b.ActionType || '';
        return actionTypeA.localeCompare(actionTypeB);
    });
    
    function handleSelect(item: ServiceCodeType): void {
        dispatch('select', item);
    }
    
    function resetFilters() {
        filterText = '';
        filterColumn = 'all';
    }
</script>

<div class="service-code-types-grid">
    <div class="grid-header">
        <h3>ServiceCodeAndActionType for ProductValidityGroup: {productDemarcation}</h3>
        <div class="grid-info">
            <span class="count-badge">{sortedServiceCodeTypes.length} items found</span>
        </div>
    </div>
    
    <!-- Filter Controls -->
    <div class="filter-controls">
        <div class="filter-row">
            <div class="filter-group">
                <label for="filterColumn">Filter ServiceCodeAndActionType by:</label>
                <select id="filterColumn" bind:value={filterColumn} class="filter-dropdown">
                    {#each filterOptions as option}
                        <option value={option.value}>{option.label}</option>
                    {/each}
                </select>
            </div>
            
            <div class="filter-group search-group">
                <input 
                    type="text" 
                    bind:value={filterText} 
                    placeholder="Search ServiceCodeAndActionType..." 
                    class="filter-search"
                />
                {#if filterText}
                    <button class="clear-filter" on:click={resetFilters}>×</button>
                {/if}
            </div>
        </div>
    </div>
    
    {#if sortedServiceCodeTypes.length === 0}
        <div class="empty-state">
            <p>No ServiceCodeAndActionType items found for this validity group</p>
        </div>
    {:else}
        <div class="grid-container">
            <div class="grid-row header">
                <div class="grid-cell">Service Code</div>
                <div class="grid-cell">Action Type</div>
                <div class="grid-cell">Description</div>
                <div class="grid-cell">Product Group</div>
                <div class="grid-cell">Product Validity Group</div>
                <div class="grid-cell">Expected Response (days)</div>
            </div>
            
            {#each sortedServiceCodeTypes as item}
                <div class="grid-row" 
                    on:click={() => handleSelect(item)} 
                    on:keypress={(e) => e.key === 'Enter' && handleSelect(item)}
                    tabindex="0"
                    role="button">
                    <div class="grid-cell">{item.ServiceCode || '-'}</div>
                    <div class="grid-cell">{item.ActionType || '-'}</div>
                    <div class="grid-cell description">{item.Description || '-'}</div>
                    <div class="grid-cell">{item.ProductGroup || '-'}</div>
                    <div class="grid-cell">{item.ProductValidityGroup || '-'}</div>
                    <div class="grid-cell">{item.ExpectedResponse || '-'}</div>
                </div>
            {/each}
        </div>
    {/if}
</div>

<style>
    .service-code-types-grid {
        width: 100%;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        margin-bottom: 1.5rem;
        overflow: hidden;
    }
    
    .grid-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.25rem 1.5rem;
        background-color: #1e3a8a;
        border-bottom: 2px solid #475569;
        border-radius: 8px 8px 0 0;
    }
    
    .grid-header h3 {
        margin: 0;
        color: #ffffff;
        font-size: 1.25rem;
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
    
    .grid-info {
        display: flex;
        align-items: center;
    }
    
    .count-badge {
        padding: 0.25rem 0.75rem;
        background-color: #3b82f6;
        color: #ffffff;
        font-size: 0.875rem;
        font-weight: 600;
        border-radius: 9999px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
    
    .empty-state {
        padding: 2rem;
        text-align: center;
        color: #1e3a8a;
        font-size: 1rem;
        background-color: #fffff0;
        border-radius: 0 0 8px 8px;
        border-top: none;
    }
    
    .grid-container {
        display: grid;
        grid-template-columns: 1fr;
        width: 100%;
    }
    
    .grid-row {
        display: grid;
        grid-template-columns: 1fr 1fr 2fr 1fr 1fr 1fr;
        grid-auto-flow: row;
        border-bottom: 1px solid #cbd5e1;
        transition: all 0.2s ease-in-out;
        cursor: pointer;
    }
    
    .grid-row:hover {
        background-color: #fff9c4;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .grid-row:nth-child(odd):not(.header) {
        background-color: #fffff0;
    }
    
    .grid-row:nth-child(even):not(.header) {
        background-color: #fffde7;
    }
    
    .grid-row.header {
        background-color: #0d2473;
        font-weight: 600;
        color: #ffffff;
        cursor: default;
        position: sticky;
        top: 0;
        z-index: 10;
    }
    
    .grid-row.header:hover {
        background-color: #0d2473;
        transform: none;
        box-shadow: none;
    }
    
    .grid-cell {
        padding: 1rem;
        display: flex;
        align-items: center;
        border-right: 1px solid #e5e7eb;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #1e3a8a;
        font-weight: 500;
    }
    
    .grid-cell:last-child {
        border-right: none;
    }
    
    .grid-cell.description {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    /* Filter Controls */
    .filter-controls {
        padding: 1rem;
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
    }
    
    .filter-row {
        display: flex;
        gap: 1rem;
        align-items: center;
    }
    
    .filter-group {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .filter-group label {
        font-weight: 500;
        color: #1e3a8a;
    }
    
    .filter-dropdown {
        padding: 0.375rem 0.75rem;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        background-color: white;
        color: #1e3a8a;
    }
    
    .search-group {
        position: relative;
        flex-grow: 1;
    }
    
    .filter-search {
        padding: 0.375rem 0.75rem;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        width: 100%;
    }
    
    .clear-filter {
        position: absolute;
        right: 0.5rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #6c757d;
        font-size: 1.25rem;
        cursor: pointer;
        padding: 0 0.25rem;
    }
    
    .clear-filter:hover {
        color: #0d2473;
    }
</style>
