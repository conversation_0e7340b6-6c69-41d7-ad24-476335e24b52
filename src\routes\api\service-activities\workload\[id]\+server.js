// API endpoint for handling individual workload operations
import { json } from '@sveltejs/kit';
import { ObjectId } from 'mongodb';
import { client } from '$lib/db';

const dbName = 'ServiceContracts';

// Get a specific workload record
export async function GET({ params }) {
  try {
    const db = client.db(dbName);
    const id = params.id;
    
    // Validate ObjectId
    if (!ObjectId.isValid(id)) {
      return json({ error: 'Invalid ID format' }, { status: 400 });
    }
    
    const workload = await db.collection('Workload').findOne({ 
      _id: new ObjectId(id) 
    });
    
    if (!workload) {
      return json({ error: 'Workload record not found' }, { status: 404 });
    }
    
    return json(workload);
  } catch (error) {
    console.error('Error fetching workload record:', error);
    return json({ error: String(error) }, { status: 500 });
  }
}

// Update a workload record
export async function PUT({ request, params }) {
  try {
    const db = client.db(dbName);
    const id = params.id;
    const data = await request.json();
    
    // Validate ObjectId
    if (!ObjectId.isValid(id)) {
      return json({ error: 'Invalid ID format' }, { status: 400 });
    }
    
    // Validate required fields
    if (!data.computerId || !data.year || !data.month || data.hours === undefined) {
      return json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    // Convert computerId to ObjectId if it's a string
    if (typeof data.computerId === 'string') {
      data.computerId = new ObjectId(data.computerId);
    }
    
    // Update timestamp
    data.updatedAt = new Date();
    
    // Remove _id from data to prevent MongoDB error
    if (data._id) {
      delete data._id;
    }
    
    // Update record
    const result = await db.collection('Workload').updateOne(
      { _id: new ObjectId(id) },
      { $set: data }
    );
    
    if (result.matchedCount === 0) {
      return json({ error: 'Workload record not found' }, { status: 404 });
    }
    
    return json({ 
      _id: id,
      ...data
    });
  } catch (error) {
    console.error('Error updating workload record:', error);
    return json({ error: String(error) }, { status: 500 });
  }
}

// Delete a workload record
export async function DELETE({ params }) {
  try {
    const db = client.db(dbName);
    const id = params.id;
    
    // Validate ObjectId
    if (!ObjectId.isValid(id)) {
      return json({ error: 'Invalid ID format' }, { status: 400 });
    }
    
    const result = await db.collection('Workload').deleteOne({ 
      _id: new ObjectId(id) 
    });
    
    if (result.deletedCount === 0) {
      return json({ error: 'Workload record not found' }, { status: 404 });
    }
    
    return json({ success: true });
  } catch (error) {
    console.error('Error deleting workload record:', error);
    return json({ error: String(error) }, { status: 500 });
  }
}
