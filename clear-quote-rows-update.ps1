# Script to remove all files and directories related to /api/quote-rows/update/

Write-Host "Removing all code related to /api/quote-rows/update/..."

# Define path to the update directory
$updatePath = "C:\SvelteProjects\Svelte-Mongo3\src\routes\api\quote-rows\update"

# Check if directory exists
$directoryExists = Test-Path $updatePath

if ($directoryExists) {
    Write-Host "Found update directory at: $updatePath"
    
    # List contents before removal
    Write-Host "Directory contains the following items:"
    Get-ChildItem -Path $updatePath -Recurse | ForEach-Object {
        Write-Host "  - $($_.FullName.Replace($updatePath, ''))"
    }
    
    # Remove the directory
    Write-Host "Removing directory and all its contents..."
    Remove-Item -Path $updatePath -Recurse -Force
    
    # Verify removal
    if (Test-Path $updatePath) {
        Write-Host "Failed to remove update directory" -ForegroundColor Red
    } else {
        Write-Host "Successfully removed update directory" -ForegroundColor Green
    }
} else {
    Write-Host "update directory not found at: $updatePath" -ForegroundColor Yellow
}

# Also check for any server files in the quote-rows directory
$serverFile = "C:\SvelteProjects\Svelte-Mongo3\src\routes\api\quote-rows\+server.js"
if (Test-Path $serverFile) {
    Write-Host "Found server file at: $serverFile"
    
    # Check if it contains references to update
    $fileContent = Get-Content -Path $serverFile -Raw
    if ($fileContent -match "update") {
        Write-Host "Server file contains references to update, removing it..."
        Remove-Item -Path $serverFile -Force
        Write-Host "Removed server file"
    } else {
        Write-Host "Server file doesn't contain references to update, leaving it intact"
    }
}

Write-Host "Operation complete. Please restart your development server."
