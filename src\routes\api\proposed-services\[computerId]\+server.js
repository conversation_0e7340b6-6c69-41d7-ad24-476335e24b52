import { json } from '@sveltejs/kit';
import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';

/**
 * Handles GET requests to fetch proposed services for a computer based on workload data
 * @param {import('@sveltejs/kit').RequestEvent} event - The request event
 * @returns {Promise<Response>} JSON response with proposed services
 */
export async function GET({ params }) {
    const { computerId } = params;
    
    if (!computerId || !ObjectId.isValid(computerId)) {
        return json({ success: false, error: 'Invalid computer ID' }, { status: 400 });
    }
    
    try {
        const client = new MongoClient(uri);
        await client.connect();
        const db = client.db('ServiceContracts');
        
        // Get collections
        const workloadCollection = db.collection('Workload');
        const computerCollection = db.collection('CustomerComputers');
        const serviceTypesCollection = db.collection('ServiceTypes');
        const serviceTypeDetailsCollection = db.collection('ServiceTypeDetails');
        
        // Get computer details
        const computer = await computerCollection.findOne({
            _id: new ObjectId(computerId)
        });
        
        if (!computer) {
            return json({ success: false, error: 'Computer not found' }, { status: 404 });
        }
        
        // Get workload data for this computer
        const workloadData = await workloadCollection.find({
            computerId: new ObjectId(computerId)
        }).sort({ year: -1, month: -1 }).toArray();
        
        // Get all service types - handle case where isActive might not exist
        const serviceTypes = await serviceTypesCollection.find({}).toArray();
        
        // Get service type details for all service types
        const serviceTypeIds = serviceTypes.map(st => st._id);
        const serviceTypeDetails = await serviceTypeDetailsCollection.find({
            serviceTypeId: { $in: serviceTypeIds }
        }).toArray();
        
        // Calculate total operating hours from workload
        const totalHours = workloadData.reduce((sum, entry) => sum + entry.hours, 0);
        
        // Get product designation and type
        const productDesignation = computer.ProductDesignation || computer.productDesignation;
        const productType = computer.productType || computer.type;
        
        // Build proposed services based on workload and product info
        /** @type {Array<{serviceType: string, serviceTypeId: string, hours: number, nextDue: Date, frequency: string, hoursUntilDue: number, description: string, components: string[], totalPrice: number, totalTime: number}>} */
        const proposedServices = [];
        
        // Group service types with their details
        const serviceTypeMap = new Map();
        serviceTypes.forEach(st => {
            serviceTypeMap.set(st._id.toString(), {
                ...st,
                details: []
            });
        });
        
        // Add details to their respective service types
        serviceTypeDetails.forEach(detail => {
            const serviceTypeId = detail.serviceTypeId.toString();
            if (serviceTypeMap.has(serviceTypeId)) {
                serviceTypeMap.get(serviceTypeId).details.push(detail);
            }
        });
        
        // Convert map to array
        const enrichedServiceTypes = Array.from(serviceTypeMap.values());
        
        // If no service types found, create some default ones based on workload
        let serviceTypesToUse = enrichedServiceTypes;
        if (enrichedServiceTypes.length === 0) {
            // Create default service plans based on workload data
            serviceTypesToUse = [
                {
                    serviceTypeName: 'Regular Maintenance',
                    details: [{
                        intervalHRS: 250,
                        intervalMonths: 6,
                        description: 'Standard maintenance service',
                        component: 'Engine'
                    }],
                    totalPrice: 0,
                    totalTime: 4
                },
                {
                    serviceTypeName: 'Major Service',
                    details: [{
                        intervalHRS: 1000,
                        intervalMonths: 12,
                        description: 'Comprehensive service including all components',
                        component: 'Full System'
                    }],
                    totalPrice: 0,
                    totalTime: 8
                },
                {
                    serviceTypeName: 'Safety Inspection',
                    details: [{
                        intervalHRS: 500,
                        intervalMonths: 3,
                        description: 'Safety and compliance check',
                        component: 'Safety Systems'
                    }],
                    totalPrice: 0,
                    totalTime: 2
                }
            ];
        }
        
        // Build proposed services based on service types and workload
        serviceTypesToUse.forEach(serviceType => {
            // Calculate next due date based on interval hours or months
            const details = serviceType.details && serviceType.details.length > 0 ? 
                serviceType.details[0] : { intervalHRS: 250, intervalMonths: 6 };
            
            const intervalHours = details.intervalHRS || 250;
            const intervalMonths = details.intervalMonths || 6;
            
            // Calculate next due date
            const today = new Date();
            let nextDueDate = new Date();
            
            if (intervalMonths > 0) {
                nextDueDate.setMonth(today.getMonth() + intervalMonths);
            }
            
            // If we have hours-based interval and total hours
            let hoursUntilDue = 0;
            if (intervalHours > 0 && totalHours > 0) {
                hoursUntilDue = intervalHours - (totalHours % intervalHours);
                if (hoursUntilDue === intervalHours) hoursUntilDue = 0;
            }
            
            // Create proposed service entry
            proposedServices.push({
                serviceType: serviceType.serviceTypeName || 'Maintenance Service',
                serviceTypeId: serviceType._id ? serviceType._id.toString() : 'default',
                hours: totalHours,
                nextDue: nextDueDate,
                frequency: intervalHours > 0 
                    ? `Every ${intervalHours} hours` 
                    : intervalMonths > 0 
                        ? `Every ${intervalMonths} months` 
                        : 'As needed',
                hoursUntilDue: hoursUntilDue,
                description: details.description || serviceType.serviceTypeName || 'Regular maintenance service',
                components: serviceType.details ? 
                    serviceType.details
                        .map((/** @type {any} */ d) => d.component)
                        .filter(Boolean) : 
                    ['General'],
                totalPrice: serviceType.totalPrice || 0,
                totalTime: serviceType.totalTime || 0
            });
        });
        
        return json({
            success: true,
            computer: {
                id: computer._id.toString(),
                productType: productType || 'Unknown',
                model: computer.model || 'Unknown',
                serialNumber: computer.serialNumber || 'Unknown',
                productDesignation: productDesignation || 'Unknown'
            },
            workload: {
                totalHours: totalHours,
                entries: workloadData.length,
                monthlyData: workloadData.map(entry => ({
                    year: entry.year,
                    month: entry.month,
                    hours: entry.hours,
                    activity: entry.activity || 'General',
                    type: entry.type || 'Standard'
                }))
            },
            proposedServices: proposedServices
        });
    } catch (error) {
        console.error('Error generating proposed services:', error);
        
        // Create fallback response with default data
        return json({
            success: true,
            computer: {
                id: computerId,
                productType: 'Unknown',
                model: 'Unknown',
                serialNumber: 'Unknown',
                productDesignation: 'Unknown'
            },
            workload: {
                totalHours: 0,
                entries: 0,
                monthlyData: []
            },
            proposedServices: [
                {
                    serviceType: 'Regular Maintenance',
                    serviceTypeId: 'default-1',
                    hours: 0,
                    nextDue: new Date(),
                    frequency: 'Every 250 hours',
                    hoursUntilDue: 250,
                    description: 'Standard maintenance service',
                    components: ['Engine'],
                    totalPrice: 0,
                    totalTime: 4
                },
                {
                    serviceType: 'Major Service',
                    serviceTypeId: 'default-2',
                    hours: 0,
                    nextDue: new Date(new Date().setMonth(new Date().getMonth() + 12)),
                    frequency: 'Every 12 months',
                    hoursUntilDue: 1000,
                    description: 'Comprehensive service including all components',
                    components: ['Full System'],
                    totalPrice: 0,
                    totalTime: 8
                }
            ]
        });
    }
}
