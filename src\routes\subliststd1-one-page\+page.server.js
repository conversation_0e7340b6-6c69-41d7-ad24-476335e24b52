import { error } from '@sveltejs/kit';
import { ObjectId } from 'mongodb';
import { getCollection } from '$lib/db/mongo';

/** @type {import('./$types').PageServerLoad} */
export async function load({ url }) {
  // Get collection, parent collection, parent ID, and item ID from URL parameters
  const collection = url.searchParams.get('collection') || 'CustomerComputers';
  const parentCollection = url.searchParams.get('parentCollection') || 'Customers';
  const parentId = url.searchParams.get('parentId');
  const itemId = url.searchParams.get('itemId');

  if (!itemId) {
    throw error(400, 'Item ID is required');
  }

  // Validate ObjectId
  if (!ObjectId.isValid(itemId)) {
    throw error(400, 'Invalid item ID format');
  }

  try {
    // Get the collection
    const coll = await getCollection(collection);
    
    // Find the item by ID
    const item = await coll.findOne({ _id: new ObjectId(itemId) });
    
    if (!item) {
      throw error(404, 'Item not found');
    }

    // Get parent item if parentId is provided
    let parentItem = null;
    if (parentId && ObjectId.isValid(parentId)) {
      const parentColl = await getCollection(parentCollection);
      parentItem = await parentColl.findOne({ _id: new ObjectId(parentId) });
    }

    // Convert ObjectIds to strings for client-side use
    const itemData = { ...item };
    if (itemData._id) itemData._id = itemData._id.toString();
    if (itemData.customerId) itemData.customerId = itemData.customerId.toString();
    
    let parentData = null;
    if (parentItem) {
      parentData = { ...parentItem };
      if (parentData._id) parentData._id = parentData._id.toString();
    }

    // Return the item data
    return {
      collection,
      parentCollection,
      parentId,
      itemId,
      item: itemData,
      parentItem: parentData
    };
  } catch (err) {
    console.error('Error loading item:', err);
    throw error(500, 'Failed to load item');
  }
}

/** @type {import('./$types').Actions} */
export const actions = {
  updateItem: async ({ request }) => {
    const formData = await request.formData();
    const itemId = formData.get('_id');
    const collection = formData.get('collection') || 'CustomerComputers';
    
    if (!itemId || !ObjectId.isValid(itemId)) {
      throw error(400, 'Valid item ID is required');
    }

    try {
      const coll = await getCollection(collection);
      
      // Build update object from form data
      const updateData = {};
      for (const [key, value] of formData.entries()) {
        if (key !== '_id' && key !== 'collection') {
          updateData[key] = value;
        }
      }
      
      // Handle special fields
      if (updateData.customerId && ObjectId.isValid(updateData.customerId)) {
        updateData.customerId = new ObjectId(updateData.customerId);
      }
      
      // Update the item
      await coll.updateOne(
        { _id: new ObjectId(itemId) },
        { $set: updateData }
      );
      
      return {
        success: true,
        message: 'Item updated successfully'
      };
    } catch (err) {
      console.error('Error updating item:', err);
      throw error(500, 'Failed to update item');
    }
  },
  
  deleteItem: async ({ request }) => {
    const formData = await request.formData();
    const itemId = formData.get('_id');
    const collection = formData.get('collection') || 'CustomerComputers';
    const parentId = formData.get('parentId');
    const parentCollection = formData.get('parentCollection') || 'Customers';
    
    if (!itemId || !ObjectId.isValid(itemId)) {
      throw error(400, 'Valid item ID is required');
    }

    try {
      const coll = await getCollection(collection);
      
      // Delete the item
      await coll.deleteOne({ _id: new ObjectId(itemId) });
      
      return {
        success: true,
        message: 'Item deleted successfully',
        parentId,
        parentCollection
      };
    } catch (err) {
      console.error('Error deleting item:', err);
      throw error(500, 'Failed to delete item');
    }
  }
};
