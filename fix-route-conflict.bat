@echo off
echo Fixing route conflict...

echo.
echo Step 1: Removing old conflicting routes
:: Remove the conflicting files
if exist "src\routes\api\quote-rows\update\[id]\+server.js" (
    echo Removing src\routes\api\quote-rows\update\[id]\+server.js
    del /Q "src\routes\api\quote-rows\update\[id]\+server.js"
)

if exist "src\routes\api\quote-rows\update\[rowId]\+server.js" (
    echo Removing src\routes\api\quote-rows\update\[rowId]\+server.js
    del /Q "src\routes\api\quote-rows\update\[rowId]\+server.js"
)

:: Try to remove directories if empty
if exist "src\routes\api\quote-rows\update\[id]" (
    rmdir "src\routes\api\quote-rows\update\[id]" 2>nul
)

if exist "src\routes\api\quote-rows\update\[rowId]" (
    rmdir "src\routes\api\quote-rows\update\[rowId]" 2>nul
)

echo.
echo Step 2: Verifying new route exists
if exist "src\routes\api\quote-rows\update-row\[id]\+server.js" (
    echo New route file exists: src\routes\api\quote-rows\update-row\[id]\+server.js
) else (
    echo WARNING: New route file not found
)

echo.
echo Step 3: Cleaning previous temporary files
if exist "delete-conflicting-route.ps1" (
    del "delete-conflicting-route.ps1"
)
if exist "remove-conflict.bat" (
    del "remove-conflict.bat"
)

echo.
echo Route conflict fix complete!
echo Please restart your dev server with: npm run dev
