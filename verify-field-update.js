const { MongoClient } = require('mongodb');

async function verifyFieldUpdate() {
    const uri = 'mongodb://localhost:27017';
    const client = new MongoClient(uri);

    try {
        await client.connect();
        const db = client.db('ServiceContracts');
        const collection = db.collection('ProductValidityGroup');

        // Check for documents with old field name
        const oldFieldCount = await collection.countDocuments({
            "Product Validity GRoup": { $exists: true }
        });

        // Check for documents with new field name
        const newFieldCount = await collection.countDocuments({
            "ProductValidityGroup": { $exists: true }
        });

        console.log(`Documents with old field "Product Validity GRoup": ${oldFieldCount}`);
        console.log(`Documents with new field "ProductValidityGroup": ${newFieldCount}`);

        // Sample a document to verify structure
        const sampleDoc = await collection.findOne({});
        console.log('\nSample document structure:', JSON.stringify(sampleDoc, null, 2));

    } catch (error) {
        console.error('Error verifying field update:', error);
    } finally {
        await client.close();
    }
}

verifyFieldUpdate();
