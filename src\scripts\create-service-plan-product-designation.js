// Script to create ServicePlanProductDesignation collection
// This script creates service plan items for a ProductDesignation based on ServiceCodeAndActionType
// For each ServiceCodeAndActionType item, it creates multiple entries based on InternalNoOfHours frequency
// from 0 hours up to 10,000 hours

import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);
const dbName = 'ServiceContracts';

/**
 * Create ServicePlanProductDesignation collection and populate it
 * @param {string} productDesignation - The ProductDesignation to create plan for
 */
async function createServicePlanProductDesignation(productDesignation = null) {
  try {
    // Connect to MongoDB
    await client.connect();
    console.log('Connected to MongoDB');
    
    // Access the ServiceContracts database
    const db = client.db(dbName);
    
    // Check if ServicePlanProductDesignation collection already exists
    const collections = await db.listCollections({ name: 'ServicePlanProductDesignation' }).toArray();
    if (collections.length > 0) {
      console.log('ServicePlanProductDesignation collection already exists. Dropping it before recreation.');
      await db.collection('ServicePlanProductDesignation').drop();
    }
    
    // Create the new ServicePlanProductDesignation collection
    await db.createCollection('ServicePlanProductDesignation');
    console.log('Created new ServicePlanProductDesignation collection');
    
    // Create indexes for better performance
    await db.collection('ServicePlanProductDesignation').createIndexes([
      { key: { productDesignation: 1 } },
      { key: { serviceCode: 1 } },
      { key: { accHours: 1 } },
      { key: { productDesignation: 1, accHours: 1 } }
    ]);
    console.log('Created indexes on ServicePlanProductDesignation collection');
    
    // If no specific ProductDesignation provided, get all unique ProductDesignations
    let productDesignations = [];
    if (productDesignation) {
      productDesignations = [productDesignation];
    } else {
      // Get all unique ProductValidityGroup values from ServiceCodeAndActionType
      const serviceCodeCollection = db.collection('ServiceCodeAndActionType');
      productDesignations = await serviceCodeCollection.distinct('ProductValidityGroup');
      console.log(`Found ${productDesignations.length} unique ProductValidityGroup values`);
    }
    
    let totalItemsCreated = 0;
    
    // Process each ProductDesignation
    for (const currentProductDesignation of productDesignations) {
      if (!currentProductDesignation) continue;
      
      console.log(`\nProcessing ProductDesignation: ${currentProductDesignation}`);
      
      // Get all ServiceCodeAndActionType items for this ProductDesignation
      const serviceCodeTypes = await db.collection('ServiceCodeAndActionType')
        .find({ ProductValidityGroup: currentProductDesignation })
        .toArray();
      
      console.log(`Found ${serviceCodeTypes.length} ServiceCodeAndActionType items for ${currentProductDesignation}`);
      
      const servicePlanItems = [];
      
      // Process each ServiceCodeAndActionType item
      for (const serviceItem of serviceCodeTypes) {
        const frequency = serviceItem.InternalNoOfHours || 1000; // Default to 1000 if not specified
        
        console.log(`  Processing ${serviceItem.ServiceCode}-${serviceItem.ActionType} with frequency ${frequency} hours`);
        
        // Create multiple items based on frequency from 0 to 10,000 hours
        let sequenceNumber = 1;
        for (let accHours = 0; accHours <= 10000; accHours += frequency) {
          const servicePlanItem = {
            productDesignation: currentProductDesignation,
            serviceCode: serviceItem.ServiceCode || '',
            actionType: serviceItem.ActionType || '',
            activityPurpose: serviceItem.ActivityPurpose || '',
            serviceActivityLabel: serviceItem.ServiceActivityLabel || '',
            partNumber: serviceItem.PartNumber || 0,
            unitOfMeasure: serviceItem.UnitOfMeasure || '',
            quantity: serviceItem.Quantity || 0,
            accHours: accHours,
            frequency: frequency,
            sequenceNumber: sequenceNumber,
            createdAt: new Date(),
            updatedAt: new Date()
          };
          
          servicePlanItems.push(servicePlanItem);
          sequenceNumber++;
        }
      }
      
      // Insert all service plan items for this ProductDesignation
      if (servicePlanItems.length > 0) {
        const result = await db.collection('ServicePlanProductDesignation').insertMany(servicePlanItems);
        console.log(`  Inserted ${result.insertedCount} service plan items for ${currentProductDesignation}`);
        totalItemsCreated += result.insertedCount;
      }
    }
    
    console.log(`\nTotal items created: ${totalItemsCreated}`);
    
    // Display a sample of the new collection
    const sampleDocs = await db.collection('ServicePlanProductDesignation').find().limit(10).toArray();
    console.log('\nSample documents from the new collection:');
    sampleDocs.forEach((doc, index) => {
      console.log(`${index + 1}. ${doc.productDesignation} - ${doc.serviceCode}-${doc.actionType} at ${doc.accHours} hours (seq: ${doc.sequenceNumber})`);
    });
    
    // Show statistics
    const totalCount = await db.collection('ServicePlanProductDesignation').countDocuments();
    const distinctProductDesignations = await db.collection('ServicePlanProductDesignation').distinct('productDesignation');
    console.log(`\nCollection statistics:`);
    console.log(`Total documents: ${totalCount}`);
    console.log(`Distinct ProductDesignations: ${distinctProductDesignations.length}`);
    
  } catch (error) {
    console.error('Error creating ServicePlanProductDesignation collection:', error);
  } finally {
    // Close the connection
    await client.close();
    console.log('\nMongoDB connection closed');
  }
}

// Check if a specific ProductDesignation was provided as command line argument
const productDesignationArg = process.argv[2];

if (productDesignationArg) {
  console.log(`Creating service plan for specific ProductDesignation: ${productDesignationArg}`);
  createServicePlanProductDesignation(productDesignationArg);
} else {
  console.log('Creating service plan for all ProductDesignations found in ServiceCodeAndActionType');
  createServicePlanProductDesignation();
}
