/**
 * Utility functions for date formatting and manipulation
 */

/**
 * Format a date to a readable string
 * @param {string|Date|null} date - The date to format
 * @param {string} format - Optional format string (defaults to locale date string)
 * @returns {string} Formatted date string or placeholder if date is invalid
 */
export function formatDate(date, format = 'locale') {
  if (!date) return 'Not set';
  
  try {
    const dateObj = new Date(date);
    
    if (isNaN(dateObj.getTime())) {
      return 'Invalid date';
    }
    
    if (format === 'locale') {
      return dateObj.toLocaleDateString();
    } else if (format === 'iso') {
      return dateObj.toISOString().split('T')[0];
    } else if (format === 'full') {
      return dateObj.toLocaleDateString() + ' ' + dateObj.toLocaleTimeString();
    } else if (format === 'year-month') {
      return `${dateObj.getFullYear()}-${String(dateObj.getMonth() + 1).padStart(2, '0')}`;
    }
    
    return dateObj.toLocaleDateString();
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid date';
  }
}

/**
 * Format a date range as a string
 * @param {string|Date|null} startDate - Start date
 * @param {string|Date|null} endDate - End date
 * @returns {string} Formatted date range
 */
export function formatDateRange(startDate, endDate) {
  const start = formatDate(startDate);
  const end = formatDate(endDate);
  
  if (start === 'Not set' && end === 'Not set') {
    return 'Date range not set';
  }
  
  return `${start} - ${end}`;
}

/**
 * Get the number of days between two dates
 * @param {string|Date} startDate - Start date
 * @param {string|Date} endDate - End date
 * @returns {number} Number of days between dates
 */
export function getDaysBetween(startDate, endDate) {
  if (!startDate || !endDate) {
    throw new Error('Both start and end dates are required');
  }
  
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffTime = Math.abs(end.getTime() - start.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * Checks if a date is in the past
 * @param {string|Date} date - Date to check
 * @returns {boolean} True if date is in the past
 */
export function isDateInPast(date) {
  if (!date) {
    throw new Error('Date is required');
  }
  
  const checkDate = new Date(date);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return checkDate < today;
}

/**
 * Get an array of years for selection menus
 * @param {number|null} startYear - Start year (defaults to 5 years ago)
 * @param {number|null} endYear - End year (defaults to 5 years in the future)
 * @returns {number[]} Array of years
 */
export function getYearsArray(startYear = null, endYear = null) {
  const currentYear = new Date().getFullYear();
  const start = startYear !== null ? startYear : currentYear - 5;
  const end = endYear !== null ? endYear : currentYear + 5;
  
  if (start > end) {
    throw new Error('Start year cannot be greater than end year');
  }
  
  const years = [];
  for (let year = start; year <= end; year++) {
    years.push(year);
  }
  
  return years;
}

/**
 * Get a month name from month number (1-12)
 * @param {number} monthNumber - Month number (1-12)
 * @param {boolean} short - Whether to return short month name
 * @returns {string} Month name
 */
export function getMonthName(monthNumber, short = false) {
  if (monthNumber < 1 || monthNumber > 12) {
    throw new Error('Month number must be between 1 and 12');
  }
  
  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  
  const shortMonths = [
    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
  ];
  
  // Adjust for 0-based index
  const index = ((monthNumber - 1) % 12 + 12) % 12;
  
  return short ? shortMonths[index] : months[index];
}

/**
 * Calculate date in the future or past
 * @param {number} days - Number of days to add (negative for past)
 * @param {Date} startDate - Starting date (defaults to today)
 * @returns {Date} The calculated date
 */
export function getDatePlusDays(days, startDate = new Date()) {
  if (typeof days !== 'number') {
    throw new Error('Days must be a number');
  }
  
  const date = new Date(startDate);
  date.setDate(date.getDate() + days);
  return date;
}
