<script lang="ts">
  import { enhance } from '$app/forms';
  import { goto } from '$app/navigation';
  import ComputerForm from '$lib/components/ComputerForm.svelte';
  
  // Define types
  interface Customer {
    _id: string;
    companyName?: string;
    name?: string;
    type: string;
    city?: string;
    country?: string;
  }
  
  interface Computer {
    _id: string;
    customerId: string;
    name: string;
    serialNumber?: string;
    type?: string;
    model?: string;
    manufacturer?: string;
    productType?: string;
    productDesignation?: string;
    engineType?: string;
    operatingHours?: number;
    installationDate?: Date | null;
    manufactureDate?: Date | null;
    isActive?: boolean;
    notes?: string;
    purchaseDate?: Date | null;
    warrantyEndDate?: Date | null;
    createdAt: Date;
    customer?: Customer;
  }
  
  // Get data from server
  export let data;
  
  // Component state
  let computers: Computer[] = data.computers || [];
  let customers: Customer[] = data.customers || [];
  let selectedCustomer: Customer | null = data.selectedCustomer;
  let selectedCustomerId: string = selectedCustomer?._id || '';
  let showAddComputerForm = false;
  let isSubmitting = false;
  let formError = '';
  let successMessage = '';
  
  // Filter computers based on selected customer
  $: filteredComputers = selectedCustomerId 
    ? computers.filter(c => c.customerId === selectedCustomerId)
    : computers;
  
  // Format date for display
  function formatDate(date: Date | null | undefined): string {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString();
  }
  
  // Get customer name by ID
  function getCustomerName(customerId: string): string {
    const customer = customers.find(c => c._id === customerId);
    return customer?.companyName || customer?.name || 'Unknown Customer';
  }
  
  // Get customer type by ID
  function getCustomerType(customerId: string): string {
    const customer = customers.find(c => c._id === customerId);
    return customer?.type || 'Retail';
  }
  
  // Handle customer selection
  function handleCustomerChange() {
    // Update URL with selected customer ID
    const url = new URL(window.location.href);
    if (selectedCustomerId) {
      url.searchParams.set('customerId', selectedCustomerId);
    } else {
      url.searchParams.delete('customerId');
    }
    history.pushState({}, '', url);
    
    // Update selected customer
    selectedCustomer = selectedCustomerId 
      ? customers.find(c => c._id === selectedCustomerId) || null
      : null;
  }
  
  // Handle computer selection
  function selectComputer(computerId: string) {
    goto(`/customer-computers/${computerId}`);
  }
  
  // Handle form submission result
  function handleFormResult(result: any) {
    isSubmitting = false;
    
    if (result.success) {
      successMessage = result.message || 'Operation completed successfully';
      showAddComputerForm = false;
      
      // Redirect to the new computer if ID is provided
      if (result.computerId) {
        goto(`/customer-computers/${result.computerId}`);
      } else {
        // Reload the page to refresh the data
        window.location.reload();
      }
    } else {
      formError = result.error || 'An error occurred';
    }
  }
</script>

<div class="container">
  <header>
    <div class="header-content">
      <h1>Customer Computers</h1>
      <p>View and manage all customer computers</p>
    </div>
    
    <div class="header-actions">
      <button class="back-button" on:click={() => history.back()}>
        ← Back
      </button>
      
      <div class="filter-section">
        <select 
          bind:value={selectedCustomerId}
          on:change={handleCustomerChange}
          class="customer-filter"
        >
          <option value="">All Customers</option>
          {#each customers as customer}
            <option value={customer._id}>
              {customer.companyName || customer.name} ({customer.type || 'Retail'})
            </option>
          {/each}
        </select>
      </div>
      
      <button 
        class="add-button" 
        on:click={() => showAddComputerForm = true}
        disabled={!selectedCustomerId}
      >
        + Add Computer
      </button>
    </div>
  </header>
  
  {#if selectedCustomer}
    <div class="customer-details">
      <div class="customer-card">
        <h2>{selectedCustomer.companyName || selectedCustomer.name}</h2>
        <div class="customer-info">
          <span class="customer-type {selectedCustomer.type.toLowerCase().replace(' ', '-')}">
            {selectedCustomer.type}
          </span>
          {#if selectedCustomer.country}
            <span class="customer-location">
              {selectedCustomer.city}, {selectedCustomer.country}
            </span>
          {/if}
        </div>
        <div class="computer-count">
          Computers: {filteredComputers.length}
        </div>
      </div>
    </div>
  {/if}
  
  {#if formError}
    <div class="alert error">
      {formError}
      <button class="close-alert" on:click={() => formError = ''}>×</button>
    </div>
  {/if}
  
  {#if successMessage}
    <div class="alert success">
      {successMessage}
      <button class="close-alert" on:click={() => successMessage = ''}>×</button>
    </div>
  {/if}
  
  {#if showAddComputerForm && selectedCustomerId}
    <div class="form-container">
      <ComputerForm 
        customerId={selectedCustomerId}
        on:cancel={() => showAddComputerForm = false}
      />
    </div>
  {/if}
  
  {#if filteredComputers.length > 0}
    <div class="table-container">
      <table>
        <thead>
          <tr>
            {#if !selectedCustomerId}
              <th>Customer</th>
            {/if}
            <th>Name</th>
            <th>Serial Number</th>
            <th>Model</th>
            <th>Product Type</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {#each filteredComputers as computer (computer._id)}
            <tr class="computer-row" on:click={() => selectComputer(computer._id)}>
              {#if !selectedCustomerId}
                <td>
                  <div class="customer-info">
                    <span class="customer-name">{getCustomerName(computer.customerId)}</span>
                    <span class="customer-type {getCustomerType(computer.customerId).toLowerCase().replace(' ', '-')}">
                      {getCustomerType(computer.customerId)}
                    </span>
                  </div>
                </td>
              {/if}
              <td>{computer.name}</td>
              <td>{computer.serialNumber || 'N/A'}</td>
              <td>{computer.model || 'N/A'}</td>
              <td>{computer.productType || 'N/A'}</td>
              <td>
                <span class="status-badge {computer.isActive ? 'active' : 'inactive'}">
                  {computer.isActive ? 'Active' : 'Inactive'}
                </span>
              </td>
              <td class="actions">
                <button 
                  class="action-button view"
                  on:click|stopPropagation={() => selectComputer(computer._id)}
                >
                  View
                </button>
                <form 
                  method="POST" 
                  action="?/deleteComputer" 
                  use:enhance={() => {
                    isSubmitting = true;
                    return async ({ result }) => {
                      handleFormResult(result.data);
                    };
                  }}
                  on:submit|stopPropagation
                >
                  <input type="hidden" name="computerId" value={computer._id} />
                  <button 
                    type="submit" 
                    class="action-button delete"
                    disabled={isSubmitting}
                    on:click|stopPropagation
                  >
                    Delete
                  </button>
                </form>
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  {:else}
    <div class="empty-state">
      {#if selectedCustomerId}
        <p>No computers found for this customer. Click "Add Computer" to add one.</p>
      {:else}
        <p>No computers found. Select a customer and click "Add Computer" to add one.</p>
      {/if}
    </div>
  {/if}
</div>

<style>
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
  }
  
  header {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
  }
  
  .header-content h1 {
    margin: 0;
    font-size: 1.8rem;
    color: #2d3748;
  }
  
  .header-content p {
    margin: 0.5rem 0 0;
    color: #718096;
  }
  
  .header-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .back-button {
    padding: 0.5rem 1rem;
    background: none;
    border: none;
    color: #4299e1;
    cursor: pointer;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .back-button:hover {
    text-decoration: underline;
  }
  
  .filter-section {
    flex-grow: 1;
  }
  
  .customer-filter {
    width: 100%;
    max-width: 400px;
    padding: 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    font-size: 0.9rem;
  }
  
  .add-button {
    padding: 0.5rem 1rem;
    background-color: #4299e1;
    color: white;
    border: none;
    border-radius: 0.25rem;
    cursor: pointer;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .add-button:hover {
    background-color: #3182ce;
  }
  
  .add-button:disabled {
    background-color: #cbd5e0;
    cursor: not-allowed;
  }
  
  .customer-details {
    margin-bottom: 1.5rem;
  }
  
  .customer-card {
    background-color: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 1rem;
  }
  
  .customer-card h2 {
    margin: 0 0 0.5rem;
    font-size: 1.25rem;
    color: #2d3748;
  }
  
  .customer-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
  }
  
  .customer-type {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    background-color: #ebf8ff;
    color: #2b6cb0;
  }
  
  .computer-count {
    font-size: 0.875rem;
    color: #718096;
  }
  
  .alert {
    padding: 0.75rem 1rem;
    border-radius: 0.25rem;
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .alert.error {
    background-color: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
  }
  
  .alert.success {
    background-color: #c6f6d5;
    color: #2f855a;
    border: 1px solid #9ae6b4;
  }
  
  .close-alert {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: inherit;
  }
  
  .form-container {
    margin-bottom: 2rem;
  }
  
  .table-container {
    overflow-x: auto;
  }
  
  table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
  }
  
  th {
    text-align: left;
    padding: 0.75rem 1rem;
    background-color: #f7fafc;
    border-bottom: 2px solid #e2e8f0;
    color: #4a5568;
    font-weight: 600;
  }
  
  td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e2e8f0;
    color: #2d3748;
  }
  
  .computer-row {
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .computer-row:hover {
    background-color: #f7fafc;
  }
  
  .status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
  }
  
  .status-badge.active {
    background-color: #c6f6d5;
    color: #2f855a;
  }
  
  .status-badge.inactive {
    background-color: #fed7d7;
    color: #c53030;
  }
  
  .actions {
    display: flex;
    gap: 0.5rem;
  }
  
  .action-button {
    padding: 0.25rem 0.5rem;
    border: none;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    cursor: pointer;
    font-weight: 500;
  }
  
  .action-button.view {
    background-color: #ebf8ff;
    color: #2b6cb0;
  }
  
  .action-button.view:hover {
    background-color: #bee3f8;
  }
  
  .action-button.delete {
    background-color: #fed7d7;
    color: #c53030;
  }
  
  .action-button.delete:hover {
    background-color: #feb2b2;
  }
  
  .empty-state {
    padding: 2rem;
    text-align: center;
    background-color: #f7fafc;
    border-radius: 0.5rem;
    color: #718096;
  }
</style>
