import { error } from '@sveltejs/kit';

/** @type {import('./$types').PageServerLoad} */
export async function load({ fetch, url }) {
    try {
        // Get selected ProductDesignation filter
        const selectedDesignation = url.searchParams.get('designation') || '';

        // Fetch product validity groups
        const response = await fetch('/api/product-validity-groups');
        if (!response.ok) {
            throw new Error(`Error fetching data: ${response.statusText}`);
        }
        const productValidityGroups = await response.json();

        // Get unique ProductDesignation values for filter dropdown
        const uniqueDesignations = [...new Set(
            productValidityGroups
                .map(item => item.ProductDesignation)
                .filter(designation => designation && designation.trim() !== '')
        )].sort();

        // Filter data if a designation is selected
        const filteredGroups = selectedDesignation
            ? productValidityGroups.filter(item => item.ProductDesignation === selectedDesignation)
            : productValidityGroups;

        return {
            productValidityGroups: filteredGroups,
            allDesignations: uniqueDesignations,
            selectedDesignation,
            totalItems: productValidityGroups.length,
            filteredItems: filteredGroups.length
        };
    } catch (err) {
        console.error('Failed to load product validity groups:', err);
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        throw error(500, errorMessage);
    }
}
