import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

// MongoDB connection URI
const uri = 'mongodb://localhost:27017';

// PUT: Update an existing service ID item
export async function PUT({ params, request }) {
  const client = new MongoClient(uri);
  
  try {
    // Parse the request body
    const data = await request.json();
    
    // Validate required fields
    if (!data.serviceCode) {
      return new Response(JSON.stringify({ error: 'Service Code is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Validate ObjectId format
    if (!ObjectId.isValid(params.id)) {
      return new Response(JSON.stringify({ error: 'Invalid ID format' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    await client.connect();
    const db = client.db('ServiceContracts');
    const serviceIdCollection = db.collection('ServiceID');
    
    // Check if a service with this code already exists (excluding the current item)
    const existing = await serviceIdCollection.findOne({ 
      serviceCode: data.serviceCode,
      _id: { $ne: new ObjectId(params.id) }
    });
    
    if (existing) {
      return new Response(JSON.stringify({ error: 'Another service with this code already exists' }), {
        status: 409,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Update the item with new data and update timestamp
    const updateData = {
      ...data,
      updatedAt: new Date()
    };
    
    const result = await serviceIdCollection.updateOne(
      { _id: new ObjectId(params.id) },
      { $set: updateData }
    );
    
    if (result.matchedCount === 0) {
      return new Response(JSON.stringify({ error: 'Service ID item not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Return the updated item
    const updatedItem = await serviceIdCollection.findOne({ _id: new ObjectId(params.id) });
    return json(updatedItem);
    
  } catch (error) {
    console.error('Error updating service ID item:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  } finally {
    await client.close();
  }
}
