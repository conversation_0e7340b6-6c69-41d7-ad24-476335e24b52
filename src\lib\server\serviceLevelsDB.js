import { COLLECTIONS, serializeDocument, createObjectId, executeDbOperation } from './serviceContractDB.js';
import serviceContractDB from './serviceContractDB.js';

const db = serviceContractDB.db;
const collection = COLLECTIONS.SERVICE_LEVELS;

/**
 * Create a new service level definition
 * @param {Object} serviceLevel - Service level data
 * @returns {Promise<Object>} Inserted document with ID
 */
export async function createServiceLevel(serviceLevel) {
  // Required fields validation
  if (!serviceLevel.code || !serviceLevel.name) {
    throw new Error('Service level code and name are required');
  }
  
  // Add timestamps
  const now = new Date();
  const documentToInsert = {
    ...serviceLevel,
    createdAt: now,
    updatedAt: now
  };
  
  return executeDbOperation(async () => {
    // Check for duplicate code
    const existing = await db.collection(collection).findOne({ 
      code: serviceLevel.code 
    });
    
    if (existing) {
      throw new Error(`Service level with code ${serviceLevel.code} already exists`);
    }
    
    const result = await db.collection(collection).insertOne(documentToInsert);
    if (!result.acknowledged) {
      throw new Error('Failed to create service level');
    }
    
    return {
      ...serializeDocument(documentToInsert),
      _id: result.insertedId.toString()
    };
  }, 'Error creating service level');
}

/**
 * Get all service levels with optional filtering
 * @param {Object} filter - MongoDB filter object
 * @param {Object} options - MongoDB options (sort, limit, etc.)
 * @returns {Promise<Array>} Array of service levels
 */
export async function getServiceLevels(filter = {}, options = {}) {
  return executeDbOperation(async () => {
    const defaultOptions = {
      sort: { code: 1 },
      ...options
    };
    
    const cursor = db.collection(collection).find(filter, defaultOptions);
    const documents = await cursor.toArray();
    return documents.map(doc => serializeDocument(doc));
  }, 'Error retrieving service levels');
}

/**
 * Get a single service level by ID
 * @param {string} id - Service level ID
 * @returns {Promise<Object|null>} Service level or null if not found
 */
export async function getServiceLevelById(id) {
  return executeDbOperation(async () => {
    const objectId = createObjectId(id);
    if (!objectId) return null;
    
    const document = await db.collection(collection).findOne({ _id: objectId });
    return document ? serializeDocument(document) : null;
  }, 'Error retrieving service level');
}

/**
 * Get a service level by code
 * @param {string} code - Service level code
 * @returns {Promise<Object|null>} Service level or null if not found
 */
export async function getServiceLevelByCode(code) {
  return executeDbOperation(async () => {
    if (!code) return null;
    
    const document = await db.collection(collection).findOne({ code });
    return document ? serializeDocument(document) : null;
  }, 'Error retrieving service level by code');
}

/**
 * Update a service level
 * @param {string} id - Service level ID
 * @param {Object} updates - Fields to update
 * @returns {Promise<Object|null>} Updated service level or null if not found
 */
export async function updateServiceLevel(id, updates) {
  return executeDbOperation(async () => {
    const objectId = createObjectId(id);
    if (!objectId) return null;
    
    // Don't allow updating the _id field
    if (updates._id) delete updates._id;
    
    // Check if trying to update code to an existing one
    if (updates.code) {
      const existingWithCode = await db.collection(collection).findOne({
        code: updates.code,
        _id: { $ne: objectId }
      });
      
      if (existingWithCode) {
        throw new Error(`Service level with code ${updates.code} already exists`);
      }
    }
    
    // Add updated timestamp
    const updateData = {
      ...updates,
      updatedAt: new Date()
    };
    
    const result = await db.collection(collection).findOneAndUpdate(
      { _id: objectId },
      { $set: updateData },
      { returnDocument: 'after' }
    );
    
    return result.value ? serializeDocument(result.value) : null;
  }, 'Error updating service level');
}

/**
 * Delete a service level
 * @param {string} id - Service level ID
 * @returns {Promise<boolean>} True if deleted, false if not found
 */
export async function deleteServiceLevel(id) {
  return executeDbOperation(async () => {
    const objectId = createObjectId(id);
    if (!objectId) return false;
    
    // Check if service level is in use
    const contractLinesUsingLevel = await db.collection(COLLECTIONS.CONTRACT_LINES)
      .countDocuments({ serviceLevelId: objectId });
    
    if (contractLinesUsingLevel > 0) {
      throw new Error(`Cannot delete service level as it is used in ${contractLinesUsingLevel} contract lines`);
    }
    
    const result = await db.collection(collection).deleteOne({ _id: objectId });
    return result.deletedCount > 0;
  }, 'Error deleting service level');
}

/**
 * Get service levels applicable to a specific product validity group
 * @param {string} productValidityGroup - Product validity group code
 * @returns {Promise<Array>} Array of applicable service levels
 */
export async function getServiceLevelsByProductGroup(productValidityGroup) {
  return executeDbOperation(async () => {
    if (!productValidityGroup) return [];
    
    const documents = await db.collection(collection).find({
      $or: [
        { "Product Validity Group": productValidityGroup },
        { applicableGroups: { $in: [productValidityGroup] } }
      ]
    }).sort({ code: 1 }).toArray();
    
    return documents.map(doc => serializeDocument(doc));
  }, 'Error retrieving service levels by product group');
}

export default {
  createServiceLevel,
  getServiceLevels,
  getServiceLevelById,
  getServiceLevelByCode,
  updateServiceLevel,
  deleteServiceLevel,
  getServiceLevelsByProductGroup
};
