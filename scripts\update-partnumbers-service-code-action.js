// Script to update PartNumbersServiceCodeAction collection to add ProductDesignationShort field
// This script adds a new field ProductDesignationShort with the same value as ProductValidityGroup

import { MongoClient } from 'mongodb';

// Connection URL and Database name
const url = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';
const collectionName = 'PartNumbersServiceCodeAction';

async function updatePartNumbersServiceCodeAction() {
  let client;

  try {
    // Connect to MongoDB
    client = new MongoClient(url);
    await client.connect();
    console.log('Connected to MongoDB server');

    // Get database and collection
    const db = client.db(dbName);
    const collection = db.collection(collectionName);

    // Count documents before update
    const totalDocuments = await collection.countDocuments();
    console.log(`Total documents in ${collectionName}: ${totalDocuments}`);

    // Check if collection has the standardized field name or the old one with spaces
    const sampleDoc = await collection.findOne({});
    console.log('Sample document structure:', Object.keys(sampleDoc || {}));
    
    // Determine which field name to use based on the memory about field standardization
    const productValidityGroupField = sampleDoc && sampleDoc.ProductValidityGroup ? 
      "ProductValidityGroup" : 
      "Product Validity Group";
    
    console.log(`Using field name: ${productValidityGroupField}`);

    // Update all documents to add ProductDesignationShort field
    const updateResult = await collection.updateMany(
      {}, // Match all documents
      [
        { 
          $set: { 
            // Set ProductDesignationShort to ProductValidityGroup value, or null if it doesn't exist
            ProductDesignationShort: { $ifNull: [`$${productValidityGroupField}`, null] }
          } 
        }
      ]
    );

    console.log(`Updated ${updateResult.modifiedCount} documents with ProductDesignationShort field`);

    // Verify update by checking a few documents
    const sampleDocs = await collection.find({}).limit(5).toArray();
    console.log('Sample documents after update:');
    sampleDocs.forEach(doc => {
      console.log(`ID: ${doc._id}, ${productValidityGroupField}: ${doc[productValidityGroupField]}, ProductDesignationShort: ${doc.ProductDesignationShort}`);
    });

  } catch (err) {
    console.error('Error updating documents:', err);
  } finally {
    // Close the connection
    if (client) {
      await client.close();
      console.log('MongoDB connection closed');
    }
  }
}

// Run the update function
updatePartNumbersServiceCodeAction().catch(console.error);
