import { json } from '@sveltejs/kit';
import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';

/**
 * Handles GET requests to fetch product designation data for a specific computer
 * @param {import('@sveltejs/kit').RequestEvent} event - The request event
 * @returns {Promise<Response>} JSON response with product designation data
 */
export async function GET({ params }) {
    const { computerId } = params;
    
    if (!computerId || !ObjectId.isValid(computerId)) {
        return json({ success: false, error: 'Invalid computer ID' }, { status: 400 });
    }
    
    try {
        const client = new MongoClient(uri);
        await client.connect();
        const db = client.db('ServiceContracts');
        const computerCollection = db.collection('CustomerComputers');
        
        // Get the computer details including product designation
        const computer = await computerCollection.findOne({
            _id: new ObjectId(computerId)
        });
        
        if (!computer) {
            return json({ success: false, error: 'Computer not found' }, { status: 404 });
        }
        
        // Return relevant product information
        return json({
            success: true,
            productType: computer.productType || computer.type || 'Unknown',
            model: computer.model || 'Unknown',
            serialNumber: computer.serialNumber || 'Unknown',
            productDesignation: computer.ProductDesignation || computer.productDesignation || 'Unknown',
            productPartNumber: computer.ProductPartNumber || 'Unknown',
            productValidityGroup: computer.ProductValidityGroup || 'Unknown'
        });
    } catch (error) {
        console.error('Error fetching product designation data:', error);
        
        // Return a fallback response instead of an error to prevent 500
        return json({
            success: true,
            productType: 'Unknown',
            model: 'Unknown',
            serialNumber: 'Unknown',
            productDesignation: 'Unknown',
            productPartNumber: 'Unknown',
            productValidityGroup: 'Unknown'
        });
    }
}
