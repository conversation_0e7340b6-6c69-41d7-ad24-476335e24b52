<script lang="ts">
  import ActualComputerHoursGrid from '$lib/components/grids/ActualComputerHoursGrid.svelte';
  import { onMount } from 'svelte';

  export let computerId;
  let actualComputerHours = [];
  let loading = true;
  let error = '';

  async function fetchHours() {
    loading = true;
    try {
      const url = `/api/actual-computer-hours?computerId=${computerId}`;
      const res = await fetch(url);
      if (!res.ok) throw new Error('Failed to fetch');
      actualComputerHours = await res.json();
    } catch (e) {
      error = e.message;
    } finally {
      loading = false;
    }
  }

  onMount(fetchHours);
</script>

<section>
  <h2>Actual Computer Hours</h2>
  {#if loading}
    <p>Loading...</p>
  {:else if error}
    <p style="color:red">{error}</p>
  {:else}
    <ActualComputerHoursGrid {actualComputerHours} />
  {/if}
</section>
