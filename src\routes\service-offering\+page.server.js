import { error } from '@sveltejs/kit';
import { ObjectId } from 'mongodb';
import { getCollection } from '$lib/db/mongo';

/** @type {import('./$types').PageServerLoad} */
export async function load({ url }) {
  try {
    // Get computer ID from URL params if provided
    const computerId = url.searchParams.get('computerId');
    const productDesignation = url.searchParams.get('productDesignation') || 'DEFAULT';

    console.log(`Loading service offering data for product designation: ${productDesignation}`);

    // Debug: Check available collections
    const { MongoClient } = await import('mongodb');
    const client = new MongoClient('mongodb://localhost:27017');
    await client.connect();
    const db = client.db('ServiceContracts');
    const collections = await db.listCollections().toArray();
    console.log('Available collections:', collections.map(c => c.name));
    await client.close();

    // Get computer details if computerId is provided
    let computer = null;
    if (computerId) {
      const computersCollection = await getCollection('CustomerComputers');
      computer = await computersCollection.findOne({ _id: new ObjectId(computerId) });
    }

    // Get service data from available collections
    let baseServices = [];
    let supportServices = [];
    let dealerServices = [];
    let replacementServices = [];

    // Try MainServices collection for base services
    try {
      const mainServicesCollection = await getCollection('MainServices');
      baseServices = await mainServicesCollection.find({}).limit(20).toArray();
      console.log(`Found ${baseServices.length} main services`);
      if (baseServices.length > 0) {
        console.log('Sample MainService:', JSON.stringify(baseServices[0], null, 2));
      }
    } catch (err) {
      console.log('MainServices collection not found:', err.message);
    }

    // Try Services collection for additional services
    try {
      const servicesCollection = await getCollection('Services');
      const allServices = await servicesCollection.find({}).limit(30).toArray();
      console.log(`Found ${allServices.length} services`);
      if (allServices.length > 0) {
        console.log('Sample Service:', JSON.stringify(allServices[0], null, 2));
      }

      // Split services into categories based on available data
      supportServices = allServices.slice(0, 10); // First 10 as support services
      dealerServices = allServices.slice(10, 20); // Next 10 as dealer services
      replacementServices = allServices.slice(20, 30); // Next 10 as replacement services
    } catch (err) {
      console.log('Services collection not found:', err.message);
    }

    // Try ServiceActivities collection for service activities
    try {
      const serviceActivitiesCollection = await getCollection('ServiceActivities');
      const activities = await serviceActivitiesCollection.find({}).limit(15).toArray();
      console.log(`Found ${activities.length} service activities`);

      // If we don't have base services yet, use activities
      if (baseServices.length === 0) {
        baseServices = activities.slice(0, 5);
        if (supportServices.length === 0) {
          supportServices = activities.slice(5, 10);
        }
        if (dealerServices.length === 0) {
          dealerServices = activities.slice(10, 15);
        }
      }
    } catch (err) {
      console.log('ServiceActivities collection not found');
    }

    // Get PriceList data for pricing lookup
    const priceListCollection = await getCollection('PriceLIst'); // Note: Collection name has capital 'I'
    const priceListItems = await priceListCollection.find({}).toArray();

    // Create a price lookup map
    const priceMap = new Map();
    priceListItems.forEach(item => {
      const partNo = item['Part No'];
      const price = parseFloat(item['Price excl VAT']?.toString().replace(',', '.')) || 0;
      if (partNo) {
        priceMap.set(partNo, price);
      }
    });

    // Helper function to get price from price list
    function getPrice(serviceCode, partNo) {
      if (partNo && priceMap.has(partNo)) {
        return priceMap.get(partNo);
      }
      return 0; // Return 0 for missing prices as requested
    }

    // Transform services into service offering format
    const baseContractServices = baseServices.map((service, index) => {
      // Try different field names that might exist in the collections
      const serviceCode = service.ServiceCode || service.serviceCode || service.code || service.id || '';
      const serviceActivity = service.ServiceActivity || service.serviceName || service.name || service.description || '';
      const partNo = service.PartNo || service.partNumber || service.partNo || '';

      const unitPrice = getPrice(serviceCode, partNo);
      return {
        _id: service._id ? service._id.toString() : `base_${index}`,
        level: index + 1,
        packageName: 'Base Contract Offering',
        serviceId: serviceCode,
        ServiceActivity: serviceActivity,
        includedInPackage: unitPrice === 0 || service.includedInPackage === true,
        required: service.required === true,
        includeInOffer: service.includeInOffer !== false, // Default to true unless explicitly false
        cost: unitPrice,
        source: 'MainServices'
      };
    });

    // Transform support services into service offering format
    const supportServiceItems = supportServices.map((service, index) => {
      const serviceCode = service.ServiceCode || service.serviceCode || service.code || service.id || '';
      const serviceActivity = service.ServiceActivity || service.serviceName || service.name || service.description || '';
      const partNo = service.PartNo || service.partNumber || service.partNo || '';

      const unitPrice = getPrice(serviceCode, partNo);
      return {
        _id: service._id ? service._id.toString() : `support_${index}`,
        level: baseContractServices.length + index + 1,
        packageName: 'Support (Self Service)',
        serviceId: serviceCode,
        ServiceActivity: serviceActivity,
        includedInPackage: unitPrice === 0 || service.includedInPackage === true,
        required: service.required === true,
        includeInOffer: service.includeInOffer === true,
        cost: unitPrice,
        source: 'Services'
      };
    });

    // Transform dealer services into service offering format
    const dealerAddOns = dealerServices.map((service, index) => {
      const serviceCode = service.ServiceCode || service.serviceCode || service.code || service.id || '';
      const serviceActivity = service.ServiceActivity || service.serviceName || service.name || service.description || '';
      const partNo = service.PartNo || service.partNumber || service.partNo || '';

      const unitPrice = getPrice(serviceCode, partNo);
      return {
        _id: service._id ? service._id.toString() : `dealer_${index}`,
        level: baseContractServices.length + supportServiceItems.length + index + 1,
        packageName: 'Dealer Add-Ons',
        serviceId: serviceCode,
        ServiceActivity: serviceActivity,
        includedInPackage: unitPrice === 0 || service.includedInPackage === true,
        required: service.required === true,
        includeInOffer: service.includeInOffer === true,
        cost: unitPrice,
        source: 'Services'
      };
    });

    // Transform replacement services into service offering format
    const replacementServiceItems = replacementServices.map((service, index) => {
      const serviceCode = service.ServiceCode || service.serviceCode || service.code || service.id || '';
      const serviceActivity = service.ServiceActivity || service.serviceName || service.name || service.description || '';
      const partNo = service.PartNo || service.partNumber || service.partNo || '';

      const unitPrice = getPrice(serviceCode, partNo);
      return {
        _id: service._id ? service._id.toString() : `replacement_${index}`,
        level: baseContractServices.length + supportServiceItems.length + dealerAddOns.length + index + 1,
        packageName: 'Retirement Plan',
        serviceId: serviceCode,
        ServiceActivity: serviceActivity,
        includedInPackage: unitPrice === 0 || service.includedInPackage === true,
        required: service.required === true,
        includeInOffer: service.includeInOffer === true,
        cost: unitPrice,
        source: 'Services'
      };
    });

    // Combine all services
    const quotationPackages = {
      baseContract: baseContractServices,
      repairPackages: dealerAddOns,
      supportServices: supportServiceItems,
      replacementServices: replacementServiceItems
    };

    console.log(`Loaded ${baseContractServices.length} base services, ${supportServiceItems.length} support services, ${dealerAddOns.length} dealer add-ons, ${replacementServiceItems.length} replacement services`);

    return {
      quotationPackages,
      computer,
      productDesignation,
      priceListItems: priceListItems.length
    };

  } catch (err) {
    console.error('Error loading service offering data:', err);
    throw error(500, `Failed to load service offering data: ${err.message}`);
  }
}
