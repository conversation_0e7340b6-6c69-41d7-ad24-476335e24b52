import { error } from '@sveltejs/kit';
import { ObjectId } from 'mongodb';
import { getCollection } from '$lib/db/mongo';

/** @type {import('./$types').PageServerLoad} */
export async function load({ url }) {
  try {
    // Get computer ID and product designation from URL params
    const computerId = url.searchParams.get('computerId');
    let productDesignation = url.searchParams.get('productDesignation');
    let productValidityGroup = url.searchParams.get('productValidityGroup');

    console.log(`Loading service offering data for computerId: ${computerId}, productDesignation: ${productDesignation}, productValidityGroup: ${productValidityGroup}`);

    // Get computer details if computerId is provided
    let computer = null;
    if (computerId) {
      const computersCollection = await getCollection('CustomerComputers');
      computer = await computersCollection.findOne({ _id: new ObjectId(computerId) });

      // Get Product Validity Group from computer if available
      if (computer) {
        productValidityGroup = computer['Product Validity Group'] || computer.ProductValidityGroup;
        productDesignation = computer.ProductDesignation || computer.productDesignation;
        console.log(`Found computer with PVG: ${productValidityGroup}, designation: ${productDesignation}`);
      }
    }

    // If no Product Validity Group yet, try to get it from ProductValidityGroupPartNumber collection
    if (!productValidityGroup && productDesignation) {
      try {
        const pvgCollection = await getCollection('ProductValidityGroupPartNumber');
        const pvgDoc = await pvgCollection.findOne({
          ProductDesignation: productDesignation
        });
        if (pvgDoc) {
          productValidityGroup = pvgDoc.ProductValidityGroup || pvgDoc['Product Validity Group'];
          console.log(`Found PVG from ProductValidityGroupPartNumber: ${productValidityGroup}`);
        }
      } catch (err) {
        console.log('ProductValidityGroupPartNumber collection not found');
      }
    }

    // Default to a known Product Validity Group for testing if none found
    if (!productValidityGroup) {
      productValidityGroup = 'TAD1640-42GE-B'; // Use the example from your image
      console.log(`Using default PVG: ${productValidityGroup}`);
    }

    // Get service items from ServiceCodeAndActionType collection based on Product Validity Group
    let serviceItems = [];
    try {
      const serviceCodeCollection = await getCollection('ServiceCodeAndActionType');
      serviceItems = await serviceCodeCollection.find({
        ProductValidityGroup: productValidityGroup
      }).toArray();
      console.log(`Found ${serviceItems.length} service items for PVG: ${productValidityGroup}`);

      if (serviceItems.length > 0) {
        console.log('Sample service item:', JSON.stringify(serviceItems[0], null, 2));
      }
    } catch (err) {
      console.log('ServiceCodeAndActionType collection not found:', err.message);
    }

    // Get pricing data from ServiceID and PriceList collections
    let serviceIDPrices = new Map();
    let priceListPrices = new Map();

    // Get ServiceID collection for service pricing
    try {
      const serviceIDCollection = await getCollection('ServiceID');
      const serviceIDItems = await serviceIDCollection.find({}).toArray();
      console.log(`Found ${serviceIDItems.length} ServiceID items`);

      serviceIDItems.forEach(item => {
        const serviceCode = item.serviceCode || item.ServiceCode;
        const price = item.price || item.Price || 0;
        if (serviceCode) {
          serviceIDPrices.set(serviceCode, parseFloat(price) || 0);
        }
      });
    } catch (err) {
      console.log('ServiceID collection not found:', err.message);
    }

    // Get PriceList data for part pricing
    try {
      const priceListCollection = await getCollection('PriceLIst'); // Note: Collection name has capital 'I'
      const priceListItems = await priceListCollection.find({}).toArray();
      console.log(`Found ${priceListItems.length} price list items`);

      priceListItems.forEach(item => {
        const partNo = item['Part No'] || item.PartNo || item.partNo;
        const price = parseFloat(item['Price excl VAT']?.toString().replace(',', '.')) || 0;
        if (partNo) {
          priceListPrices.set(partNo.toString(), price);
        }
      });
    } catch (err) {
      console.log('PriceLIst collection not found:', err.message);
    }

    // Helper function to get price for a service item
    const getPrice = (item) => {
      // First try ServiceID collection by ServiceCode
      const serviceCode = item.ServiceCode || item.serviceCode;
      if (serviceCode && serviceIDPrices.has(serviceCode)) {
        return serviceIDPrices.get(serviceCode);
      }

      // Then try PriceList collection by PartNumber
      const partNumber = item.PartNumber || item.partNumber || item['Part Number'];
      if (partNumber && priceListPrices.has(partNumber.toString())) {
        return priceListPrices.get(partNumber.toString());
      }

      // Return 0 for missing values as requested
      return 0;
    };

    // Organize service items by Service Activity Label (S, A, B, C, D, E)
    const servicesByLabel = {
      'S': [], // Service level S
      'A': [], // Service level A
      'B': [], // Service level B
      'C': [], // Service level C
      'D': [], // Service level D
      'E': []  // Service level E
    };

    // Group service items by their Service Activity Label
    serviceItems.forEach(item => {
      const label = item.ServiceActivityLabel || item.serviceActivityLabel || '';
      if (servicesByLabel[label]) {
        const price = getPrice(item);
        const transformedItem = {
          _id: item._id ? item._id.toString() : `${item.ServiceCode}_${label}`,
          serviceCode: item.ServiceCode || '',
          actionType: item.ActionType || '',
          activityPurpose: item.ActivityPurpose || '',
          serviceActivityLabel: label,
          partNumber: item.PartNumber || '',
          hours: item.InternalNoOfHours || 0,
          cost: price,
          includeInOffer: true
        };
        servicesByLabel[label].push(transformedItem);
      }
    });

    // Create package offerings based on service activity labels
    const packageOfferings = {
      levelS: servicesByLabel['S'], // Service level S items
      levelA: servicesByLabel['A'], // Service level A items
      levelB: servicesByLabel['B'], // Service level B items
      levelC: servicesByLabel['C'], // Service level C items
      levelD: servicesByLabel['D'], // Service level D items
      levelE: servicesByLabel['E']  // Service level E items
    };

    // Calculate totals for each level
    const totals = {};
    Object.keys(servicesByLabel).forEach(label => {
      const items = servicesByLabel[label];
      totals[`level${label}`] = {
        count: items.length,
        totalCost: items.reduce((sum, item) => sum + item.cost, 0),
        totalHours: items.reduce((sum, item) => sum + item.hours, 0)
      };
    });

    console.log(`Service items organized by label:`, Object.keys(servicesByLabel).map(label =>
      `${label}: ${servicesByLabel[label].length} items`
    ).join(', '));

    return {
      productValidityGroup,
      productDesignation,
      computer,
      serviceItems: servicesByLabel,
      packageOfferings,
      totals,
      dataSource: {
        serviceCodeAndActionType: serviceItems.length,
        serviceIDPrices: serviceIDPrices.size,
        priceListPrices: priceListPrices.size
      }
    };

  } catch (err) {
    console.error('Error loading service offering data:', err);
    throw error(500, `Failed to load service offering data: ${err.message}`);
  }
}
