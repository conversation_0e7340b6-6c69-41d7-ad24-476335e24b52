# Script to remove the specific [id] route

Write-Host "Removing the /api/quote-rows/update/[id] route..."

# Define path to the specific route
$routePath = "C:\SvelteProjects\Svelte-Mongo3\src\routes\api\quote-rows\update\[id]"

# Check if directory exists
$directoryExists = Test-Path $routePath

if ($directoryExists) {
    Write-Host "Found [id] route at: $routePath"
    
    # List contents before removal
    Write-Host "Directory contains the following items:"
    Get-ChildItem -Path $routePath -Recurse | ForEach-Object {
        Write-Host "  - $($_.Name)"
    }
    
    # Remove the directory
    Write-Host "Removing directory..."
    Remove-Item -Path $routePath -Recurse -Force
    
    # Verify removal
    if (Test-Path $routePath) {
        Write-Host "Failed to remove [id] route" -ForegroundColor Red
    } else {
        Write-Host "Successfully removed [id] route" -ForegroundColor Green
    }
} else {
    Write-Host "[id] route not found at: $routePath" -ForegroundColor Yellow
    
    # Check if the file exists directly
    $serverFile = "C:\SvelteProjects\Svelte-Mongo3\src\routes\api\quote-rows\update\[id]\+server.js"
    if (Test-Path $serverFile) {
        Write-Host "Found server file at: $serverFile"
        Remove-Item -Path $serverFile -Force
        Write-Host "Removed server file"
    }
}

Write-Host "Operation complete. Please restart your development server."
