<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  export let years: number[] = [];
  export let months: string[] = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  
  // Add new props for editing functionality
  export let editMode: boolean = false;
  export let editingYear: number | null = null;
  export let editingMonth: string | null = null;
  
  // Events
  export let onSave: (year: number, month: string) => void = () => {};
  export let onCancel: () => void = () => {};
  
  // Convert month name to index (0-based)
  function getMonthIndex(monthName: string): number {
    return months.findIndex(m => m === monthName);
  }
  
  // Compute the CSS grid columns template based on months length
  $: gridColumns = `60px repeat(${months.length}, minmax(40px, 1fr))`;
</script>

<style>
  .service-matrix {
    display: grid;
    grid-template-columns: var(--grid-columns, 60px repeat(12, minmax(40px, 1fr)));
    width: 100%;
    gap: 0;
  }

  /* Header row */  
  .header-row {
    display: contents;
  }
  
  .year-header {
    width: 60px;
    padding: 0.5rem;
    text-align: center;
    background: #f0f2f5;
    border: 1px solid #ddd;
    font-weight: 500;
  }
  
  .month-header {
    padding: 0.5rem;
    text-align: center;
    background: #e2e8f0;
    border: 1px solid #ddd;
    font-weight: 500;
  }
  
  /* Data rows */
  .year-row {
    display: contents;
  }
  
  .year-cell {
    width: 60px;
    padding: 0.5rem;
    text-align: center;
    background: #f0f2f5;
    border: 1px solid #ddd;
  }
  
  .activity-cell {
    padding: 0.5rem;
    text-align: center;
    border: 1px solid #ddd;
    min-width: 40px;
    position: relative;
  }
  
  .activity-cell:hover {
    background: #f8fafc;
  }
  
  .empty-cell {
    background: #f8f9fa;
  }
  
  /* Editing styles */
  .editing-cell {
    background: #e5f6fd !important;
    box-shadow: inset 0 0 0 2px #38b2e0;
    position: relative;
  }
  
  .edit-controls {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 6px;
  }
  
  .btn-save, .btn-cancel {
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .btn-save {
    background-color: #4caf50;
    color: white;
  }
  
  .btn-save:hover {
    background-color: #43a047;
  }
  
  .btn-cancel {
    background-color: #f44336;
    color: white;
  }
  
  .btn-cancel:hover {
    background-color: #e53935;
  }
  
  .icon {
    margin-right: 4px;
    font-size: 14px;
  }
  
  .edit-form {
    padding: 6px;
  }
  
  .edit-form input, 
  .edit-form select {
    width: 100%;
    padding: 4px;
    margin-bottom: 4px;
    border: 1px solid #ccc;
    border-radius: 3px;
  }
</style>

<BaseGrid>
  <div class="service-matrix" style="--grid-columns: {gridColumns};">
    <!-- Headers -->
    <div class="header-row">
      <div class="year-header">Year</div>
      {#each months as month}
        <div class="month-header">{month}</div>
      {/each}
    </div>
    
    <!-- Data rows -->
    {#each years as year}
      <div class="year-row">
        <div class="year-cell">{year}</div>
        {#each months as month, monthIndex}
          <div class="activity-cell" class:editing-cell={editMode && editingYear === year && editingMonth === month}>
            {#if editMode && editingYear === year && editingMonth === month}
              <div class="edit-form">
                <slot name="edit-form" {year} {month} {monthIndex}>
                  <!-- Default edit form when no custom form is provided -->
                  <input type="text" placeholder="Hours" />
                  <input type="text" placeholder="Activity" />
                </slot>
                
                <div class="edit-controls">
                  <button 
                    class="btn-save" 
                    on:click={() => onSave(year, month)}
                    title="Save changes"
                  >
                    <span class="icon">✓</span> Save
                  </button>
                  <button 
                    class="btn-cancel" 
                    on:click={onCancel}
                    title="Discard changes"
                  >
                    <span class="icon">✕</span> Cancel
                  </button>
                </div>
              </div>
            {:else}
              <slot name="cell" {year} {month} {monthIndex}>
                <div class="empty-cell"></div>
              </slot>
            {/if}
          </div>
        {/each}
      </div>
    {/each}
  </div>
</BaseGrid>
