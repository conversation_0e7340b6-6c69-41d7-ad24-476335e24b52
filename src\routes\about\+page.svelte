<script>
    export let data;
</script>

<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6 text-blue-600">{data.title}</h1>
    
    <div class="bg-white p-6 rounded-lg shadow-md mb-8">
        <p class="text-gray-700 text-lg leading-relaxed mb-6">{data.description}</p>
        
        <h2 class="text-xl font-semibold mb-4 text-gray-800">Key Features</h2>
        <ul class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {#each data.keyFeatures as feature}
                <li class="text-gray-600">• {feature}</li>
            {/each}
        </ul>
    </div>
</div>

<style>
    :global(body) {
        background-color: #f8fafc;
    }
</style>
