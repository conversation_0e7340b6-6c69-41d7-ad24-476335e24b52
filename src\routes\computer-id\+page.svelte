<script lang="ts">
    import { enhance } from '$app/forms';

    interface ServiceCodeAction {
        _id: string;
        ProductValidityGroup: string;
        ActivityPurpose: string;
        ServiceActivityLabel: string;
        ServiceCode: string;
        ActionType: string;
        PartNumber: number;
        UnitOfMeasure: string;
        Quantity: number;
        InternalNoOfHours: number;
        InternalNoOfMonths: number | null;
    }

    interface ActionFormData {
        productDesignation: string;
        productPartNumber: string;
    }

    interface ActionResponse {
        status: number;
        data: {
            items: ServiceCodeAction[];
            formData: ActionFormData;
            error?: string;
        };
    }

    interface PageData {
        computers: any[];
        products: any[];
        serviceElements: ServiceCodeAction[];
    }

    export let data: PageData;
    export let form: ActionResponse | null = null;
    const { computers, products, serviceElements } = data;

    let partNumber = '';
    let designation = '';

    // Initialize with form response data if available, otherwise use data
    $: displayItems = form?.data?.items || serviceElements || [];

    // Search inputs for each column
    let filters = {
        validityGroup: '',
        activityPurpose: '',
        activityLabel: '',
        serviceCode: '',
        actionType: '',
        partNumber: '',
        unit: '',
        quantity: '',
        hours: '',
        months: ''
    };

    // Filter the items based on all filters
    $: filteredItems = displayItems?.filter((item: ServiceCodeAction) => {
        const validityGroupMatch = item.ProductValidityGroup.toLowerCase().includes(filters.validityGroup.toLowerCase());
        const activityPurposeMatch = item.ActivityPurpose.toLowerCase().includes(filters.activityPurpose.toLowerCase());
        const activityLabelMatch = item.ServiceActivityLabel.toLowerCase().includes(filters.activityLabel.toLowerCase());
        const serviceCodeMatch = item.ServiceCode.toLowerCase().includes(filters.serviceCode.toLowerCase());
        const actionTypeMatch = item.ActionType.toLowerCase().includes(filters.actionType.toLowerCase());
        const partNumberMatch = item.PartNumber.toString().includes(filters.partNumber);
        const unitMatch = item.UnitOfMeasure.toLowerCase().includes(filters.unit.toLowerCase());
        const quantityMatch = item.Quantity.toString().includes(filters.quantity);
        const hoursMatch = item.InternalNoOfHours.toString().includes(filters.hours);
        const monthsMatch = item.InternalNoOfMonths?.toString().includes(filters.months) ?? false;

        return validityGroupMatch && activityPurposeMatch && activityLabelMatch && 
               serviceCodeMatch && actionTypeMatch && partNumberMatch && 
               unitMatch && quantityMatch && hoursMatch && monthsMatch;
    }) || [];

    function handleFilter() {
        const params = new URLSearchParams();
        if (partNumber) params.set('partNumber', partNumber);
        if (designation) params.set('designation', designation);
        window.location.href = `/computer-id?${params.toString()}`;
    }

    async function createServiceActivities(computerId: string) {
        try {
            const response = await fetch(`/computer-id/${computerId}/create-activities`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            });

            const result = await response.json();
            
            if (result.success) {
                alert(result.message);
                window.location.reload();
            } else {
                throw new Error(result.message);
            }
        } catch (err: unknown) {
            const error = err instanceof Error ? err.message : 'An unknown error occurred';
            alert('Failed to create service activities: ' + error);
        }
    }

    // Handle form submission for service elements
    function handleSubmit(event: SubmitEvent) {
        return async ({ result, update }: { result: ActionResponse, update: () => Promise<void> }) => {
            await update();
        };
    }
</script>

<div class="container mx-auto p-4">
    <div class="mb-8">
        <h1 class="text-2xl font-bold mb-6">Product Validity Group</h1>
        <div class="mb-6">
            <h2 class="text-xl font-semibold mb-4">Filter Products</h2>
            <div class="flex gap-4 mb-4">
                <div class="flex-1">
                    <label for="partNumber" class="block text-sm font-medium text-gray-700 mb-1">Part Number</label>
                    <input
                        type="text"
                        id="partNumber"
                        bind:value={partNumber}
                        placeholder="Enter part number"
                        class="w-full p-2 border border-gray-300 rounded"
                    />
                </div>
                <div class="flex-1">
                    <label for="designation" class="block text-sm font-medium text-gray-700 mb-1">Designation</label>
                    <input
                        type="text"
                        id="designation"
                        bind:value={designation}
                        placeholder="Enter designation"
                        class="w-full p-2 border border-gray-300 rounded"
                    />
                </div>
                <div class="flex items-end">
                    <button
                        on:click={handleFilter}
                        class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
                    >
                        Filter
                    </button>
                </div>
            </div>

            {#if products && products.length > 0}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {#each products as product}
                        <div class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow border border-gray-200">
                            <div class="mb-2">
                                <h3 class="font-semibold text-lg text-gray-800">{product.ProductName || 'N/A'}</h3>
                            </div>
                            <div class="space-y-1 text-sm">
                                <p class="text-gray-600">
                                    <span class="font-medium">Part Number:</span> 
                                    <span class="font-mono">{product.ProductPartNumber || 'N/A'}</span>
                                </p>
                                <p class="text-gray-600">
                                    <span class="font-medium">Designation:</span> 
                                    {product.ProductDesignation || 'N/A'}
                                </p>
                                <p class="text-gray-600">
                                    <span class="font-medium">Group ID:</span> 
                                    {product.ProductGroupId || 'N/A'}
                                </p>
                                <p class="text-gray-600">
                                    <span class="font-medium">ID:</span> 
                                    {product.Id || 'N/A'}
                                </p>
                                <p class="text-gray-600">
                                    <span class="font-medium">Validity Group:</span> 
                                    {product['Product Validity GRoup'] || 'N/A'}
                                </p>
                            </div>
                        </div>
                    {/each}
                </div>
            {:else}
                <div class="bg-yellow-50 border border-yellow-200 p-4 rounded text-yellow-800">
                    No products found matching the filter criteria.
                </div>
            {/if}
        </div>
    </div>

    {#if designation}
        <div class="mb-8">
            <h2 class="text-2xl font-bold mb-6">Service Elements for {designation}</h2>
            
            {#if filteredItems && filteredItems.length > 0}
                <div class="overflow-x-auto">
                    <table class="min-w-full border-collapse">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="border px-2 py-1">
                                    <input
                                        type="text"
                                        bind:value={filters.validityGroup}
                                        placeholder="Validity Group"
                                        class="w-full p-1 text-sm"
                                    >
                                </th>
                                <th class="border px-2 py-1">
                                    <input
                                        type="text"
                                        bind:value={filters.activityPurpose}
                                        placeholder="Activity Purpose"
                                        class="w-full p-1 text-sm"
                                    >
                                </th>
                                <th class="border px-2 py-1">
                                    <input
                                        type="text"
                                        bind:value={filters.activityLabel}
                                        placeholder="Activity Label"
                                        class="w-full p-1 text-sm"
                                    >
                                </th>
                                <th class="border px-2 py-1">
                                    <input
                                        type="text"
                                        bind:value={filters.serviceCode}
                                        placeholder="Service Code"
                                        class="w-full p-1 text-sm"
                                    >
                                </th>
                                <th class="border px-2 py-1">
                                    <input
                                        type="text"
                                        bind:value={filters.actionType}
                                        placeholder="Action Type"
                                        class="w-full p-1 text-sm"
                                    >
                                </th>
                                <th class="border px-2 py-1">
                                    <input
                                        type="text"
                                        bind:value={filters.partNumber}
                                        placeholder="Part Number"
                                        class="w-full p-1 text-sm"
                                    >
                                </th>
                                <th class="border px-2 py-1">
                                    <input
                                        type="text"
                                        bind:value={filters.unit}
                                        placeholder="Unit"
                                        class="w-full p-1 text-sm"
                                    >
                                </th>
                                <th class="border px-2 py-1">
                                    <input
                                        type="text"
                                        bind:value={filters.quantity}
                                        placeholder="Quantity"
                                        class="w-full p-1 text-sm"
                                    >
                                </th>
                                <th class="border px-2 py-1">
                                    <input
                                        type="text"
                                        bind:value={filters.hours}
                                        placeholder="Hours"
                                        class="w-full p-1 text-sm"
                                    >
                                </th>
                                <th class="border px-2 py-1">
                                    <input
                                        type="text"
                                        bind:value={filters.months}
                                        placeholder="Months"
                                        class="w-full p-1 text-sm"
                                    >
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {#each filteredItems as item}
                                <tr class="hover:bg-gray-50">
                                    <td class="border px-2 py-1">{item.ProductValidityGroup}</td>
                                    <td class="border px-2 py-1">{item.ActivityPurpose}</td>
                                    <td class="border px-2 py-1">{item.ServiceActivityLabel}</td>
                                    <td class="border px-2 py-1">{item.ServiceCode}</td>
                                    <td class="border px-2 py-1">{item.ActionType}</td>
                                    <td class="border px-2 py-1">{item.PartNumber}</td>
                                    <td class="border px-2 py-1">{item.UnitOfMeasure}</td>
                                    <td class="border px-2 py-1">{item.Quantity}</td>
                                    <td class="border px-2 py-1">{item.InternalNoOfHours}</td>
                                    <td class="border px-2 py-1">{item.InternalNoOfMonths}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                </div>
            {:else}
                <div class="mb-6 p-4 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded">
                    No service elements found for this designation.
                </div>
            {/if}
        </div>
    {/if}

    <div class="mb-8">
        <h1 class="text-2xl font-bold mb-4">Computers</h1>
        
        {#if computers && computers.length > 0}
            <div class="overflow-x-auto">
                <table class="min-w-full border-collapse bg-white shadow-sm rounded-lg">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Computer ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {#each computers as computer}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{computer.computerId}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button
                                        on:click={() => createServiceActivities(computer._id)}
                                        class="text-indigo-600 hover:text-indigo-900"
                                    >
                                        Create Service Activities
                                    </button>
                                </td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
            </div>
        {:else}
            <div class="bg-yellow-50 border border-yellow-200 p-4 rounded text-yellow-800">
                No computers found.
            </div>
        {/if}
    </div>
</div>

<style>
    .container {
        max-width: 100%;
        margin: 0 auto;
    }

    table {
        border-collapse: collapse;
        width: 100%;
    }

    th, td {
        border: 1px solid #e2e8f0;
        padding: 0.5rem;
    }

    th {
        background-color: #f8fafc;
        font-weight: 600;
    }

    tr:hover {
        background-color: #f8fafc;
    }

    input {
        border: 1px solid #e2e8f0;
        border-radius: 0.25rem;
    }

    input:focus {
        outline: none;
        border-color: #3b82f6;
    }
</style>
