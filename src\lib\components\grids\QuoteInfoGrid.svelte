<!-- QuoteInfoGrid.svelte -->
<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  
  export let gap: string = '1rem';
  export let padding: string = '1rem';
  export let backgroundColor: string = '#ffffff';
  export let className: string = '';
</script>

<BaseGrid 
  columns="repeat(auto-fill, minmax(250px, 1fr))" 
  rows="auto"
  {gap} 
  {padding} 
  {backgroundColor}
  className="quote-info-grid {className}"
>
  <slot />
</BaseGrid>

<style>
  :global(.quote-info-grid) {
    width: 100%;
  }
  
  @media (max-width: 768px) {
    :global(.quote-info-grid) {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media (max-width: 480px) {
    :global(.quote-info-grid) {
      grid-template-columns: 1fr;
    }
  }
</style>
