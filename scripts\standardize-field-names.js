const { MongoClient } = require('mongodb');

// Connection URI
const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

// Database Name
const dbName = 'ServiceContracts';

// Collections to update with exact field names from the database
const collectionsToUpdate = [
    {
        name: 'ProductValidityGroup',
        oldField: 'Product Validity GRoup', // Exact field name with typo from the database
        newField: 'ProductValidityGroup'    // New standardized PascalCase field name
    }
];

async function updateCollection(db, collectionInfo) {
    console.log(`\nProcessing collection: ${collectionInfo.name}`);
    const collection = db.collection(collectionInfo.name);

    try {
        // Check current state
        const beforeCount = await collection.countDocuments({ [collectionInfo.oldField]: { $exists: true } });
        console.log(`Found ${beforeCount} documents with old field name "${collectionInfo.oldField}"`);

        if (beforeCount > 0) {
            // Get a sample document before update
            const beforeDoc = await collection.findOne({ [collectionInfo.oldField]: { $exists: true } });
            console.log('Sample document before update:');
            console.log(JSON.stringify(beforeDoc, null, 2));

            // Update documents
            const result = await collection.updateMany(
                { [collectionInfo.oldField]: { $exists: true } },
                [
                    {
                        $set: {
                            [collectionInfo.newField]: `$${collectionInfo.oldField}`
                        }
                    },
                    {
                        $unset: [collectionInfo.oldField]
                    }
                ]
            );

            console.log(`Updated ${result.modifiedCount} documents`);

            // Verify the changes
            if (beforeDoc._id) {
                const afterDoc = await collection.findOne({ _id: beforeDoc._id });
                console.log('\nSame document after update:');
                console.log(JSON.stringify(afterDoc, null, 2));
            }
        } else {
            // Check if documents already use the new field name
            const newFieldCount = await collection.countDocuments({ [collectionInfo.newField]: { $exists: true } });
            if (newFieldCount > 0) {
                console.log(`Collection already using new field name "${collectionInfo.newField}" (${newFieldCount} documents)`);
                const sampleDoc = await collection.findOne({ [collectionInfo.newField]: { $exists: true } });
                console.log('Sample document:');
                console.log(JSON.stringify(sampleDoc, null, 2));
            } else {
                console.log(`No documents found with either field name in ${collectionInfo.name}`);
            }
        }
    } catch (error) {
        console.error(`Error processing collection ${collectionInfo.name}:`, error);
    }
}

async function standardizeFieldNames() {
    try {
        console.log('Connecting to MongoDB...');
        await client.connect();
        console.log('Connected successfully to MongoDB server');
        
        const db = client.db(dbName);
        console.log(`Using database: ${dbName}`);

        // Process each collection
        for (const collectionInfo of collectionsToUpdate) {
            await updateCollection(db, collectionInfo);
        }

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await client.close();
        console.log('\nDisconnected from MongoDB');
    }
}

// Run the standardization
standardizeFieldNames().catch(console.error);
