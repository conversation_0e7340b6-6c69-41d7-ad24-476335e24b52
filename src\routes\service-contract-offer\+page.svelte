<script>
  import ServiceOfferingTable from '$lib/components/ServiceOfferingTable.svelte';

  /** @type {import('./$types').PageData} */
  export let data;

  // Extract data from server
  const {
    computer,
    customer,
    offer,
    packageOffering,
    serviceItems,
    groupedServices,
    productDesignation,
    productValidityGroup,
    dataSource
  } = data;

  // Transform service items for display
  $: packageOfferingLevels = packageOffering || [];
  $: individualServices = serviceItems || [];

  // Calculate total cost
  $: totalCost = individualServices
    .filter(service => service.includeInOffer || service.includeInQuote)
    .reduce((sum, service) => sum + (service.cost || 0), 0);

  // Handle service toggle
  function handleServiceToggle(event) {
    const { id } = event.detail;
    console.log('Toggle service:', id);
    // Update service state
  }

  // Handle service selection
  function handleServiceSelect(event) {
    const { id, serviceCode } = event.detail;
    console.log('Select service:', id, serviceCode);
    // Update service selection
  }


</script>

<svelte:head>
  <title>Service Offering - {productDesignation}</title>
</svelte:head>

<div class="service-offering-page">
  <h1>Service Offering</h1>

  <!-- Package offering section -->
  <ServiceOfferingTable
    title="Package offering"
    services={packageOfferingLevels}
    isPackageOffering={true}
    on:toggleService={handleServiceToggle}
    on:selectService={handleServiceSelect}
  />

  <!-- Individual service section -->
  <ServiceOfferingTable
    title="Individual service"
    services={individualServices}
    isPackageOffering={false}
    on:toggleService={handleServiceToggle}
    on:selectService={handleServiceSelect}
  />

  <!-- Total Cost Section -->
  <div class="total-section">
    <table class="total-table">
      <tbody>
        <tr>
          <td class="total-label">Total cost in quote</td>
          <td class="total-value">{totalCost.toFixed(2)} EUR</td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- Grouped Services Section -->
  <div class="grouped-section">
    <table class="grouped-table">
      <thead>
        <tr>
          <th colspan="2">Grouped services</th>
        </tr>
        <tr>
          <th>Level</th>
          <th>Package</th>
        </tr>
      </thead>
      <tbody>
        {#each groupedServices as group}
          <tr>
            <td class="level-cell">{group.level}</td>
            <td class="package-cell">{group.packageName}</td>
          </tr>
        {/each}
      </tbody>
    </table>
  </div>

  <!-- Debug Information -->
  <div class="debug-section">
    <h3>Debug Information</h3>
    <p><strong>Product Designation:</strong> {productDesignation}</p>
    <p><strong>Product Validity Group:</strong> {productValidityGroup}</p>
    <p><strong>Total Service Items:</strong> {individualServices.length}</p>
    <p><strong>Data Source:</strong> ServiceCodeAndActionType ({dataSource?.serviceCodeAndActionType || 0} items)</p>
  </div>
</div>

<style>
  .service-offering-page {
    padding: 1rem;
    max-width: 1400px;
    margin: 0 auto;
  }

  .service-offering-page h1 {
    color: #333;
    margin-bottom: 2rem;
    text-align: center;
  }

  .total-section {
    margin: 1rem 0;
  }

  .total-table {
    width: 100%;
    border-collapse: collapse;
    border: 2px solid #333;
  }

  .total-table td {
    padding: 0.5rem;
    border: 1px solid #333;
    text-align: center;
    font-weight: bold;
  }

  .total-label {
    background-color: #f5f5f5;
    text-align: left;
  }

  .total-value {
    background-color: #FFD700;
    color: #333;
    text-align: right;
  }

  .grouped-section {
    margin: 1rem 0;
  }

  .grouped-table {
    width: 100%;
    border-collapse: collapse;
    border: 2px solid #333;
  }

  .grouped-table th {
    background-color: #f5f5f5;
    padding: 0.5rem;
    border: 1px solid #333;
    text-align: center;
    font-weight: bold;
  }

  .grouped-table td {
    padding: 0.5rem;
    border: 1px solid #333;
    text-align: center;
  }

  .level-cell {
    width: 60px;
    font-weight: bold;
  }

  .package-cell {
    text-align: left;
  }

  .debug-section {
    margin-top: 2rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #ddd;
  }

  .debug-section h3 {
    color: #333;
    margin-bottom: 1rem;
  }

  .debug-section p {
    margin: 0.5rem 0;
    color: #666;
  }

  @media (max-width: 768px) {
    .service-offering-page {
      padding: 0.5rem;
    }
  }
</style>