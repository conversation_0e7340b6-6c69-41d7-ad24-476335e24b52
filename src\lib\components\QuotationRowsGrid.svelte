<!-- @component
A grid component for displaying grouped quotation rows
-->
<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import type { QuotationRow, QuoteGroup } from '$lib/types';
  import { formatCurrency, formatBoolean, formatDate } from '$lib/utils/formatters';
  import QuotationRowForm from './QuotationRowForm.svelte';

  export let groupedRows: Record<string, QuoteGroup> = {};
  export let currentQuoteId: string;

  const dispatch = createEventDispatcher();

  let showModal = false;
  let editingRow: QuotationRow | null = null;

  function handleEdit(row: QuotationRow) {
    editingRow = row;
    showModal = true;
  }

  function handleDelete(row: QuotationRow) {
    if (confirm('Are you sure you want to delete this row?')) {
      dispatch('deleteRow', row);
    }
  }

  function handleSave(event: CustomEvent<QuotationRow>) {
    showModal = false;
    editingRow = null;
    dispatch('dataUpdated');
  }

  function handleCancel() {
    showModal = false;
    editingRow = null;
  }

  function handleAdd() {
    editingRow = null;
    showModal = true;
  }
</script>

<div class="grid-container">
  <div class="add-section">
    <button class="add-button" on:click={handleAdd}>
      Add Row
    </button>
  </div>

  {#each Object.entries(groupedRows) as [type, group]}
    <div class="group-section">
      <div class="group-header">
        <h2>{group.Title}</h2>
        <span class="group-total">Total: {formatCurrency(group.Subtotal)}</span>
      </div>

      <div class="table-responsive">
        <table class="quotation-table">
          <thead>
            <tr>
              <th>Level</th>
              <th>Package</th>
              <th>Service ID</th>
              <th>Service</th>
              <th>Included in Package Pricing?</th>
              <th>Required</th>
              <th>KCOS</th>
              <th>OEM Importer</th>
              <th>Fleet Owner</th>
              <th>SSP</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {#each group.Rows as row (row._id)}
              <tr>
                <td>{group.Title}</td>
                <td>{row.PackageName || '-'}</td>
                <td>{row.RowOrder || '-'}</td>
                <td>{row.ServiceActivity || '-'}</td>
                <td>{row.IncludeInOffer ? 'Yes' : 'No'}</td>
                <td>{row.Required ? 'Mandatory' : 'No'}</td>
                <td class="number-cell">{row.Cost ? formatCurrency(row.Cost) : '€ -'}</td>
                <td class="number-cell">{row.OemImporter ? formatCurrency(row.OemImporter) : '€ -'}</td>
                <td class="number-cell">{row.FleetOwner ? formatCurrency(row.FleetOwner) : '€ -'}</td>
                <td class="number-cell">{row.SSP ? formatCurrency(row.SSP) : '€ -'}</td>
                <td class="actions">
                  <button 
                    class="action-button edit" 
                    on:click={() => handleEdit(row)}
                  >
                    Edit
                  </button>
                  <button 
                    class="action-button delete" 
                    on:click={() => handleDelete(row)}
                  >
                    Delete
                  </button>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    </div>
  {/each}
</div>

{#if showModal}
  <QuotationRowForm
    row={editingRow}
    currentQuoteId={currentQuoteId}
    on:save={handleSave}
    on:cancel={handleCancel}
  />
{/if}

<style>
  .grid-container {
    display: grid;
    grid-template-rows: auto 1fr;
    gap: 2rem;
    width: 100%;
  }

  .group-section {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .group-header {
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    margin-bottom: 0.5rem;
  }

  .group-header h2 {
    margin: 0;
    font-size: 1.25rem;
    color: #2c3e50;
  }

  .group-total {
    font-size: 1.1rem;
    font-weight: 500;
    color: #2c3e50;
  }

  .table-responsive {
    overflow-x: auto;
    width: 100%;
  }

  .quotation-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #dee2e6;
    font-size: 0.875rem;
  }

  .quotation-table th,
  .quotation-table td {
    padding: 0.75rem;
    text-align: left;
    border: 1px solid #dee2e6;
  }

  .quotation-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 1;
  }

  .quotation-table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
  }

  .quotation-table tbody tr:hover {
    background-color: #e9ecef;
  }

  .number-cell {
    text-align: right;
    white-space: nowrap;
  }

  .actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-start;
    align-items: center;
  }

  .action-button {
    padding: 0.5rem;
    border: none;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;
  }

  .action-button:hover {
    transform: translateY(-1px);
  }

  .edit {
    background-color: #e9ecef;
    color: #495057;
  }

  .edit:hover {
    background-color: #dee2e6;
  }

  .delete {
    background-color: #dc3545;
    color: white;
  }

  .delete:hover {
    background-color: #c82333;
  }

  .add-section {
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
  }

  .add-button {
    padding: 0.75rem 1.5rem;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: background-color 0.2s, transform 0.1s;
  }

  .add-button:hover {
    background-color: #218838;
    transform: translateY(-1px);
  }
</style>
