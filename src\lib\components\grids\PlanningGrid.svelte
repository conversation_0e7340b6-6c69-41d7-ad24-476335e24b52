<script>
    /**
     * PlanningGrid.svelte - Grid component for service planning pages
     * 
     * This component provides a responsive CSS grid layout for service planning screens.
     * It includes areas for header, sidebar, main content, and footer.
     */
</script>

<div class="planning-grid">
    <header class="header">
        <slot name="header" />
    </header>
    
    <aside class="sidebar">
        <slot name="sidebar" />
    </aside>
    
    <main class="main-content">
        <slot name="content" />
    </main>
    
    <footer class="footer">
        <slot name="footer" />
    </footer>
</div>

<style>
    .planning-grid {
        display: grid;
        grid-template-areas:
            "header header"
            "sidebar content"
            "footer footer";
        grid-template-columns: minmax(250px, 1fr) 3fr;
        grid-template-rows: auto 1fr auto;
        gap: 1rem;
        height: 100%;
        width: 100%;
        max-width: 1800px;
        margin: 0 auto;
        padding: 1rem;
    }
    
    .header {
        grid-area: header;
        background-color: #f5f5f5;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .sidebar {
        grid-area: sidebar;
        background-color: #f5f5f5;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .main-content {
        grid-area: content;
        background-color: #f5f5f5;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        overflow-y: auto;
    }
    
    .footer {
        grid-area: footer;
        background-color: #f5f5f5;
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-top: 1rem;
    }
    
    /* Responsive layout for mobile devices */
    @media (max-width: 768px) {
        .planning-grid {
            grid-template-areas:
                "header"
                "sidebar"
                "content"
                "footer";
            grid-template-columns: 1fr;
            grid-template-rows: auto auto 1fr auto;
        }
    }
</style>
