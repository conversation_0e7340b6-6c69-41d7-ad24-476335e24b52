<script>
  import { goto } from '$app/navigation';
  import { enhance } from '$app/forms';

  /** @type {import('./$types').PageData} */
  export let data;

  /** @type {import('./$types').ActionData} */
  export let form;

  // Component state
  let isEditing = false;
  let editedItem = { ...data.item };

  // Handle edit mode toggle
  function toggleEdit() {
    if (isEditing) {
      // Reset to original data if canceling
      editedItem = { ...data.item };
    }
    isEditing = !isEditing;
  }

  // Handle delete
  async function handleDelete() {
    if (!confirm('Are you sure you want to delete this service plan item?')) {
      return;
    }

    try {
      const response = await fetch(`/api/service-plan-product-designation/${data.item._id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        // Navigate back to the list
        goto('/service-plan-product-designation');
      } else {
        alert('Error deleting item');
      }
    } catch (error) {
      console.error('Error deleting item:', error);
      alert('Error deleting item');
    }
  }

  // Handle form submission success
  $: if (form?.success) {
    isEditing = false;
    // Update the local data with the response
    if (form.item) {
      editedItem = { ...form.item };
      data.item = { ...form.item };
    }
  }
</script>

<svelte:head>
  <title>Service Plan Item Details</title>
</svelte:head>

<div class="container">
  <div class="header-content">
    <h1>Service Plan Item Details</h1>
    <div class="header-actions">
      <a href="/service-plan-product-designation" class="btn-secondary">← Back to List</a>
    </div>
  </div>

  {#if form?.error}
    <div class="error-message">
      {form.error}
    </div>
  {/if}

  {#if form?.success}
    <div class="success-message">
      {form.message || 'Item updated successfully'}
    </div>
  {/if}

  <div class="detail-section">
    <div class="detail-header">
      <h2>{data.item.productDesignation} - {data.item.serviceCode}</h2>
      <div class="action-buttons">
        <button
          class="btn-primary"
          on:click={toggleEdit}
        >
          {isEditing ? 'Cancel' : 'Edit'}
        </button>

        {#if !isEditing}
          <button
            class="btn-danger"
            on:click={handleDelete}
          >
            Delete
          </button>
        {/if}
      </div>
    </div>

    {#if isEditing}
      <!-- Edit form -->
      <form method="POST" action="?/update" use:enhance>
        <input type="hidden" name="id" value={data.item._id} />

        <div class="form-grid">
          <div class="form-group">
            <label for="productDesignation">Product Designation:</label>
            <input
              type="text"
              id="productDesignation"
              name="productDesignation"
              bind:value={editedItem.productDesignation}
              required
            />
          </div>

          <div class="form-group">
            <label for="serviceCode">Service Code:</label>
            <input
              type="text"
              id="serviceCode"
              name="serviceCode"
              bind:value={editedItem.serviceCode}
              required
            />
          </div>

          <div class="form-group">
            <label for="actionType">Action Type:</label>
            <input
              type="text"
              id="actionType"
              name="actionType"
              bind:value={editedItem.actionType}
              required
            />
          </div>

          <div class="form-group">
            <label for="activityPurpose">Activity Purpose:</label>
            <input
              type="text"
              id="activityPurpose"
              name="activityPurpose"
              bind:value={editedItem.activityPurpose}
            />
          </div>

          <div class="form-group">
            <label for="serviceActivityLabel">Service Activity Label:</label>
            <input
              type="text"
              id="serviceActivityLabel"
              name="serviceActivityLabel"
              bind:value={editedItem.serviceActivityLabel}
            />
          </div>

          <div class="form-group">
            <label for="partNumber">Part Number:</label>
            <input
              type="number"
              id="partNumber"
              name="partNumber"
              bind:value={editedItem.partNumber}
            />
          </div>

          <div class="form-group">
            <label for="unitOfMeasure">Unit of Measure:</label>
            <input
              type="text"
              id="unitOfMeasure"
              name="unitOfMeasure"
              bind:value={editedItem.unitOfMeasure}
            />
          </div>

          <div class="form-group">
            <label for="quantity">Quantity:</label>
            <input
              type="number"
              id="quantity"
              name="quantity"
              bind:value={editedItem.quantity}
            />
          </div>

          <div class="form-group">
            <label for="accHours">Accumulated Hours:</label>
            <input
              type="number"
              id="accHours"
              name="accHours"
              bind:value={editedItem.accHours}
              required
            />
          </div>

          <div class="form-group">
            <label for="frequency">Frequency (Hours):</label>
            <input
              type="number"
              id="frequency"
              name="frequency"
              bind:value={editedItem.frequency}
              required
            />
          </div>

          <div class="form-group">
            <label for="sequenceNumber">Sequence Number:</label>
            <input
              type="number"
              id="sequenceNumber"
              name="sequenceNumber"
              bind:value={editedItem.sequenceNumber}
              required
            />
          </div>
        </div>

        <div class="form-actions">
          <button type="submit" class="btn-primary">Save Changes</button>
          <button type="button" class="btn-secondary" on:click={toggleEdit}>Cancel</button>
        </div>
      </form>
    {:else}
      <!-- View mode -->
      <div class="detail-grid">
        <div class="detail-item">
          <div class="field-label">Product Designation:</div>
          <span class="value">{data.item.productDesignation}</span>
        </div>

        <div class="detail-item">
          <div class="field-label">Service Code:</div>
          <span class="value">{data.item.serviceCode}</span>
        </div>

        <div class="detail-item">
          <div class="field-label">Action Type:</div>
          <span class="value">{data.item.actionType}</span>
        </div>

        <div class="detail-item">
          <div class="field-label">Activity Purpose:</div>
          <span class="value">{data.item.activityPurpose || '-'}</span>
        </div>

        <div class="detail-item">
          <div class="field-label">Service Activity Label:</div>
          <span class="value">{data.item.serviceActivityLabel || '-'}</span>
        </div>

        <div class="detail-item">
          <div class="field-label">Part Number:</div>
          <span class="value">{data.item.partNumber || '-'}</span>
        </div>

        <div class="detail-item">
          <div class="field-label">Unit of Measure:</div>
          <span class="value">{data.item.unitOfMeasure || '-'}</span>
        </div>

        <div class="detail-item">
          <div class="field-label">Quantity:</div>
          <span class="value">{data.item.quantity || '-'}</span>
        </div>

        <div class="detail-item highlight">
          <div class="field-label">Accumulated Hours:</div>
          <span class="value hours-value">{data.item.accHours}</span>
        </div>

        <div class="detail-item">
          <div class="field-label">Frequency (Hours):</div>
          <span class="value">{data.item.frequency}</span>
        </div>

        <div class="detail-item">
          <div class="field-label">Sequence Number:</div>
          <span class="value">{data.item.sequenceNumber}</span>
        </div>

        <div class="detail-item">
          <div class="field-label">Created At:</div>
          <span class="value">{new Date(data.item.createdAt).toLocaleString()}</span>
        </div>

        <div class="detail-item">
          <div class="field-label">Updated At:</div>
          <span class="value">{new Date(data.item.updatedAt).toLocaleString()}</span>
        </div>
      </div>
    {/if}
  </div>
</div>

<style>
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e5e7eb;
  }

  .header-content h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
  }

  .error-message {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1.5rem;
  }

  .success-message {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #059669;
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1.5rem;
  }

  .detail-section {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 2rem;
  }

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .detail-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
  }

  .action-buttons {
    display: flex;
    gap: 1rem;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-group label {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
  }

  .form-group input {
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 1rem;
  }

  .form-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-start;
  }

  .detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }

  .detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 6px;
  }

  .detail-item.highlight {
    background: #eff6ff;
    border: 1px solid #bfdbfe;
  }

  .detail-item .field-label {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .detail-item .value {
    font-size: 1rem;
    color: #1f2937;
    font-weight: 500;
  }

  .hours-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #059669;
  }

  /* Button styles */
  .btn-primary {
    background: #1e40af;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: background-color 0.2s;
  }

  .btn-primary:hover {
    background: #1d4ed8;
  }

  .btn-secondary {
    background: #6b7280;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: background-color 0.2s;
  }

  .btn-secondary:hover {
    background: #4b5563;
  }

  .btn-danger {
    background: #dc2626;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .btn-danger:hover {
    background: #b91c1c;
  }
</style>
