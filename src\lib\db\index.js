import { MongoClient } from 'mongodb';

const url = 'mongodb://localhost:27017';
const client = new MongoClient(url);
const dbName = 'ServiceContracts';

// Export an init function that connects to MongoDB and returns the database
export async function init() {
  try {
    await client.connect();
    return client.db(dbName);
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    throw error;
  }
}

// Also export the db directly for components that need immediate access
export const db = client.db(dbName);
