import { MongoClient, ObjectId } from 'mongodb';
import { getCollection } from './src/lib/db/mongo.js';

/**
 * Add D-series service codes to the ServiceCodeHeader collection
 */
async function addServiceCodes() {
  try {
    // Get the ServiceCodeHeader collection
    const serviceCodeHeaderCollection = await getCollection('ServiceCodeHeader');
    
    // D-series service codes data
    const serviceCodes = [
      {
        ServiceCode: 'S-D13',
        'Service activity Label': 'Standard Service D13',
        'Activity purpose': 'Standard maintenance for D13 engines',
        'Product Validity Group': 'D13',
        'Internal No of Hours': 2,
        'Internal No of Months': 6,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        ServiceCode: 'A-D13',
        'Service activity Label': 'Advanced Service D13',
        'Activity purpose': 'Advanced maintenance for D13 engines',
        'Product Validity Group': 'D13',
        'Internal No of Hours': 4,
        'Internal No of Months': 12,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        ServiceCode: 'B-D13',
        'Service activity Label': 'Basic Service D13',
        'Activity purpose': 'Basic maintenance for D13 engines',
        'Product Validity Group': 'D13',
        'Internal No of Hours': 1,
        'Internal No of Months': 3,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        ServiceCode: 'B1-D13',
        'Service activity Label': 'Basic Level 1 Service D13',
        'Activity purpose': 'Basic level 1 maintenance for D13 engines',
        'Product Validity Group': 'D13',
        'Internal No of Hours': 1.5,
        'Internal No of Months': 3,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        ServiceCode: 'B2-D13',
        'Service activity Label': 'Basic Level 2 Service D13',
        'Activity purpose': 'Basic level 2 maintenance for D13 engines',
        'Product Validity Group': 'D13',
        'Internal No of Hours': 2,
        'Internal No of Months': 3,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        ServiceCode: 'B3-D13',
        'Service activity Label': 'Basic Level 3 Service D13',
        'Activity purpose': 'Basic level 3 maintenance for D13 engines',
        'Product Validity Group': 'D13',
        'Internal No of Hours': 2.5,
        'Internal No of Months': 3,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        ServiceCode: 'C-D13',
        'Service activity Label': 'Comprehensive Service D13',
        'Activity purpose': 'Comprehensive maintenance for D13 engines',
        'Product Validity Group': 'D13',
        'Internal No of Hours': 6,
        'Internal No of Months': 12,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        ServiceCode: 'D-D13',
        'Service activity Label': 'Diagnostic Service D13',
        'Activity purpose': 'Diagnostic check for D13 engines',
        'Product Validity Group': 'D13',
        'Internal No of Hours': 1,
        'Internal No of Months': 1,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        ServiceCode: 'E-D13',
        'Service activity Label': 'Extended Service D13',
        'Activity purpose': 'Extended maintenance for D13 engines',
        'Product Validity Group': 'D13',
        'Internal No of Hours': 8,
        'Internal No of Months': 24,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        ServiceCode: 'F-D13',
        'Service activity Label': 'Full Service D13',
        'Activity purpose': 'Full maintenance for D13 engines',
        'Product Validity Group': 'D13',
        'Internal No of Hours': 10,
        'Internal No of Months': 24,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
    
    console.log(`Prepared ${serviceCodes.length} service codes for insertion`);
    
    // Check for existing service codes to avoid duplicates
    const existingCodes = await serviceCodeHeaderCollection.find({
      ServiceCode: { $in: serviceCodes.map(code => code.ServiceCode) }
    }).toArray();
    
    if (existingCodes.length > 0) {
      console.log(`Found ${existingCodes.length} existing service codes`);
      console.log('Existing service codes:', existingCodes.map(code => code.ServiceCode).join(', '));
      
      // Filter out service codes that already exist
      const newCodes = serviceCodes.filter(code => 
        !existingCodes.some(existing => existing.ServiceCode === code.ServiceCode)
      );
      
      if (newCodes.length === 0) {
        console.log('All service codes already exist. No new codes to add.');
        return;
      }
      
      console.log(`Adding ${newCodes.length} new service codes`);
      
      // Insert new service codes
      const result = await serviceCodeHeaderCollection.insertMany(newCodes);
      console.log(`Added ${result.insertedCount} new service codes to the ServiceCodeHeader collection`);
    } else {
      // Insert all service codes
      const result = await serviceCodeHeaderCollection.insertMany(serviceCodes);
      console.log(`Added ${result.insertedCount} service codes to the ServiceCodeHeader collection`);
    }
    
    // List all service codes in the collection
    const allCodes = await serviceCodeHeaderCollection.find({}).toArray();
    console.log('\nAll service codes in collection:');
    allCodes.forEach(code => {
      console.log(`- ${code.ServiceCode}: ${code['Service activity Label']}`);
      console.log(`  Purpose: ${code['Activity purpose']}`);
      console.log(`  Product Validity Group: ${code['Product Validity Group']}`);
      console.log(`  Hours: ${code['Internal No of Hours']}, Months: ${code['Internal No of Months']}`);
    });
    
  } catch (error) {
    console.error('Error adding service codes:', error);
  }
}

// Run the function
addServiceCodes();
