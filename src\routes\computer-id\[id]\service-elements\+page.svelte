<script>
  import { page } from '$app/stores';
  
  /** @type {import('./$types').PageData} */
  export let data;
  
  const computerId = $page.params.id;
</script>

<div class="container">
  <div class="header">
    <h1>Computer Service Elements</h1>
    <div class="button-group">
      <a href="/computer-id/{computerId}" class="primary-button">
        Back to Computer
      </a>
    </div>
  </div>

  {#if data.success}
    <div class="card">
      <h2>Basic Information</h2>
      <div class="info-grid">
        <div class="info-row">
          <span class="label">Computer ID:</span>
          <span class="value">{computerId}</span>
        </div>
        <div class="info-row">
          <span class="label">Name/Type:</span>
          <span class="value">{data?.computer?._id || 'N/A'}</span>
        </div>
        <div class="info-row highlighted">
          <span class="label">Product Validity Group:</span>
          <span class="value">{data.productValidityGroup || 'N/A'}</span>
        </div>
        <div class="info-row highlighted">
          <span class="label">Category:</span>
          <span class="value">{data.computerCategory || 'N/A'}</span>
        </div>
        <div class="info-row">
          <span class="label">Hours At Contract Start:</span>
          <span class="value">{data?.computer?.HoursAtContractStart || 'N/A'}</span>
        </div>
      </div>
    </div>

    <div class="card">
      <h2>Service Elements for {data.productValidityGroup || 'Unknown'} Group</h2>
      {#if data.serviceElements.length > 0}
        <div class="service-elements-grid">
          <div class="service-elements-header">
            <div class="column">Product Validity Group</div>
            <div class="column">Activity Purpose</div>
            <div class="column">Service Activity Label</div>
            <div class="column">Service Code</div>
            <div class="column">Action Type</div>
            <div class="column">Part Number</div>
            <div class="column">Unit of Measure</div>
            <div class="column">Quantity</div>
            <div class="column">Internal No Of Hours</div>
            <div class="column">Internal No Of Months</div>
          </div>
          
          {#each data.serviceElements as element}
            <div class="service-elements-row">
              <div class="column">{element.ProductValidityGroup || ''}</div>
              <div class="column">{element.ActivityPurpose || ''}</div>
              <div class="column">{element.ServiceActivityLabel || ''}</div>
              <div class="column">{element.ServiceCode || ''}</div>
              <div class="column">{element.ActionType || ''}</div>
              <div class="column">{element.PartNumber || ''}</div>
              <div class="column">{element.UnitOfMeasure || ''}</div>
              <div class="column">{element.Quantity || ''}</div>
              <div class="column">{element.InternalNoOfHours || ''}</div>
              <div class="column">{element.InternalNoOfMonths || ''}</div>
            </div>
          {/each}
        </div>
        
        <div class="details-section">
          <h3>Service Details</h3>
          <div class="details-grid">
            {#each [...new Set(data.serviceElements.map(e => e.ServiceCode))] as serviceCode}
              {@const service = data.serviceElements.find(e => e.ServiceCode === serviceCode)}
              {#if service}
                <div class="service-detail-card">
                  <div class="service-header">
                    <h4>{service.ServiceCode}</h4>
                    <span class="category">{service.ServiceDescription || ''}</span>
                  </div>
                  <div class="service-body">
                    <div class="detail-row">
                      <span class="detail-label">Activity:</span>
                      <span class="detail-value">{service.ServiceActivityLabel || 'N/A'}</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">Purpose:</span>
                      <span class="detail-value">{service.ActivityPurpose || 'N/A'}</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">Internal No Of Hours:</span>
                      <span class="detail-value">{service.InternalNoOfHours}</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">Internal No Of Months:</span>
                      <span class="detail-value">{service.InternalNoOfMonths}</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">VST Code:</span>
                      <span class="detail-value">{service.VSTCode || 'N/A'}</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">VST Hours:</span>
                      <span class="detail-value">{service.VSTHours || 'N/A'}</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">Service Phase:</span>
                      <span class="detail-value">{service.ServicePhase || 'N/A'}</span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">Category:</span>
                      <span class="detail-value">{service.ComputerCategory || 'N/A'}</span>
                    </div>
                  </div>
                </div>
              {/if}
            {/each}
          </div>
        </div>
      {:else}
        <p class="empty-message">No service elements found for this product validity group.</p>
      {/if}
    </div>

    {#if data.labourTimeRecords && data.labourTimeRecords.length > 0}
      <div class="card">
        <h2>Labour Time Records for Category: {data.computerCategory}</h2>
        <div class="labour-time-grid">
          <div class="labour-time-header">
            <div class="column">Service Code</div>
            <div class="column">Service Description</div>
            <div class="column">VST Code</div>
            <div class="column">VST Hours</div>
            <div class="column">Service Phase</div>
          </div>
          {#each data.labourTimeRecords as labour}
            <div class="labour-time-row">
              <div class="column">{labour.ServiceCode || ''}</div>
              <div class="column">{labour.ServiceDescription || ''}</div>
              <div class="column">{labour.VSTCode || ''}</div>
              <div class="column">{labour.VSTHours || ''}</div>
              <div class="column">{labour.ServicePhase || ''}</div>
            </div>
          {/each}
        </div>
      </div>
    {:else}
      <p class="empty-message">No labour time records found for this category.</p>
    {/if}

    {#if data.serviceContracts && data.serviceContracts.length > 0}
      <div class="card">
        <h2>Service Contracts for this Computer</h2>
        <div class="contracts-grid">
          <div class="contracts-header">
            <div class="column">Contract Number</div>
            <div class="column">Start Date</div>
            <div class="column">End Date</div>
            <div class="column">Type</div>
            <div class="column">Status</div>
            <div class="column">Amount</div>
          </div>
          {#each data.serviceContracts as contract}
            <div class="contracts-row">
              <div class="column">{contract.contractNumber || ''}</div>
              <div class="column">{contract.startDate ? new Date(contract.startDate).toLocaleDateString() : 'N/A'}</div>
              <div class="column">{contract.endDate ? new Date(contract.endDate).toLocaleDateString() : 'N/A'}</div>
              <div class="column">{contract.type || ''}</div>
              <div class="column">
                <span class="status-badge {contract.status?.toLowerCase() || 'active'}">{contract.status || 'Active'}</span>
              </div>
              <div class="column">{typeof contract.amount === 'number' ? contract.amount.toFixed(2) : contract.amount || '0.00'}</div>
            </div>
          {/each}
        </div>
      </div>
    {:else}
      <p class="empty-message">No service contracts found for this computer.</p>
    {/if}
  {:else}
    <div class="error-message">
      <h2>Error</h2>
      <p>{data.error || 'Failed to load service elements'}</p>
    </div>
  {/if}
</div>

<style>
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }

  h1 {
    margin: 0;
    color: #2196f3;
    font-size: 1.8rem;
  }

  h2 {
    margin-top: 0;
    color: #2196f3;
    font-size: 1.4rem;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
  }

  .card {
    background-color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .info-row {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 1rem;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 0.5rem;
  }

  .info-row.highlighted {
    background-color: #f9f9f9;
    padding: 0.5rem;
    border-radius: 4px;
  }

  .label {
    font-weight: 500;
    color: #555;
  }

  .value {
    color: #333;
  }

  .service-elements-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .service-elements-header {
    display: grid;
    grid-template-columns: repeat(11, 1fr);
    gap: 1rem;
    background-color: #f5f5f5;
    padding: 0.75rem;
    border-radius: 4px;
    font-weight: bold;
  }

  .service-elements-row {
    display: grid;
    grid-template-columns: repeat(11, 1fr);
    gap: 1rem;
    padding: 0.75rem;
    border-bottom: 1px solid #f0f0f0;
  }

  .service-elements-row:hover {
    background-color: #f9f9f9;
  }

  .column {
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .button-group {
    display: flex;
    gap: 1rem;
  }

  .primary-button {
    background-color: #2196f3;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.5rem 1rem;
    cursor: pointer;
    text-decoration: none;
    font-size: 0.9rem;
    display: inline-flex;
    align-items: center;
    transition: background-color 0.2s;
  }

  .primary-button:hover {
    background-color: #1976d2;
  }

  .empty-message {
    color: #757575;
    font-style: italic;
    margin: 1rem 0;
  }

  .error-message {
    color: #f44336;
    background-color: #ffebee;
    padding: 1rem;
    border-radius: 4px;
    margin-top: 1rem;
  }

  .details-section {
    margin-top: 2rem;
  }

  .details-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .service-detail-card {
    background-color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 1.5rem;
  }

  .service-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .service-header h4 {
    margin: 0;
  }

  .service-header .category {
    font-size: 0.9rem;
    color: #666;
  }

  .service-body {
    padding: 1rem;
    border-top: 1px solid #f0f0f0;
  }

  .detail-row {
    display: grid;
    grid-template-columns: 150px 1fr;
    gap: 1rem;
    margin-bottom: 0.5rem;
  }

  .detail-label {
    font-weight: 500;
    color: #555;
  }

  .detail-value {
    color: #333;
  }

  .labour-time-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .labour-time-header {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
    background-color: #f5f5f5;
    padding: 0.75rem;
    border-radius: 4px;
    font-weight: bold;
  }

  .labour-time-row {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
    padding: 0.75rem;
    border-bottom: 1px solid #f0f0f0;
  }

  .contracts-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .contracts-header {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 1rem;
    background-color: #f5f5f5;
    padding: 0.75rem;
    border-radius: 4px;
    font-weight: bold;
  }

  .contracts-row {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 1rem;
    padding: 0.75rem;
    border-bottom: 1px solid #f0f0f0;
  }

  .status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
  }

  .status-badge.active {
    background-color: #4caf50;
    color: white;
  }

  .status-badge.inactive {
    background-color: #f44336;
    color: white;
  }

  @media (max-width: 768px) {
    .info-row {
      grid-template-columns: 1fr;
    }

    .service-elements-header,
    .service-elements-row {
      grid-template-columns: repeat(3, 1fr);
    }

    .service-elements-header .column:nth-child(4),
    .service-elements-header .column:nth-child(5),
    .service-elements-header .column:nth-child(6),
    .service-elements-header .column:nth-child(7),
    .service-elements-header .column:nth-child(8),
    .service-elements-header .column:nth-child(9),
    .service-elements-header .column:nth-child(10),
    .service-elements-header .column:nth-child(11),
    .service-elements-row .column:nth-child(4),
    .service-elements-row .column:nth-child(5),
    .service-elements-row .column:nth-child(6),
    .service-elements-row .column:nth-child(7),
    .service-elements-row .column:nth-child(8),
    .service-elements-row .column:nth-child(9),
    .service-elements-row .column:nth-child(10),
    .service-elements-row .column:nth-child(11) {
      display: none;
    }
  }
</style>
