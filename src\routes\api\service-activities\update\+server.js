import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

// Define valid activity types for validation
const VALID_ACTIVITY_TYPES = [
    'Regular Service',
    'Contract Service',
    'Maintenance',
    'Repair',
    'Inspection',
    'Installation',
    'Remote Support',
    'Software Update',
    'Hardware Update',
    'Training',
    'Consultation'
];

/** @type {import('./$types').RequestHandler} */
export async function PUT({ request }) {
    let mongoClient = null;
    
    try {
        mongoClient = new MongoClient(uri);
        await mongoClient.connect();
        
        const db = mongoClient.db('ServiceContracts');
        const activitiesCollection = db.collection('ServiceActivities');
        
        const data = await request.json();
        const { _id, year, month, hours, activity, hasFixed, computerId } = data;
        
        console.log('Updating activity:', data);
        
        // Validate ObjectId format
        if (!_id) {
            return json({ 
                success: false, 
                message: 'Activity ID is required for updates. Use the create endpoint for new activities.',
                validActivities: VALID_ACTIVITY_TYPES
            }, { status: 400 });
        }
        
        // Check if _id is valid ObjectId format
        let objectId;
        try {
            objectId = new ObjectId(_id);
        } catch (error) {
            return json({ 
                success: false, 
                message: `Invalid activity ID format: ${_id}`,
                validActivities: VALID_ACTIVITY_TYPES
            }, { status: 400 });
        }
        
        // Check if the activity exists first
        const existingActivity = await activitiesCollection.findOne({ _id: objectId });
        
        if (!existingActivity) {
            console.error(`Activity not found for ID: ${_id}`);
            return json({ 
                success: false, 
                message: 'Activity not found. It may have been deleted or never existed.',
                validActivities: VALID_ACTIVITY_TYPES 
            }, { status: 404 });
        }
        
        if (!year || !month) {
            return json({ 
                success: false, 
                message: 'Year and month are required',
                validActivities: VALID_ACTIVITY_TYPES
            }, { status: 400 });
        }
        
        if (hours === undefined) {
            return json({ 
                success: false, 
                message: 'Hours value is required',
                validActivities: VALID_ACTIVITY_TYPES
            }, { status: 400 });
        }
        
        // Create update object with all fields
        const updateObj = {};
        
        // Always include hours and updatedAt
        updateObj.hours = parseFloat(hours) || 0;
        updateObj.updatedAt = new Date();
        
        // Add optional fields if provided
        if (activity !== undefined) updateObj.activity = activity;
        if (hasFixed !== undefined) updateObj.hasFixed = Boolean(hasFixed);
        
        console.log(`Updating activity ${_id} with:`, updateObj);
        
        const result = await activitiesCollection.updateOne(
            { _id: objectId },
            { $set: updateObj }
        );
        
        // Double-check update success
        if (result.matchedCount === 0) {
            return json({ 
                success: false, 
                message: 'Activity not found after validation. Please try again.',
                validActivities: VALID_ACTIVITY_TYPES 
            }, { status: 404 });
        }
        
        // Get the updated document
        const updatedActivity = await activitiesCollection.findOne({ _id: objectId });
        
        // Convert ObjectId to string for client-side use
        const responseData = {
            ...updatedActivity,
            _id: updatedActivity._id.toString()
        };
        
        return json({ 
            success: true,
            message: 'Activity updated successfully',
            data: responseData,
            validActivities: VALID_ACTIVITY_TYPES
        });
    } catch (error) {
        console.error('Error updating activity:', error);
        return json({ 
            success: false, 
            message: error.message,
            validActivities: VALID_ACTIVITY_TYPES
        }, { status: 500 });
    } finally {
        if (mongoClient) {
            await mongoClient.close();
        }
    }
}
