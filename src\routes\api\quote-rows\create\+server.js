import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

// MongoDB connection URI
const uri = 'mongodb://localhost:27017';

// POST: Create a new quote row
export async function POST({ request }) {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    const db = client.db('ServiceContracts');
    
    // Using PascalCase for collection name per project convention
    const quotationRows = db.collection('QuotationRows');
    
    // Get the quote row data from the request body
    const quoteRowData = await request.json();
    
    // Validate required fields
    if (!quoteRowData.quotationId) {
      return new Response(JSON.stringify({ error: 'quotationId is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Validate and convert quotationId to ObjectId
    if (!ObjectId.isValid(quoteRowData.quotationId)) {
      return new Response(JSON.stringify({ error: 'Invalid quotationId format' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Convert quotationId string to ObjectId for storage
    quoteRowData.quotationId = new ObjectId(quoteRowData.quotationId);
    
    // Add timestamp fields
    quoteRowData.createdAt = new Date();
    quoteRowData.updatedAt = new Date();
    
    // Insert the new quote row
    const result = await quotationRows.insertOne(quoteRowData);
    
    // Return the new quote row ID
    return json({ 
      success: true, 
      id: result.insertedId.toString(),
      message: 'Quote row created successfully'
    });
    
  } catch (error) {
    console.error('Error creating quote row:', error);
    return json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    }, { status: 500 });
  } finally {
    await client.close();
  }
}
