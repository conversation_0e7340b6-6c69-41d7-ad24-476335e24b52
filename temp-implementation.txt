// Add these state variables after the existing state variables
let addingNewService = false;
let newServiceData = {
  year: new Date().getFullYear(),
  month: new Date().getMonth() + 1,
  hours: 0,
  activity: '',
  hasFixed: false
};

// Add these functions after the existing functions
function addNewService() {
  addingNewService = true;
  // Set default values for the new service
  newServiceData = {
    year: filterYear || new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    hours: 0,
    activity: '',
    hasFixed: false
  };
}

async function saveNewService() {
  if (saving) return;
  
  try {
    saving = true;
    
    const formData = new FormData();
    formData.append('data', JSON.stringify({
      computerId,
      year: newServiceData.year,
      month: newServiceData.month,
      hours: newServiceData.hours,
      activity: newServiceData.activity,
      hasFixed: newServiceData.hasFixed
    }));
    
    const response = await fetch(`/computer-id/${computerId}/service-activities/service-planning?/updateWorkload`, {
      method: 'POST',
      body: formData
    });

    const result = await response.json();
    
    if (result.success) {
      // Refresh data from server
      await invalidate(`/computer-id/${computerId}/service-activities/service-planning`);
      addingNewService = false;
    } else {
      alert(`Failed to add service: ${result.error || 'Unknown error'}`);
    }
  } catch (error) {
    console.error('Error adding service:', error);
    alert('An error occurred while adding the service data');
  } finally {
    saving = false;
  }
}

function cancelNewService() {
  addingNewService = false;
}

// Add this UI component after the selectedCell edit panel
{#if addingNewService}
  <div class="edit-panel">
    <h3>Add New Service</h3>
    <div class="edit-form">
      <div class="form-group">
        <label for="new-year">Year:</label>
        <select id="new-year" bind:value={newServiceData.year} class="form-control">
          {#each years.concat([new Date().getFullYear() + 1, new Date().getFullYear() + 2])
            .filter((y, i, arr) => arr.indexOf(y) === i).sort() as year}
            <option value={year}>{year}</option>
          {/each}
        </select>
      </div>
      
      <div class="form-group">
        <label for="new-month">Month:</label>
        <select id="new-month" bind:value={newServiceData.month} class="form-control">
          {#each months as month, i}
            <option value={i + 1}>{month}</option>
          {/each}
        </select>
      </div>
      
      <div class="form-group">
        <label for="new-hours">Machine Hours:</label>
        <input 
          type="number" 
          id="new-hours" 
          bind:value={newServiceData.hours} 
          min="0"
          class="form-control"
        />
      </div>
      
      <div class="form-group">
        <label for="new-activity">Activity:</label>
        <select id="new-activity" bind:value={newServiceData.activity} class="form-control">
          <option value="">-- Select Activity --</option>
          {#each activities as activity}
            <option value={activity}>{activity}</option>
          {/each}
        </select>
      </div>
      
      <div class="form-group checkbox">
        <label for="new-fixed">
          <input type="checkbox" id="new-fixed" bind:checked={newServiceData.hasFixed} />
          Completed
        </label>
      </div>
      
      <div class="form-actions">
        <button class="secondary-button" on:click={cancelNewService}>Cancel</button>
        <button class="primary-button" on:click={saveNewService} disabled={saving}>
          {saving ? 'Saving...' : 'Save'}
        </button>
      </div>
    </div>
  </div>
{/if}

// Add this button to the footer section
<div class="stats">
  <p>Total records: {totalRecords}</p>
  <button class="primary-button add-service-button" on:click={addNewService}>
    Add Service
  </button>
</div>

// Add this CSS to the style section
.add-service-button {
  margin-left: 1rem;
}
