import { error } from '@sveltejs/kit';
import { MongoClient, ObjectId } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

/**
 * @type {import('./$types').PageServerLoad}
 */
export async function load({ params }) {
  let client;
  
  try {
    const { id } = params;
    
    // Validate computer ID
    if (!id || !ObjectId.isValid(id)) {
      throw error(400, 'Invalid computer ID');
    }
    
    // Connect to MongoDB
    client = new MongoClient(uri);
    await client.connect();
    const db = client.db(dbName);
    
    // Get computer details with customer information
    const pipeline = [
      { 
        $match: { 
          _id: new ObjectId(id) 
        } 
      },
      {
        $lookup: {
          from: 'Customers',
          localField: 'customerId',
          foreignField: '_id',
          as: 'customer'
        }
      },
      {
        $unwind: {
          path: '$customer',
          preserveNullAndEmptyArrays: true
        }
      }
    ];
    
    const computers = await db.collection('CustomerComputers')
      .aggregate(pipeline)
      .toArray();
    
    if (computers.length === 0) {
      throw error(404, 'Computer not found');
    }
    
    const computer = computers[0];
    
    // Transform data for client-side use
    const serializedComputer = {
      ...computer,
      _id: computer._id.toString(),
      customerId: computer.customerId.toString(),
      customer: computer.customer ? {
        ...computer.customer,
        _id: computer.customer._id.toString()
      } : null,
      purchaseDate: computer.purchaseDate ? new Date(computer.purchaseDate) : null,
      warrantyEndDate: computer.warrantyEndDate ? new Date(computer.warrantyEndDate) : null,
      installationDate: computer.installationDate ? new Date(computer.installationDate) : null,
      manufactureDate: computer.manufactureDate ? new Date(computer.manufactureDate) : null,
      createdAt: computer.createdAt ? new Date(computer.createdAt) : null,
      updatedAt: computer.updatedAt ? new Date(computer.updatedAt) : null
    };
    
    return {
      computer: serializedComputer
    };
  } catch (err) {
    console.error('Error loading computer details:', err);
    if (err.status) {
      throw err; // Re-throw SvelteKit errors
    }
    throw error(500, 'Failed to load computer details');
  } finally {
    if (client) {
      await client.close();
    }
  }
}

/**
 * @type {import('./$types').Actions}
 */
export const actions = {
  updateComputer: async ({ request, params }) => {
    let client;
    
    try {
      const { id } = params;
      
      // Validate computer ID
      if (!id || !ObjectId.isValid(id)) {
        return { success: false, error: 'Invalid computer ID' };
      }
      
      const formData = await request.formData();
      
      // Get required fields
      const serialNumber = formData.get('serialNumber')?.toString();
      const model = formData.get('model')?.toString();
      const productType = formData.get('productType')?.toString();
      
      // Validate required fields
      if (!serialNumber || !model || !productType) {
        return { success: false, error: 'Missing required fields' };
      }
      
      // Generate a name for the computer if not provided
      const name = formData.get('name')?.toString() || `${model} - ${serialNumber}`;
      
      // Parse numeric values
      const operatingHours = parseInt(formData.get('operatingHours')?.toString() || '0', 10);
      
      // Create update data object
      const updateData = {
        name,
        serialNumber,
        model,
        productType,
        productDesignation: formData.get('productDesignation')?.toString() || '',
        engineType: formData.get('engineType')?.toString() || '',
        operatingHours: isNaN(operatingHours) ? 0 : operatingHours,
        installationDate: formData.get('installationDate')?.toString() || null,
        manufactureDate: formData.get('manufactureDate')?.toString() || null,
        isActive: formData.get('isActive') === 'on',
        notes: formData.get('notes')?.toString() || '',
        updatedAt: new Date()
      };
      
      client = new MongoClient(uri);
      await client.connect();
      const db = client.db(dbName);
      const computersCollection = db.collection('CustomerComputers');
      
      // Update the computer
      const result = await computersCollection.updateOne(
        { _id: new ObjectId(id) },
        { $set: updateData }
      );
      
      if (!result.matchedCount) {
        return { success: false, error: 'Computer not found' };
      }
      
      if (!result.modifiedCount) {
        return { success: false, error: 'No changes were made' };
      }
      
      return { 
        success: true, 
        message: 'Computer updated successfully'
      };
    } catch (err) {
      console.error('Error updating computer:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Unknown error' 
      };
    } finally {
      if (client) {
        await client.close();
      }
    }
  },
  
  deleteComputer: async ({ params }) => {
    let client;
    
    try {
      const { id } = params;
      
      // Validate computer ID
      if (!id || !ObjectId.isValid(id)) {
        return { success: false, error: 'Invalid computer ID' };
      }
      
      client = new MongoClient(uri);
      await client.connect();
      const db = client.db(dbName);
      
      // Delete computer
      const result = await db.collection('CustomerComputers').deleteOne({
        _id: new ObjectId(id)
      });
      
      if (!result.deletedCount) {
        return { success: false, error: 'Computer not found or already deleted' };
      }
      
      return { 
        success: true, 
        message: 'Computer deleted successfully',
        redirect: '/customer-computers'
      };
    } catch (err) {
      console.error('Error deleting computer:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Unknown error' 
      };
    } finally {
      if (client) {
        await client.close();
      }
    }
  }
};
