import { MongoClient } from 'mongodb';

// From memory: use localhost and ServiceContracts database
const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);
const dbName = 'ServiceContracts';

async function main() {
    try {
        console.log('Connecting to MongoDB...');
        await client.connect();
        console.log('Connected successfully to MongoDB');

        const db = client.db(dbName);

        // From memory: use PascalCase for collection names
        const productValidityGroupCollection = db.collection('ProductValidityGroup');
        const baseServicesCollection = db.collection('BaseServices');
        const productDesignationCollection = db.collection('ProductDesignation');

        // Create indexes
        await productDesignationCollection.createIndexes([
            { key: { ProductDesignation: 1 }, unique: true },
            { key: { ProductValidityGroup: 1 } }
        ]);

        // Get unique product designations from ProductValidityGroup collection
        const productValidityGroups = await productValidityGroupCollection.find({}).toArray();
        const designations = new Map();

        for (const group of productValidityGroups) {
            if (group.ProductDesignation && group.ProductValidityGroup) {
                designations.set(group.ProductDesignation, {
                    ProductDesignation: group.ProductDesignation,
                    ProductValidityGroup: group.ProductValidityGroup,
                    Description: '',
                    CreatedAt: new Date(),
                    UpdatedAt: new Date()
                });
            }
        }

        // Get unique product validity groups from BaseServices
        const baseServices = await baseServicesCollection.find({}).toArray();
        for (const service of baseServices) {
            if (service['Product Validity Group']) {
                const group = service['Product Validity Group'];
                if (!Array.from(designations.values()).some(d => d.ProductValidityGroup === group)) {
                    designations.set(group, {
                        ProductDesignation: group,
                        ProductValidityGroup: group,
                        Description: '',
                        CreatedAt: new Date(),
                        UpdatedAt: new Date()
                    });
                }
            }
        }

        // Insert all unique designations
        if (designations.size > 0) {
            const result = await productDesignationCollection.insertMany(
                Array.from(designations.values())
            );
            console.log(`Successfully inserted ${result.insertedCount} product designations`);
        } else {
            console.log('No product designations found to insert');
        }

    } catch (error) {
        console.error('Error:', error);
    } finally {
        await client.close();
        console.log('Connection closed');
    }
}

main().catch(console.error);
