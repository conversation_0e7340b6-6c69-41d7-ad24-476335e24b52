// Script to add German Regions to the database
import { MongoClient, ObjectId } from 'mongodb';

async function addGermanRegions() {
  const client = new MongoClient('mongodb://localhost:27017');
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db('service-management');
    const RegionsCollection = db.collection('Regions');
    
    // Check for existing German Regions
    const existingGermanRegions = await RegionsCollection.find({ country: 'Germany' }).toArray();
    console.log(`Found ${existingGermanRegions.length} existing German Regions`);
    
    // Add more German Regions if there are fewer than 5
    if (existingGermanRegions.length < 5) {
      const germanRegions = [
        {
          name: 'Bavaria',
          country: 'Germany',
          description: 'Southern German region including Munich',
          createdAt: new Date()
        },
        {
          name: 'Berlin',
          country: 'Germany',
          description: 'Capital city region',
          createdAt: new Date()
        },
        {
          name: 'North Rhine-Westphalia',
          country: 'Germany',
          description: 'Western German region including Cologne and Düsseldorf',
          createdAt: new Date()
        },
        {
          name: 'Saxony',
          country: 'Germany',
          description: 'Eastern German region including Dresden and Leipzig',
          createdAt: new Date()
        },
        {
          name: 'Hamburg',
          country: 'Germany',
          description: 'Northern German city-state',
          createdAt: new Date()
        }
      ];
      
      // Filter out Regions that already exist (by name)
      const existingNames = existingGermanRegions.map(region => region.name);
      const newRegions = germanRegions.filter(region => !existingNames.includes(region.name));
      
      if (newRegions.length > 0) {
        const result = await RegionsCollection.insertMany(newRegions);
        console.log(`Added ${result.insertedCount} new German Regions`);
      } else {
        console.log('No new German Regions to add');
      }
    } else {
      console.log('Already have enough German Regions');
    }
    
    // List all German Regions
    const allGermanRegions = await RegionsCollection.find({ country: 'Germany' }).toArray();
    console.log('All German Regions:');
    allGermanRegions.forEach(region => {
      console.log(`- ${region.name}: ${region.description}`);
    });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

// Run the function
addGermanRegions();
