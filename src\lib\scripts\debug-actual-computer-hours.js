import { MongoClient, ObjectId } from 'mongodb';

// Standard MongoDB connection using the project's convention
const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);
const dbName = 'ServiceContracts';
const collectionName = 'ActualComputerHours';

// Utility function to print formatted objects
function printObject(label, object) {
  console.log(`\n===== ${label} =====`);
  console.log(JSON.stringify(object, null, 2));
  console.log(`===== END ${label} =====\n`);
}

// Utility function to check for missing fields
function checkFields(doc, requiredFields) {
  const missingFields = [];
  for (const field of requiredFields) {
    if (doc[field] === undefined || doc[field] === null || doc[field] === '') {
      missingFields.push(field);
    }
  }
  return missingFields;
}

async function debugActualComputerHours() {
  try {
    console.log('Connecting to MongoDB...');
    await client.connect();
    console.log('Connected successfully to MongoDB');
    
    const db = client.db(dbName);
    const collection = db.collection(collectionName);
    
    // Get count of documents
    const count = await collection.countDocuments();
    console.log(`Total documents in ${collectionName}: ${count}`);
    
    // Get sample documents
    const docs = await collection.find({}).limit(10).toArray();
    console.log(`Retrieved ${docs.length} sample documents`);
    
    // Analyze the schema of the collection
    console.log('\n--- COLLECTION SCHEMA ANALYSIS ---');
    const fieldFrequency = {};
    const fieldTypes = {};
    
    docs.forEach((doc, index) => {
      console.log(`\nDocument #${index + 1} (ID: ${doc._id}):`);
      
      Object.keys(doc).forEach(field => {
        // Count field frequency
        fieldFrequency[field] = (fieldFrequency[field] || 0) + 1;
        
        // Determine field type
        const type = doc[field] === null ? 'null' : typeof doc[field];
        if (!fieldTypes[field]) {
          fieldTypes[field] = new Set();
        }
        fieldTypes[field].add(type);
        
        // Print field value
        console.log(`  ${field}: ${
          type === 'object' && doc[field] !== null ? 
            (doc[field] instanceof ObjectId ? 
              `ObjectId("${doc[field]}")` : 
              JSON.stringify(doc[field])
            ) : 
            doc[field]
        } (${type})`);
      });
      
      // Check for important fields
      const requiredFields = [
        'activity', 'hours', 'ReportDate', 'ReportTime', 
        'CalculatedHoursPerMonth', 'computerId', 'hasFixed', 'isIdle'
      ];
      
      const missing = checkFields(doc, requiredFields);
      if (missing.length > 0) {
        console.log(`  WARNING: Missing fields: ${missing.join(', ')}`);
      }
    });
    
    console.log('\n--- FIELD FREQUENCY ANALYSIS ---');
    console.log('Field frequencies (how many documents contain each field):');
    Object.entries(fieldFrequency)
      .sort((a, b) => b[1] - a[1])
      .forEach(([field, frequency]) => {
        const percentage = (frequency / docs.length * 100).toFixed(2);
        console.log(`  ${field}: ${frequency}/${docs.length} (${percentage}%)`);
      });
    
    console.log('\n--- FIELD TYPE ANALYSIS ---');
    console.log('Field types:');
    Object.entries(fieldTypes).forEach(([field, types]) => {
      console.log(`  ${field}: ${Array.from(types).join(', ')}`);
    });
    
    // Update any records missing ReportDate or ReportTime
    console.log('\n--- FIXING MISSING DATE/TIME FIELDS ---');
    const now = new Date();
    const formattedDate = now.toISOString().split('T')[0];
    const formattedTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
    
    const recordsNeedingFix = await collection.find({
      $or: [
        { ReportDate: { $exists: false } },
        { ReportDate: null },
        { ReportDate: '' },
        { ReportTime: { $exists: false } },
        { ReportTime: null },
        { ReportTime: '' }
      ]
    }).toArray();
    
    console.log(`Found ${recordsNeedingFix.length} records with missing date/time fields`);
    
    if (recordsNeedingFix.length > 0) {
      for (const record of recordsNeedingFix) {
        const update = { $set: {} };
        let needsUpdate = false;
        
        if (!record.ReportDate) {
          update.$set.ReportDate = formattedDate;
          needsUpdate = true;
        }
        
        if (!record.ReportTime) {
          update.$set.ReportTime = formattedTime;
          needsUpdate = true;
        }
        
        if (needsUpdate) {
          console.log(`Updating record ${record._id}:`, update.$set);
          const result = await collection.updateOne(
            { _id: record._id },
            update
          );
          console.log(`  Update result: ${result.modifiedCount} document(s) modified`);
        }
      }
    }
    
    // Display relationships with other collections
    console.log('\n--- RELATIONSHIP ANALYSIS ---');
    // Get unique computerIds
    const computerIds = await collection.distinct('computerId');
    console.log(`Found ${computerIds.length} unique computerIds`);
    
    const customerComputerCollection = db.collection('CustomerComputers');
    const linkedComputers = await customerComputerCollection.find({
      _id: { $in: computerIds.filter(id => id instanceof ObjectId) }
    }).toArray();
    
    console.log(`Found ${linkedComputers.length} linked computers in CustomerComputers collection`);
    
    if (linkedComputers.length > 0) {
      console.log('Sample linked computers:');
      linkedComputers.slice(0, 3).forEach((computer, index) => {
        console.log(`Computer #${index + 1}:`);
        console.log(`  _id: ${computer._id}`);
        console.log(`  name: ${computer.name || 'N/A'}`);
        console.log(`  customerId: ${computer.customerId || 'N/A'}`);
      });
    }
    
    console.log('\nDebug analysis completed successfully');
  } catch (error) {
    console.error('Error during debugging:', error);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the debug function
debugActualComputerHours();
