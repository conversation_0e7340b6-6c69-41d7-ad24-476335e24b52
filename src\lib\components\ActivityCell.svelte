<script lang="ts">
  // Props
  export let value: number | string = 0;
  export let activity: string = '';
  export let hasFixed: boolean = false;
  export let isSelected: boolean = false;
  
  // Computed
  $: hasActivity = !!activity && activity.trim() !== '';
  
  // Get activity class based on the activity name
  function getActivityClass(activity: string): string {
    if (!activity) return '';
    
    if (activity.toLowerCase().includes('contract')) return 'activity-contract';
    if (activity.toLowerCase().includes('delivery')) return 'activity-delivery';
    if (activity.toLowerCase().includes('service')) return 'activity-service';
    if (activity.toLowerCase().includes('repair')) return 'activity-repair';
    if (activity.toLowerCase().includes('inspection')) return 'activity-inspection';
    if (activity.toLowerCase().includes('maintenance')) return 'activity-maintenance';
    
    return 'activity-default';
  }
</script>

<div 
  class="activity-cell {hasFixed ? 'fixed' : ''} {isSelected ? 'selected' : ''} {hasActivity ? 'has-activity' : ''} {getActivityClass(activity)}"
  on:click
  on:keydown
>
  <div class="cell-content">
    <span class="value">{value}</span>
    {#if hasActivity}
      <span class="activity-name">{activity}</span>
    {/if}
    {#if hasFixed}
      <span class="fixed-indicator">*</span>
    {/if}
  </div>
</div>

<style>
  .activity-cell {
    background: #222b3a;
    color: #eaf1fb;
    min-height: 40px;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
    position: relative;
    padding: 0.5rem 0.25rem;
  }

  .activity-cell.fixed {
    background: #2563eb;
    color: #fff;
  }
  
  .activity-cell.selected {
    outline: 2px solid #60a5fa;
    z-index: 1;
  }

  .activity-cell:hover {
    background: #2d3748;
  }

  .activity-cell.fixed:hover {
    background: #1d4ed8;
  }

  .cell-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
  }

  .value {
    font-size: 1.1rem;
    font-weight: 500;
  }

  .activity-name {
    font-size: 0.75rem;
    color: #94a3b8;
    margin-top: 0.2rem;
    max-width: 58px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .fixed-indicator {
    position: absolute;
    top: 2px;
    right: 4px;
    font-size: 0.85rem;
  }
  
  .has-activity {
    font-weight: bold;
  }
  
  /* Activity colors */
  .activity-contract {
    border-left: 3px solid #3b82f6;
  }
  
  .activity-delivery {
    border-left: 3px solid #10b981;
  }
  
  .activity-service {
    border-left: 3px solid #f59e0b;
  }
  
  .activity-repair {
    border-left: 3px solid #ef4444;
  }
  
  .activity-inspection {
    border-left: 3px solid #8b5cf6;
  }
  
  .activity-maintenance {
    border-left: 3px solid #ec4899;
  }
  
  .activity-default {
    border-left: 3px solid #64748b;
  }
</style>
