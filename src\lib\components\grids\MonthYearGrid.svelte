<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';

  // Props
  export let years: number[] = [];
  export let months: string[] = [];
  export let title: string = 'Activity Grid';
</script>

<BaseGrid>
  <div class="month-year-grid">
    <header class="grid-header">
      <h2>{title}</h2>
    </header>
    
    <div class="grid-container">
      <div class="year-column-headers">
        <div class="corner-cell"></div>
        {#each years as year}
          <div class="year-header">{year}</div>
        {/each}
      </div>
      
      <div class="grid-body">
        {#each months as month, monthIdx}
          <div class="month-row">
            <div class="month-header">{month}</div>
            
            {#each years as year}
              <div class="grid-cell">
                <slot name="cell" {year} {monthIdx}></slot>
              </div>
            {/each}
          </div>
        {/each}
      </div>
    </div>
    
    <footer class="grid-footer">
      <slot name="footer"></slot>
    </footer>
  </div>
</BaseGrid>

<style>
  .month-year-grid {
    display: grid;
    grid-template-rows: auto 1fr auto;
    gap: 1rem;
    height: 100%;
    width: 100%;
  }
  
  .grid-header {
    padding: 0.5rem 1rem;
  }
  
  .grid-header h2 {
    margin: 0;
    color: #60a5fa;
    font-size: 1.5rem;
  }
  
  .grid-container {
    display: grid;
    grid-template-rows: auto 1fr;
    overflow: auto;
    border-radius: 8px;
    background: #1e293b;
  }
  
  .year-column-headers {
    display: grid;
    grid-template-columns: 100px repeat(auto-fit, minmax(80px, 1fr));
    position: sticky;
    top: 0;
    background: #0f172a;
    z-index: 2;
    border-bottom: 1px solid #334155;
  }
  
  .corner-cell {
    background: #0f172a;
    border-right: 1px solid #334155;
    border-bottom: 1px solid #334155;
  }
  
  .year-header {
    padding: 0.75rem 0.5rem;
    text-align: center;
    font-weight: 500;
    color: #94a3b8;
    border-right: 1px solid #334155;
    border-bottom: 1px solid #334155;
    background: #1e293b;
  }
  
  .grid-body {
    display: grid;
    grid-template-rows: repeat(auto-fill, minmax(60px, 1fr));
  }
  
  .month-row {
    display: grid;
    grid-template-columns: 100px repeat(auto-fit, minmax(80px, 1fr));
    border-bottom: 1px solid #334155;
  }
  
  .month-header {
    padding: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    background: #1e293b;
    color: #94a3b8;
    font-weight: 500;
    position: sticky;
    left: 0;
    z-index: 1;
    border-right: 1px solid #334155;
  }
  
  .grid-cell {
    padding: 0.25rem;
    border-right: 1px solid #334155;
    min-height: 60px;
    background: #0f172a;
  }
  
  .grid-footer {
    padding: 0.5rem 1rem;
  }
</style>
