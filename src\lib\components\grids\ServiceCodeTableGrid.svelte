<script lang="ts">
  import BaseGrid from './BaseGrid.svelte';
  
  export let columns: {id: string, label: string, width?: string}[] = [];
  export let showFilters: boolean = true;
  export let items: Record<string, any>[] = [];
  
  // Define CSS grid template columns based on column configuration
  $: gridTemplateColumns = columns.map(col => col.width || '1fr').join(' ') + ' auto';

  // Function to get the proper slot content with fallback
  function getDisplayValue(item: Record<string, any>, columnId: string) {
    return item[columnId] !== undefined ? item[columnId] : '';
  }
</script>

<BaseGrid>
  <div class="grid-container">
    {#if showFilters}
      <!-- Filter section -->
      <div class="filter-section" style="grid-template-columns: {gridTemplateColumns};">
        <!-- We'll render the filter fields passed in from the parent -->
        <slot name="filter-row"></slot>
        <div class="filter-cell actions-column">
          <slot name="filter-actions"></slot>
        </div>
      </div>
    {/if}
    
    <!-- Headers section -->
    <div class="header-section" style="grid-template-columns: {gridTemplateColumns};">
      {#each columns as column}
        <div class="header-cell" data-column-id={column.id}>
          {column.label}
        </div>
      {/each}
      <div class="header-cell actions-column">
        Actions
      </div>
    </div>
    
    <!-- Data rows section -->
    <div class="data-section">
      {#if items.length === 0}
        <div class="empty-message">
          <slot name="empty-message">No records found.</slot>
        </div>
      {:else}
        {#each items as item}
          <div class="data-row" style="grid-template-columns: {gridTemplateColumns};">
            {#each columns as column}
              <div class="data-cell" data-column-id={column.id}>
                {getDisplayValue(item, column.id)}
              </div>
            {/each}
            <div class="data-cell actions-column">
              <slot name="row-actions" {item}>
                <!-- Default actions will be provided by the parent -->
              </slot>
            </div>
          </div>
        {/each}
      {/if}
    </div>
  </div>
</BaseGrid>

<style>
  .grid-container {
    display: grid;
    grid-template-rows: auto auto 1fr;
    width: 100%;
    gap: 0;
    overflow-x: auto;
  }
  
  .filter-section, .header-section, .data-row {
    display: grid;
    width: 100%;
    min-width: max-content;
  }
  
  .filter-cell, .header-cell, .data-cell {
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    overflow: visible;
    white-space: normal;
    word-break: break-word;
  }
  
  .header-cell {
    background-color: #f7fafc;
    font-weight: 600;
    text-align: left;
    border-bottom: 2px solid #a0aec0;
  }
  
  .filter-placeholder {
    color: #a0aec0;
    font-style: italic;
    font-size: 0.9rem;
  }
  
  .actions-column {
    justify-self: center;
    text-align: center;
  }
  
  .data-section {
    display: grid;
    grid-template-rows: auto;
  }
  
  .data-row {
    border-bottom: 1px solid #e2e8f0;
  }
  
  .data-row:nth-child(even) {
    background-color: #f9fafb;
  }
  
  .data-row:hover {
    background-color: #edf2f7;
  }
  
  .empty-message {
    grid-column: 1 / -1;
    padding: 1rem;
    text-align: center;
    color: #718096;
  }
  
  @media (max-width: 768px) {
    .grid-container {
      overflow-x: auto;
    }
  }
</style>
