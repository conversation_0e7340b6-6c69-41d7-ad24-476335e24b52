import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

async function updateLabourTimeCollection() {
    const client = new MongoClient(uri);
    
    try {
        await client.connect();
        console.log('Connected to MongoDB');
        
        const db = client.db(dbName);
        const labourTimeCollection = db.collection('LabourTime');

        // First, get all documents
        const documents = await labourTimeCollection.find({}).toArray();
        console.log(`Found ${documents.length} documents to update`);

        // Update each document
        for (const doc of documents) {
            const [servicePart, computerPart] = (doc['Service Code'] || '').split('-');
            
            const updates = {
                $set: {
                    'ServicePhase': servicePart || '',
                    'ComputerCategory': computerPart || '',
                    'VST Hours': parseFloat(doc['VST Hours']?.toString().replace(',', '.')) || 0
                }
            };

            await labourTimeCollection.updateOne(
                { _id: doc._id },
                updates
            );
        }

        // Verify updates
        const updatedDocs = await labourTimeCollection.find({}).limit(5).toArray();
        console.log('\nSample updated documents:');
        updatedDocs.forEach(doc => {
            console.log(doc);
        });
        
    } catch (error) {
        console.error('Error updating collection:', error);
    } finally {
        await client.close();
        console.log('\nDisconnected from MongoDB');
    }
}

// Run the update
updateLabourTimeCollection().catch(console.error);
