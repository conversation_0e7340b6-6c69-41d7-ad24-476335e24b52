import { ObjectId } from 'mongodb';
import serviceContractDB, { COLLECTIONS } from '$lib/server/serviceContractDB';

/**
 * @typedef {Object} Computer
 * @property {import('mongodb').ObjectId} _id
 * @property {string} [Category]
 * @property {string} [ProductValidityGroup]
 */

/**
 * @typedef {Object} ServicePackage
 * @property {import('mongodb').ObjectId} _id
 * @property {string} [name]
 * @property {string} [serviceCode]
 * @property {string} [ServiceActivity]
 * @property {string} [packageType]
 * @property {number} [cost]
 * @property {boolean} [required]
 * @property {boolean} [includeInOffer]
 * @property {boolean} [customerSpecific]
 * @property {boolean} [oemImporter]
 * @property {boolean} [fleetOwner]
 */

/**
 * @typedef {Object} BaseService
 * @property {import('mongodb').ObjectId} _id
 * @property {string} [ServiceActivity]
 * @property {string} [serviceCode]
 * @property {number} [cost]
 * @property {boolean} [required]
 * @property {boolean} [includeInOffer]
 */

/**
 * @typedef {Object} SupportService
 * @property {import('mongodb').ObjectId} _id
 * @property {string} [ServiceActivity]
 * @property {string} [serviceCode]
 * @property {number} [cost]
 * @property {boolean} [required]
 * @property {boolean} [includeInOffer]
 */

/**
 * @typedef {Object} QuotationLine
 * @property {string} quotationId
 * @property {string} lineType
 * @property {string} lineNumber
 * @property {string} serviceCode
 * @property {string} ServiceActivity
 * @property {number} cost
 * @property {boolean} customerSpecific
 * @property {boolean} oemImporter
 * @property {boolean} fleetOwner
 * @property {boolean} includeInOffer
 * @property {boolean} required
 * @property {string} notes
 * @property {Date} createdAt
 * @property {Date} updatedAt
 */

/**
 * @typedef {Object} ExistingContract
 * @property {import('mongodb').ObjectId} _id
 * @property {import('mongodb').ObjectId} computerId
 * @property {number} contractLength
 * @property {Date} startDate
 */

/**
 * @typedef {Object} QuotationPackages
 * @property {Object[]} baseContract
 * @property {Object[]} repairPackages
 * @property {Object[]} supportServices
 * @property {Object[]} replacementServices
 * @property {Object} basicInfo
 */

/**
 * @typedef {Object} LoadResponse
 * @property {Object} computer
 * @property {QuotationPackages} quotationPackages
 * @property {boolean} success
 * @property {string} [error]
 */

/**
 * @typedef {Object} LoadParams
 * @property {string} id
 */

/**
 * @typedef {Object} QuotationBasicInfo
 * @property {number} desiredContractLength
 * @property {Date} desiredContractStartDate
 */

/**
 * @typedef {Object} QuotationData
 * @property {import('mongodb').ObjectId} computerId
 * @property {string} customerName
 * @property {string} customerType
 * @property {Array<any>} baseContract
 * @property {Array<any>} repairPackages
 * @property {Array<any>} supportServices
 * @property {Array<any>} replacementServices
 * @property {number} totalCost
 * @property {QuotationBasicInfo} basicInfo
 * @property {Date} createdAt
 * @property {string} [quotationId]
 * @property {string} [notes]
 */

/**
 * @typedef {Object} QuotationLineInput
 * @property {string} id
 * @property {string} [serviceCode]
 * @property {string} [ServiceActivity]
 * @property {string} [name]
 * @property {number} [cost]
 * @property {boolean} [customerSpecific]
 * @property {boolean} [oemImporter]
 * @property {boolean} [fleetOwner]
 * @property {boolean} includeInOffer
 * @property {boolean} [required]
 * @property {string} [notes]
 */

/**
 * Loads service quotation data for a given computer ID
 * @param {LoadParams} params
 * @returns {Promise<LoadResponse>}
 */
/** @type {import('./$types').PageServerLoad} */
export async function load({ params }) {
    try {
        const db = serviceContractDB.db;
        const computerId = params.id;
        
        console.log(`Loading service quotation data for computer ID: ${computerId}`);
        
        // Get computer details
        const computer = await db.collection('CustomerComputers')
            .findOne({ _id: new ObjectId(computerId) });
            
        if (!computer) {
            throw new Error('Computer not found');
        }
        
        // Get service packages with proper null checks
        const servicePackages = await db.collection('ServicePackages')
            .find({ 
                active: true,
                $or: [
                    { universalOffer: true },
                    { computerCategories: { $in: [computer.Category || ''] } }
                ]
            })
            .sort({ packageLevel: 1 })
            .toArray();
            
        // Get base service offerings with proper null checks
        const baseServices = await db.collection('BaseServices')
            .find({
                $or: [
                    { universalOffer: true },
                    { ProductValidityGroup: computer.ProductValidityGroup || "" }
                ]
            })
            .toArray();
            
        // Get extended support offerings with proper null checks
        const supportServices = await db.collection('SupportServices')
            .find({
                $or: [
                    { universalOffer: true },
                    { applicableCategories: { $in: [computer.Category || ''] } }
                ]
            })
            .toArray();
            
        // Get existing contract if any
        const existingContract = await db.collection('ServiceContracts')
            .findOne({ computerId: new ObjectId(computerId) });
            
        // Get any existing quotation lines
        const quotationLines = (await db.collection(COLLECTIONS.QUOTATION_LINES)
            .find({ computerId: new ObjectId(computerId) })
            .toArray())
            .map(doc => ({
                quotationId: doc.quotationId,
                lineType: doc.lineType,
                lineNumber: doc.lineNumber,
                serviceCode: doc.serviceCode,
                ServiceActivity: doc.ServiceActivity,
                cost: doc.cost,
                customerSpecific: doc.customerSpecific,
                oemImporter: doc.oemImporter,
                fleetOwner: doc.fleetOwner,
                includeInOffer: doc.includeInOffer,
                required: doc.required,
                notes: doc.notes,
                createdAt: doc.createdAt,
                updatedAt: doc.updatedAt
            }));
            
        // Build the quotation packages
        const quotationPackages = buildQuotationPackages(
            /** @type {Computer} */ (computer),
            /** @type {ServicePackage[]} */ (servicePackages),
            /** @type {BaseService[]} */ (baseServices),
            /** @type {SupportService[]} */ (supportServices),
            /** @type {ExistingContract | null} */ (existingContract),
            /** @type {QuotationLine[]} */ (quotationLines)
        );
        
        return {
            computer: serviceContractDB.serializeDocument(computer),
            quotationPackages,
            success: true
        };
        
    } catch (error) {
        console.error('Error loading service quotation data:', error);
        return {
            computer: null,
            quotationPackages: null,
            success: false,
            error: error instanceof Error ? error.message : 'Failed to load service quotation data'
        };
    }
}

/**
 * Builds quotation packages from various data sources
 * @param {Computer} computer
 * @param {ServicePackage[]} servicePackages
 * @param {BaseService[]} baseServices
 * @param {SupportService[]} supportServices
 * @param {ExistingContract | null} existingContract
 * @param {QuotationLine[]} quotationLines
 * @returns {Object} Quotation packages object
 */
function buildQuotationPackages(computer, servicePackages, baseServices, supportServices, existingContract, quotationLines) {
    return {
        baseContract: mapBaseServices(baseServices, quotationLines, 'BaseContract'),
        repairPackages: mapRepairPackages(servicePackages, quotationLines, 'DealerAddOns'),
        supportServices: mapSupportServices(supportServices, quotationLines, 'SupportServices'),
        replacementServices: mapReplacementServices(servicePackages, quotationLines, 'ReplacementServices'),
        basicInfo: {
            desiredContractLength: existingContract?.contractLength || 12,
            desiredContractStartDate: existingContract?.startDate || new Date()
        }
    };
}

/**
 * Maps base services to quotation lines
 * @param {BaseService[]} baseServices
 * @param {QuotationLine[]} quotationLines
 * @param {string} lineType
 * @returns {Object[]}
 */
function mapBaseServices(baseServices, quotationLines, lineType) {
    return baseServices.map(service => {
        // Find matching quotation line
        const existingLine = quotationLines.find(line => 
            line.serviceCode === service.serviceCode && 
            line.lineType === lineType
        );
        
        return {
            id: service._id.toString(),
            serviceCode: service.serviceCode || '',
            ServiceActivity: service.ServiceActivity || '',
            cost: service.cost || 0,
            customerSpecific: existingLine?.customerSpecific || false,
            oemImporter: existingLine?.oemImporter || false,
            fleetOwner: existingLine?.fleetOwner || false,
            includeInOffer: existingLine?.includeInOffer || false,
            required: service.required || false,
            notes: existingLine?.notes || ''
        };
    });
}

/**
 * Maps repair packages to quotation lines
 * @param {ServicePackage[]} servicePackages
 * @param {QuotationLine[]} quotationLines
 * @param {string} lineType
 * @returns {Object[]}
 */
function mapRepairPackages(servicePackages, quotationLines, lineType) {
    return servicePackages
        .filter(pkg => pkg.packageType === 'repair')
        .map(pkg => {
            const existingLine = quotationLines.find(line => 
                line.serviceCode === pkg.serviceCode && 
                line.lineType === lineType
            );
            
            return {
                id: pkg._id.toString(),
                serviceCode: pkg.serviceCode || '',
                ServiceActivity: pkg.ServiceActivity || pkg.name || '',
                cost: pkg.cost || 0,
                customerSpecific: existingLine?.customerSpecific || false,
                oemImporter: existingLine?.oemImporter || false,
                fleetOwner: existingLine?.fleetOwner || false,
                includeInOffer: existingLine?.includeInOffer || false,
                required: pkg.required || false,
                notes: existingLine?.notes || ''
            };
        });
}

/**
 * Maps support services to quotation lines
 * @param {SupportService[]} supportServices
 * @param {QuotationLine[]} quotationLines
 * @param {string} lineType
 * @returns {Object[]}
 */
function mapSupportServices(supportServices, quotationLines, lineType) {
    return supportServices.map(service => {
        const existingLine = quotationLines.find(line => 
            line.serviceCode === service.serviceCode && 
            line.lineType === lineType
        );
        
        return {
            id: service._id.toString(),
            serviceCode: service.serviceCode || '',
            ServiceActivity: service.ServiceActivity || '',
            cost: service.cost || 0,
            customerSpecific: existingLine?.customerSpecific || false,
            oemImporter: existingLine?.oemImporter || false,
            fleetOwner: existingLine?.fleetOwner || false,
            includeInOffer: existingLine?.includeInOffer || false,
            required: service.required || false,
            notes: existingLine?.notes || ''
        };
    });
}

/**
 * Maps replacement services to quotation lines
 * @param {ServicePackage[]} servicePackages
 * @param {QuotationLine[]} quotationLines
 * @param {string} lineType
 * @returns {Object[]}
 */
function mapReplacementServices(servicePackages, quotationLines, lineType) {
    return servicePackages
        .filter(pkg => pkg.packageType === 'replacement')
        .map(pkg => {
            const existingLine = quotationLines.find(line => 
                line.serviceCode === pkg.serviceCode && 
                line.lineType === lineType
            );
            
            return {
                id: pkg._id.toString(),
                serviceCode: pkg.serviceCode || '',
                ServiceActivity: pkg.ServiceActivity || pkg.name || '',
                cost: pkg.cost || 0,
                customerSpecific: existingLine?.customerSpecific || false,
                oemImporter: existingLine?.oemImporter || false,
                fleetOwner: existingLine?.fleetOwner || false,
                includeInOffer: existingLine?.includeInOffer || false,
                required: pkg.required || false,
                notes: existingLine?.notes || ''
            };
        });
}

/**
 * Calculate total amount from quotation data
 * @param {QuotationData} quotationData
 * @returns {number}
 */
function calculateTotalAmount(quotationData) {
    let total = 0;
    
    // Calculate base contract total
    if (quotationData.baseContract && Array.isArray(quotationData.baseContract)) {
        total += quotationData.baseContract
            .filter(line => line && line.includeInOffer)
            .reduce((sum, line) => sum + (line.cost || 0), 0);
    }
    
    // Calculate repair packages total
    if (quotationData.repairPackages && Array.isArray(quotationData.repairPackages)) {
        total += quotationData.repairPackages
            .filter(line => line && line.includeInOffer)
            .reduce((sum, line) => sum + (line.cost || 0), 0);
    }
    
    // Calculate support services total
    if (quotationData.supportServices && Array.isArray(quotationData.supportServices)) {
        total += quotationData.supportServices
            .filter(line => line && line.includeInOffer)
            .reduce((sum, line) => sum + (line.cost || 0), 0);
    }
    
    // Calculate replacement services total
    if (quotationData.replacementServices && Array.isArray(quotationData.replacementServices)) {
        total += quotationData.replacementServices
            .filter(line => line && line.includeInOffer)
            .reduce((sum, line) => sum + (line.cost || 0), 0);
    }
    
    return total;
}

/** @type {import('./$types').Actions} */
export const actions = {
    updateQuotation: async ({ request }) => {
        try {
            const formData = await request.formData();
            const quotationDataStr = formData.get('quotationData');
            
            if (!quotationDataStr || typeof quotationDataStr !== 'string') {
                throw new Error('Invalid quotation data format');
            }
            
            /** @type {QuotationData} */
            const quotationData = JSON.parse(quotationDataStr);
            const computerId = quotationData.computerId;
            
            if (!computerId) {
                throw new Error('Computer ID is required');
            }
            
            const db = serviceContractDB.db;
            
            // First handle the quotation header
            let quotationHeaderId = quotationData.quotationId || null;
            
            // Create or update the quotation header
            if (!quotationHeaderId) {
                // Create new quotation header
                const newHeader = {
                    computerId: new ObjectId(computerId),
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    status: 'Draft',
                    validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
                    amount: calculateTotalAmount(quotationData),
                    contractLength: quotationData.basicInfo?.desiredContractLength || 12,
                    contractStartDate: quotationData.basicInfo?.desiredContractStartDate || new Date(),
                    notes: quotationData.notes || ''
                };
                
                const result = await db.collection(COLLECTIONS.QUOTATION_HEADERS).insertOne(newHeader);
                quotationHeaderId = result.insertedId.toString();
            } else {
                // Update existing quotation header
                await db.collection(COLLECTIONS.QUOTATION_HEADERS).updateOne(
                    { _id: new ObjectId(quotationHeaderId) },
                    {
                        $set: {
                            updatedAt: new Date(),
                            amount: calculateTotalAmount(quotationData),
                            contractLength: quotationData.basicInfo?.desiredContractLength || 12,
                            contractStartDate: quotationData.basicInfo?.desiredContractStartDate || new Date(),
                            notes: quotationData.notes || ''
                        }
                    }
                );
            }
            
            // Delete existing quotation lines
            if (quotationHeaderId) {
                await db.collection(COLLECTIONS.QUOTATION_LINES).deleteMany({
                    quotationId: quotationHeaderId
                });
            }
            
            /** @type {QuotationLine[]} */
            const quotationLines = [];
            
            // Process base contract lines
            if (quotationData.baseContract && Array.isArray(quotationData.baseContract)) {
                quotationData.baseContract.forEach((/** @type {QuotationLineInput} */ line) => {
                    if (line && line.includeInOffer) {
                        quotationLines.push({
                            quotationId: quotationHeaderId,
                            lineType: 'BaseContract',
                            lineNumber: line.id,
                            serviceCode: line.serviceCode || '',
                            ServiceActivity: line.ServiceActivity || line.name || '',
                            cost: line.cost || 0,
                            customerSpecific: line.customerSpecific || false,
                            oemImporter: line.oemImporter || false,
                            fleetOwner: line.fleetOwner || false,
                            includeInOffer: true,
                            required: line.required || false,
                            notes: line.notes || '',
                            createdAt: new Date(),
                            updatedAt: new Date()
                        });
                    }
                });
            }
            
            // Process repair packages
            if (quotationData.repairPackages && Array.isArray(quotationData.repairPackages)) {
                quotationData.repairPackages.forEach((/** @type {QuotationLineInput} */ line) => {
                    if (line && line.includeInOffer) {
                        quotationLines.push({
                            quotationId: quotationHeaderId,
                            lineType: 'DealerAddOns',
                            lineNumber: line.id,
                            serviceCode: line.serviceCode || '',
                            ServiceActivity: line.ServiceActivity || line.name || '',
                            cost: line.cost || 0,
                            customerSpecific: line.customerSpecific || false,
                            oemImporter: line.oemImporter || false,
                            fleetOwner: line.fleetOwner || false,
                            includeInOffer: true,
                            required: line.required || false,
                            notes: line.notes || '',
                            createdAt: new Date(),
                            updatedAt: new Date()
                        });
                    }
                });
            }
            
            // Process support services
            if (quotationData.supportServices && Array.isArray(quotationData.supportServices)) {
                quotationData.supportServices.forEach((/** @type {QuotationLineInput} */ line) => {
                    if (line && line.includeInOffer) {
                        quotationLines.push({
                            quotationId: quotationHeaderId,
                            lineType: 'SupportServices',
                            lineNumber: line.id,
                            serviceCode: line.serviceCode || '',
                            ServiceActivity: line.ServiceActivity || line.name || '',
                            cost: line.cost || 0,
                            customerSpecific: line.customerSpecific || false,
                            oemImporter: line.oemImporter || false,
                            fleetOwner: line.fleetOwner || false,
                            includeInOffer: true,
                            required: line.required || false,
                            notes: line.notes || '',
                            createdAt: new Date(),
                            updatedAt: new Date()
                        });
                    }
                });
            }
            
            // Process replacement services
            if (quotationData.replacementServices && Array.isArray(quotationData.replacementServices)) {
                quotationData.replacementServices.forEach((/** @type {QuotationLineInput} */ line) => {
                    if (line && line.includeInOffer) {
                        quotationLines.push({
                            quotationId: quotationHeaderId,
                            lineType: 'ReplacementServices',
                            lineNumber: line.id,
                            serviceCode: line.serviceCode || '',
                            ServiceActivity: line.ServiceActivity || line.name || '',
                            cost: line.cost || 0,
                            customerSpecific: line.customerSpecific || false,
                            oemImporter: line.oemImporter || false,
                            fleetOwner: line.fleetOwner || false,
                            includeInOffer: true,
                            required: line.required || false,
                            notes: line.notes || '',
                            createdAt: new Date(),
                            updatedAt: new Date()
                        });
                    }
                });
            }
            
            // Insert all quotation lines
            if (quotationLines.length > 0) {
                await db.collection(COLLECTIONS.QUOTATION_LINES).insertMany(quotationLines);
            }
            
            // Get updated quotation header
            const updatedHeader = await db.collection(COLLECTIONS.QUOTATION_HEADERS)
                .findOne({ _id: new ObjectId(quotationHeaderId) });
                
            return {
                success: true,
                quotationHeader: updatedHeader ? serviceContractDB.serializeDocument(updatedHeader) : null
            };
            
        } catch (error) {
            console.error('Error updating quotation:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to update quotation'
            };
        }
    }
};
