import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function updateActualComputerHours() {
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db('ServiceContracts');
    const collection = db.collection('ActualComputerHours');
    
    // Get all documents
    const documents = await collection.find({}).toArray();
    console.log(`Found ${documents.length} documents to update`);
    
    // Counter for successful updates
    let updateCount = 0;
    
    // Process each document
    for (const doc of documents) {
      // Skip documents that don't have ReportDateTime
      if (!doc.ReportDateTime) {
        console.log(`Document with _id ${doc._id} has no ReportDateTime field, skipping`);
        continue;
      }
      
      // Parse the ReportDateTime
      const reportDateTime = new Date(doc.ReportDateTime);
      
      // Format the date as YYYY-MM-DD
      const reportDate = reportDateTime.toISOString().split('T')[0];
      
      // Format the time as HH:MM
      const hours = reportDateTime.getHours().toString().padStart(2, '0');
      const minutes = reportDateTime.getMinutes().toString().padStart(2, '0');
      const reportTime = `${hours}:${minutes}`;
      
      // Update the document
      const result = await collection.updateOne(
        { _id: doc._id },
        { 
          $set: { 
            ReportDate: reportDate,
            ReportTime: reportTime 
          },
          $unset: { 
            ReportDateTime: "" 
          }
        }
      );
      
      if (result.modifiedCount > 0) {
        updateCount++;
      }
    }
    
    console.log(`Successfully updated ${updateCount} documents`);
    
  } catch (err) {
    console.error('Error updating documents:', err);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the update function
updateActualComputerHours().catch(console.error);
