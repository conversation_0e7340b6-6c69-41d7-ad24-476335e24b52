import { MongoClient, ObjectId } from 'mongodb';
import { json } from '@sveltejs/kit';

const uri = 'mongodb://localhost:27017';
const dbName = 'ServiceContracts';

/** @type {import('./$types').RequestHandler} */
export async function GET({ url }) {
  const computerId = url.searchParams.get('computerId') || '682ae75f05ee3d76deed9776';
  const productDesignation = url.searchParams.get('productDesignation') || 'TAD1640-42GE-B';

  const client = new MongoClient(uri);

  try {
    await client.connect();
    const db = client.db(dbName);

    console.log('🔍 Starting data diagnostic...');

    // Check Workload collection
    console.log('📊 Checking Workload collection...');
    const workloadTotal = await db.collection('Workload').countDocuments();
    const workloadForComputer = await db.collection('Workload').countDocuments({
      computerId: new ObjectId(computerId)
    });

    // Get sample workload data for this computer
    const workloadSample = await db.collection('Workload')
      .find({ computerId: new ObjectId(computerId) })
      .sort({ year: 1, month: 1 })
      .limit(10)
      .toArray();

    // Check ServiceCodeAndActionType collection
    console.log('🔧 Checking ServiceCodeAndActionType collection...');
    const serviceCodeTotal = await db.collection('ServiceCodeAndActionType').countDocuments();
    const serviceCodeForProduct = await db.collection('ServiceCodeAndActionType').countDocuments({
      ProductValidityGroup: productDesignation
    });

    // Get sample service code data for this product
    const serviceCodeSample = await db.collection('ServiceCodeAndActionType')
      .find({ ProductValidityGroup: productDesignation })
      .sort({ InternalNoOfHours: 1 })
      .limit(10)
      .toArray();

    // Check all available product validity groups
    const allProductValidityGroups = await db.collection('ServiceCodeAndActionType')
      .distinct('ProductValidityGroup');

    // Also check ServicePlanProductDesignation collection for comparison
    console.log('📋 Checking ServicePlanProductDesignation collection...');
    const servicePlanTotal = await db.collection('ServicePlanProductDesignation').countDocuments();
    const servicePlanForProduct = await db.collection('ServicePlanProductDesignation').countDocuments({
      productDesignation: productDesignation
    });

    // Check Computer collection
    console.log('💻 Checking CustomerComputers collection...');
    const computer = await db.collection('CustomerComputers')
      .findOne({ _id: new ObjectId(computerId) });

    // Check if computer exists with string ID instead
    let computerStringId = null;
    if (!computer) {
      computerStringId = await db.collection('CustomerComputers')
        .findOne({ _id: computerId });
    }

    // Get all computers count
    const totalComputers = await db.collection('CustomerComputers').countDocuments();

    // Check collections that exist
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);

    const diagnostic = {
      timestamp: new Date().toISOString(),
      searchCriteria: {
        computerId,
        productDesignation
      },
      collections: {
        available: collectionNames,
        total: collections.length
      },
      workload: {
        totalRecords: workloadTotal,
        recordsForComputer: workloadForComputer,
        sampleData: workloadSample.map(w => ({
          year: w.year,
          month: w.month,
          hours: w.hours,
          activity: w.activity,
          accHours: w.accHours || 'NOT SET'
        })),
        hasAccHours: workloadSample.some(w => w.accHours !== undefined)
      },
      serviceCodeAndActionType: {
        totalRecords: serviceCodeTotal,
        recordsForProduct: serviceCodeForProduct,
        availableProducts: allProductValidityGroups.length,
        productValidityGroups: allProductValidityGroups.slice(0, 20), // First 20
        sampleData: serviceCodeSample.map(s => ({
          serviceCode: s.ServiceCode,
          actionType: s.ActionType,
          internalNoOfHours: s.InternalNoOfHours,
          activityPurpose: s.ActivityPurpose,
          partNumber: s.PartNumber,
          productValidityGroup: s.ProductValidityGroup
        }))
      },
      servicePlan: {
        totalRecords: servicePlanTotal,
        recordsForProduct: servicePlanForProduct,
        note: 'This collection appears to be empty - using ServiceCodeAndActionType instead'
      },
      computer: {
        totalComputers,
        found: !!computer || !!computerStringId,
        computerData: computer || computerStringId || null,
        searchedAsObjectId: !!computer,
        searchedAsString: !!computerStringId
      },
      issues: []
    };

    // Identify issues
    if (workloadTotal === 0) {
      diagnostic.issues.push('❌ No Workload data found in database');
    }

    if (workloadForComputer === 0) {
      diagnostic.issues.push(`❌ No Workload data found for computer ${computerId}`);
    }

    if (serviceCodeTotal === 0) {
      diagnostic.issues.push('❌ No ServiceCodeAndActionType data found in database');
    }

    if (serviceCodeForProduct === 0) {
      diagnostic.issues.push(`❌ No ServiceCodeAndActionType data found for product ${productDesignation}`);
    }

    if (!computer && !computerStringId) {
      diagnostic.issues.push(`❌ Computer ${computerId} not found in CustomerComputers collection`);
    }

    if (!diagnostic.workload.hasAccHours) {
      diagnostic.issues.push('⚠️ Workload data exists but AccHours field is missing or not calculated');
    }

    if (!allProductValidityGroups.includes(productDesignation)) {
      diagnostic.issues.push(`⚠️ Product designation '${productDesignation}' not found in ServiceCodeAndActionType ProductValidityGroups`);
    }

    if (servicePlanTotal === 0) {
      diagnostic.issues.push('ℹ️ ServicePlanProductDesignation collection is empty - using ServiceCodeAndActionType instead');
    }

    console.log('📋 Diagnostic complete:', diagnostic);

    return json(diagnostic);

  } catch (err) {
    console.error('❌ Error in diagnostic:', err);
    return json({
      error: 'Failed to run diagnostic',
      details: err.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  } finally {
    await client.close();
  }
}
