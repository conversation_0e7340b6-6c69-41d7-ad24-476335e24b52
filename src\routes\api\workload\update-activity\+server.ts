import { init } from '$lib/db';
import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async () => {
    try {
        const db = await init();
        const Workload = db.collection("Workload");
        
        // Update ALL documents to set activity to 'idle'
        const result = await Workload.updateMany(
            {}, // update all documents
            { 
                $set: { 
                    activity: 'idle',
                    updatedAt: new Date()
                } 
            }
        );

        return json({
            success: true,
            message: `Updated ${result.modifiedCount} workload records with 'idle' activity`,
            modifiedCount: result.modifiedCount,
            matchedCount: result.matchedCount
        });
    } catch (error) {
        console.error('Error updating workload activity:', error);
        return json(
            { 
                success: false, 
                message: 'Failed to update workload activity',
                error: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        );
    }
};
